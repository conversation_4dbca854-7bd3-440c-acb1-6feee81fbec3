#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const I18N_FILE_PATH = path.resolve(__dirname, '../src/i18n/i18n.fr.json')
const VERBOSE = process.argv.includes('verbose=1')
const REFERENCE_BRANCHES = ['origin/isoprod', 'origin/prod']

function debug(message, onlyVerbose = false) {
  if (!onlyVerbose || (onlyVerbose && VERBOSE)) {
    console.log(`[DEBUG] ${message}`)
  }
}

function handleError(error) {
  console.error('Error occurred:', error)
  console.error('Stack trace:', error.stack)

  try {
    debug('Attempting to abort merge due to error')
    execSync('git merge --abort')
    console.log('Git merge aborted successfully')
  } catch (abortError) {
    console.error('Failed to abort merge:', abortError)
  }

  process.exit(1)
}

function cleanLine(line) {
  // Remove trailing commas and whitespace
  line = line.trim().replace(/,$/, '')

  // Fix common encoding issues
  return line
    .replace(/M-CM-/g, 'é')
    .replace(/M-CM-\^@/g, 'à')
    .replace(/M-CM-\^I/g, 'É')
    .replace(/M-BM-/g, '°')
    .replace(/M-CM-\*/g, 'ê')
    .replace(/M-CM--/g, 'è')
    .replace(/M-CM-9/g, 'ù')
    .replace(/M-CM-\)/g, 'é')
    .replace(/M-CM-\(/g, 'è')
}

function parseKeyValue(line) {
  // Skip empty lines, brackets, or conflict markers
  if (
    !line ||
    line === '{' ||
    line === '}' ||
    line.startsWith('<<<<<<<') ||
    line === '=======' ||
    line.startsWith('>>>>>>>')
  ) {
    return null
  }

  // Extract key and value using regex to handle potential colons in the value
  const match = line.match(/^\s*"([^"]+)"\s*:\s*"([^"]+)"\s*$/)
  if (!match) {
    return null
  }

  return {
    key: match[1],
    value: match[2],
  }
}

function handleValidJson(content) {
  debug('Handling valid JSON case')
  try {
    // Try to parse as valid JSON first
    const parsed = JSON.parse(content)
    debug('Successfully parsed as valid JSON')
    return parsed
  } catch (error) {
    debug('Failed to parse as valid JSON, will try line-by-line parsing')
    return null
  }
}

function handleConflictedJson(lines) {
  debug('Handling conflicted JSON case')
  const translations = {}
  let inConflict = false
  let currentSection = null
  let currentBlock = []

  for (let line of lines) {
    line = line.trim()
    if (!line) continue

    // Handle conflict markers
    if (line.startsWith('<<<<<<<')) {
      inConflict = true
      currentSection = 'ours'
      continue
    } else if (line === '=======') {
      currentSection = 'theirs'
      continue
    } else if (line.startsWith('>>>>>>>')) {
      inConflict = false
      // Process the collected block
      for (let blockLine of currentBlock) {
        const cleaned = cleanLine(blockLine)
        const parsed = parseKeyValue(cleaned)
        if (parsed) {
          translations[parsed.key] = parsed.value
        }
      }
      currentBlock = []
      continue
    }

    // Collect lines in conflict blocks or process normal lines
    if (inConflict) {
      currentBlock.push(line)
    } else {
      const cleaned = cleanLine(line)
      const parsed = parseKeyValue(cleaned)
      if (parsed) {
        translations[parsed.key] = parsed.value
      }
    }
  }

  return translations
}

function getBranchVersion(branch) {
  debug(`Fetching ${branch} version of translations`)
  try {
    // Create a temporary directory with branch name to avoid conflicts
    const branchSafeName = branch.replace(/\//g, '-')
    const tempDir = fs.mkdtempSync(`/tmp/i18n-${branchSafeName}-`)
    const tempFile = path.join(tempDir, 'i18n.fr.json')

    // Get the file from the specified branch
    execSync(`git show ${branch}:src/i18n/i18n.fr.json > ${tempFile}`)

    // Read and parse the branch version
    const branchContent = fs.readFileSync(tempFile, 'utf8')
    const branchTranslations = JSON.parse(branchContent)

    // Cleanup
    fs.rmSync(tempDir, { recursive: true, force: true })

    debug(`Successfully fetched ${branch} version`)
    return branchTranslations
  } catch (error) {
    debug(`Failed to fetch ${branch} version: ${error.message}`)
    return null
  }
}

function validateWithReferenceBranches(translations) {
  debug('Validating translations against reference branches')

  // Fetch latest changes from remote
  try {
    debug('Fetching latest changes from remote')
    execSync('git fetch origin')
    debug('Successfully fetched latest changes')
  } catch (error) {
    console.warn('Warning: Failed to fetch latest changes:', error.message)
    debug('Will proceed with local versions of branches')
  }

  let missingTranslations = {}
  let totalMissingCount = 0

  // Check each reference branch
  for (const branch of REFERENCE_BRANCHES) {
    debug(`Checking against ${branch}`)

    const branchTranslations = getBranchVersion(branch)
    if (!branchTranslations) {
      debug(`No ${branch} version available for validation, skipping`)
      continue
    }

    // Check for missing keys
    let branchMissingCount = 0
    for (const [key, value] of Object.entries(branchTranslations)) {
      if (!translations[key] && !missingTranslations[key]) {
        debug(`Found missing key from ${branch}: ${key}`)
        missingTranslations[key] = {
          value,
          source: branch,
        }
        branchMissingCount++
      }
    }

    if (branchMissingCount > 0) {
      console.log(
        `Found ${branchMissingCount} missing translations in ${branch}`
      )
      totalMissingCount += branchMissingCount
    } else {
      debug(`No missing translations found in ${branch}`)
    }
  }

  // Add missing translations if any were found
  if (totalMissingCount > 0) {
    console.log(`\nAdding ${totalMissingCount} total missing translations:`)
    for (const [key, { value, source }] of Object.entries(
      missingTranslations
    )) {
      translations[key] = value
      console.log(`- "${key}": "${value}" (from ${source})`)
    }
    console.log('')
  } else {
    debug('No missing translations found in any reference branch')
  }

  return translations
}

async function resolveConflicts() {
  try {
    debug('Starting conflict resolution process')

    // Read the file content
    const fileContent = fs.readFileSync(I18N_FILE_PATH, 'utf8')
    debug('File content loaded')

    // First try to handle as valid JSON
    let translations = handleValidJson(fileContent)

    // If that fails, handle as potentially conflicted JSON
    if (!translations) {
      debug('Not valid JSON, processing line by line')
      const lines = fileContent.split('\n')
      translations = handleConflictedJson(lines)
    }

    // Validate against reference branches
    translations = validateWithReferenceBranches(translations)

    // Sort keys alphabetically
    const sortedKeys = Object.keys(translations).sort()

    // Generate the new file content
    let newContent = '{\n'
    sortedKeys.forEach((key, index) => {
      const value = translations[key]
      const comma = index < sortedKeys.length - 1 ? ',' : ''
      newContent += `  "${key}": "${value}"${comma}\n`
    })
    newContent += '}\n'

    // Validate the generated content
    try {
      JSON.parse(newContent)
      debug('Generated content is valid JSON')
    } catch (error) {
      throw new Error('Generated content is not valid JSON: ' + error.message)
    }

    // Write the file
    fs.writeFileSync(I18N_FILE_PATH, newContent, 'utf8')
    debug('File written successfully')

    // Stage the changes
    execSync(`git add ${I18N_FILE_PATH}`)
    debug('Changes staged in git')

    console.log('✅ Translation file has been processed and formatted')
  } catch (error) {
    handleError(error)
  }
}

// Start the resolution process
resolveConflicts()

#!/usr/bin/env node

const { program } = require('commander')
const inquirer = require('inquirer')
const { execSync } = require('child_process')
const path = require('path')

// Configure CLI options
program
  .requiredOption(
    '-b, --branch <branch>',
    'Ticket branch to merge (e.g., GEO-XXX)'
  )
  .option('-r, --remote <remote>', 'Git remote to use', 'origin')
  .parse(process.argv)

const options = program.opts()
let originalBranch = ''

// Helper functions
const execCommand = (command, errorMessage) => {
  try {
    return execSync(command, { encoding: 'utf8', stdio: 'pipe' })
  } catch (error) {
    console.error(errorMessage || `Error executing command: ${command}`)
    console.error(error.message)
    restoreOriginalBranch()
    process.exit(1)
  }
}

const getCurrentBranch = () => {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', {
      encoding: 'utf8',
    }).trim()
  } catch (error) {
    console.error('Error getting current branch:', error.message)
    process.exit(1)
  }
}

const restoreOriginalBranch = () => {
  if (originalBranch && getCurrentBranch() !== originalBranch) {
    try {
      console.log(`\nRestoring original branch: ${originalBranch}...`)
      execSync(`git checkout ${originalBranch}`, { encoding: 'utf8' })
      console.log('Successfully restored original branch.')
    } catch (error) {
      console.error(
        `Failed to restore original branch ${originalBranch}:`,
        error.message
      )
    }
  }
}

const checkGitStatus = () => {
  try {
    const status = execSync('git status --porcelain', { encoding: 'utf8' })
    if (status) {
      console.error(
        'Error: Working directory is not clean. Please commit or stash your changes.'
      )
      process.exit(1)
    }
  } catch (error) {
    console.error('Error checking git status:', error.message)
    process.exit(1)
  }
}

const validateBranchExists = (remote, branch) => {
  try {
    execSync(`git ls-remote --heads ${remote} ${branch}`, { encoding: 'utf8' })
  } catch (error) {
    console.error(
      `Error: Branch "${branch}" does not exist in remote "${remote}"`
    )
    process.exit(1)
  }
}

const getLatestProdTag = (remote) => {
  try {
    const prodRef = execSync(`git log ${remote}/prod --format=%D -1`, {
      encoding: 'utf8',
    }).trim()
    const tagMatch = prodRef.match(/tag: ([^,\s]+)/)
    if (!tagMatch) {
      console.error('No tag found in prod branch')
      restoreOriginalBranch()
      process.exit(1)
    }
    return tagMatch[1]
  } catch (error) {
    console.error('Error getting latest prod tag:', error.message)
    restoreOriginalBranch()
    process.exit(1)
  }
}

const getLatestPreprodTag = (remote) => {
  try {
    const preprodRef = execSync(`git log ${remote}/preprod --format=%D -1`, {
      encoding: 'utf8',
    }).trim()
    const tagMatch = preprodRef.match(/tag: ([^,\s]+)/)
    return tagMatch ? tagMatch[1] : null
  } catch (error) {
    console.error('Error getting latest preprod tag:', error.message)
    return null
  }
}

const parseVersion = (tag) => {
  const match = tag.match(/(\d+)\.(\d+)\.(\d+)(?:-RC(\d+))?/)
  if (!match) return null

  return {
    major: parseInt(match[1]),
    minor: parseInt(match[2]),
    patch: parseInt(match[3]),
    rc: match[4] ? parseInt(match[4]) : 0,
  }
}

const incrementVersion = (version, type = 'rc') => {
  const newVersion = { ...version }

  switch (type) {
    case 'rc':
      newVersion.rc = (newVersion.rc || 0) + 1
      break
    case 'patch':
      newVersion.patch += 1
      newVersion.rc = 1
      break
  }

  return newVersion
}

const formatVersion = (version, includeRC = true) => {
  const baseVersion = `${version.major}.${version.minor}.${version.patch}`
  return includeRC && version.rc
    ? `${baseVersion}-RC${version.rc}`
    : baseVersion
}

const checkForChanges = (remote, lastTag) => {
  try {
    // Get the commit hash of the last tag
    const tagCommit = execSync(`git rev-list -n 1 ${lastTag}`, {
      encoding: 'utf8',
    }).trim()
    // Get the current commit hash
    const currentCommit = execSync('git rev-parse HEAD', {
      encoding: 'utf8',
    }).trim()

    return tagCommit !== currentCommit
  } catch (error) {
    console.error('Error checking for changes:', error.message)
    restoreOriginalBranch()
    process.exit(1)
  }
}

const generateNextTag = (remote) => {
  const prodTag = getLatestProdTag(remote)
  const preprodTag = getLatestPreprodTag(remote)

  console.log(`Current prod tag: ${prodTag}`)
  if (preprodTag) {
    console.log(`Current preprod tag: ${preprodTag}`)
  }

  // Check if there are any changes since the last preprod tag
  if (preprodTag && !checkForChanges(remote, preprodTag)) {
    console.log('\nNo changes detected since the last tag.')
    console.log(`Current HEAD is already tagged as: ${preprodTag}`)
    restoreOriginalBranch()
    process.exit(0)
  }

  const prodVersion = parseVersion(prodTag)
  const preprodVersion = preprodTag ? parseVersion(preprodTag) : null

  if (!prodVersion) {
    console.error('Invalid prod tag format')
    restoreOriginalBranch()
    process.exit(1)
  }

  // If no preprod tag exists, increment patch and add RC1
  if (!preprodVersion) {
    const newVersion = incrementVersion(prodVersion, 'patch')
    return formatVersion(newVersion, true)
  }

  // If preprod has RC tag for current prod version, increment RC
  if (
    prodVersion.major === preprodVersion.major &&
    prodVersion.minor === preprodVersion.minor &&
    prodVersion.patch === preprodVersion.patch
  ) {
    const newVersion = incrementVersion(preprodVersion, 'rc')
    return formatVersion(newVersion, true)
  }

  // If preprod has RC tag for next version, increment RC
  const nextProdVersion = incrementVersion(prodVersion, 'patch')
  if (
    preprodVersion.major === nextProdVersion.major &&
    preprodVersion.minor === nextProdVersion.minor &&
    preprodVersion.patch === nextProdVersion.patch
  ) {
    const newVersion = incrementVersion(preprodVersion, 'rc')
    return formatVersion(newVersion, true)
  }

  // Otherwise, increment prod version and start at RC1
  const newVersion = incrementVersion(prodVersion, 'patch')
  return formatVersion(newVersion, true)
}

// Main script
async function main() {
  try {
    // Store original branch
    originalBranch = getCurrentBranch()
    console.log(`Current branch: ${originalBranch}`)

    // Check git status
    console.log('Checking git status...')
    checkGitStatus()

    // Validate branch exists
    console.log(
      `Validating branch ${options.branch} exists in remote ${options.remote}...`
    )
    validateBranchExists(options.remote, options.branch)

    // Switch to preprod branch
    console.log(`Using remote: ${options.remote}`)
    console.log('Switching to preprod branch...')
    execCommand('git checkout preprod', 'Error switching to preprod branch')

    // Pull latest preprod
    console.log(`Pulling latest preprod from ${options.remote}...`)
    execCommand(
      `git pull ${options.remote} preprod`,
      'Error pulling latest preprod'
    )

    // Pull ticket branch
    console.log(`Pulling ${options.branch} from ${options.remote}...`)
    try {
      execCommand(
        `git pull ${options.remote} ${options.branch}`,
        'Error pulling ticket branch'
      )
    } catch (error) {
      console.error('Merge conflicts detected:')
      console.error(error.message)
      restoreOriginalBranch()
      process.exit(1)
    }

    // Generate next tag
    const proposedTag = generateNextTag(options.remote)
    console.log(`Proposed new tag: ${proposedTag}`)

    // Confirm tag simulation
    const { simulate } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'simulate',
        message: 'Simulate create tag and push?',
        default: true,
      },
    ])

    if (simulate) {
      console.log('\nSimulation summary:')
      console.log(`- Branch to be merged: ${options.branch}`)
      console.log(`- Target remote: ${options.remote}`)
      console.log(`- New tag to be created: ${proposedTag}`)

      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: 'Proceed with actual tag creation and push?',
          default: false,
        },
      ])

      if (confirm) {
        console.log('Creating tag and pushing changes...')
        execCommand(`git tag ${proposedTag}`, 'Error creating tag')
        execCommand(
          `git push ${options.remote} preprod ${proposedTag}`,
          'Error pushing changes'
        )
        console.log('Successfully created tag and pushed changes!')
      }
    }

    // Return to original branch
    restoreOriginalBranch()
  } catch (error) {
    console.error('An error occurred:', error.message)
    restoreOriginalBranch()
    process.exit(1)
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nScript interrupted')
  restoreOriginalBranch()
  process.exit(1)
})

main().catch((error) => {
  console.error('Fatal error:', error)
  restoreOriginalBranch()
  process.exit(1)
})

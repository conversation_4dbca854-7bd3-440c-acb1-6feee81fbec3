# Preprod Push Script Proposal

## Overview

A Node.js script to automate the process of merging ticket branches into the preprod branch and managing RC tags.

## Usage

```bash
node preprod.js --branch=GEO-XXX [--remote=origin]
```

### Arguments

- `--branch`: (Required) The ticket branch to merge (e.g., GEO-XXX)
- `--remote`: (Optional) The git remote to use (defaults to 'origin')

## Script Flow

### 1. Initial Checks

- Check git status for dirty state
  - If working tree is not clean, warn and exit
- Store original branch for safety

### 2. Preprod Branch Operations

- Checkout preprod branch
- Pull latest changes from origin/preprod
- Pull specified ticket branch
  - If conflicts occur:
    - Report conflicts
    - Checkout original branch
    - Exit safely

### 3. Tag Management

- Prompt user: "Simulate create tag and push?"
- If yes:
  1. Check production branch tag via `git log origin/prod`
  2. Determine latest tag version
  3. Handle tag creation logic:
     - If no RC tag exists for current version:
       - Create new tag with -RC1 suffix
     - If RC tag exists:
       - Increment RC number (e.g., 2.3.5-RC1 → 2.3.5-RC2)
  4. <PERSON><PERSON><PERSON> proposed new tag
  5. Prompt for confirmation
  6. If confirmed:
     - Create new tag
     - Push changes to origin preprod
     - Push new tag

## Example Flow

```bash
# Default remote (origin)
$ node preprod.js --branch=GEO-45546

# Custom remote
$ node preprod.js --branch=GEO-45546 --remote=gitlab

# Script output example
> Checking git status...
> Using remote: gitlab
> Switching to preprod branch...
> Pulling latest preprod from gitlab...
> Pulling GEO-45546 from gitlab...
> Merge successful
> Current prod tag: 2.3.21
> Proposed new tag: 2.3.22-RC1
> Create and push? [y/N]
> Pushing to gitlab preprod and creating tag...
> Done! Switched back to original branch
```

## Error Handling

- Dirty git status
- Merge conflicts
- Invalid branch names
- Failed git operations
- User cancellation

## Dependencies

- Node.js
- Git command line tools
- Commander.js (for argument parsing)
- Inquirer.js (for prompts)

## Notes

- Script requires git command line access
- Supports configurable git remotes (defaults to 'origin')
- Assumes standard git flow with preprod and prod branches
- Maintains safety by checking state and conflicts
- Provides simulation option before actual tag creation
- Preserves original branch state on error

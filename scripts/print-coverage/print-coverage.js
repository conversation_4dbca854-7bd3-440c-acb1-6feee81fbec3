const fs = require('fs').promises
const path = require('path')
const xml2js = require('xml2js')
const inquirer = require('inquirer')

async function findCoverageFiles() {
  const coverageDir = path.join(process.cwd(), 'coverage')
  try {
    const files = await fs.readdir(coverageDir)
    return files
      .filter((file) => file.includes('cobertura-coverage.xml'))
      .map((file) => path.join(coverageDir, file))
  } catch (err) {
    if (err.code === 'ENOENT') {
      console.error('No coverage directory found at project root.')
      process.exit(1)
    }
    throw err
  }
}

async function parseCoverageFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf-8')
    const result = await xml2js.parseStringPromise(data)
    const coverage = result.coverage
    if (!coverage || !coverage.$) {
      throw new Error('Invalid cobertura-coverage.xml format')
    }
    return {
      lineRate: (parseFloat(coverage.$['line-rate']) * 100).toFixed(2),
      branchRate: (parseFloat(coverage.$['branch-rate']) * 100).toFixed(2),
      linesCovered: coverage.$['lines-covered'],
      linesValid: coverage.$['lines-valid'],
      branchesCovered: coverage.$['branches-covered'],
      branchesValid: coverage.$['branches-valid'],
    }
  } catch (err) {
    console.error(`Error processing ${filePath}: ${err.message}`)
    throw err
  }
}

async function displayStats(stats, filePath) {
  console.log(`\nCoverage Statistics for ${path.basename(filePath)}:`)
  console.log(
    `Line Coverage: ${stats.lineRate}% (${stats.linesCovered}/${stats.linesValid} lines)`
  )
  console.log(
    `Branch Coverage: ${stats.branchRate}% (${stats.branchesCovered}/${stats.branchesValid} branches)`
  )
}

async function main() {
  if (process.argv.includes('--help')) {
    console.log('Usage: node scripts/coverage.js')
    console.log(
      'Interactively select and display coverage stats from cobertura-coverage.xml in /coverage.'
    )
    process.exit(0)
  }

  try {
    const coverageFiles = await findCoverageFiles()
    if (coverageFiles.length === 0) {
      console.error('No cobertura-coverage.xml files found in /coverage.')
      process.exit(1)
    }

    let selectedFile
    if (coverageFiles.length === 1) {
      selectedFile = coverageFiles[0]
      console.log(`Found: ${path.basename(selectedFile)}`)
    } else {
      const answers = await inquirer.prompt([
        {
          type: 'list',
          name: 'file',
          message: 'Select a coverage file:',
          choices: coverageFiles.map((file) => path.basename(file)),
        },
      ])
      selectedFile = coverageFiles.find(
        (file) => path.basename(file) === answers.file
      )
    }

    const stats = await parseCoverageFile(selectedFile)
    await displayStats(stats, selectedFile)

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What next?',
        choices: ['View again', 'Exit'],
      },
    ])

    if (action === 'View again') {
      await main() // Restart for another selection
    }
  } catch (err) {
    process.exit(1)
  }
}

main()

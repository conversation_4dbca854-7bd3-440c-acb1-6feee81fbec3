<!DOCTYPE html>
<html lang="fr">
  <head>
    <title>Geored</title>
    
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico" /> -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
    <link
    rel="stylesheet"
    href="/lib/animate.min.css"
  />
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">

    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <link
      href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,600,600i,700,700i,800,800i&display=swap"
      rel="stylesheet"
    />

    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.13.1/css/all.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.datatables.net/1.10.20/css/jquery.dataTables.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.datatables.net/1.10.21/css/dataTables.bootstrap4.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdn.datatables.net/buttons/1.6.1/css/buttons.dataTables.min.css"
      rel="stylesheet"
    />
    <link href="https://unpkg.com/nprogress@0.2.0/nprogress.css" rel="stylesheet"/>
  </head>

  <body>
    <noscript>
      <strong>
        We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.
      </strong>
    </noscript>
    <script src="./lib/color.js"></script>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>

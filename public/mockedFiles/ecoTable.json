{"@context": "/contexts/EcoDrivingVehicle", "@id": "/eco_driving_vehicles", "@type": "hydra:Collection", "hydra:member": [{"@id": "/eco_driving_vehicles/3223", "@type": "EcoDrivingVehicle", "id": 3223, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11959, "clientName": "Sabatier", "vehicleName": "drhddrhd", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2020-05-08T08:16:14+00:00", "activityEndDate": "2020-05-08T11:58:31+00:00", "distanceTotal": 14049233, "distanceFreewheel": 5147623, "distanceAccelerate": 3496318, "distanceBrake": 6270710, "distanceDeclutch": 8715078, "distanceWork": 4109160, "distanceCruiseControl": null, "durationTotal": 2743294, "durationEngineOn": 210573, "durationEngineOff": 1467599, "durationIdleExcess": 4525589, "durationIdle": 15542264, "durationWork": 9224790, "durationMove": 6437804, "durationBrake": 3276834, "durationDeclutch": 14163080, "durationFreewheel": 9383704, "durationAccelerate": 8846123, "durationAccelerateExcess": 6741433, "durationEngineUnder": 4063852, "durationEngineEco": 3210207, "durationEngineOver": 13221033, "durationEngineTotal": 4870548, "durationCruiseControl": 3419782, "phaseStop": 9093, "phaseIdleExcess": 3315, "phaseBrake": 14005, "phaseFreewheel": 64701, "phaseAccelerate": 52755, "phaseEngineOver": 63191, "phaseDeclutch": 60876, "phaseParkingBrake": 8175, "phaseCruiseControl": 45706, "speedAverage": 2.7793604328035, "ratioAcceleratorPedal": 72, "ratioBrakePedal": 82, "ratioClutchPedal": 53, "kineticIndex": 47033, "engineSpeed": 27849, "co2": 248616, "consumptionAverage": 0.44321112882808, "consumptionTotal": 1350212, "consumptionMove": 14497125, "consumptionWork": 13550975, "consumptionIdle": 15608439, "consumptionIdleExcess": 8995898, "ratingIdleExcess": 4.513, "ratingAccelerateExcess": 1.391, "ratingEngineUnder": 7.161, "ratingEngineOver": 8.809, "ratingBrake": 4.02, "ratingFreewheel": 5.36, "rating": 6.534}, {"@id": "/eco_driving_vehicles/3242", "@type": "EcoDrivingVehicle", "id": 3242, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11960, "clientName": "Sabatier", "vehicleName": "358205085682006", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2019-05-22T10:29:15+00:00", "activityEndDate": "2019-05-23T03:47:17+00:00", "distanceTotal": 5129553, "distanceFreewheel": 13843868, "distanceAccelerate": 4822120, "distanceBrake": 10098470, "distanceDeclutch": 10566962, "distanceWork": 4997650, "distanceCruiseControl": null, "durationTotal": 4019938, "durationEngineOn": 3291815, "durationEngineOff": 5500750, "durationIdleExcess": 14171540, "durationIdle": 14232419, "durationWork": 2930905, "durationMove": 1970585, "durationBrake": 11262969, "durationDeclutch": 3126832, "durationFreewheel": 238662, "durationAccelerate": 1647826, "durationAccelerateExcess": 15822136, "durationEngineUnder": 4309049, "durationEngineEco": 5842504, "durationEngineOver": 10986586, "durationEngineTotal": 12249459, "durationCruiseControl": 1173549, "phaseStop": 34839, "phaseIdleExcess": 50390, "phaseBrake": 9073, "phaseFreewheel": 42041, "phaseAccelerate": 21209, "phaseEngineOver": 20272, "phaseDeclutch": 49601, "phaseParkingBrake": 32184, "phaseCruiseControl": 63768, "speedAverage": 1.5462774608304, "ratioAcceleratorPedal": 78, "ratioBrakePedal": 2, "ratioClutchPedal": 43, "kineticIndex": 37467, "engineSpeed": 33920, "co2": 359641, "consumptionAverage": 0.8891401926163, "consumptionTotal": 4520417, "consumptionMove": 5454433, "consumptionWork": 5296426, "consumptionIdle": 10846675, "consumptionIdleExcess": 13591794, "ratingIdleExcess": 6.302, "ratingAccelerateExcess": 0.277, "ratingEngineUnder": 7.85, "ratingEngineOver": 2.062, "ratingBrake": 1.843, "ratingFreewheel": 7.439, "rating": 2.572}, {"@id": "/eco_driving_vehicles/3290", "@type": "EcoDrivingVehicle", "id": 3290, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11960, "clientName": "Sabatier", "vehicleName": "358205085682006", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2018-10-09T20:00:17+00:00", "activityEndDate": "2018-10-09T20:55:47+00:00", "distanceTotal": 8104422, "distanceFreewheel": 6987203, "distanceAccelerate": 10206517, "distanceBrake": 11754911, "distanceDeclutch": 14622618, "distanceWork": 4556368, "distanceCruiseControl": null, "durationTotal": 1324457, "durationEngineOn": 1196055, "durationEngineOff": 605339, "durationIdleExcess": 11642262, "durationIdle": 5247617, "durationWork": 9461008, "durationMove": 7936459, "durationBrake": 8522981, "durationDeclutch": 4100184, "durationFreewheel": 7610410, "durationAccelerate": 1002839, "durationAccelerateExcess": 12999723, "durationEngineUnder": 9148007, "durationEngineEco": 11182091, "durationEngineOver": 14146463, "durationEngineTotal": 10111144, "durationCruiseControl": 8188620, "phaseStop": 14685, "phaseIdleExcess": 15500, "phaseBrake": 3350, "phaseFreewheel": 50921, "phaseAccelerate": 29660, "phaseEngineOver": 31709, "phaseDeclutch": 35124, "phaseParkingBrake": 17019, "phaseCruiseControl": 24243, "speedAverage": 1.2899942354122, "ratioAcceleratorPedal": 0, "ratioBrakePedal": 0, "ratioClutchPedal": 40, "kineticIndex": 20625, "engineSpeed": 51001, "co2": 400121, "consumptionAverage": 2.3740034407926, "consumptionTotal": 16037709, "consumptionMove": 7014682, "consumptionWork": 16529374, "consumptionIdle": 12505473, "consumptionIdleExcess": 2672131, "ratingIdleExcess": 8.291, "ratingAccelerateExcess": 1.79, "ratingEngineUnder": 1.485, "ratingEngineOver": 9.287, "ratingBrake": 2.232, "ratingFreewheel": 3.702, "rating": 1.329}, {"@id": "/eco_driving_vehicles/3297", "@type": "EcoDrivingVehicle", "id": 3297, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11960, "clientName": "Sabatier", "vehicleName": "358205085682006", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2019-09-21T17:39:41+00:00", "activityEndDate": "2019-09-21T21:45:06+00:00", "distanceTotal": 12757213, "distanceFreewheel": 9427268, "distanceAccelerate": 9280813, "distanceBrake": 12031956, "distanceDeclutch": 14751635, "distanceWork": 5647659, "distanceCruiseControl": null, "durationTotal": 1009667, "durationEngineOn": 13722422, "durationEngineOff": 12365941, "durationIdleExcess": 10790092, "durationIdle": 3436642, "durationWork": 5059628, "durationMove": 8736814, "durationBrake": 15221636, "durationDeclutch": 2272124, "durationFreewheel": 11444421, "durationAccelerate": 10113176, "durationAccelerateExcess": 2585074, "durationEngineUnder": 2163446, "durationEngineEco": 3664010, "durationEngineOver": 864457, "durationEngineTotal": 15154647, "durationCruiseControl": 7388461, "phaseStop": 58521, "phaseIdleExcess": 3860, "phaseBrake": 1276, "phaseFreewheel": 18385, "phaseAccelerate": 17430, "phaseEngineOver": 28215, "phaseDeclutch": 37690, "phaseParkingBrake": 53415, "phaseCruiseControl": 64160, "speedAverage": 1.7606729221222, "ratioAcceleratorPedal": 66, "ratioBrakePedal": 66, "ratioClutchPedal": 26, "kineticIndex": 5919, "engineSpeed": 42454, "co2": 80900, "consumptionAverage": 0.82292649775846, "consumptionTotal": 16394688, "consumptionMove": 15760689, "consumptionWork": 2007750, "consumptionIdle": 5184296, "consumptionIdleExcess": 11167381, "ratingIdleExcess": 6.327, "ratingAccelerateExcess": 6.32, "ratingEngineUnder": 1.505, "ratingEngineOver": 8.027, "ratingBrake": 7.504, "ratingFreewheel": 9.161, "rating": 2.246}, {"@id": "/eco_driving_vehicles/3306", "@type": "EcoDrivingVehicle", "id": 3306, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11959, "clientName": "Sabatier", "vehicleName": "drhddrhd", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2020-06-09T21:25:17+00:00", "activityEndDate": "2020-06-10T10:49:49+00:00", "distanceTotal": 12814746, "distanceFreewheel": 12825945, "distanceAccelerate": 1354271, "distanceBrake": 15713451, "distanceDeclutch": 7010800, "distanceWork": 16649015, "distanceCruiseControl": null, "durationTotal": 5270871, "durationEngineOn": 11431338, "durationEngineOff": 9980390, "durationIdleExcess": 442116, "durationIdle": 7200932, "durationWork": 1150420, "durationMove": 2354671, "durationBrake": 8854200, "durationDeclutch": 3280319, "durationFreewheel": 1096495, "durationAccelerate": 791562, "durationAccelerateExcess": 14583653, "durationEngineUnder": 5535927, "durationEngineEco": 13719233, "durationEngineOver": 1091107, "durationEngineTotal": 9376178, "durationCruiseControl": 13860009, "phaseStop": 28041, "phaseIdleExcess": 4695, "phaseBrake": 63359, "phaseFreewheel": 33718, "phaseAccelerate": 42676, "phaseEngineOver": 54774, "phaseDeclutch": 15294, "phaseParkingBrake": 50367, "phaseCruiseControl": 15341, "speedAverage": 2.5805550909952, "ratioAcceleratorPedal": 34, "ratioBrakePedal": 52, "ratioClutchPedal": 24, "kineticIndex": 48440, "engineSpeed": 61033, "co2": 104478, "consumptionAverage": 0.36372391833345, "consumptionTotal": 13551979, "consumptionMove": 6926734, "consumptionWork": 11122928, "consumptionIdle": 7263268, "consumptionIdleExcess": 11933771, "ratingIdleExcess": 8.645, "ratingAccelerateExcess": 0.603, "ratingEngineUnder": 6, "ratingEngineOver": 6.638, "ratingBrake": 9.71, "ratingFreewheel": 2.102, "rating": 2.789}, {"@id": "/eco_driving_vehicles/3335", "@type": "EcoDrivingVehicle", "id": 3335, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11960, "clientName": "Sabatier", "vehicleName": "358205085682006", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2019-04-05T04:14:55+00:00", "activityEndDate": "2019-04-05T10:44:21+00:00", "distanceTotal": 15699966, "distanceFreewheel": 4653450, "distanceAccelerate": 8888653, "distanceBrake": 8201608, "distanceDeclutch": 5825561, "distanceWork": 13751484, "distanceCruiseControl": null, "durationTotal": 12506524, "durationEngineOn": 11140265, "durationEngineOff": 6175065, "durationIdleExcess": 6431787, "durationIdle": 8790255, "durationWork": 2426749, "durationMove": 12298860, "durationBrake": 2216520, "durationDeclutch": 4941599, "durationFreewheel": 8242922, "durationAccelerate": 10715029, "durationAccelerateExcess": 3574734, "durationEngineUnder": 8622607, "durationEngineEco": 3368066, "durationEngineOver": 12971672, "durationEngineTotal": 4508963, "durationCruiseControl": 311905, "phaseStop": 23422, "phaseIdleExcess": 17404, "phaseBrake": 39219, "phaseFreewheel": 41373, "phaseAccelerate": 17138, "phaseEngineOver": 54954, "phaseDeclutch": 42956, "phaseParkingBrake": 11357, "phaseCruiseControl": 1187, "speedAverage": 1.8574430255598, "ratioAcceleratorPedal": 52, "ratioBrakePedal": 85, "ratioClutchPedal": 8, "kineticIndex": 38428, "engineSpeed": 36711, "co2": 30683, "consumptionAverage": 0.52856127420129, "consumptionTotal": 2289238, "consumptionMove": 7726073, "consumptionWork": 348591, "consumptionIdle": 15679643, "consumptionIdleExcess": 6601477, "ratingIdleExcess": 3.555, "ratingAccelerateExcess": 9.985, "ratingEngineUnder": 9.2, "ratingEngineOver": 5.409, "ratingBrake": 8.3, "ratingFreewheel": 5.702, "rating": 5.287}, {"@id": "/eco_driving_vehicles/3348", "@type": "EcoDrivingVehicle", "id": 3348, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11959, "clientName": "Sabatier", "vehicleName": "drhddrhd", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2018-12-27T15:18:33+00:00", "activityEndDate": "2018-12-27T16:15:13+00:00", "distanceTotal": 7025242, "distanceFreewheel": 12325892, "distanceAccelerate": 3400522, "distanceBrake": 13305566, "distanceDeclutch": 947596, "distanceWork": 4893369, "distanceCruiseControl": null, "durationTotal": 14033317, "durationEngineOn": 4541665, "durationEngineOff": 3288859, "durationIdleExcess": 15523052, "durationIdle": 14243859, "durationWork": 8438838, "durationMove": 14822265, "durationBrake": 1064888, "durationDeclutch": 15820558, "durationFreewheel": 2780900, "durationAccelerate": 233198, "durationAccelerateExcess": 8497536, "durationEngineUnder": 16209315, "durationEngineEco": 10205050, "durationEngineOver": 6661867, "durationEngineTotal": 11539297, "durationCruiseControl": 5403162, "phaseStop": 4219, "phaseIdleExcess": 10803, "phaseBrake": 45347, "phaseFreewheel": 16932, "phaseAccelerate": 2050, "phaseEngineOver": 4374, "phaseDeclutch": 6319, "phaseParkingBrake": 13863, "phaseCruiseControl": 9144, "speedAverage": 0.51931825809509, "ratioAcceleratorPedal": 34, "ratioBrakePedal": 82, "ratioClutchPedal": 72, "kineticIndex": 38958, "engineSpeed": 57513, "co2": 420632, "consumptionAverage": 5.039624500056, "consumptionTotal": 2503653, "consumptionMove": 1564404, "consumptionWork": 9562450, "consumptionIdle": 1583802, "consumptionIdleExcess": 12287310, "ratingIdleExcess": 0.054, "ratingAccelerateExcess": 0.517, "ratingEngineUnder": 5.287, "ratingEngineOver": 9.753, "ratingBrake": 2.023, "ratingFreewheel": 7.082, "rating": 0.186}, {"@id": "/eco_driving_vehicles/3355", "@type": "EcoDrivingVehicle", "id": 3355, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 11960, "clientName": "Sabatier", "vehicleName": "358205085682006", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2020-02-20T05:27:17+00:00", "activityEndDate": "2020-02-20T12:39:53+00:00", "distanceTotal": 15062677, "distanceFreewheel": 12471309, "distanceAccelerate": 6276881, "distanceBrake": 14618002, "distanceDeclutch": 3662692, "distanceWork": 1750948, "distanceCruiseControl": null, "durationTotal": 7077959, "durationEngineOn": 8299412, "durationEngineOff": 5268774, "durationIdleExcess": 9865681, "durationIdle": 16561162, "durationWork": 8452468, "durationMove": 15473414, "durationBrake": 14536527, "durationDeclutch": 8192661, "durationFreewheel": 4289109, "durationAccelerate": 3175697, "durationAccelerateExcess": 702220, "durationEngineUnder": 1414979, "durationEngineEco": 10087041, "durationEngineOver": 5286285, "durationEngineTotal": 16403296, "durationCruiseControl": 1422398, "phaseStop": 35356, "phaseIdleExcess": 326, "phaseBrake": 6996, "phaseFreewheel": 33602, "phaseAccelerate": 32747, "phaseEngineOver": 43258, "phaseDeclutch": 29489, "phaseParkingBrake": 50806, "phaseCruiseControl": 40213, "speedAverage": 0.72036142599209, "ratioAcceleratorPedal": 88, "ratioBrakePedal": 49, "ratioClutchPedal": 56, "kineticIndex": 64710, "engineSpeed": 48637, "co2": 276979, "consumptionAverage": 2.5233897441028, "consumptionTotal": 6361847, "consumptionMove": 14937979, "consumptionWork": 16134089, "consumptionIdle": 3636814, "consumptionIdleExcess": 7517030, "ratingIdleExcess": 9.375, "ratingAccelerateExcess": 3.843, "ratingEngineUnder": 1.82, "ratingEngineOver": 3.163, "ratingBrake": 6.865, "ratingFreewheel": 1.161, "rating": 4.596}, {"@id": "/eco_driving_vehicles/3369", "@type": "EcoDrivingVehicle", "id": 3369, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 32272, "clientName": "Sabatier", "vehicleName": "esgseg", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2019-12-15T00:07:50+00:00", "activityEndDate": "2019-12-15T10:45:10+00:00", "distanceTotal": 13080238, "distanceFreewheel": 3126394, "distanceAccelerate": 8950116, "distanceBrake": 15698052, "distanceDeclutch": 4131277, "distanceWork": 7540088, "distanceCruiseControl": null, "durationTotal": 2762132, "durationEngineOn": 7151883, "durationEngineOff": 1225736, "durationIdleExcess": 3533904, "durationIdle": 14385647, "durationWork": 12255148, "durationMove": 10205825, "durationBrake": 5520144, "durationDeclutch": 4246917, "durationFreewheel": 1310436, "durationAccelerate": 8237085, "durationAccelerateExcess": 5528426, "durationEngineUnder": 3977164, "durationEngineEco": 10223671, "durationEngineOver": 13073476, "durationEngineTotal": 404847, "durationCruiseControl": 1711076, "phaseStop": 51247, "phaseIdleExcess": 39471, "phaseBrake": 32619, "phaseFreewheel": 13715, "phaseAccelerate": 23464, "phaseEngineOver": 20569, "phaseDeclutch": 65066, "phaseParkingBrake": 18825, "phaseCruiseControl": 36554, "speedAverage": 0.82761115679887, "ratioAcceleratorPedal": 38, "ratioBrakePedal": 69, "ratioClutchPedal": 75, "kineticIndex": 165, "engineSpeed": 3201, "co2": 90024, "consumptionAverage": 0.52647778121233, "consumptionTotal": 4518792, "consumptionMove": 5424973, "consumptionWork": 16352296, "consumptionIdle": 6397895, "consumptionIdleExcess": 8687098, "ratingIdleExcess": 7.444, "ratingAccelerateExcess": 9.946, "ratingEngineUnder": 3.986, "ratingEngineOver": 8.684, "ratingBrake": 3.224, "ratingFreewheel": 3.193, "rating": 7.289}, {"@id": "/eco_driving_vehicles/3389", "@type": "EcoDrivingVehicle", "id": 3389, "updatedAt": "2020-06-18T14:27:58+00:00", "createdAt": "2020-06-18T14:27:58+00:00", "clientId": 73, "vehicleId": 32272, "clientName": "Sabatier", "vehicleName": "esgseg", "consolidationDate": "2020-06-18T00:00:00+00:00", "activityStartDate": "2018-10-19T15:22:31+00:00", "activityEndDate": "2018-10-20T06:50:39+00:00", "distanceTotal": 9421019, "distanceFreewheel": 13441705, "distanceAccelerate": 11152075, "distanceBrake": 16124826, "distanceDeclutch": 10861365, "distanceWork": 477333, "distanceCruiseControl": null, "durationTotal": 1393461, "durationEngineOn": 10144696, "durationEngineOff": 4732637, "durationIdleExcess": 7325188, "durationIdle": 978031, "durationWork": 8652232, "durationMove": 3954336, "durationBrake": 5776714, "durationDeclutch": 13192813, "durationFreewheel": 8248445, "durationAccelerate": 16390254, "durationAccelerateExcess": 15601363, "durationEngineUnder": 13796919, "durationEngineEco": 3204437, "durationEngineOver": 849109, "durationEngineTotal": 5654539, "durationCruiseControl": 10754409, "phaseStop": 22249, "phaseIdleExcess": 6939, "phaseBrake": 54007, "phaseFreewheel": 65535, "phaseAccelerate": 49629, "phaseEngineOver": 45316, "phaseDeclutch": 21088, "phaseParkingBrake": 12513, "phaseCruiseControl": 44595, "speedAverage": 0.97114383933576, "ratioAcceleratorPedal": 98, "ratioBrakePedal": 74, "ratioClutchPedal": 64, "kineticIndex": 58487, "engineSpeed": 13344, "co2": 491190, "consumptionAverage": 0.39005172931807, "consumptionTotal": 4080052, "consumptionMove": 8311407, "consumptionWork": 12279826, "consumptionIdle": 900633, "consumptionIdleExcess": 1393079, "ratingIdleExcess": 6.294, "ratingAccelerateExcess": 1.318, "ratingEngineUnder": 0.941, "ratingEngineOver": 7.593, "ratingBrake": 6.832, "ratingFreewheel": 2.363, "rating": 6.184}], "hydra:totalItems": 81, "hydra:view": {"@id": "/eco_driving_vehicles?consolidationDate%5B%5D=2020-06-18&vehicleId%5B%5D=36236&vehicleId%5B%5D=35592&vehicleId%5B%5D=35598&page=1", "@type": "hydra:PartialCollectionView", "hydra:first": "/eco_driving_vehicles?consolidationDate%5B%5D=2020-06-18&vehicleId%5B%5D=36236&vehicleId%5B%5D=35592&vehicleId%5B%5D=35598&page=1", "hydra:last": "/eco_driving_vehicles?consolidationDate%5B%5D=2020-06-18&vehicleId%5B%5D=36236&vehicleId%5B%5D=35592&vehicleId%5B%5D=35598&page=9", "hydra:next": "/eco_driving_vehicles?consolidationDate%5B%5D=2020-06-18&vehicleId%5B%5D=36236&vehicleId%5B%5D=35592&vehicleId%5B%5D=35598&page=2"}, "hydra:search": {"@type": "hydra:IriTemplate", "hydra:template": "/eco_driving_vehicles{?consolidationDate,consolidationDate[],clientId,clientId[],vehicleId,vehicleId[]}", "hydra:variableRepresentation": "BasicRepresentation", "hydra:mapping": [{"@type": "IriTemplateMapping", "variable": "consolidationDate", "property": "consolidationDate", "required": false}, {"@type": "IriTemplateMapping", "variable": "consolidationDate[]", "property": "consolidationDate", "required": false}, {"@type": "IriTemplateMapping", "variable": "clientId", "property": "clientId", "required": false}, {"@type": "IriTemplateMapping", "variable": "clientId[]", "property": "clientId", "required": false}, {"@type": "IriTemplateMapping", "variable": "vehicleId", "property": "vehicleId", "required": false}, {"@type": "IriTemplateMapping", "variable": "vehicleId[]", "property": "vehicleId", "required": false}]}}
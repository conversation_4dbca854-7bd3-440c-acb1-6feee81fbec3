onmessage = (event) => {
  const { action, params } = event.data

  switch (action) {
    case 'filterSort':
      return filterSort(params)
    default:
      console.log('Action is not valid', action)
  }
}


function filterSort({ id, filterType, sortingType, cachedItems } = {}) {
  console.log('alerts-worker filterSort start', cachedItems.length)
  const data = cachedItems
    .filter((item) => {
      if (filterType === 'ACK') {
        return item.isAck
      }
      if (filterType === 'NOACK') {
        return !item.isAck
      }
      return true
    })
    .sort((a, b) => {
      switch (sortingType) {
        case 'NONE': {
          return a.timestamp > b.timestamp ? -1 : 1
          break
        }
        case 'RECENT': {
          return a.timestamp > b.timestamp ? -1 : 1
          break
        }
        case 'NOACK': {
          return !(a.isAck && !b.isAck) ? -1 : 1
          break
        }
        case 'ACK': {
          return a.isAck && !b.isAck ? -1 : 1
          break
        }
        case 'TYPE': {
          return a.type < b.type ? -1 : 1
          break
        }
        case 'NAME': {
          return a.title < b.title ? -1 : 1
          break
        }
        case 'VEHICLE': {
          return a.vehicleLabel < b.vehicleLabel ? -1 : 1
          break
        }
        case 'VEHICLE_CATEGORY': {
          return a.vehicleCategory < b.vehicleCategory ? -1 : 1
          break
        }
      }
    })
  console.log('alerts-worker filterSort end', data.length)
  postMessage({
    id,
    items: data
  })
}



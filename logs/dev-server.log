The CJS build of Vite's Node API iPort 8080 is in use, trying another one...
Port 8081 is in use, trying another one...

  VITE v6.2.0  ready in 978 ms

  ➜  Local:   http://localhost:8082/
  ➜  Network: use --host to expose
onents/desktop/EcoConduite/tabs.vue) has naming conflicts with other components, ignored.
[unplugin-vue-components] component "Checkbox"(/home/<USER>/projets/geored-v3/src/components/shared/DataTable/Checkbox.vue) has naming conflicts with other components, ignored.
[unplugin-vue-components] component "ChronoIcon"(/home/<USER>/projets/geored-v3/src/components/location/LocationChrono/assets/ChronoIcon.vue) has naming conflicts with other components, ignored.
[unplugin-vue-components] component "ContactIcon"(/home/<USER>/projets/geored-v3/src/components/shared/SearchResults/Submenu/ContactIcon.vue) has naming conflicts with other components, ignored.

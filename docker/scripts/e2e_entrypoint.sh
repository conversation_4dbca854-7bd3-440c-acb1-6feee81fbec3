#npm ci && npm run custom-install -- --force playwright && npm run build -- --mode=isoprod;

# Will install depedencies if launched with -d param
while getopts 'dha:' OPTION; do
    case "$OPTION" in
        d)
            npm run custom-install -- --force playwright
        ;;
        ?)
            echo "script usage: $(basename \$0) [-l] [-h] [-a somevalue]" >&2
            exit 1
        ;;
    esac
done
shift "$(($OPTIND -1))"

#echo '127.0.0.1 georedv3' >> /etc/hosts;npx --yes serve ./dist -p 8080 & (sleep 5 && npx --yes codeceptjs run --steps)

echo '127.0.0.1 georedv3' >> /etc/hosts||true;

#npx --yes concurrently -k "npx --yes serve ./dist -p 8085" "sleep 5 && E2E_PORT=8085 npx --yes codeceptjs run --steps"

# update browser binaries
npx playwright install

npx --yes serve ./dist -p 8085&
sleep 5
E2E_PORT=8085 npx --yes codeceptjs run --steps;
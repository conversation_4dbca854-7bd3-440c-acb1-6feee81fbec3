# AAA (Arrange-Act-Assert) Pattern

The AAA pattern is a common pattern for structuring unit tests. It helps make tests more readable and maintainable by clearly separating the test into three distinct phases.

## Pattern Structure

```javascript
it('should do something', () => {
  // Arrange
  // Set up the test conditions and initialize required objects
  
  // Act
  // Execute the code being tested
  
  // Assert
  // Verify the results meet expectations
})
```

## Phases Explained

### 1. Arrange
- Set up the test environment
- Initialize variables, mocks, and stubs
- Prepare input data
- Define expected outcomes

Example:
```javascript
// Arrange
const mockStore = { user: { id: 123 } }
const service = new UserService(mockStore)
```

### 2. Act
- Execute the code being tested
- Usually a single action/method call
- Capture the result if needed

Example:
```javascript
// Act
const result = service.getUserId()
```

### 3. Assert
- Verify the results match expectations
- Check state changes
- Validate mock interactions

Example:
```javascript
// Assert
expect(result).toBe(123)
```

## Best Practices

1. **Clear Separation**
   - Keep the three sections visually distinct
   - Add comments to mark each section
   - One action per test

2. **Minimal Arrangement**
   - Only arrange what's necessary for the test
   - Use test helpers/factories for complex objects
   - Keep setup code DRY using beforeEach when appropriate

3. **Single Act**
   - Test should typically have only one act
   - Multiple acts might indicate the test is doing too much

4. **Focused Assertions**
   - Assert only what's relevant to the test case
   - Avoid testing multiple behaviors in one test
   - Make assertion messages clear and specific

## Example

```javascript
describe('UserService', () => {
  it('should return formatted user name', () => {
    // Arrange
    const mockUser = {
      firstName: 'John',
      lastName: 'Doe'
    }
    const service = new UserService(mockUser)
    
    // Act
    const fullName = service.getFullName()
    
    // Assert
    expect(fullName).toBe('John Doe')
  })
})
```

## Common Pitfalls to Avoid

1. **Hidden Arrangement**
   - Avoid arranging in beforeEach unless truly needed by all tests
   - Make test dependencies explicit

2. **Multiple Acts**
   - Split into multiple tests if testing different behaviors
   - Each test should verify one specific thing

3. **Unrelated Assertions**
   - Keep assertions focused on the behavior being tested
   - Create separate tests for different aspects

4. **Missing Comments**
   - Always mark the three sections with comments
   - Makes tests more maintainable and easier to review

## When to Use

Use the AAA pattern when:
- Writing unit tests
- Testing individual components or functions
- Need clear structure in test cases
- Want to improve test maintainability

The pattern is particularly useful for:
- New team members understanding test structure
- Code reviews
- Debugging failing tests

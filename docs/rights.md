# Rights

There are two types of rights:

- Access right: Will show/hide or enable/disable access to a main module (i.g Location module)
- Feature rights: Will show/hide or enable/disable certain feature (i.g Location module - Position analysis)

## Format

_Important_ GeoredV3 right codes should be unique and self explanatory. Follow the format:

```
[APP_NAME]_[MODULE]_[ACTION]
//Example GEOV3_LOCATION_CIRCUIT (Location module, vehicle details, circuit tab)
```

## Used rights (droit_action)

### GeoredV3

#### Access

Rights for module access.

- GEOV3_LOCALISATION_ACCESS
- GEOV3_CIRCUIT_ACCESS
- GEOV3_DASHBOARD_ACCESS
- GEOV3_OBJECTS_ACCESS
- GEOV3_ZONES_ACCESS
- GEOV3_DIAGNOSTIC_ACCESS
- GEOV3_HELP_ACCESS
- GEOV3_IDENTIFICATION_ACCESS
- GEOV3_ECOCONDUITE_ACCESS
- GEOV3_EVENTS_ACCESS

#### Specific

Rights for feature access (tabs, buttons, nested views, specific information panels, etc).

- (We will create new rights under GeoredV3 tab)

### Citipav, Analyses, Admin, Citifret

- Fetch all, shows external link to app if user has at least one right.

### Feature rights

#### How to test feature rights quickly?

How to test feature rights quickly:

Location module - Circuit tab (Visible)
http://localhost:8082/#/?add_rights=GEOV3_LOCATION_CIRCUIT&remove_rights=
Location module - Circuit tab (Hidden)
http://localhost:8082/#/?add_rights=GEOV3_LOCATION_CIRCUIT&remove_rights=GEOV3_LOCATION_CIRCUIT

(add_rights and remove_rights rights will add and remove on top of existing user rights. Available in dev, recette, isoprod, preprod-test, preprod)
# Migration from Vue CLI to Vite

## History

### 18/12/2024

- Installed Vite alongside Vue CLI
- Installed Vite plugin for Vue 2
- Created vite.config.js 
- Configured Vite aliases
- Configured Vite extensions
- Added `.vue` extension to components imports

### 23/12/2024

- Replaced `require` statements with `imports` or workarounds
- Installed necessary `types` as devDependencies
- Replaced `process.env.VUE_APP` with `import.meta.env.VITE`

### 24/12/2024
 
- Replaced env variables prefixes `VUE_APP` with `VITE`
- Fixed imports paths
- Created `index.html` at project root
- Fixed broken methods in `/src/api/api-platform-resources.js` and `/src/composables/useLocationAtoSelectFirst.ts`
- Fixed jest & babel configs
- Create mock for `bootstrap-vue.js`
- Fixed Vite config
- Fixed scripts in `package.json` : dev, test, build
- Fixed broken unit test in `/tests/unit/useLocationAutoSelectFirst.spec.js`

### 03/01/2025

- Added `unplugin` to vite config
- Fixed broken `loginAs` feature : use `getAPIV3Pooling` instead of getAllPooling
- Removes Vue CLI related packages
- Fixed bootstrap style issue : import `main.scss` after bootstrap in `main.ts`

### 06/01/2025

- Fixed width style : css property `width: fill-available;` broke style, added `width: -webkit-fill-available;` to necessary classes
- Added `dev:isoprod` script to `package.json`
- Replaced remaining require with dynamic import in `TripHistoryDetails.vue`
- Began inspection of every module and component to find errors and/or issues

### 07/01/2025

- Fixed broken search in history mode (location module) : fix `getNormalizeItemHandler` method in `/src/api/api-platform-resources.js`
- Replaced missing require with dynamic imports + refactor dynamic import in a `/src/composables/useDynamicImport.ts` composable
- Fixed `hexOpacity` import in `ZonesList.vue` component
- Continue inspection of modules and components to find errors and/or issues

### 08/01/2025

- Replaced broken `setImmediate` method with `setTimeout`
- Added missing Vite alias for desktop components
- Fixed remaining `hexOpacity` imports in blackspots and zones modules
- Finish inspection of modules and components

## Summary of migration steps

Migrating from Vue CLI / Webpack to Vite involves crucial steps. Begin with installation of Vite alongside Vue CLI. Next, configure Vite module resolution, aliases to match your current Webpack configuration. Next, be careful and inspect Vue components ; add `.vue` extension to all components imports. Then, you'll have to replace every require statement by import ; be careful as dynamic imports should require workaround to work properly (see `TripHistoryDetails.vue` for instance). Next, don't forget to replace `process.env.VUE_APP` with `import.meta.env.VITE` ; replace your env variables prefixes from `VUE_APP` to `VITE`. Create an `index.html` file at project's root. Try to launch the app and fix any necessary issue that prevents the app to compile. Once the app is compiled, you should take a careful tour of every feature and module to resolve any remaining issue, error or style discrepancies.
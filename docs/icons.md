## Icons

Here are the multple ways we use to integrate icons into the project.

### Font Awesome 5

- https://fontawesome.com/icons

```html
<em class="fas fa-info info_icon" @click="showInfo($event, item)" />
```

### Material Design Icons

- vue-material-design-icons
- https://materialdesignicons.com/

### Iconify (Multiple icon sets)

- @iconify/iconify
- @iconify/vue2
- https://icon-sets.iconify.design/

```pug
PowerIcon(:fillColor="appColors.color_denim" :size="30")
```

### Raw SVG

- These are icons wrapped around Vue components
- Example: ZonesIcon.vue

```pug
ZonesIcon(class="mr-1" color="var(--color-dark-blue)")
```
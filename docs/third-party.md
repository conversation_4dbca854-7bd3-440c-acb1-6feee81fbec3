## i18n

- https://kazupon.github.io/vue-i18n

## Polyfills

- https://polyfill.io/v3/

## Icons

- https://iconify.design/icon-sets/mdi/

## Logging

- vuejs-logger
- URL: https://www.npmjs.com/package/vuejs-logger

### Usage

```js
console.debugVerbose(8,"Verbose message"); //Available if NODE_ENV=development
Vue.$log.info("Info message"); //Available if NODE_ENV=development
Vue.$log.warn("Warn message");
Vue.$log.error("Error message");
Vue.$log.fatal("Fatal message");
```

## Other

- vue-simple-svg (loads images as inline svg to edit attributes via css)
- vue-meta (set meta tags dynamically)

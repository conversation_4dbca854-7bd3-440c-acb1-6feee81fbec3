# Deprecation notice

- Deprecated workflow: Location module uses a dynamic layout (See :sync-with-vuex="true")
- Current workflow: Other modules change layout blocks using properties

# Application layout

- There is a common layout component to handle the main application layout (**TLayout**)
- Layout is composed by blocks/menus

## Menu blocks

- Menus/Blocks are

  - Sidebar (Used for module links)
  - Menu (Used for search forms)
  - SubMenu (Used for item details)
  - RightMenu (Used for tables and maps)
  - WideMenu (Unused)

### Sidebar

Module links goes here.

Attributes:
- sidebar
- sidebar_extended

### Menu

In Location module, the search component goes here (Form mode).

Extra attributes:

- Collapsed (attribute: menu_collapsed)
    
    The menu will toggle into a reduced version in width.

- menu_full_collapse

    Toggling the menu will collapse it completly rather than reducing his size

- menu_toggle

    Whenever to show or not the toggle button to collapse the menu.

### Sub menu

In Location module, the Details view (Item details window) goes here.

Attribute:
- sub_menu
### Right menu

Used by Location module for 2nd Map display and table display.

Attributes:
- right_menu
- right_menu_bottom

### Wide menu

A floating menu that fills the entire screen. Unused.

Attributes:
- wide_menu




## Layout Configuration (Deprecated) (Used by Location module only)

The layout can be configured from any component by dispatching an action.

```js
this.$store.dispatch("app/changeLayout", {
  /*flags*/
});
```

#### **Available flags**:

All the keys in the following object.

```js
let matchTable = {
  sidebar: "isSidebarVisible",
  sidebar_extended: "isSidebarExtended",
  menu: "toggleMenu",
  menu_collapsed: "toggleMenuCollapsed",
  menu_full_collapse: "willMenuCollapseCompletely",
  menu_toggle: "isMenuToggleVisible",
  right_menu: "toggleRightMenu",
  right_menu_bottom: "toggleRightMenuBottom",
  sub_menu: "toggleSubMenu",
  wide_menu: "toggleWideMenu",
};
```

### Example: Show Sidebar and Menu (Blank)

```js
{
    created() {
        this.$store.dispatch("app/changeLayout", {
            sidebar: true,
            menu: true,
        });
    },
}
```

### Example: Show Menu and mount a global component inside

```js
{
    created() {
        this.$store.dispatch("app/changeLayout", {
            menu: 'MyGlobalComponent',
        });
    },
}
```

### Example: Show Right menu and lazy mount component inside

```js
{
    created() {
        this.$store.dispatch("app/changeLayout", {
            rigth_menu: ()=> '@c/SimpleMap.vue',
        });
    },
}
```

### Example: Show Right menu with a map and a table.

```js
{
    created() {
        this.$store.dispatch("app/changeLayout", {
            rigth_menu: ()=> '@c/SimpleMap.vue',
            right_menu_bottom: ()=> '@c/Table.vue'
        });
    },
}
```

### Example: Show a table on a wide menu

```js
{
    created() {
        this.$store.dispatch("app/changeLayout", {
            wide_menu: ()=> '@c/BigTable.vue'
        });
    },
}
```

### Example: Reset the layout to defaults

```js
{
    created() {
        this.$store.dispatch("app/resetLayout")
    },
}
```

### Example: Implement the layout

```js
<t-layout
    @onSidebarExtended="onSidebarExtended"
    @onMenuToggle="onMenuToggle"
>
</t-layout>
```

### Example: Implement the layout and mount the router view in the main block

```js
<t-layout>
  <template v-slot:main>
    <router-view />
  </template>
</t-layout>
```

## Layout Static Context

It is defined in the `TLayout` component and provides a context object that allows components to interact with the current layout's view model. This context is used by components like `MapToolbox` to adjust their behavior based on the layout state.

### Properties
- **vm**: Reference to the current layout's view model, enabling access to the layout's state and methods.
- **name**: Retrieves the current layout name from the view model.
- **isRightMenuVisible**: Checks if the right menu is visible.
- **isRightMenuBottomVisible**: Checks if the bottom part of the right menu is visible.
- **$data**: Provides access to the data object of the layout's view model.

### Usage
- **Global Access**: The `layoutStaticContext` is globally accessible, allowing components to use it for layout-related logic.
- **Integration with MapToolbox**: The `MapToolbox` component uses `layoutStaticContext` to determine its visibility and behavior based on the layout state.

# Cache

A cache feature is available.

### Set

```js
let storage = Vue.$localStorage.fromNamespace("app");
storage.cache.set("key", { foo: 1 });
```

### Get

```js
storage.cache.get("key");
```

- Will return **null** if the cache is expired

### Cache expiration

Cache times are picked from a JSON file **cache-times.json**.

```json
{
  "/v2/mob_categoriesvehicules": "VUE_APP_CACHE_DURATION_MILLI_CAT_VEHICLES",
  "/v2/circuit/categorie": "VUE_APP_CACHE_DURATION_MILLI_CAT_VEHICLES",
  "/v2/chauffeur/categorie": 36000
}
```

- Value can be either a number or a en existing env variable.
- Expiration time is expected to be set in milliseconds
- Example: _Expiration time will be computed from **process.env.VUE_APP_CACHE_DURATION_MILLI_CAT_VEHICLES**_
- More about envs at https://cli.vuejs.org/guide/mode-and-env.html

### Cache invalidation

- Remove stored data
  - You can remove the stored data from DevTools -> Application (Storage section)
- "nocache" parameter
  - You can add ?nocache=1 in the URL to skip cache (Existing stored cache data is not removed)

_Code: src/plugins/vue-local-storage.js#createAPICacheManager

## Client-side storage

We also have an storage feature we can use when cache expiration is not needed. Based on https://github.com/localForage/localForage

### Usage

```js
const sidebarStorage = Vue.$localStorage.fromNamespace("sidebar");

sidebarStorage.setItem("background_color", "blue");
```

- Will save "sidebar_background_color" = "blue" using the best offline method available.

```js
document.querySelector("#sidebar").style.background = sidebarStorage.getItem(
  "background_color"
);
```

- Get the value back

Note: It supports Array/Object without the need of stringify the value.

_Code: src/plugins/vue-local-storage#getLocalStoragePlugin_

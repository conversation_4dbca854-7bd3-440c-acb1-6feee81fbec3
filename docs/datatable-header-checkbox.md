# Datatable checkbox header documentation

## Overview

Ticket [#47964](https://easyredmine.simpliciti.fr/issues/47964) introduced the need to allow the user to mass select all current elements of a datatable.

### Component checkbox

A component has been created here : /src/components/shared/DataTable/Checkbox.vue. It allows to display a checkbox button, fully customizable by its props.

#### Props : 

```javascript
const props = defineProps({
//The state of the checkbox
modelValue: {
type: <PERSON>olean,
default: () => false,
required: true,
},
//The icon to display
icon: {
type: String,
default: '',
required: true,
},
iconColor: {
type: String,
default: 'var(--color-main)',
},
iconStyle: {
type: Object,
default: () => ({ fontSize: '16px' }),
},
//The name of the emit event
emitName: {
type: String,
default: '',
},
//The name of the tooltip
tooltip: {
type: String,
default: '',
},
//Determines if the checkbox should be displayed or not
shouldDisplay: {
type: <PERSON><PERSON><PERSON>,
default: true,
},
})
```

### Datatable mixin

In the mixin src/mixins/datatable.js, a datatable component is registered like this, which takes all necessary props.

```javascript
registerDatatableComponent('CheckboxWrapper', {
computed: {
...mapGetters({
checkboxProps: 'datatable/getCheckboxProps',
}),
},
components: {
Checkbox: async () =>
import('@c/shared/DataTable/Checkbox.vue'),
},
template: `<Checkbox
  :model-value="checkboxProps.modelValue"
  :icon="checkboxProps.icon"
  :tooltip="checkboxProps.tooltip"
  :icon-color="checkboxProps.iconColor"
  :icon-style="checkboxProps.iconStyle"
  :emit-name="checkboxProps.emitName"
  :should-display="checkboxProps.shouldDisplay"
/>`,
})
```

Note that checkboxProps are stored in datatable store to be accessible from anywhere in the app.

```javascript
export default {
  namespaced: true,
  state: {
    checkboxProps: {
      modelValue: false,
      icon: '',
      tooltip: '',
      iconColor: 'var(--color-main)',
      iconStyle: { fontSize: '16px' },
      emitName: '',
      shouldDisplay: true,
    },
  },
}
```

### Implement checkbox from a component

Implementing this checkbox is pretty easy. To do so, you'll have to modify the extraOptions object in the component. 

```vue
extraOptions: {
...this.configureColumnsFilters((filters) => {
    return [
      filters.createCheckbox(),
      null,
      filters.createTextFilter({ column: 2 }),
      filters.createTextFilter({ column: 3 }),
      filters.createTextFilter({ column: 4, inputStyle: 'width:80px' }),
      filters.createTextFilter({ column: 5, inputStyle: 'width:40px' }),
      filters.createTextFilter({ column: 6, inputStyle: 'width:40px' }),
    ]
  }),
...this.configureFooter(),
    dom: 'rtip',
    pageLength: 25,
    info: true,
},
```

Setting filters.createCheckbox() for the first column will add the filter. 

Now, we can pass the appropriate parameters for customization :

- From lifecycle hooks (example with Options API) : 

```vue
mounted() {
  this.$store.commit('datatable/resetCheckboxProps')

  this.$store.state.datatable.checkboxProps = {
    modelValue: false,
    icon: 'mdi:eye-off',
    tooltip: this.$t('reference_circuit.tooltips.show_all_circuits'),
    iconColor: 'var(--color-main)',
    iconStyle: { fontSize: '16px' },
    emitName: 'toggleCurrentReferenceCircuitsVisibility',
    shouldDisplay: true,
  }
},

destroyed() {
  this.$store.commit('datatable/resetCheckboxProps')
},
```

- From a watcher, to rewrite dynamically props (useful to bind checkbox props to component state) :

```vue
//Set checkbox dynamic properties
watch: {
  //Set checkbox dynamic properties
  propertyToWatch: {
    handler(newValue) {
      Object.assign(this.$store.state.datatable.checkboxProps, {
        modelValue: newValue,
        icon: newValue === true ? 'mdi:eye' : 'mdi:eye-off',
        tooltip: this.$t(
          'reference_circuit.tooltips.' +
          (this.areAllCircuitsInTableVisibleOnMap ? 'hide' : 'show') +
          '_all_circuits'
        ),
      })
    },
  },
},
```


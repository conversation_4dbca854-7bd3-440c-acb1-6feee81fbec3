# LocationV2 POC Plan

## Goal
Develop a Proof of Concept (POC) for a new `LocationV2` module. This module will integrate a raw Leaflet map and incorporate features from various existing modules, strictly without reusing any existing Vue components, services, mixins, plugins, or composables. All data interactions will be done through existing APIs.

## Architecture

The `LocationV2` module will be a self-contained Vue component. It will directly import and utilize **Mapbox GL JS** for map rendering instead of Leaflet. All data fetching will be performed using `axios` or similar direct HTTP request libraries, interacting with existing backend APIs. No shared frontend utilities will be used.

Mapbox GL JS provides superior performance for large datasets and offers better styling capabilities through Mapbox Studio, making it ideal for real-time vehicle tracking applications.

## Features to Include in POC

1. **Mapbox Map Initialization**: Display a basic Mapbox GL JS map with custom style.
2. **View vehicles real-time information**: Fetch and display real-time vehicle data from an API, similar to the existing Location module. This includes displaying vehicle positions, status, and other relevant real-time attributes on the map as markers or symbols.
3. **Basic Map Interaction**: Enable panning, zooming, rotation, and tilt controls.
4. **Information Display on Marker Click**: When a vehicle marker is clicked, fetch additional detailed data for that vehicle from an API and display it (e.g., in a simple `div`).
5. **Clustering Support**: Implement marker clustering for better performance with large vehicle fleets.
6. **Custom Map Styling**: Utilize Mapbox Studio styles for consistent branding.

## Implementation Phases

### Phase 1: Basic Setup and Mapbox Integration
1. **Create Base Structure**: Create `src/components/location-v2/LocationV2.vue`.
2. **Mapbox GL JS Integration**: Import Mapbox GL JS and initialize the map within the component's `mounted` lifecycle hook.
3. **Access Token Configuration**: Set up Mapbox access token (from environment variables).
4. **Custom Map Style**: Configure custom Mapbox Studio style URL.
5. **Basic Styling**: Implement minimal inline or component-scoped CSS for the map container.
6. **Map Controls**: Add navigation controls (zoom, rotation, pitch) and scale control.

### Phase 2: Real-time Vehicle Integration
1. **API Integration**: Connect to real-time APIs for vehicle positions.
2. **Marker/Symbol Rendering**: Create custom Mapbox symbols/markers for vehicles based on their status.
3. **Data Refresh**: Implement polling mechanism to refresh vehicle data at regular intervals.
4. **Data Normalization**: Create data normalization functions to optimize object size.
5. **Clustering**: Implement Mapbox clustering for handling large numbers of vehicles.

### Phase 3: Interaction and Additional Features
1. **Click Interactions**: Implement click handlers for vehicle markers using Mapbox's event system.
2. **Detailed Information Panel**: Create a simple panel to display vehicle details on marker click.
3. **API Integration for Details**: Connect to vehicle detail APIs to fetch additional information.
4. **Basic Filtering**: Implement simple filtering for vehicle types or statuses.
5. **Popups**: Use Mapbox popups for displaying vehicle information on map clicks.

### Phase 4: Testing and Optimization
1. **Performance Testing**: Test with large datasets to ensure smooth performance using Mapbox's efficient rendering.
2. **Memory Usage Optimization**: Optimize memory usage for large vehicle fleets.
3. **Browser Compatibility**: Ensure compatibility with target browsers.
4. **Mobile Responsiveness**: Test touch interactions and mobile performance.
5. **Final Verification**: Verify all features work as expected with Mapbox GL JS.

### Phase 2: Real-time Vehicle Integration
1. **API Integration**: Connect to real-time APIs for vehicle positions.
2. **Marker Rendering**: Create custom markers for vehicles based on their status.
3. **Data Refresh**: Implement polling mechanism to refresh vehicle data at regular intervals.
4. **Data Normalization**: Create data normalization functions to optimize object size.

### Phase 3: Interaction and Additional Features
1. **Click Interactions**: Implement click handlers for vehicle markers.
2. **Detailed Information Panel**: Create a simple panel to display vehicle details on marker click.
3. **API Integration for Details**: Connect to vehicle detail APIs to fetch additional information.
4. **Basic Filtering**: Implement simple filtering for vehicle types or statuses.

### Phase 4: Testing and Optimization
1. **Performance Testing**: Test with large datasets to ensure smooth performance.
2. **Memory Usage Optimization**: Optimize memory usage for large vehicle fleets.
3. **Browser Compatibility**: Ensure compatibility with target browsers.
4. **Final Verification**: Verify all features work as expected.

## API Integration

Based on the `simpliciti-apis.js` configuration, the following APIs will be used:

* **Real-time Vehicle Locations**:
  * Primary: `APIV3_GEORED_SERVICE_REALTIME`: `/geored/real_time/service/real_time`
  * Alternative: `APIV3_GEORED_SERVICE_CARTOGRAPHY_LAST_VEHICLES`: `/service/cartography/last_vehicles`

* **Vehicle Details**:
  * `APIV2_TEMPS_REEL_BY_VEHICLE`: `/v2/temps_reel/by_vehicule`
  * `APIV3_VEHICLE_DETAILS`: `/geored/vehicle/vehicles`

* **Reverse Geocoding (for address lookup)**:
  * `APIV3_CARTO_GEOCODING_REVERSE`: `/service/cartography/reverse_geocoding`

## Data Normalization

API responses will be normalized to reduce object size and memory footprint, especially for large arrays of data. This involves:

* Creating 'freezed' (immutable) smaller versions of objects, containing only data relevant for rendering.
* Reducing key lengths (e.g., `tauxRealizationCircuit` to `completionRate` to `cr`).
* Applying the normalization directly in the LocationV2 component, rather than reusing existing normalization code.

## Mapbox Configuration

### Dependencies
- **mapbox-gl**: `^3.x.x` (latest stable version)
- **mapbox-gl-vue**: Optional wrapper for Vue integration (if needed)
- **axios**: For API calls (already mentioned)

### Environment Variables
```bash
VITE_MAPBOX_ACCESS_TOKEN=your_mapbox_access_token_here
VITE_MAPBOX_STYLE_URL=mapbox://styles/your-style-id
```

### Mapbox Studio Setup
1. **Create Custom Style**: Use Mapbox Studio to create a custom map style matching the application branding
2. **Configure Style URL**: Store the style URL in environment variables
3. **Set Up Sprite Icons**: Create custom sprite icons for different vehicle types and statuses
4. **Define Data Layers**: Configure layers for vehicle markers, clusters, and other map elements

## Non-Reuse Constraint Enforcement

* Manually verify that no imports from `src/components`, `src/services`, `src/mixins`, `src/plugins`, `src/composables` are used in the `LocationV2` component or its immediate sub-files.
* Only direct imports from `node_modules` (e.g., `mapbox-gl`, `axios`) are permitted.
* Create all required utility functions directly within the LocationV2 component or related files.
* Do not use any existing map-related utilities or services from the codebase.

## Verification Criteria

* Manual inspection of the `LocationV2.vue` component to ensure all constraints are met.
* Verify the component initializes and renders correctly with Mapbox GL JS.
* Confirm real-time data is fetched and displayed properly.
* Test that vehicle markers appear and respond to clicks.
* Ensure detailed vehicle information is displayed correctly.
* Check that clustering works efficiently with large datasets.
* Validate mobile responsiveness and touch interactions.
* Ensure the implementation works without any dependencies on existing code.

## Additional Mapbox Features to Consider

* **3D Terrain**: Utilize Mapbox's 3D terrain capabilities for elevation visualization
* **Custom Markers**: Use Mapbox's marker system with custom HTML/CSS for vehicle icons
* **Layer Management**: Implement dynamic layer management for different vehicle types
* **Performance Optimization**: Leverage Mapbox's built-in performance optimizations for large datasets
* **Offline Support**: Consider Mapbox's offline capabilities for mobile applications
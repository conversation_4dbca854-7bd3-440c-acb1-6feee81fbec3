# Project structure

- Root
  - src
    - views
      - RealTimeScreen.vue
        1. _Ecran Temp reel_
        2. _Route components (Views) are named like [name]Screen.vue_
      - AdminDashboardScreen.vue
    - components
      1. _Top level folders can be namespace folders (i.g desktop, admin, analysis, shared)_
      - desktop
        - LocationMap.vue
      - admin
      - analysis
      - shared
        - Sidebar
          1. _A complex component can have his folder, SAME name._
          - Sidebar.vue
    - assets
      1. _Common assets_
      2. _The preferred way to add store assets is along their components, not this folder_
    - plugins
      1. _Integrations with third party libraries might go here_
      2. _Our custom vue plugins can go here as well_
    - router
      1. _routes definitions_
      2. _e.g: we might need to link /admin with a route component called admin_module.vue_
    - store
      1. _vuex stores go here_
      2. \_the global store can be subdivided into modules
      - sidebar/index.js
      1. _Example: The sidebar is a complex component? Maybe it has his own store_
      - location_module/index.js
      1. _There is at least one store module per app module (location,dashboard,zones, etc)_

# Naming conventions

## Assets, CSS selectors, Vue Routes

- Lower case
- Separate words with underscore.

```js
//GOOD
require("./map_icon.svg");

//BAD
require("./MapIcon.svg");

//BAD
require("./mapIcon.svg");
```

```js
//GOOD
const routes = [
  {
    path: "/dashboard",
    name: "app_quickmenu_screen",
  },
];

//BAD
const routes = [
  {
    path: "/dashboard",
    name: "AppDashboardScreen",
  },
];
```

- Routes URLs: Lowercase separated with hyphens (i.g: **/client-details**)

## CSS selectors

- Use lower case and underscores.
- Avoid dashes (harder to select with the mouse)

```css
/*GOOD*/
.map_icon {
}

/*BAD*/
.map-icon {
}

/*BAD*/
.mapIcon {
}

/*BAD*/
.MapIcon {
}
```

- Use double underscore for separating namespaces.

```css
/*GOOD*/
.my_button__icon {
}

/*BAD*/
.my_button_icon {
}
```

## Components

- Use Pascal Case (Same as Camel Case but first letter is capital.)

```js
//GOOD
import MapIcon from "@/components/MapIcon.vue";

//BAD
import mapIcon from "@/components/mapIcon.vue";

//BAD
import map_icon from "@/components/map_icon.vue";
```

# Switchable Popup Mixin Documentation

## Overview
The Switchable Popup Mixin (`switchable-popup-mixin.ts`) is a Vue mixin that enables users to quickly switch between marker popups on a map when multiple markers are in close proximity to each other. This is particularly useful when markers overlap or are very close to each other, making it difficult to access individual popups.

## Key Features
- Allows navigation between nearby markers using arrow keys or on-screen controls
- Displays a visual indicator (circle) showing the radius within which nearby markers are considered
- Automatically detects and manages nearby markers within a specified radius
- Maintains state across marker switches
- Cleans up resources when popups are closed
- Is bound by default to the main map, or can be bound to any other map if specified 
- Can be unbound from any component

## Usage

### 1. Import and Include the Mixin
```typescript
import switchablePopupsMixin from '@/mixins/switchable-popup-mixin.ts'

export default {
  mixins: [switchablePopupsMixin],
  // ... rest of your component
}
```

### 2. How to bind the mixin to a specific map

The app sometimes uses floating maps in which we display markers (e.g. Nearby Vehicles or Nearby Events).

To bind the mixin to another map than the main : 

- Within the floating map component, Add a css class of your choice to LeafletMap

```vue
<template>
  <div class="d-flex flex-column">
    <div class="address">{{ currentFormattedAddress }}</div>
    <LeafletMap ref="map" class="map nearby-vehicles-map"></LeafletMap>
  </div>
</template>
```

- In the mounted hook, call addCustomClassToLeafletContainer with following parameters : 
  - parentSelector is the class you added to your LeafletMap wrapper
  - childSelector is the class to add to the map itself
  - add a timeout to ensure reactivity, otherwise parentSelector children won't be found

```typescript
onMounted(() => {
  setMapVmRef(map)

  setTimeout(() => {
    switchablePopupMixin.methods.addCustomClassToLeafletContainer(
      'map.nearby-vehicles-map',
      'nearby-vehicles'
    )
  }, 100)
})
```

- The method does the following :
  - Find the map among the wrapper children
  - Sets the parent map selector, used to bind the arrow wrapper
  - Sets the map selector itself to bind click events

```typescript
 addCustomClassToLeafletContainer(
   parentSelector: string,
   childSelector: string
 ) {
   const defaultMapContainer = '.map.leaflet-container'

   const parentMap = document.querySelector('.' + parentSelector)

   if (parentMap) {
     // Find appropriate child to add class to
     const mapContainer = Array.from(parentMap.children).find(
       (child) =>
         child.classList.contains('map') &&
         child.classList.contains('leaflet-container')
     )

     // Return if no mapContainer found
     if (!mapContainer) {
       console.warn('Map container not found')
       return
     }

     //Otherwise add the specific class
     mapContainer.classList.add(childSelector)

     const selector = defaultMapContainer + '.' + childSelector

     this.setParentMapSelector(parentSelector)
     this.setSpmMapSelector(selector)
   }
 }
```

**NB :** if you don't add any class to the map wrapper and don't call addCustomClassToLeafletContainer, default map will be used.

### 3. Initialize the Mixin
In your component's mounted hook or when appropriate, call:

```typescript
this.initSwitchablePopupMixin({
  spmLayerGroupName: 'your-layer-group-name',  // The layer group containing your markers
  spmLabelPath: 'properties.item.name',        // Path to the label property in your marker data
  radius: 100                                  // Radius in meters to search for nearby markers
})
```

### 4. Force unbind from any component

It can be interesting to force unbind from any component within the app, on lifecycle hooks for example.
For instance, in nearby items context, it's necessary to unbind when component is unmounted or destroyed.

To do this, simply call unbindFromDestroyedComponent() method from within your component.

```typescript
onBeforeUnmount(() => {
   switchablePopupMixin.methods.unbindFromDestroyedComponent()
})
```

To trigger this method : 

```typescript
unbindFromDestroyedComponent() {
   if (singleRootScope) {
     singleRootScope.unbind()
   }
}
```

### Important Parameters

#### spmLayerGroupName
- This is a crucial parameter that determines which layer group the mixin will search for nearby markers
- Must correspond to an existing layer group name in your Leaflet map
- If the layer group doesn't exist or is empty, the nearby markers detection won't work

#### spmLabelPath
- Defines the path in your marker data object where the label/name of the marker can be found
- Default: 'properties.item.vehicule.nom'
- Use dot notation to specify nested properties

#### radius
- Defines the search radius in meters
- Default value is obtained from environment variables or falls back to 100 meters

## How It Works

1. **Initialization**
   - When `initSwitchablePopupMixin()` is called, the mixin:
     - Sets up event listeners for map clicks and keyboard inputs
     - Creates a circle indicator showing the search radius
     - Identifies nearby markers within the specified radius

2. **Nearby Marker Detection**
   - Uses the `getNearbyLeafletLayersInRadius()` method to find markers:
     - Searches within the specified layer group
     - Calculates distances between markers
     - Returns an array of nearby layers

3. **Navigation**
   - Users can navigate between nearby markers using:
     - Left/Right arrow keys
     - On-screen arrow controls
     - Each navigation updates the popup content and maintains the circle indicator

4. **Cleanup**
   - The mixin automatically cleans up when:
     - User clicks elsewhere on the map
     - The popup is closed
     - The component is destroyed

## Best Practices

1. **Layer Group Names**
   - Always ensure the `spmLayerGroupName` corresponds to an existing layer group
   - Verify that your markers are properly added to the specified layer group
   - Use consistent layer group names across your application

2. **Label Paths**
   - Configure `spmLabelPath` to match your data structure
   - Ensure the path exists in your marker data to avoid undefined labels

3. **Radius Configuration**
   - Choose an appropriate radius based on your use case
   - Consider marker density when setting the radius
   - Too large a radius may include too many markers
   - Too small a radius may miss relevant nearby markers

## Troubleshooting

If nearby marker detection isn't working:

1. Verify that `spmLayerGroupName` matches an existing layer group
2. Check that markers are properly added to the specified layer group
3. Confirm that the radius is appropriate for your use case
4. Ensure the label path matches your data structure

## Example Implementation

```vue
<script>
import switchablePopupsMixin from '@/mixins/switchable-popup-mixin.ts'

export default {
  name: 'MarkerPopup',
  mixins: [switchablePopupsMixin],
  
  mounted() {
    this.initSwitchablePopupMixin({
      spmLayerGroupName: 'vehicles',  // Your layer group name
      spmLabelPath: 'properties.name', // Path to marker label
      radius: 150 // Search radius in meters
    })
  }
}
</script>
```

# Environmental variables

## Documentation / Whitelist

Add your env into envs-config.js

## Stagging override

Envs can be overriden using query string parameters

```sh
?messagesSearchMaxParallelReq=10
```

Envs can be overriden using local storage

vue.$env.envCacheManager.set(key, value)

## Retrieval

Use getEnvValue or getEnvIntValue

## Usage


**Recommended: This is the new way to get envs without hardcoded values**
```js
getEnvIntValue(envNames=>envNames.alertsSearchMaxSelectionLimit)
```

---

```js
import envService from '@/services/env-service.js'
getEnvValue("VUE_APP_ROUTER_DEFAULT_ROUTE")
```

```js
var App = {
  function mounted(){
    this.$env.getEnvValue("VUE_APP_ROUTER_DEFAULT_ROUTE")
  }
}
```

```js
import Vue from 'vue'
Vue.$env.getEnvValue("VUE_APP_ROUTER_DEFAULT_ROUTE")
```

Note: Priorize using the service instead **process.env**

## Testing variables effect without setting the variable

In development envs (dev, recette,isoprod, preprod), some variables can be set directly in the URL using querystring:

```sh
#Set the real-time refresh interval to 10s
?VUE_APP_AUTOREFRESH_INTERVAL=10000
```

## Available variables

- VUE_APP_ALERT_MODULE_SEARCH_SELECTION_LIMIT

  Search selection limit in Alerts module.

- E2E_LOGIN_USER

  Default account username for E2E tests

- E2E_LOGIN_CLIENT

  Default account client for E2E tests

- E2E_LOGIN_PWD

  Default account password for E2E tests

- E2E_LOGIN_USER_DGD

  Default account username for E2E tests for DGD client

- E2E_LOGIN_PWD_DGD

  Default account password for E2E tests for DGD client

- VUE_APP_DISABLE_USER_AUDIT

  Disable user audit (session/acitivties)
  Default account password for E2E tests for DGD client

- VUE_APP_FILTERS_MAX_SELECTION

  Default max selection for SearchModule (Location) / Ecoconduite
  
  Deprecated in favor of VUE_APP_SEARCH_SELECTION_LIMIT

- VUE_APP_FILTERS_MAX_SELECTION_DATES (Deprecated)

  Default max date selection for Ecoconduite

- VUE_APP_SEARCH_SELECTION_LIMIT

  Default max selection for SearchModule (Identification/Alerts)

- VUE_APP_IDENTIFICATION_MODULE_SEARCH_SELECTION_LIMIT

  Default max selection for Identification

- VUE_APP_AUTOREFRESH_INTERVAL

  Auto refresh interval for location module. Default to 300000 (5 minutes)



## Max selections per Module

### Messages module

- Rule (Fallback):
 - messagesfeatureSearchSelectionLimit / VUE_APP_MESSAGESFEATURE_SEARCH_SELECTION_LIMIT
 - messagesFeatureSearchSelectionLimit / VUE_APP_MESSAGES_FEATURE_SEARCH_SELECTION_LIMIT
 - 100

- Note 1: messagesfeatureSearchSelectionLimit is deprecated (Added for retro-compatibility with existing client params in production DB)


### Events module

- Rule (Fallback):
 - eventsModuleSearchSelectionLimit / VUE_APP_EVENTS_MODULE_SEARCH_SELECTION_LIMIT
 - searchSelectionLimit / VUE_APP_SEARCH_SELECTION_LIMIT
 - 100

### Identification module

- Rule (Fallback):
 - identificationModuleSearchSelectionLimit / VUE_APP_IDENTIFICATION_MODULE_SEARCH_SELECTION_LIMIT
 - searchSelectionLimit / VUE_APP_SEARCH_SELECTION_LIMIT
 - 50

### Alerts module

- Rule (Fallback):
 - alertsSearchMaxSelectionLimit / VUE_APP_ALERTS_SEARCH_MAX_SELECTION_LIMIT
 - searchSelectionLimit / VUE_APP_SEARCH_SELECTION_LIMIT
 - 100

### Location module

- Rule (Fallback):
 - locationSearchSelectionLimit / VUE_APP_LOCATION_SEARCH_SELECTION_LIMIT
 - searchSelectionLimit / VUE_APP_SEARCH_SELECTION_LIMIT
 - 120

## Debugging

Enable logging

```js
window.localStorage.debugVerboseLevel=9
window.localStorage.debugVerboseScopes='getEnvValue'
```

### Output example

8 getEnvValue:
```json
{
    "providedName": "alertsSearchMaxSelectionLimit",
    "friendlyName": "alertsSearchMaxSelectionLimit",
    "envName": "VUE_APP_ALERTS_SEARCH_MAX_SELECTION_LIMIT",
    "isStagging": true,
    "wasOverrideViaClientParam": true,
    "wasOverideViaLocalStorage": false,
    "wasOverrideViaQueryString": false,
    "value": "1500"
}
```
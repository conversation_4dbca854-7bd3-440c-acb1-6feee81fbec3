# Location Module Performance Quick Wins Part 2: Circuit Tab & Containers

This document focuses on performance optimizations for the **Circuit Tab view** which experiences significant slowdowns when loading containers data and positions, particularly with Leaflet rendering performance.

## 🎯 Problem Analysis

### Current Performance Issues
- **Leaflet Lag**: Map becomes unresponsive with large container datasets
- **Circuit Polylines**: Heavy rendering of complex route polylines with arrowheads
- **Container Markers**: Thousands of container markers without clustering
- **Deep Watchers**: Expensive reactivity on large circuit execution arrays
- **Memory Leaks**: Circuit data not properly frozen, causing memory bloat

### Impact on Users
- **Loading Times**: 5-15 seconds for circuit tab with containers
- **UI Freezing**: <PERSON>rows<PERSON> becomes unresponsive during data processing
- **Memory Usage**: 200-500MB increase per circuit with containers
- **Poor UX**: Laggy map interactions, delayed responses

## 🚀 Priority Quick Wins

| Priority | Quick Win | Impact | Effort | Est. Time | Files |
|----------|-----------|--------|--------|-----------|-------|
| **P1** | Container marker clustering | Very High | Low | 45min | 2 files |
| **P1** | Object.freeze circuit data | High | Low | 30min | 2 files |
| **P1** | Remove deep watchers | High | Low | 30min | 3 files |
| **P2** | Optimize polyline arrowheads | High | Medium | 1.5hr | 2 files |
| **P2** | Batch container operations | High | Medium | 1hr | 2 files |
| **P3** | Virtual container rendering | Very High | High | 3hr | 3 files |

## 🚀 Quick Win #1: Container Marker Clustering

**Impact**: Very High - Reduces DOM elements from 1000s to 10s  
**Effort**: Low - Use existing clustering infrastructure  
**Estimated Time**: 45 minutes

### Problem
```javascript
// Current: All containers rendered as individual markers
containers.forEach(container => {
  const marker = L.marker([container.lat, container.lng])
  map.addLayer(marker) // 1000+ DOM elements!
})
```

### Solution

#### `src/components/shared/SimplicitiMap/draw-container-markers-mixin.js`
```javascript
// Add clustering support
import 'leaflet.markercluster'

export default {
  data() {
    return {
      containerClusterGroup: null,
    }
  },

  methods: {
    drawContainersMarkers(containers = []) {
      // Clear existing cluster group
      if (this.containerClusterGroup) {
        this.map.removeLayer(this.containerClusterGroup)
      }

      // Create cluster group with optimized settings
      this.containerClusterGroup = L.markerClusterGroup({
        chunkedLoading: true,
        chunkInterval: 100,
        maxClusterRadius: 50,
        spiderfyOnMaxZoom: false,
        showCoverageOnHover: false,
        zoomToBoundsOnClick: true,
        // Custom cluster icon
        iconCreateFunction: this.getClusterIcon
      })

      // Batch add markers to cluster
      const markers = containers.map(container => {
        return L.marker([container.lat, container.lng], {
          icon: this.getContainerIcon(container)
        }).bindPopup(this.getContainerPopup(container))
      })

      this.containerClusterGroup.addLayers(markers)
      this.map.addLayer(this.containerClusterGroup)
    },

    getClusterIcon(cluster) {
      const count = cluster.getChildCount()
      let className = 'marker-cluster-small'
      
      if (count > 100) className = 'marker-cluster-large'
      else if (count > 10) className = 'marker-cluster-medium'
      
      return L.divIcon({
        html: `<div><span>${count}</span></div>`,
        className: `marker-cluster ${className}`,
        iconSize: L.point(40, 40)
      })
    }
  }
}
```

#### `src/composables/useLocationCircuitContainers.ts`
```javascript
// Optimize container data processing
function drawContainers() {
  // Only render containers in current viewport + buffer
  const bounds = mapRef.value?.getBounds()
  if (!bounds) return
  
  const visibleContainers = containers.value.filter(container => {
    return bounds.contains([container.lat, container.lng])
  })
  
  // Use clustering for large datasets
  if (visibleContainers.length > 50) {
    mapVmRef.value?.drawContainersMarkers(visibleContainers)
  } else {
    // Direct rendering for small datasets
    mapVmRef.value?.drawContainersMarkersSimple(visibleContainers)
  }
}
```

## 🚀 Quick Win #2: Object.freeze Circuit Data

**Impact**: High - Prevents deep reactivity on large circuit arrays  
**Effort**: Low - Add Object.freeze to mutations  
**Estimated Time**: 30 minutes

### Files to Update

#### `src/store/location_module/circuit-details.js`
```javascript
// Line ~25: Freeze circuit execution details
setCircuitExecutionDetails(state, circuitExecutionDetails) {
  state.circuitExecutionDetails = Object.freeze(circuitExecutionDetails || {})
},

// Line ~30: Freeze circuit steps/troncons
updateCircuitExecutionDetails(state, { circuitExecutionId, circuitExecutionDetails }) {
  const frozenDetails = Object.freeze({
    ...circuitExecutionDetails,
    troncons: Object.freeze(circuitExecutionDetails.troncons || [])
  })
  
  Vue.set(state.circuitExecutionDetails, circuitExecutionId, frozenDetails)
}
```

#### `src/components/location/LocationCircuitTab/LocationCircuitTab.vue`
```javascript
// Line ~180: Freeze polylines data before committing
async updateCircuitData() {
  // ... existing code ...
  
  const frozenPolylines = Object.freeze(
    polylines.map(polyline => Object.freeze(polyline))
  )
  
  await this.$store.dispatch('simpliciti_map/setDataset', {
    type: 'circuitExecutionsPolylines',
    data: frozenPolylines,
  })
}
```

## 🚀 Quick Win #3: Remove Deep Watchers

**Impact**: High - Eliminates expensive deep watching  
**Effort**: Low - Replace with shallow alternatives  
**Estimated Time**: 30 minutes

### Files to Update

#### `src/components/location/LocationCircuitTab/CircuitExecDetails/CircuitExecDetails.vue`
```javascript
// Replace deep watcher with computed property
computed: {
  stepsCount() {
    return this.steps?.length || 0
  },
  
  filteredStepsCount() {
    return this.filteredSteps?.length || 0
  }
},

watch: {
  // Watch counts instead of deep objects
  stepsCount() {
    this.updateStepsDisplay()
  },
  
  filteredStepsCount() {
    this.updateFilteredDisplay()
  }
}
```

#### `src/components/shared/SimplicitiMap/draw-single-polylines-mixin.js`
```javascript
// Line ~45: Replace deep watcher with manual trigger
watch: {
  // Remove deep: true, use length-based watching
  'circuitPolylines.length': {
    handler() {
      this.updateCircuitPolylines()
    },
    immediate: true
  }
}
```

## 🚀 Quick Win #4: Optimize Polyline Arrowheads

**Impact**: High - Reduces rendering overhead for complex routes  
**Effort**: Medium - Optimize arrowhead rendering logic  
**Estimated Time**: 1.5 hours

### Problem
```javascript
// Current: Arrowheads rendered for all polylines always
setInterval(() => {
  polyline.bringToFront() // Expensive DOM operation!
}, 100)
```

### Solution

#### `src/components/shared/SimplicitiMap/draw-single-polylines-mixin.js`
```javascript
import { queueOperationOnce } from '@/utils/promise.js'
import { throttleSimple } from '@/utils/async'

export default {
  data() {
    return {
      arrowheadUpdateInterval: null,
      lastZoomLevel: null
    }
  },

  methods: {
    onZoomLevelChange_circuitPolylines() {
      const currentZoom = this.map.getZoom()
      
      // Only update if zoom changed significantly
      if (Math.abs(currentZoom - this.lastZoomLevel) < 0.5) return
      this.lastZoomLevel = currentZoom
      
      queueOperationOnce(
        'circuit-polylines-zoom-update',
        () => {
          this.updateCircuitPolylinesZoom(currentZoom)
        },
        { timeout: 150, clearPreviousTimeout: true }
      )
    },

    updateCircuitPolylinesZoom(zoomLevel) {
      const shouldShowArrowheads = zoomLevel >= 14
      const weight = zoomLevel >= 16 ? 6 : 4
      
      this.circuitPolylines.forEach(polylineItem => {
        if (polylineItem.leafletPolyline) {
          // Update weight
          polylineItem.leafletPolyline.setStyle({ weight })
          
          // Toggle arrowheads based on zoom
          if (shouldShowArrowheads && !polylineItem.hasArrowheads) {
            this.addArrowheads(polylineItem)
          } else if (!shouldShowArrowheads && polylineItem.hasArrowheads) {
            this.removeArrowheads(polylineItem)
          }
        }
      })
    },

    addArrowheads(polylineItem) {
      // Only add arrowheads if not already present
      if (polylineItem.hasArrowheads) return
      
      polylineItem.leafletPolyline.arrowheads({
        frequency: '100px',
        size: '15px'
      })
      polylineItem.hasArrowheads = true
    },

    removeArrowheads(polylineItem) {
      if (!polylineItem.hasArrowheads) return
      
      polylineItem.leafletPolyline.deleteArrowheads()
      polylineItem.hasArrowheads = false
    },

    // Throttled bringToFront for highlighted polylines only
    highlightPolyline: throttleSimple(function(polylineItem) {
      if (polylineItem.leafletPolyline && polylineItem.isHighlighted) {
        polylineItem.leafletPolyline.bringToFront()
      }
    }, 200)
  },

  destroyed() {
    if (this.arrowheadUpdateInterval) {
      clearInterval(this.arrowheadUpdateInterval)
    }
  }
}
```

## 🚀 Quick Win #5: Batch Container Operations

**Impact**: High - Reduces map redraws  
**Effort**: Medium - Implement batching logic  
**Estimated Time**: 1 hour

### Solution

#### `src/composables/useLocationCircuitContainers.ts`
```javascript
import { queueOperationOnce } from '@/utils/promise.js'
import { chunkArray } from '@/utils/array.js'

// Batch container updates
function updateContainers(newContainers: Container[]) {
  queueOperationOnce(
    'containers-update',
    () => {
      processBatchedContainers(newContainers)
    },
    {
      timeout: 300,
      clearPreviousTimeout: true
    }
  )
}

function processBatchedContainers(containers: Container[]) {
  // Process in chunks to avoid blocking UI
  const chunks = chunkArray(containers, 100)
  
  chunks.forEach((chunk, index) => {
    setTimeout(() => {
      addContainerChunk(chunk)
    }, index * 50) // Spread processing over time
  })
}

function addContainerChunk(containerChunk: Container[]) {
  // Batch DOM operations
  const fragment = document.createDocumentFragment()
  
  containerChunk.forEach(container => {
    const marker = createContainerMarker(container)
    // Add to fragment instead of directly to map
    fragment.appendChild(marker.getElement())
  })
  
  // Single DOM update
  mapRef.value?.getContainer().appendChild(fragment)
}
```

## 🔧 Implementation Checklist

### Phase 1: Critical Performance Fixes (Est. 1.75 hours)
- [ ] **QW1**: Implement container marker clustering
- [ ] **QW2**: Add Object.freeze to circuit-details.js
- [ ] **QW2**: Add Object.freeze to LocationCircuitTab.vue
- [ ] **QW3**: Remove deep watchers from CircuitExecDetails.vue
- [ ] **QW3**: Optimize watchers in draw-single-polylines-mixin.js

### Phase 2: Rendering Optimizations (Est. 2.5 hours)
- [ ] **QW4**: Optimize polyline arrowhead rendering
- [ ] **QW4**: Implement zoom-based arrowhead toggling
- [ ] **QW5**: Batch container marker operations
- [ ] **QW5**: Implement chunked container processing

### Phase 3: Advanced Optimizations (Est. 3 hours)
- [ ] **QW6**: Virtual container rendering for viewport
- [ ] **QW6**: Implement spatial indexing for containers
- [ ] **QW6**: Add progressive loading for large circuits

## 📊 Expected Performance Improvements

### Before Implementation
- **Container Markers**: 1000+ individual DOM elements
- **Memory Usage**: 200-500MB for circuit with containers
- **Render Time**: 5-15 seconds for initial load
- **Map Interactions**: 200-500ms lag during pan/zoom
- **Polyline Updates**: 100-300ms per zoom level change

### After Implementation
- **Container Markers**: 10-50 cluster elements
- **Memory Usage**: 50-150MB (60-70% reduction)
- **Render Time**: 1-3 seconds for initial load
- **Map Interactions**: 50-100ms response time
- **Polyline Updates**: 20-50ms per zoom level change

## 🧪 Testing Strategy

### Performance Test Scenarios
1. **Large Container Dataset**: 2000+ containers in circuit view
2. **Complex Circuit Routes**: Multi-stop circuits with 100+ waypoints
3. **Real-time Updates**: Live container status changes
4. **Memory Stress Test**: Multiple circuit tabs with containers
5. **Mobile Performance**: Test on lower-end devices

### Validation Metrics
- **FPS**: Maintain 30+ FPS during map interactions
- **Memory**: <150MB for large datasets
- **Load Time**: <3 seconds for circuit with containers
- **Responsiveness**: <100ms for user interactions

## 🚨 Risk Assessment

### Low Risk
- **Container Clustering**: Well-established pattern
- **Object.freeze**: Already used elsewhere in codebase

### Medium Risk
- **Arrowhead Optimization**: May affect visual consistency
  - *Mitigation*: Test zoom thresholds thoroughly
- **Batch Operations**: Could delay real-time updates
  - *Mitigation*: Use appropriate timeout values

### High Risk
- **Virtual Rendering**: Complex implementation
  - *Mitigation*: Implement incrementally, test extensively

## 📈 Monitoring & Success Metrics

### Key Performance Indicators
- **Page Load Time**: Circuit tab load time
- **Memory Usage**: Heap size during container rendering
- **Frame Rate**: FPS during map interactions
- **User Satisfaction**: Reduced support tickets about slowness

### Monitoring Tools
- Chrome DevTools Performance tab
- Lighthouse performance audits
- Real User Monitoring (RUM) metrics
- Memory profiling for leak detection

---

**Implementation Priority**: Start with Phase 1 for immediate relief, then Phase 2 for substantial improvements. Phase 3 can be implemented based on remaining performance needs.
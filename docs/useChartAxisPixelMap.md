# useChartAxisPixelMap composable documentation

## Purpose

This composable exposes a utility to compute and store the pixel positions of x-axis tick labels from an ECharts instance. It allows a separate DOM-based "external axis" to align precisely with chart ticks — especially useful in layouts where charts don’t render labels directly.
NB : This composable is specifically designed for use with ECharts and may require further adjustments to be compatible with other charting libraries.

## Overview

Ticket [#48485](https://easyredmine.simpliciti.fr/issues/48485) introduced the need to make diagnostics charts header sticky, including echarts horizontal axis.
This composables aims to :

- Improve external x-axis readability
- Support complex layout scenarios (e.g., floating axis)
- React to resizes and keep the axis sync’d with the chart

## API

### `updateStickyLabelPixelMap(params: UpdatePixelMapParams): Promise<void>`

Updates the Vuex store with a map of tick values to their pixel coordinates on the x-axis.

#### Parameters

| Parameter        | Type                | Required | Description                                                                  |
|------------------|---------------------|----------|------------------------------------------------------------------------------|
| `chartInstance`  | `ECharts \| null`   | ✅       | The instance of the ECharts chart to extract axis tick coordinates from.     |
| `isFirstChart`   | `boolean`           | ✅       | Whether the current chart is the first one (only this one updates the map).  |
| `store`          | `Store<any>`        | ✅       | Vuex store instance where the pixel map will be saved.                       |

#### Returns

`Promise<void>` — Resolves after the next DOM tick and store update.

#### Usage Example

```ts
import { useChartAxisPixelMap } from '@/composables/useChartAxisPixelMap'

const { updateStickyLabelPixelMap } = useChartAxisPixelMap()

await updateStickyLabelPixelMap({
  chartInstance: this.chartInstance,
  isFirstChart: true,
  store: this.$store
})
```

## Internal logic

The pixel map is calculated using:

- chart.getModel().getComponent('xAxis') (private API access)
- Tick coordinates via axis.getTicksCoords()
- Conversion to pixels via chart.convertToPixel(...)

Only in-range ticks (excluding edge noise) are retained via a margin and buffer.

## Technical Notes

- ⚠ Relies on private methods from the ECharts API. May break on version changes.
- ⚠ Tightly coupled to ECharts: refactor computePixelMapEcharts() when switching to D3 or another library.

## Usage 

### Inside a chart component

```vue
// Inside a Vue Options API component:
import { useChartAxisPixelMap } from '@/composables/useChartAxisPixelMap'

const { updateStickyLabelPixelMap } = useChartAxisPixelMap()

// Example usage on mount and resize:
mounted() {
  updateStickyLabelPixelMap({
    chartInstance: this.chartInstance,
    isFirstChart: this.isFirstChart,
    store: this.$store
  })
}
```

### Render the pixelMap in a template

```vue

<template>
  <div class="col-12" style="position: relative; height: 20px">
    <div
      class="external-x-axis"
      style="position: absolute; top: 0; height: 20px"
    >
      <div
        v-for="(px, ts) in pixelMap"
        :key="ts"
        :style="{
                position: 'absolute',
                left: px + 'px',
                transform: 'translateX(-50%)',
                whiteSpace: 'nowrap',
                fontSize: '12px',
                color: '#6e7079',
              }"
      >
        {{ getFormattedPixelMap(ts) }}
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import { adjustDateWithTimezone } from '@/utils/dates'

  export default {
    computed: {
      ...mapGetters({
        pixelMap: 'diagnostics/getPixelMap',
      }),
    },
    methods: {
      getFormattedPixelMap(value) {
        return adjustDateWithTimezone(Number(value)).format('HH:mm')
      },
    }
  }
</script>
```
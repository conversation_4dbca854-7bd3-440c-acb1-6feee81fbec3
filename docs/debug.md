# Debugging Utilities (`debug.js`)

This document describes how to use the advanced debugging features provided by `src/libs/debug.js` in the Geored v3 frontend. It covers two main usage patterns:

- **Console Namespace Replication (`console.replicate`)**
- **Interactive Debug Modal (SHIFT+D+ENTER shortcut)**

---

## 1. Console Namespace Replication (`console.replicate`)

The debugging library overrides the browser `console` object, allowing you to create new console-like loggers bound to specific debug namespaces. This enables organized, color-coded logging for different app modules.

### Usage

```js
const myConsole = console.replicate('LocationV2');
myConsole.log('This is a log message for LocationV2');
myConsole.debug('Debug details for LocationV2');
myConsole.error('An error in LocationV2');
```

- The first argument is the namespace (e.g., `'LocationV2'`).
- Optionally, you can provide a namespace prefix (defaults to `'app:'`).
- All standard console methods (`log`, `debug`, `info`, `warn`, `error`, etc.) are available, and are routed through the debug logger for that namespace.
- You can nest/replicate further: `myConsole.replicate('SubModule')`.

### Why Use Namespaces?
- Enables fine-grained control over which logs are visible.
- Color-coded output per namespace in browser devtools.
- Toggle visibility via the debug modal or `localStorage.debug`.

---

## 2. Interactive Debug Modal (SHIFT+D+ENTER)

You can configure which debug namespaces are enabled at runtime using a built-in modal dialog, accessible via a keyboard shortcut.

### How to Open
- Press and hold **SHIFT**, then press **D**, then **ENTER** (in sequence, not simultaneously) anywhere in the app.
- The modal will appear, centered on the screen.

### Features
- **Current Debug Value:** Shows and allows editing of the raw `localStorage.debug` string (e.g., `app:*,LocationV2:*`).
- **Apply/Clear:** Enable or disable debug output for selected namespaces.
- **Wildcard Patterns:** Clickable buttons for quick namespace toggling (e.g., `app:*`, `LocationV2:*`).
- **Add to Debug Value:** Use `+` to append patterns to the current debug filter.
- **Close:** Click the × button or backdrop to close the modal.

### What Does This Control?
- The modal updates `localStorage.debug` and immediately applies changes via `window.debug.enable()`.
- Only logs from enabled namespaces will appear in the console.
- You can use wildcards (e.g., `app:*`, `*:error`), and multiple patterns separated by commas.

---

## Disabling the Debug Modal Shortcut in Production

To prevent accidental exposure of debug tools in production, the debug modal shortcut (**SHIFT+D+ENTER**) is programmatically disabled in production-like environments.

### How It Works

In `src/main.ts`, the following logic is applied:

```ts
//@ts-ignore
if (typeof window?.debug?.disableShorcut !== undefined) {
  //@ts-ignore
  window.debug.disableShorcut = () =>
    envService.isProduction() &&
    !envService.isBeta() &&
    envService.isStagging().isPreprod();
}
```

- The `window.debug.disableShorcut` property is set to a function that returns `true` only in strict production (not beta, not preprod/staging).
- The debug modal will **not** appear if this function returns `true`.
- This ensures the debug modal is only available to developers and testers, not end users.

### How the Shortcut is Blocked

- The modal logic in `debug.js` checks `window.debug.disableShorcut` before opening:
    - If it's a function and returns `true`, the modal is blocked.
    - If it's a boolean and `true`, the modal is blocked.
- This logic is enforced at runtime, so no build step is required.

### Customizing Environments

- The detection uses `envService` to check for production, beta, staging, and preprod flags.
- You can adjust the logic in `main.ts` to fit your deployment pipeline or add more granular controls as needed.

---

## Example: Enabling Debug for a Module

1. Open the debug modal (**SHIFT+D+ENTER**).
2. Click a wildcard (e.g., `LocationV2:*`) or type it into the input.
3. Click **Apply**. Only logs from that namespace will appear.

---

## Notes
- Debug configuration is persisted in `localStorage`.
- To disable all debug output, clear the debug value in the modal or call `window.debug.disable()`.
- This system is for development and troubleshooting; remove or restrict debug output in production builds as appropriate.

---

For more details, see the implementation in [`src/libs/debug.js`].

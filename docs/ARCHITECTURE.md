# Architecture

This document provides a high-level overview of the technical architecture of the Geored v3 application.

## Frontend Architecture

The frontend is a **Single Page Application (SPA)** built with **Vue.js**. It follows a component-based architecture, promoting reusability and maintainability.

- **Framework**: [Vue.js](https://vuejs.org/) (likely Vue 2, given the presence of `mixins` and a `vue.config.js` file, but also using `composables` which suggests a potential migration path or hybrid approach with Vue 3's Composition API).
- **Routing**: [Vue Router](https://router.vuejs.org/) handles client-side routing, enabling navigation between different views without full page reloads.
- **Styling**: The project uses a combination of global stylesheets, component-scoped styles, and likely a CSS preprocessor like Sass/SCSS, as indicated by the `styles` directory.
- **Internationalization**: The application supports multiple languages through the `i18n` directory.

## Data Flow and State Management

The application follows a unidirectional data flow, which makes the state more predictable and easier to debug. The core components of the data flow are:

1.  **Components**: Vue components are responsible for rendering the UI and dispatching actions in response to user interactions.
2.  **Actions (Vuex)**: Actions are dispatched from components. They are responsible for committing mutations and can contain asynchronous operations, such as calling a service to fetch data from the backend.
3.  **Services**: The services in the `src/services` directory encapsulate the application's business logic. They are called by Vuex actions to perform tasks like making API requests, processing data, and interacting with other parts of the application.
4.  **Mutations (Vuex)**: Mutations are the only way to change the state in the Vuex store. They are synchronous and are committed by actions.
5.  **State (Vuex)**: The state is the single source of truth for the application's data. It is reactive, so when the state changes, the components that depend on it are automatically updated.

### State Management (Vuex)

[Vuex](https://vuex.vuejs.org/) is used for centralized state management. The store is highly modular, with each module representing a specific domain of the application's state (e.g., `auth`, `diagnostics`, `events`). This modularity helps to keep the store organized and scalable.

### Services and Business Logic

### Example Feature Flow: Diagnostics

To illustrate how these architectural components work together, let's trace the data flow for the diagnostics feature:

1.  **View (`DiagnosticsModule.vue`)**: The process starts in this view. When a user selects a vehicle and a date, the `updateAnalysisViewData` method is triggered.
2.  **Action Dispatch**: Inside `updateAnalysisViewData`, a Vuex action is dispatched: `this.$store.dispatch('diagnostics/updateChronoData', ...)`.
3.  **Service Calls**: The same method also directly calls the `analyzeHistorySegment` function from `history-service.js`.
4.  **API Interaction**: Both the `diagnostics/updateChronoData` action (via `diagnostics-service.js`) and the `analyzeHistorySegment` service make asynchronous API calls to the backend to fetch the necessary data.
5.  **State Mutation**: Once the data is retrieved, Vuex actions commit mutations (e.g., `SET_CHRONO_DATA`) to update the state in the `diagnostics` store module. The result from the `analyzeHistorySegment` service is also placed in the store.
6.  **Reactive UI Update**: The Vuex store's state is reactive. As soon as the diagnostics data is updated in the store, any component that uses this data (via getters or by directly accessing the state) will automatically re-render.
7.  **Component Rendering**: The `DiagnosticsCharts.vue` and `DiagnosticsChart.vue` components, which get their data from the `diagnostics` store module, display the new information to the user without needing to be manually refreshed. This creates a seamless and efficient user experience.

The `src/services` directory contains the core business logic of the application. This separation of concerns keeps the components and the store lean, focusing on the UI and state management, respectively. Key services include:

- `auth-service.js`: Handles user authentication and authorization.
- `vehicle-service.js`: Manages vehicle-related data and operations.
- `diagnostics-service.js`: Deals with vehicle diagnostics.
- `geocoding-service.js`: Provides location-based services.

## API Communication

The frontend is decoupled from the backend and interacts with it via a RESTful API. The logic for making the actual HTTP requests is abstracted away in the `src/api` directory, which is then used by the services. This separation allows for easier maintenance and testing.

## Testing

The project includes a comprehensive testing strategy:

- **Unit & Integration Tests**: Located in the `tests` directory, likely using a framework like Jest.
- **End-to-End (E2E) Tests**: Located in the `e2e` directory, using a framework like CodeceptJS.

## Build & Deployment

- **Build Tool**: [Vite](https://vitejs.dev/) or [Vue CLI](https://cli.vuejs.org/) is used for building and bundling the application.
- **CI/CD**: The `.gitlab-ci.yml` file indicates that GitLab CI/CD is used for continuous integration and deployment.
- **Containerization**: The `Dockerfile` suggests that the application is containerized using Docker for consistent development and production environments.

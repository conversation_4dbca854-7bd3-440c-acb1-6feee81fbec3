# Testing

## Tools/Features

- JEST (Unit tests)
- Coverage: LCOV, cobertura
- https://codecept.io/ (Puppeter driver) (E2E Tests)
- Zero config unit test GUI for development

## Unit Tests

```sh
npx vue-cli-service test:unit --mode=cidev
```

Will run all the unit tests with envs from cidev.

```sh
npx vue-cli-service test:unit --coverage=false --watch --mode=cidev store.location-module.last-circuit
```

Will only run tests/unit/store.location-module.last-circuit.spec.js in watch mode, with coverage disabled and loading envs from cidev.

### Development GUI

Opens a GUI for development in the browser.

```sh
npx majestic
```

### Login to APIV2

```js
import store from "@/store";
beforeAll(() => {
  return store.dispatch("auth/login", {
    login: process.env.UNIT_LOGIN_USER_DGD,
    client: "DGD",
    password: process.env.UNIT_LOGIN_PWD,
  });
});
```

Will login as unit/DGD (Development environment)

### Logging

Enable logging by lowering the NODE_ENV to development.

```sh
NODE_ENV=development npx vue-cli-service test:unit --coverage=false --watch --mode=cidev
```

## E2E Tests

### Development

Launch the E2E Codecept GUI on port 3001

```sh
npx vue-cli-service test:e2e:open
```

### Production

Launch the E2E tests in headless mode

```sh
npx vue-cli-service test:e2e -- --headless
```

# Vue 2.7 -> Vue 3 : v-model compatibility

Reference : https://v3-migration.vuejs.org/breaking-changes/v-model.html

Vue 3 introduced a new way to use v-model directives. 

In 2.x, using a v-model on a component was an equivalent of passing a value prop and emitting an input event.

In 3.x v-model on the custom component is an equivalent of passing a modelValue prop and emitting an update:modelValue event.

A composable method has been developed to automatically detect which version of Vue is used to emit the right event name.

The goal is, when the migration to Vue 3 is done, all v-model work as expected. 

## Composable src/composables/useVModelEmit.ts

```typescript
import Vue from 'vue'

/**
 * This composable is used to emit the correct event for v-model in Vue 2 and Vue 3.
 * @param emit - The emit function from the component. Composition API : const emit = defineEmits([‘emit1‘, ‘emit2’]). Options API : this.$emit.
 * @param instance (Optional) - Pass this from the component if you want to use `this` instead of `emit` (workaround for Options API)
 */
export function useVModelEmit<T>(
  emit: (event: string, value: T) => void,
  instance?: Vue
) {
  const vModelEmit = (val: T) => {
    // If instance is of wrong type, log an error and return
    // Workaround for components written in plain js
    if (instance && !(instance instanceof Vue)) {
      console.error(
        'Invalid instance: Expected a Vue component instance.',
        instance
      )
      return
    }

    if (!Vue) {
      console.error('Vue is not defined')
      return
    }

    if (!Vue.version) {
      console.error('Vue version is not defined')
      return
    }

    const isVue3 = !!(Vue && Vue.version && Vue.version.startsWith('3.'))

    if (isVue3) {
      if (!instance) {
        emit('update:modelValue', val)
      } else {
        instance.$emit('update:modelValue', val)
      }
    } else {
      if (!Vue.version.startsWith('2.')) {
        console.error('Error in Vue version. Return')
        return
      }
      if (!instance) {
        emit('input', val)
      } else {
        instance.$emit('input', val)
      }
    }
  }

  return { vModelEmit }
}
```

The goal will be to detect which version of Vue is used : 

`const isVue3 = !!(Vue && Vue.version && Vue.version.startsWith('3.'))`

If Vue 3 is detected, then we'll emit the event `update:modelValue`, otherwise we'll emit `input`.

The method takes as first mandatory parameter the emit function from the component :

- Composition API : `const emit = defineEmits([‘emit1‘, ‘emit2’])`
- Options API : `this.$emit`

### Bonus : compatibility with Options API and Composition API

The purpose of the second optional parameter is to allow the use of `this` instead of `emit` in the component.

In a Options API component, you can pass `this` as the second parameter. This way, composable will be able to emit `this.$emit` just as a standard Options API component would do.

If the second parameter is not provided, then we consider that the component is written in Composition API and the composable will emit `emit`.

## Usage from components

### Composition API

Import the composable :

```typescript
import { useVModelEmit } from '@/composables/useVModelEmit'
```

Then use it in your component :

```typescript
const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)
```

```typescript
vModelEmit({ ...value })
```

Full example : 

```vue
<script setup lang="ts">
  import { useVModelEmit } from '@/composables/useVModelEmit'

  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({}),
    },
    items: {
      type: Array,
      default: () => [],
    },
  })

  const emit = defineEmits(['input', 'update:modelValue'])

  const { vModelEmit } = useVModelEmit(emit)

  const onInput = (selected) => {
    console.log('OnInput', {
      selected,
    })

    vModelEmit({ ...selected })
  }
</script>
```

### Options API

Import the composable :

```typescript
import { useVModelEmit } from '@/composables/useVModelEmit'
```

Then use it in your component :

```typescript
const { vModelEmit } = useVModelEmit(this.$emit, this)

vModelEmit({ ...value })
```

Full example : 

```vue
import { useVModelEmit } from '@/composables/useVModelEmit'

export default {
  selectedValue() {
      let value = this.selectedValue

      if (this.selectedType === 'range') {
        const dates = value.map((d) => new Date(d))
        value = getConsecutiveDays(dates[0], dates[1])
      }

      const { vModelEmit } = useVModelEmit(this.$emit, this)

      vModelEmit({ ...value })
    },
}
```

## Case of v-model on third-party components

If you are using a third-party component that uses v-model, then you'll have to explicitly pass the prop and listen to the event to avoid breaking changes.

Example :

```vue
<my-component-third-party
 :value="myVar"
 @input="handleInput"
/>****
```


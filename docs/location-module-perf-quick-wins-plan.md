# Location Module Performance Quick Wins Plan

This document outlines actionable performance improvements for the Location module based on analysis of the codebase against the [performance guidelines](./perf-guidelines.md).

## 🎯 Priority Matrix

| Priority | Quick Win | Impact | Effort | Files Affected |
|----------|-----------|--------|--------|-----------------|
| **P1** | Object.freeze() on large datasets | High | Low | 3 files |
| **P1** | Remove deep watchers | High | Low | 2 files |
| **P2** | Debounce map events | High | Medium | 2 files |
| **P2** | Batch polyline operations | High | Low | 1 file |
| **P3** | Optimize API chunking | Medium | High | 1 file |
| **P3** | Add manual chart updates | Medium | Medium | 1 file |

## 🚀 Quick Win #1: Object.freeze() Large Datasets

**Impact**: High - Prevents deep reactivity overhead on large position/history arrays  
**Effort**: Low - Add Object.freeze() to existing mutations  
**Estimated Time**: 30 minutes

### Files to Update:

#### `src/store/location_module/history.js`
```javascript
// Line ~45: currentTripHistory mutation
currentTripHistory(state, tripHistory = []) {
  state.currentTripHistory = Object.freeze(tripHistory || [])
}

// Line ~47: currentChartTripHistory mutation  
currentChartTripHistory: (state, currentChartTripHistory = []) =>
  (state.currentChartTripHistory = Object.freeze(currentChartTripHistory))
```

#### `src/store/location_module/main_search.js`
```javascript
// Line ~420: setResultsFromAPIResponse action
const normalizedResults = Object.freeze(getNormalizedResults(
  infos,
  selectionType,
  rootGetters,
  isHistoryMode,
  shouldUseApiV3
))
```

#### `src/store/location_module/index.js`
```javascript
// Line ~85: setResults mutation
setResults(state, { type, value }) {
  state[`${type}Results`] = Object.freeze(value)
}

// Line ~92: mergeResults mutation
mergeResults(state, { type, value }) {
  // ... existing filtering logic ...
  state[`${type}Results`] = Object.freeze([
    ...state[`${type}Results`],
    ...filteredValues,
  ])
}
```

## 🚀 Quick Win #2: Remove Deep Watchers

**Impact**: High - Eliminates expensive deep watching on large arrays  
**Effort**: Low - Replace with shallow watchers  
**Estimated Time**: 45 minutes

### Files to Update:

#### `src/components/location/LocationMap/LocationHistoryMap/LocationHistoryMap.vue`
```javascript
// Line ~65: Remove deep: true from tripHistoryPolylines watcher
tripHistoryPolylines: {
  async handler(data) {
    if (data.length > 0) {
      await this.$store.dispatch('simpliciti_map/setDataset', {
        type: 'tripHistoryPolylines',
        data,
      })
    }
  },
  immediate: true,
  // deep: true, // REMOVE THIS LINE
}
```

#### `src/components/shared/SimplicitiMap/SimplicitiMap.vue`
```javascript
// Line ~180: Replace deep watcher with computed property
computed: {
  // ... existing computed properties ...
  locationModulePolylinesLength() {
    return this.locationModulePolylines.length
  }
},
watch: {
  // Replace deep watcher with length-based watcher
  locationModulePolylinesLength: {
    handler() {
      this.updateLocationModulePolylines()
    },
    immediate: true,
  }
}
```

## 🚀 Quick Win #3: Debounce Map Events

**Impact**: High - Reduces jank from frequent map updates  
**Effort**: Medium - Add throttling to event handlers  
**Estimated Time**: 1 hour

### Files to Update:

#### `src/components/shared/SimplicitiMap/LeafletMap.vue`
```javascript
// Add import at top
import { throttleSimple } from '@/utils/async'

// Line ~320: Throttle moveend handler
created() {
  // ... existing code ...
  this.throttledMoveEnd = throttleSimple(this.onMoveEnd.bind(this), 150)
}

// Line ~450: Use throttled handler
initializeMap() {
  // ... existing code ...
  this.map.on('moveend', this.throttledMoveEnd)
  // ... rest of code ...
}
```

#### `src/components/shared/SimplicitiMap/SimplicitiMap.vue`
```javascript
// Add import at top
import { queueOperationOnce } from '@/utils/promise.js'

// Line ~200: Debounce polyline updates
locationModulePolylines: {
  handler(items = []) {
    queueOperationOnce(
      'location-module-polylines-update',
      () => {
        this.updateLocationModulePolylines(items)
      },
      {
        timeout: 200,
        clearPreviousTimeout: true
      }
    )
  },
  immediate: true,
}
```

## 🚀 Quick Win #4: Batch Polyline Operations

**Impact**: High - Improves rendering performance for large datasets  
**Effort**: Low - Use existing queueOperationOnce utility  
**Estimated Time**: 30 minutes

### Files to Update:

#### `src/components/shared/SimplicitiMap/SimplicitiMap.vue`
```javascript
// Line ~600: Batch drawPolylines calls
drawPolylines(polylines = [], options = {}) {
  queueOperationOnce(
    `draw-polylines-${options.layer || 'default'}`,
    () => {
      this.executeDrawPolylines(polylines, options)
    },
    {
      timeout: 100,
      clearPreviousTimeout: true
    }
  )
}

// Rename existing drawPolylines to executeDrawPolylines
executeDrawPolylines(polylines = [], options = {}) {
  // ... existing drawPolylines implementation ...
}
```

## 🔧 Implementation Checklist

### Phase 1: Critical Fixes (Est. 2 hours)
- [ ] **QW1**: Add Object.freeze() to history.js mutations
- [ ] **QW1**: Add Object.freeze() to main_search.js results
- [ ] **QW1**: Add Object.freeze() to index.js mutations
- [ ] **QW2**: Remove deep watcher from LocationHistoryMap.vue
- [ ] **QW2**: Replace deep watcher in SimplicitiMap.vue

### Phase 2: Performance Optimizations (Est. 1.5 hours)
- [ ] **QW3**: Add throttling to LeafletMap moveend events
- [ ] **QW3**: Debounce SimplicitiMap polyline updates
- [ ] **QW4**: Batch polyline drawing operations

### Phase 3: Advanced Optimizations (Est. 3 hours)
- [ ] **QW5**: Implement adaptive API chunking
- [ ] **QW6**: Add manual chart update controls

## 📊 Expected Performance Gains

### Before Implementation:
- **Memory Usage**: High reactivity overhead on 1000+ position arrays
- **CPU Usage**: Spikes during map panning with large datasets
- **Render Time**: 200-500ms for polyline updates
- **User Experience**: Noticeable jank during real-time updates

### After Implementation:
- **Memory Usage**: 30-50% reduction from frozen objects
- **CPU Usage**: 40-60% reduction from eliminated deep watchers
- **Render Time**: 100-200ms for batched polyline updates
- **User Experience**: Smooth interactions with large datasets

## 🧪 Testing Strategy

### Performance Test Scenarios:
1. **Large Dataset Test**: 500+ vehicles in real-time mode
2. **History Mode Test**: Multi-day range with 10k+ positions
3. **Map Interaction Test**: Rapid panning/zooming with polylines
4. **Memory Leak Test**: Extended usage with frequent data updates

### Validation Criteria:
- [ ] No console errors after changes
- [ ] Existing functionality preserved
- [ ] Improved performance metrics in dev tools
- [ ] Smooth user interactions

## 🚨 Potential Risks

### Low Risk:
- **Object.freeze()**: May break code that mutates frozen objects
  - *Mitigation*: Review mutation patterns, use spread operator for updates

### Medium Risk:
- **Removing deep watchers**: May miss nested property changes
  - *Mitigation*: Test thoroughly, add manual triggers where needed

### High Risk:
- **Debouncing**: May delay critical real-time updates
  - *Mitigation*: Use appropriate timeout values, test with real data

## 📈 Monitoring & Metrics

### Key Performance Indicators:
- **Bundle Size**: Monitor for any increases
- **Memory Usage**: Track heap size during heavy usage
- **Render Performance**: Measure FPS during map interactions
- **API Response Times**: Ensure chunking doesn't slow requests

### Tools:
- Chrome DevTools Performance tab
- Vue DevTools for component performance
- Network tab for API monitoring
- Memory tab for leak detection

---

**Next Steps**: Start with Phase 1 (Critical Fixes) as these provide the highest impact with lowest risk. Each phase can be implemented and tested independently.
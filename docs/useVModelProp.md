# Vue 2.7 -> Vue 3 : v-model compatibility

Reference : https://v3-migration.vuejs.org/breaking-changes/v-model.html

Vue 3 introduced a new way to use v-model directives.

In 2.x, using a v-model on a component was an equivalent of passing a value prop and emitting an input event.

In 3.x v-model on the custom component is an equivalent of passing a modelValue prop and emitting an update:modelValue event.

A composable method has been developed to automatically detect which version of Vue is used to set the right prop name.

The goal is, when the migration to Vue 3 is done, all v-model work as expected. 

## Composable src/composables/useVModelProp.ts

```typescript
import Vue from 'vue'

/**
 * This composable is used to determine the correct v-model prop name in Vue 2 and Vue 3.
 * @returns The correct prop name: "modelValue" for Vue 3, "value" for Vue 2.
 */
export function useVModelProp() {
  if (!Vue || !Vue.version) {
    console.error('Vue is not defined or version is missing')
    return 'value' //Fallback to value
  }

  const isVue3 = Vue.version.startsWith('3')
  return isVue3 ? 'modelValue' : 'value'
}
```

## Usage from components

Usage is pretty straightforward and works similarly in Options and Composition API. 

Simply import the composable in your component, and use it in place of the prop name. Then, set a computed for the dynamic prop name.

Example in Options API :

```vue
import { useVModelProp } from '@/composables/useVModelProp'


export default {
  props: {
    [useVModelProp()]: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    vModelProp() {
      return useVModelProp()
    },
  },
}
```

Example in Composition API

```vue
const props = defineProps({
  [useVModelProp()]: {
    type: Date,
    default: () => new Date(),
  },
})

const vModelProp = computed(() => useVModelProp())
```

### Hack : watch dynamic prop

Vue doesn't support dynamic property keys in watch. To work around this limitation, we need to set the watcher in onMounted hook (composition API) or mounted hook (options API) to watch the computed.

Example in options API

```Vue
mounted() {
    //Watch vModelProp
    //hack : set watch onMounted because watch does not work on dynamic props names
    this.$watch(
      () => this[this.vModelProp],
      (newValue) => {
        console.log('has changed : ', newValue)
        if (JSON.stringify(newValue) !== JSON.stringify(this.selectedValue)) {
          this.selectedValue = newValue
        }
      },
      {
        deep: true,
        immediate: true,
      }
    )
  },
```

Example in composition API

```Vue
onMounted(() => {
  watch(
    () => props[vModelProp.value], // Watch the dynamic prop
    (newValue) => {
      if (newValue !== selectedValue.value) {
        console.log('Prop changed:', newValue)
        selectedValue.value = newValue
      }
    },
    {
      deep: true,
      immediate: true,
    }
  )
})
```
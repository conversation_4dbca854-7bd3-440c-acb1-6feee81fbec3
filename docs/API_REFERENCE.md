# API Reference

This document provides a reference for the API endpoints used by the Geored v3 application.

## History API

This API is used to retrieve historical data for vehicles, including positions, trips, and sensor data. The client-side logic is located in `src/api/historyApi.js`.

---

### 1. Get Vehicle Positions (v3)

- **Function**: `getVehiclePositionsV3(vehicleId, date, options = {})`
- **Description**: Fetches detailed sensor and position data for a specific vehicle and date range. This is the preferred modern endpoint.
- **Method**: `GET`
- **Endpoint**: `/positions/geored/get_sensors` (from `APIUrls.APIV3_GET_SENSORS`)
- **Parameters**:
    - `vehicleId` (required): The ID of the vehicle.
    - `startDatetime` (required): The start of the date range.
    - `endDatetime` (required): The end of the date range.
    - `positions`: A boolean to indicate if positions should be included.

---

### 2. Get Vehicle History Information (v2)

- **Function**: `getVehicleHistoryInfosFromAPIV2(vehicleId, dateOrDates, options = {})`
- **Description**: Retrieves a summary of a vehicle's history, including average speed and distance. This is used by the Diagnostics module for the analysis overview.
- **Method**: `GET`
- **Endpoint**: `/v2/historique/by_vehicule_dates` (from `APIUrls.APIV2_HISTORIQUE_BY_VEHICLE_DATES`)
- **Parameters**:
    - `vehicule_id` (required): The ID of the vehicle.
    - `date_debut` (required): The start date.
    - `date_fin` (required): The end date.

---

### 3. Get Trip History

- **Function**: `getTripHistoryFromVehicleId(vehicleId, date = new Date())`
- **Description**: Fetches the history of trips for a vehicle on a specific date. Used by the Location module. This function uses a generic search service, so a direct endpoint is not specified in `historyApi.js`.
- **Method**: `GET`
- **Endpoint**: Not directly specified. Uses a generic search service.
- **Parameters**:
    - `vehicleId` (required): The ID of the vehicle.
    - `date` (required): The date for which to fetch the trips.

---

### 4. Get Vehicle Positions (v2)

- **Function**: `getVehiclePositionsV2(vehicleId, date, options = {})`
- **Description**: A legacy endpoint to retrieve vehicle positions and sensor configurations. Used by the Location module's trip history table.
- **Method**: `POST`
- **Endpoint**: `/v2/historique/positions_capteurs` (from `APIUrls.APIV2_POSITIONS_CAPTEURS`)
- **Parameters**:
    - `vehicule_id` (required): The ID of the vehicle.
    - `dateheure_debut` (required): The start datetime.
    - `dateheure_fin` (required): The end datetime.
    - `positions`: A boolean to indicate if positions should be included.

---

## Dashboard API

This API provides data for the various widgets on the dashboard. The client-side logic is located in `src/api/dashboardApi.js`.

### 1. Get Real-time Top Anomalies

- **Function**: `getRealtimeTopAnomaliesDashboardData()`
- **Description**: Fetches a count of the top anomalies for the dashboard.
- **Method**: `GET`
- **Endpoint**: `/v2/evenements_client/indicateurs.json` (from `APIUrls.APIV2_EVENEMENTS_CLIENT_INDICATEURS`)
- **Parameters**:
    - `dates` (required): The date for which to fetch the anomalies.
    - `vehicule_categorie_id` (required): A comma-separated list of vehicle category IDs.

---

### 2. Get Real-time Dashboard Data

- **Function**: `getRealtimeDashboardDataByVehicle()`
- **Description**: A versatile endpoint that retrieves data for multiple dashboard widgets, including active vehicles, circuit status, and vehicle status.
- **Method**: `GET`
- **Endpoint**: `/v2/temps_reel/indicateurs/by_vehicule.json` (from `APIUrls.APIV2_TEMP_REEL_INDICATEUR_BY_VEHICLE`)

# API

## API usage

```js
{
  mounted(){
    let events = (await this.$api.v2.get('/v2/evenements')).data
  }
}
```

## API Rights

### How it works

We retrieve, compute a list rights codes, and store them in a Vue store (Vuex). We can later check that codes from inside components.

- Reference: auth store

### How to check rights?

Simply check if the user has the required right code.

- Reference: auth store state (user_rights)
- Reference: Sidebar.vue (Reference)

Example:

```js
{
  computed:{
    canViewMessagesTab(){
      return this.$store.state.auth.user_rights.includes('API_GEORED_MESSAGE_TEXTE_RECUPERATION')
    }
  }
}
```

## APIs in use

The APIs shown below are displayed in the following format:

```js
- RELATIVE_URL
 - COMPUTED RIGHT CODE (FRONT-END)
```

### Geored API V2

- /public/authentification
- /public/check_token
- /v2/evenements
- /v2/temps_reel/by_circuit
- /v2/temps_reel/by_chauffeur
- /v2/temps_reel/by_vehicule
- /v2/historique/circuit_troncons
- /v2/circuit
- /v2/historique/positions
- /v2/historique/by_circuit_dates
- /v2/historique/trajets
- /v2/historique/by_chauffeur_dates
- /v2/historique/by_vehicule_dates
- /v2/chauffeur/categorie
- /v2/circuit/categorie
- /v2/circuits
- /v2/chauffeur
- /v2/mob_vehicules
- /v2/mob_categoriesvehicules
- /v2/utilisateur/rights

#### Messages

- /v2/message/get_by_vehicule
  - API_GEORED_MESSAGE_TEXTE_RECUPERATION
- /v2/message/send_to_vehicule
  /v2/message_predefini/get

### Geored API V3

...

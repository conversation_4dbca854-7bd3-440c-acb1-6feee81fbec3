# usePdfExport composable

Utility composable to generate styled PDF tables using jspdf and jspdf-autotable. Supports custom fonts, themes, document metadata, and optional title headers.

## Features

- Customizable document layout (A4, landscape, portrait, etc.)

- Optional timestamp-based filename

- Custom fonts (add/register and use)

- Table styling support (headers, body, margin, column styles, etc.)

- Optional title display centered at the top of the PDF

## Parameters

- columnsNames - Header labels for the table
- filenamePrefix - Prefix for the exported filename
- options - Config for document, font, and table styling
- body - 2D array containing table data
- shouldAppendDateToFilename - If true, appends formatted timestamp to the filename (default: true)
- shouldDisplayTitle	If true, displays the filename as the title in the document (default: true)

## Custom types

```typescript
interface GeneratePdfOptions {
  docOptions?: DocOptions
  docStyle?: DocStyle
  tableStyles?: TableStyles
}

type DocOptions = {
  orientation?: 'p' | 'portrait' | 'l' | 'landscape'
  unit?: 'pt' | 'px' | 'in' | 'mm' | 'cm' | 'ex' | 'em' | 'pc'
  format?: string | number[]
  compressPdf?: boolean
}

type DocStyle = {
  fonts?: FontStyle[]
  fontId?: string
  fontStyle?: string
  fontSize?: number
  textColor?: string | [number, number, number]
}

type FontStyle = {
  path: string
  id: string
  fontStyle: string
}

type TableStyles = {
  theme?: 'striped' | 'grid' | 'plain'
  startY?: number
  headStyles?: Record<string, any>
  bodyStyles?: Record<string, any>
  styles?: Record<string, any>
  columnStyles?: Record<string, any>
  margin?: { top: number; right: number; bottom: number; left: number }
  tableWidth?: 'auto' | 'wrap' | '100%'
}
```

GeneratePdfOptions is used as type for options parameter.

## Usage from components

To use the composable, you need to import it and call it in your component :

```vue
import { usePdfExport } from '@/composables/usePdfExport'

const { generatePdf } = usePdfExport()

const columns = ['Column 1', 'Column 2', 'Column 3']

const filenamePrefix = 'example_export'

const docOptions = {
orientation: 'l',
format: 'a4',
}
const docStyle = {
fontSize: 12,
}
const tableStyles = {
theme: 'striped',
startY: 10,
headStyles: {
fontSize: 7,
fillColor: '#0A71AE',
},
bodyStyles: {
fontSize: 7,
},
columnStyles: {},
margin: { top: 10, right: 10, bottom: 10, left: 10 },
}

const exportPdfOptions = {
docOptions,
docStyle,
tableStyles,
},

const columns = ["Date", "Heure", "Véhicule"]

const data = [
    {
        "col_1": "18-04-2025",
        "col_2": "04:06:49",
        "col_3": "BOM06",
    },
    {
        "col_1": "18-04-2025",
        "col_2": "04:50:39",
        "col_3": "BOM06",
    },
]

const getExportPdfTableBody = (data, columns) => {
    return data.map((item) => {
        return columns.map((col) => item[col])
    })
}

const exportPdf = async () => {
  generatePdf(columns, filenamePrefix, exportPdfOptions, getExportPdfTableBody(data, columns), true, true)
}
```

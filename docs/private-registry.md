# NPM Private registry

We might use some private npm packages. Private packages could come from a package registry in GeoredV3 project (Gitlab) or any other project under the same scope (@RSoft)

## Setup

If you have problems installing private packages, configure the private registry first.

npm config set @simpliciti:registry https://git.geored.fr/api/v4/packages/npm/
npm config set -- '//git.geored.fr/api/v4/packages/npm/:_authToken' "{ACCESS_API_KEY}"

npm config set @simpliciti:registry https://git.geored.fr/api/v4/projects/89/packages/npm/
npm config set -- '//git.geored.fr/api/v4/projects/89/packages/npm/:_authToken' "{ACCESS_API_KEY}"

Note: An existing configuration for the entire Gitlab instance (git.geored.fr) is already present in .npmrc, but you should override that config with the commands above in order to use an Access Token coming from your personal account.

## Continuous Integration

The Access token set in .npmrc will need to be adapted to be taken from an environmental variable, instead.
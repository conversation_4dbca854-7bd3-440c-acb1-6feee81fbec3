# Project Structure

This document outlines the directory structure of the Geored v3 project.

- **`__mocks__`**: Contains mock files for testing purposes.
- **`dist`**: This directory contains the compiled, minified, and optimized files for production.
- **`docs`**: Contains project documentation.
- **`e2e`**: End-to-end test files.
- **`node_modules`**: Contains all the project's dependencies.
- **`public`**: This directory contains static assets that are publicly accessible.
- **`scripts`**: A place to store shell scripts related to the project (e.g., deployment scripts, build scripts).
- **`src`**: This is where the application's source code resides.
  - **`api`**: Contains all the logic for making API calls to the backend.
  - **`assets`**: Static assets like images, fonts, and icons.
  - **`components`**: Reusable Vue components.
  - **`composables`**: Vue 3 composition functions for reusable logic.
  - **`config`**: Application configuration files.
  - **`directives`**: Custom Vue directives.
  - **`filters`**: Custom Vue filters.
  - **`i18n`**: Internationalization and localization files.
  - **`libs`**: Third-party libraries or internal libraries.
  - **`mixins`**: Reusable Vue mixins.
  - **`plugins`**: Vue plugins.
  - **`router`**: Application routing configuration.
  - **`services`**: Contains the core business logic of the application, separated into different domains. Key services include `auth-service.js` for handling user authentication, `vehicle-service.js` for managing vehicle data, `diagnostics-service.js` for handling vehicle diagnostics, and `geocoding-service.js` for location-based services.
  - **`store`**: Manages the application's state using Vuex. The store is highly modular, with each module corresponding to a specific feature or domain. For example, `auth` for authentication state, `diagnostics-store.js` for diagnostics data, and `events` for event-related state.
  - **`styles`**: Global styles, variables, and mixins.
  - **`utils`**: Utility functions.
  - **`views`**: Application pages/views.
- **`tests`**: Contains all the unit and integration tests for the project.

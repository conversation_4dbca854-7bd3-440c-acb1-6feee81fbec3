# Performance Guidelines for Vue2, Vuex, Leaflet & ECharts

This document provides quick wins (QW) and best practices for optimizing performance in our Vue 2.7 application using Vuex, Leaflet, and ECharts.

## 🚀 Quick Wins (Immediate Impact)

### Vue 2 Quick Wins

#### QW1: Use Object.freeze() for Large Immutable Data
```javascript
// ✅ Good - Prevents deep reactivity overhead
state.positions = Object.freeze(normalizedPositions)
state.analysisResult = Object.freeze(value)

// ❌ Bad - Creates deep reactive watchers
state.positions = normalizedPositions
```

#### QW2: Implement Manual Chart Updates
```javascript
// ✅ Good - Manual control over expensive operations
<VChart :manual-update="true" ref="vchart" />

// In component:
this.updateChartInstanceOptions()
this.setOption(this.chartInstanceOptions)
```

#### QW3: Use queueOperationOnce for Debouncing
```javascript
// ✅ Good - Prevents excessive re-renders
import { queueOperationOnce } from '@/utils/promise.js'

queueOperationOnce(
  'unique-operation-key',
  () => {
    this.expensiveOperation()
  },
  {
    timeout: 300,
    clearPreviousTimeout: true
  }
)
```

#### QW4: Optimize Watchers
```javascript
// ✅ Good - Shallow watching with immediate handler
watch: {
  filteredDataItems: {
    handler(newItems) {
      this.updateChart()
    },
    // deep: true, // Avoid unless absolutely necessary
  }
}

// ✅ Good - Use computed properties instead of watchers when possible
computed: {
  filteredDataItems() {
    return this.$store.getters['diagnostics/filterChartDataItemsUsingSliderDataset'](this.dataItems)
  }
}
```

### Vuex Quick Wins

#### QW5: Freeze Large State Objects
```javascript
// ✅ Good - In mutations/actions
state.positions = Object.freeze(getNormalizedPositionsForChart(positions))
state.chartsData = Object.freeze(chartData)
```

#### QW6: Use Getters for Computed State
```javascript
// ✅ Good - Cached and reactive
getters: {
  filteredPositions(state) {
    return state.positions.filter(pos => pos.hasContactOn)
  }
}

// ❌ Bad - Direct state access in components
computed: {
  filteredPositions() {
    return this.$store.state.positions.filter(pos => pos.hasContactOn)
  }
}
```

#### QW7: Batch State Updates
```javascript
// ✅ Good - Single mutation for multiple updates
mutations: {
  SET_MULTIPLE_DATASETS(state, datasets) {
    Object.assign(state, datasets)
  }
}

// ❌ Bad - Multiple mutations
commit('SET_POSITIONS', positions)
commit('SET_CHARTS_DATA', chartsData)
commit('SET_LOADING', false)
```

### Leaflet Quick Wins

#### QW8: Use Canvas Renderer for Large Datasets
```javascript
// ✅ Good - Better performance for many objects
const map = L.map('mapid', { 
  preferCanvas: true,
  renderer: L.canvas()
})
```

#### QW9: Implement Marker Clustering
```javascript
// ✅ Good - Reduces DOM elements
import 'leaflet.markercluster'

const markers = L.markerClusterGroup({
  chunkedLoading: true,
  chunkInterval: 200
})
```

#### QW10: Batch Map Operations
```javascript
// ✅ Good - Group operations to minimize redraws
map.off('moveend') // Temporarily disable events
markers.forEach(marker => map.addLayer(marker))
map.on('moveend', this.onMoveEnd) // Re-enable events
```

### ECharts Quick Wins

#### QW11: Disable Animations for Large Datasets
```javascript
// ✅ Good - Significant performance boost
chartOptions = {
  animation: false,
  animationThreshold: 1000 // Disable animation above 1000 data points
}
```

#### QW12: Use Lazy Updates
```javascript
// ✅ Good - Batch chart updates
chart.setOption(newOption, { 
  lazyUpdate: true,
  silent: true // Suppress events during update
})
```

#### QW13: Optimize Chart Data Structure
```javascript
// ✅ Good - Use TypedArray for large datasets
const data = new Float32Array(largeDataset)

// ✅ Good - Pre-process data format
const chartData = positions.map(pos => [pos.timestamp, pos.value])
```

## 📊 Advanced Performance Patterns

### Memory Management

#### Pattern 1: Component Cleanup
```javascript
// ✅ Good - Proper cleanup in destroyed hook
destroyed() {
  this.$mitt.off('resizeable_stop', this.onResizeableStop)
  window.removeEventListener('resize', this.onResizeableStop)
  
  // Clear chart instance
  if (this.chartInstance) {
    this.chartInstance.dispose()
  }
}
```

#### Pattern 2: Event Listener Management
```javascript
// ✅ Good - Use passive listeners for scroll events
window.addEventListener('scroll', this.onScroll, { passive: true })

// ✅ Good - Throttle expensive event handlers
import { throttle } from '@/utils/async.js'

mounted() {
  this.throttledResize = throttle(this.onResize, 100)
  window.addEventListener('resize', this.throttledResize)
}
```

### Data Processing Optimization

#### Pattern 3: Batch Processing
```javascript
// ✅ Good - Process data in chunks
import { chunkArray } from '@/utils/array.js'

processLargeDataset(data) {
  const chunks = chunkArray(data, 1000)
  
  chunks.forEach((chunk, index) => {
    setTimeout(() => {
      this.processChunk(chunk)
    }, index * 10) // Spread processing over time
  })
}
```

#### Pattern 4: Memoization
```javascript
// ✅ Good - Cache expensive computations
const memoizedNormalize = (() => {
  const cache = new Map()
  return (data, key) => {
    if (cache.has(key)) {
      return cache.get(key)
    }
    const result = expensiveNormalization(data)
    cache.set(key, result)
    return result
  }
})()
```

### Rendering Optimization

#### Pattern 5: Virtual Scrolling for Large Lists
```javascript
// ✅ Good - Only render visible items
<template>
  <div class="virtual-list" @scroll="onScroll">
    <div :style="{ height: totalHeight + 'px' }">
      <div 
        v-for="item in visibleItems" 
        :key="item.id"
        :style="{ transform: `translateY(${item.offset}px)` }"
      >
        {{ item.data }}
      </div>
    </div>
  </div>
</template>
```

#### Pattern 6: Conditional Rendering
```javascript
// ✅ Good - Use v-show for frequently toggled elements
<div v-show="isVisible">Expensive component</div>

// ✅ Good - Use v-if for rarely shown elements
<ExpensiveComponent v-if="shouldRender" />
```

## 🔧 Development Tools & Monitoring

### Performance Monitoring

#### Tool 1: Custom Performance Tracker
```javascript
// ✅ Good - Track performance in development
const track = console.trackTime('expensive-operation', [8, 'performance'])
// ... expensive operation
track.stop()
```

#### Tool 2: Memory Usage Monitoring
```javascript
// ✅ Good - Monitor memory in development
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    console.log('Memory:', performance.memory)
  }, 5000)
}
```

### Bundle Optimization

#### Strategy 1: Lazy Loading
```javascript
// ✅ Good - Lazy load heavy components
const DiagnosticsModule = () => import('@/views/DiagnosticsModule.vue')

// ✅ Good - Lazy load chart libraries
const loadECharts = () => import('echarts/core')
```

#### Strategy 2: Tree Shaking
```javascript
// ✅ Good - Import only what you need
import { LineChart, ScatterChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'

// ❌ Bad - Imports entire library
import * as echarts from 'echarts'
```

## 📋 Performance Checklist

### Before Code Review
- [ ] Large arrays/objects are frozen with `Object.freeze()`
- [ ] Expensive operations use `queueOperationOnce` for debouncing
- [ ] Chart components use `:manual-update="true"`
- [ ] Event listeners are properly cleaned up in `destroyed()`
- [ ] Watchers are shallow unless deep watching is required
- [ ] Computed properties are used instead of methods for derived data

### For Large Datasets (>1000 items)
- [ ] ECharts animations are disabled
- [ ] Leaflet uses Canvas renderer
- [ ] Marker clustering is implemented
- [ ] Data is processed in chunks
- [ ] Virtual scrolling is used for lists

### For Real-time Updates
- [ ] Updates are throttled/debounced
- [ ] WebSocket connections are properly managed
- [ ] State updates are batched
- [ ] Unnecessary re-renders are prevented

## 🚨 Performance Anti-patterns to Avoid

### ❌ Don't Do This

```javascript
// ❌ Deep watching large objects
watch: {
  largeObject: {
    handler() { /* ... */ },
    deep: true // Expensive!
  }
}

// ❌ Creating functions in templates
<div v-for="item in items" @click="() => handleClick(item)">

// ❌ Accessing store state directly in loops
computed: {
  processedItems() {
    return this.items.map(item => {
      return this.$store.state.someExpensiveComputation(item) // Expensive!
    })
  }
}

// ❌ Not cleaning up chart instances
destroyed() {
  // Missing: this.chartInstance.dispose()
}
```

### ✅ Do This Instead

```javascript
// ✅ Shallow watching with specific properties
watch: {
  'largeObject.specificProperty'() { /* ... */ }
}

// ✅ Pre-bound methods
methods: {
  handleClick(item) { /* ... */ }
}

// ✅ Memoized getters
getters: {
  processedItems: (state) => {
    return memoize(state.items, (items) => {
      return items.map(item => expensiveComputation(item))
    })
  }
}

// ✅ Proper cleanup
destroyed() {
  if (this.chartInstance) {
    this.chartInstance.dispose()
    this.chartInstance = null
  }
}
```

## 📚 Additional Resources

- [Vue 2 Performance Guide](https://vuejs.org/v2/guide/optimizations.html)
- [Vuex Best Practices](https://vuex.vuejs.org/guide/)
- [Leaflet Performance Tips](https://leafletjs.com/examples/)
- [ECharts Performance Optimization](https://echarts.apache.org/en/tutorial.html#Performance)
- [Project-specific utils](/src/utils/) - Custom performance utilities

---

**Remember**: Profile first, optimize second. Use browser dev tools to identify actual bottlenecks before applying optimizations.
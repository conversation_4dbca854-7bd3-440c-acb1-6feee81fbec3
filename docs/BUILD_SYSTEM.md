# Build System and Dependencies

This document provides an overview of the build system, dependencies, and scripts used in the Geored v3 project.

## Build Tool

The project uses **Vite** as its primary build tool for development, and production builds. The relevant scripts in `package.json` are:

- `npm run serve`: Starts the development server with hot-reloading.
- `npm run build`: Creates a production-ready build of the application.
- `npm run dev`: An alias for running the Vite development server.

## Core Dependencies

The application is built on the following core technologies:

- **Vue.js**: The project uses Vue 2 (`^2.7.13`).
- **Vuex**: For state management (`^3.1.2`).
- **Vue Router**: For client-side routing (`^3.1.5`).

## Key Libraries and Frameworks

- **UI Components**: The UI is built using **BootstrapVue**.
- **Charting**: Data visualizations and charts are rendered using **ECharts**.
- **Mapping**: The application uses **Leaflet** for its mapping features, along with several plugins for additional functionality (e.g., `leaflet.markercluster`).
- **API Communication**: **Axios** is used for making HTTP requests to the backend.

## Testing

The project has a comprehensive testing setup:

- **Unit Testing**: **Jest** is used for running unit tests. You can run them with `npm run test:unit`.
- **End-to-End (E2E) Testing**: The E2E tests are written using **CodeceptJS** and **Playwright**. They can be run with `npm run test:e2e`.

## Code Quality and Formatting

To ensure code quality and a consistent style, the project uses:

- **ESLint**: For identifying and fixing problems in the JavaScript code.
- **Prettier**: For automatically formatting the code.
- **Husky and lint-staged**: These are used to run linting and formatting checks automatically before commits.

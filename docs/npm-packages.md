# Packages

The goal is to have as few npm packages as possible. Remove unused often.

- ajv: A JavaScript library for validating JSON objects
- animejs: A lightweight JavaScript animation library
- autoprefixer: A tool for parsing CSS and adding vendor prefixes to CSS rules
- axios: A promise-based HTTP client **Core**
- babel-eslint: A parser that enables ESLint to understand the latest JavaScript syntax
- babel-plugin-module-resolver: A plugin for Babel to enable custom module resolution
- codeceptjs: An end-to-end testing framework for node.js
- concurrently: A tool to run multiple commands concurrently
- dotenv: A zero-dependency module that loads environment variables from a .env file
- draggabilly: A library for making elements draggable **Dashboard module**
- echarts: A powerful charting library **Diagnostics module**
- eslint: A linter for JavaScript code **Core**
- eslint-config-prettier: A configuration that combines eslint with prettier **Core**
- eslint-plugin-json: An ESLint plugin that lints JSON **Core**
- eslint-plugin-prettier: An ESLint plugin that formats code using prettier **Core**
- eslint-plugin-vue: An ESLint plugin for Vue.js **Core**
- faker: A library for generating fake data
- flush-promises: A library for flushing pending promises
- hex-color-opacity: A library for generating hex color codes with opacity
- hex-rgb: A library for converting hex to RGB and vice versa
- husky: A library for running githooks **Core**
- jest: A JavaScript testing framework
- jquery: A JavaScript library
- jspdf: A library for creating PDF files (Required by Ecoconduite, remove when possible)
- jspdf-autotable: A library for creating tables in PDF files (Required by Ecoconduite, remove when possible)
- jszip: A library for creating and reading zip files
- jwt-decode: A library for decoding JWT tokens
- lint-staged: A library for running code linters on git staged files **Core**
- localforage: A library for storing data in the browser **Core**
- md5: A library for generating MD5 hashes
- mitt: A tiny (2kB) event emitter
- moment: A library for working with dates and times **Core**
- moment-timezone: An extension to moment for working with timezones **Core**
- npm: A package manager for JavaScript
- nprogress: A library for creating slim progress bars
- object-hash: A library for generating hashes of JavaScript objects
- packery: A library for creating draggable layouts
- playwright: A library for automating browsers
- popper.js: A library for positioning elements on a page
- portal-vue: A library for creating portals in Vue.js
- prettier: A code formatting tool **Core**
- promise-sequential: A library for running promises sequentially
- pug: A template engine for HTML **Core**
- pug-plain-loader: Webpack loader for pug **Core**
- qs: A library for parsing and stringifying query strings
- query-string: A library for parsing and stringifying query strings
- ramda: A library for functional programming in JavaScript
- sander: A library for working with the file system
- sass: A library for writing CSS
- sass-loader: A webpack loader for loading Sass files
- screen-size-detector: A library for detecting the size of the screen
- serve: A library for serving static files
- shortid: A library for generating unique ids
- spin.js: A library for creating animated loading indicators
- style-loader: A webpack loader for loading CSS files
- ts-jest: A library for testing TypeScript code with Jest
- typescript: A typed superset of JavaScript
- unplugin-auto-import: A library for auto-importing modules
- v-click-outside: A directive for detecting clicks outside of an element
- vue: A JavaScript library for building user interfaces
- vue-cli-plugin-bootstrap-vue: A plugin for Bootstrap Vue
- vue-cookie: A library for working with cookies **Used by i18n, should be replaced with vue-cookies, which support Vue3**
- vue-css-donut-chart: A library for creating animated donut charts
- vue-daterangepicker-component: A library for creating date range pickers
- vue-echarts: A library for creating interactive charts with ECharts
- vue-flatpickr-component: A library for creating date and time pickers
- vue-i18n: A library for internationalization
- vue-infinite-loading: A library for creating infinite loading lists
- vue-json-excel: A library for creating and downloading JSON files
- vue-material-design-icons: A library for working with material design icons
- vue-meta: A library for managing meta tags
- vue-multiselect: A library for creating multi-select fields
- vue-rangedate-picker: A library for creating date range pickers
- vue-router: A library for creating single-page applications
- vue-scrollbar-directive: A directive for creating scroll bars
- vue-select: A library for creating select fields
- vue-simple-svg: A library for creating and animating SVG elements
- vue-slider-component: A library for creating sliders
- vue-template-compiler: A library for compiling Vue templates
- vue-tinybox: A library for creating modal windows **Used by Event photos** **anomalies**
- vue2-datepicker: A library for creating date pickers
- vue2-leaflet: A library for creating interactive maps with Leaflet **Core**
- vue2-leaflet-markercluster: A library for creating marker clusters with Leaflet **Core**
- vue2-teleport: A library for teleporting elements between different parts of the page **Core**
- vuelidate: A library for validating Vue.js models
- identity-obj-proxy **used by jest to prevent exceptions**

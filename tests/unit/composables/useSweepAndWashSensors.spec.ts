import { ref, computed } from 'vue'
import i18n from '@/i18n'
import { getWashAndSweepRows } from '@/composables/useSweepAndWashSensors'

jest.mock('@/i18n', () => ({
  t: (key: string) => key,
}))

describe('getWashAndSweepRows', () => {
  it('returns expected structure and respects visibility flags', () => {
    const isWasher = ref(true)
    const isSweeper = ref(false)
    const isSinglePosition = ref(true)

    const rows = getWashAndSweepRows({
      isWasher: computed(() => isWasher.value),
      isSweeper: computed(() => isSweeper.value),
      isSinglePosition: computed(() => isSinglePosition.value),
    })

    // Should return an array with length > 0
    expect(Array.isArray(rows)).toBe(true)
    expect(rows.length).toBeGreaterThan(0)

    // Check that first row has visible function that returns true (because isWasher is true)
    expect(rows[0].visible?.()).toBe(true)

    // Check the first twoColumns item visible function returns true because isSinglePosition is true
    expect(rows[0].twoColumns[0].visible?.()).toBe(true)

    // Test when flags change
    // Change flags
    isWasher.value = false
    isSweeper.value = true
    isSinglePosition.value = false

    // Create new rows with updated refs
    const rows2 = getWashAndSweepRows({
      isWasher: computed(() => isWasher.value),
      isSweeper: computed(() => isSweeper.value),
      isSinglePosition: computed(() => isSinglePosition.value),
    })

    // Now first row visible should be false (isWasher is false)
    expect(rows2[0].visible?.()).toBe(false)

    // Some rows depend on isSweeper.value = true, so visible should be true there
    const sweepSection = rows2.find((r) =>
      r.twoColumns.some((c) => c.name.includes('sweepBrushSp'))
    )
    // Find the item itself
    const sweepBrushSpItem = sweepSection?.twoColumns.find(
      (c) => c.name === 'sweepBrushSp'
    )

    // Now check visibility on the item
    expect(sweepBrushSpItem?.visible?.()).toBe(true)
  })

  it('labels are translated strings (mocked)', () => {
    const isWasher = ref(true)
    const isSweeper = ref(true)
    const isSinglePosition = ref(true)

    const rows = getWashAndSweepRows({
      isWasher: computed(() => isWasher.value),
      isSweeper: computed(() => isSweeper.value),
      isSinglePosition: computed(() => isSinglePosition.value),
    })

    // Check some labels are strings matching keys (due to mock)
    expect(rows[0].twoColumns[0].label).toBe(
      'diagnostics.chart.category.washPressure'
    )
    expect(rows[rows.length - 1].twoColumns[0].label).toContain(
      'diagnostics.chart.category.'
    )
  })
})

import * as mapUtils from '@/utils/map'
import DrawSinglePolylinesMixin, {
  createDrawPolylinesWatch,
} from '@c/shared/SimplicitiMap/draw-single-polylines-mixin'
import { shallowMount } from '@vue/test-utils'

jest.mock('@/utils/map', () => ({
  ...jest.requireActual('@/utils/map'),
  getArrowsSegmentsFromPolylines: jest.fn(),
}))

describe('draw-single-polylines-mixin', () => {
  describe('createDrawPolylinesWatch', () => {
    let context

    beforeEach(() => {
      context = {
        isLocationMainSearch: false,
        isEventsMap: false,
        isAlertsMap: false,
        isCircuitMap: false,
        $store: {
          state: {
            location_module: {
              hideTripHistoryPolylinesIfCircuitTab: false,
            },
          },
        },
        singleTripHistoryPolylines: [
          {
            polyline: [
              [48.0, 2.0],
              [48.1, 2.1],
            ],
            color: 'blue',
          },
        ],
        tripHistoryPolylinesBoundsCount: 0,
        waitForMapRef: jest.fn((cb) => {
          return Promise.resolve().then(() => cb())
        }),
        drawPolylines: jest.fn(() => Promise.resolve()),
      }

      // Mock the utility function return value
      mapUtils.getArrowsSegmentsFromPolylines.mockReturnValue([
        {
          polyline: [
            { lat: 48.0, lng: 2.0 },
            { lat: 48.05, lng: 2.05 },
          ],
          color: 'blue',
        },
      ])
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('calls drawPolylines twice with correct arrowhead settings', async () => {
      const watcher = createDrawPolylinesWatch('singleTripHistoryPolylines')

      // Call handler and wait for it
      await watcher.handler.call(
        context,
        context.singleTripHistoryPolylines,
        undefined
      )

      // Wait for all microtasks and promises to flush
      await Promise.resolve()

      // Now assertions
      expect(context.drawPolylines).toHaveBeenCalledTimes(2)

      const [, firstOptions] = context.drawPolylines.mock.calls[0]
      expect(firstOptions.arrowheads).toBeNull()

      const [, secondOptions] = context.drawPolylines.mock.calls[1]
      expect(secondOptions.arrowheads).toMatchObject({
        frequency: 'endonly',
        size: '12px',
        yawn: 60,
        color: 'blue',
      })

      expect(secondOptions.opacity).toBe(0)
      expect(secondOptions.weight).toBe(0)
      expect(secondOptions.layer).toBe('tripHistoryPolylines_arrows')
    })

    it('does not call drawPolylines when isLocationMainSearch is true', () => {
      context.isLocationMainSearch = true

      const watcher = createDrawPolylinesWatch('singleTripHistoryPolylines')
      watcher.handler.call(context, [], undefined)

      expect(context.drawPolylines).not.toHaveBeenCalled()
    })
  })
})

describe('Arrowhead visibility mixin methods', () => {
  const createWrapper = (zoomLevel = 15) => {
    return shallowMount({
      mixins: [DrawSinglePolylinesMixin],
      computed: {
        zoomLevel: () => zoomLevel,
        singleCircuitExecPolylines: () => [],
        singleTripHistoryPolylines: () => [],
      },
      methods: {
        waitForMapRef(callback) {
          // Just immediately invoke the callback for testing purposes
          callback()
        },
        drawPolylines: jest.fn(), // Also needed if you're calling this
        drawCircuitArrowsFromPolylines: jest.fn(),
      },
      render() {}, // No template needed
    })
  }

  it('sets arrow opacity to 1 when zoomLevel >= 15', () => {
    const wrapper = createWrapper(15)

    const fakeLayer = {
      setStyle: jest.fn(),
    }

    wrapper.vm.setArrowheadsGeometriesOpacityDependingOnZoomLevel([fakeLayer])

    expect(fakeLayer.setStyle).toHaveBeenCalledWith({
      opacity: 1,
      fillOpacity: 1,
    })
  })

  it('sets arrow opacity to 0 when zoomLevel < 15', () => {
    const wrapper = createWrapper(14)

    const fakeLayer = {
      setStyle: jest.fn(),
    }

    wrapper.vm.setArrowheadsGeometriesOpacityDependingOnZoomLevel([fakeLayer])

    expect(fakeLayer.setStyle).toHaveBeenCalledWith({
      opacity: 0,
      fillOpacity: 0,
    })
  })
})

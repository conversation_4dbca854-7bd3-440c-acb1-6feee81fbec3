const {
  normalizeString,
  getEmptyStringIfStringIncludes,
  isNumericString,
  camelToKebabString,
  normalizeValueDisplay,
} = require('@/utils/string')

describe('getEmptyStringIfStringIncludes', () => {
  it('returns the first string if the second string is not included', () => {
    expect(
      getEmptyStringIfStringIncludes('Welcome', 'i18n.core')
    ).toStrictEqual('Welcome')
  })

  it('returns the original string if it does not include the specified substring', () => {
    expect(getEmptyStringIfStringIncludes('hello world', 'foo')).toBe(
      'hello world'
    )
  })

  it('returns an empty string if the string is equal to the specified substring', () => {
    expect(getEmptyStringIfStringIncludes('world', 'world')).toBe('')
  })

  it('returns an empty string if the string includes the specified substring multiple times', () => {
    expect(getEmptyStringIfStringIncludes('hello world world', 'world')).toBe(
      ''
    )
  })
})

describe('normalizeString', () => {
  it('should normalize the string with default options', () => {
    const input = 'Absence de poids bac (nombre)'
    const expected = 'absence_de_poids_bac_nombre'
    const result = normalizeString(input, {
      replaceSpaces: true,
      removeParentheses: true,
    })
    expect(result).toBe(expected)
  })

  it('should normalize the string with custom options', () => {
    const input = 'Absence de poids bac (nombre)'
    const expected = 'PREFIX_absence_de_poids_bac_nombre'
    const options = {
      replaceSpaces: true,
      convertToLowercase: true,
      removeParentheses: true,
      removeAccents: true,
      prefix: 'PREFIX_',
    }
    const result = normalizeString(input, options)
    expect(result).toBe(expected)
  })

  it('should normalize the string without removing accents', () => {
    const input = 'ÀÁÂÄÈÉÊËÌÍÎÏÒÓÔÖÙÚÛÜàáâäèéêëìíîïòóôöùúûü'
    const expected = 'aaaaeeeeiiiioooouuuuaaaaeeeeiiiioooouuuu'
    const options = {
      removeAccents: true,
    }
    const result = normalizeString(input, options)
    expect(result).toBe(expected)
  })

  it('should normalize the string without removing accents', () => {
    const input = 'Café au Lait'
    const expected = 'café au lait'
    const options = {
      removeAccents: false,
    }
    const result = normalizeString(input, options)
    expect(result).toBe(expected)
  })

  it('should normalize the string with removing parentheses', () => {
    const input = 'String (with) parentheses'
    const expected = 'string with parentheses'
    const options = {
      removeParentheses: true,
    }
    const result = normalizeString(input, options)
    expect(result).toBe(expected)
  })

  it('should normalize the string without converting to lowercase', () => {
    const input = 'UPPERCASE STRING'
    const expected = 'UPPERCASE STRING'
    const options = {
      convertToLowercase: false,
    }
    const result = normalizeString(input, options)
    expect(result).toBe(expected)
  })
})

describe('isNumericString', () => {
  it('should return true for numeric strings', () => {
    expect(isNumericString('123456')).toBe(true)
    expect(isNumericString('0')).toBe(true)
    expect(isNumericString('9999999999')).toBe(true)
  })

  it('should return false for strings with non-numeric characters', () => {
    expect(isNumericString('123a456')).toBe(false)
    expect(isNumericString(' 123 ')).toBe(false)
    expect(isNumericString('123.456')).toBe(false)
    expect(isNumericString('12-34')).toBe(false)
  })

  it('should return false for empty strings', () => {
    expect(isNumericString('')).toBe(false)
  })

  it('should return false for non-string inputs', () => {
    expect(isNumericString(123456)).toBe(false)
    expect(isNumericString(null)).toBe(false)
    expect(isNumericString(undefined)).toBe(false)
    expect(isNumericString([])).toBe(false)
    expect(isNumericString({})).toBe(false)
  })
})

describe('camelToKebabString', () => {
  it('should convert camelCase to kebab-case', () => {
    expect(camelToKebabString('camelCase')).toBe('camel-case')
  })

  it('should handle strings with multiple uppercase letters', () => {
    expect(camelToKebabString('thisIsATest')).toBe('this-is-a-test')
  })

  it('should return the same string if no uppercase letters are present', () => {
    expect(camelToKebabString('lowercase')).toBe('lowercase')
  })

  it('should handle empty strings', () => {
    expect(camelToKebabString('')).toBe('')
  })

  it('should handle strings that start with an uppercase letter', () => {
    expect(camelToKebabString('UppercaseStart')).toBe('-uppercase-start')
  })

  it('should handle strings with consecutive uppercase letters', () => {
    expect(camelToKebabString('JSONData')).toBe('-j-s-o-n-data')
  })
})

describe('normalizeValueDisplay', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return translation when value is undefined', () => {
    expect(normalizeValueDisplay(undefined, 'kg')).toBe('n/c')
  })

  it('should return translation when value is null', () => {
    expect(normalizeValueDisplay(null, 'kg')).toBe('n/c')
  })

  it('should return value with unit when both provided', () => {
    expect(normalizeValueDisplay(10.1234, 'kg')).toBe('10.12\u00a0kg')
  })

  it('should return value with fallback unit translation when unit is null', () => {
    expect(normalizeValueDisplay(10.1234, null)).toBe('10.12\u00a0null')
  })

  it('should return value with fallback unit translation when unit is undefined', () => {
    expect(normalizeValueDisplay(10.1234, undefined)).toBe(
      '10.12\u00a0undefined'
    )
  })
})

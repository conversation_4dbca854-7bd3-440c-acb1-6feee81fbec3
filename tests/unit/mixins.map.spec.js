import { linestringsToPolylines } from '@/mixins/map.js'
import mapMixin from '@/mixins/map.js'

describe('linestringsToPolylines', () => {
  it("It should support 'linestring' as string", () => {
    expect(
      linestringsToPolylines([
        {
          linestring: '',
        },
      ]).length
    ).toStrictEqual(0)

    expect(
      linestringsToPolylines([
        {
          linestring:
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
        },
      ]).length
    ).toStrictEqual(1)

    expect(
      linestringsToPolylines([
        {
          linestring:
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
        },
        {
          linestring:
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
        },
      ]).length
    ).toStrictEqual(2)
  })

  it("It should support 'linestring' as array", () => {
    expect(
      linestringsToPolylines([
        {
          linestring: [],
        },
      ]).length
    ).toStrictEqual(0)

    expect(
      linestringsToPolylines([
        {
          linestring: [
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
          ],
        },
      ]).length
    ).toStrictEqual(1)

    expect(
      linestringsToPolylines([
        {
          linestring: [
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
          ],
        },
        {
          linestring: [
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
            '1.4659516666667 45.162342314815,1.4659516666667 45.162342314815',
          ],
        },
      ]).length
    ).toStrictEqual(4)
  })
})

describe('fitBoundsAndOpenPopup', () => {
  let mockMitt
  let mixinMethods

  beforeEach(() => {
    jest.useFakeTimers()
    // Mock mitt
    mockMitt = { emit: jest.fn() }

    // Create an object that includes the mixin's methods
    mixinMethods = {
      ...mapMixin.methods,
      $mitt: mockMitt, // Attach the mock event bus
    }

    // Spy on console.warn
    jest.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.useRealTimers()
    jest.clearAllMocks()
  })

  it('should log a warning and not emit SIMPLICITI_MAP_FIT_TO_BOUNDS when coordinates are null', async () => {
    await mixinMethods.fitBoundsAndOpenPopup(null, 'bacs_markers', 123)

    expect(console.warn).toHaveBeenCalledWith(
      'Invalid coordinates provided',
      null
    )

    expect(mockMitt.emit).not.toHaveBeenCalledWith(
      'SIMPLICITI_MAP_FIT_TO_BOUNDS',
      expect.anything()
    )

    //Reflect setTimeout of 100ms
    jest.advanceTimersByTime(100)

    expect(mockMitt.emit).toHaveBeenCalledWith(
      'SIMPLICITI_MAP_OPEN_MARKER_POPUP',
      {
        groupName: 'bacs_markers',
        layerId: 123,
      }
    )
  })

  it('should log a warning and not emit SIMPLICITI_MAP_FIT_TO_BOUNDS when coordinates are invalid', async () => {
    const invalidCoordinates = { lat: 91, lng: 181 }

    await mixinMethods.fitBoundsAndOpenPopup(
      invalidCoordinates,
      'bacs_markers',
      123
    )

    expect(console.warn).toHaveBeenCalledWith(
      'Invalid coordinates provided',
      invalidCoordinates
    )

    expect(mockMitt.emit).not.toHaveBeenCalledWith(
      'SIMPLICITI_MAP_FIT_TO_BOUNDS',
      expect.anything()
    )

    //Reflect setTimeout of 100ms
    jest.advanceTimersByTime(100)

    expect(mockMitt.emit).toHaveBeenCalledWith(
      'SIMPLICITI_MAP_OPEN_MARKER_POPUP',
      {
        groupName: 'bacs_markers',
        layerId: 123,
      }
    )
  })

  it('should log a warning and not emit SIMPLICITI_MAP_OPEN_MARKER_POPUP when groupName or layerId are missing', async () => {
    await mixinMethods.fitBoundsAndOpenPopup(
      { lat: 48.8566, lng: 2.3522 },
      null,
      123
    )

    expect(console.warn).toHaveBeenCalledWith(
      'Invalid groupName or layerId provided',
      {
        groupName: null,
        layerId: 123,
      }
    )

    expect(mockMitt.emit).toHaveBeenCalledWith('SIMPLICITI_MAP_FIT_TO_BOUNDS', [
      [48.8566, 2.3522],
    ])

    expect(mockMitt.emit).not.toHaveBeenCalledWith(
      'SIMPLICITI_MAP_OPEN_MARKER_POPUP',
      expect.anything()
    )
  })

  it('should emit events when valid inputs are provided', async () => {
    await mixinMethods.fitBoundsAndOpenPopup(
      { lat: 48.8566, lng: 2.3522 },
      'bacs_markers',
      123
    )

    expect(console.warn).not.toHaveBeenCalled()

    expect(mockMitt.emit).toHaveBeenCalledWith('SIMPLICITI_MAP_FIT_TO_BOUNDS', [
      [48.8566, 2.3522],
    ])

    //Reflect setTimeout of 100ms
    jest.advanceTimersByTime(100)

    expect(mockMitt.emit).toHaveBeenCalledWith(
      'SIMPLICITI_MAP_OPEN_MARKER_POPUP',
      {
        groupName: 'bacs_markers',
        layerId: 123,
      }
    )
  })
})

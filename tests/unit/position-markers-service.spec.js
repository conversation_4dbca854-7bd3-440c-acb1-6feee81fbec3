/**
 * Unit tests for position-markers-service.js
 * Tests the service in complete isolation with mocked dependencies
 */

import '@/main-unit.js'

// Import the service first
import positionMarkersService from '@/services/position-markers-service.js'

// Create a mock speed filter that can be modified by tests
let mockSpeedFilter = { value: null, condition: null }

// Create a dynamic mock store for testing that always returns current mockSpeedFilter
const mockStore = {
  getters: {}
}

// Use Object.defineProperty to create a proper dynamic getter
Object.defineProperty(mockStore.getters, 'map_options/speedFilter', {
  get: function() {
    return mockSpeedFilter
  },
  configurable: true,
  enumerable: true
})

describe('PositionMarkersService', () => {
  // Test data fixtures - deterministic and predictable
  const mockPositions = [
    {
      id: 'pos1',
      code: 'POS',
      dateheure: '2023-01-01T10:00:00Z',
      vitesse: 25,
      lat: 48.8566,
      lng: 2.3522
    },
    {
      id: 'pos2',
      code: 'TOR1',
      dateheure: '2023-01-01T10:05:00Z',
      vitesse: 45,
      lat: 48.8576,
      lng: 2.3532
    },
    {
      id: 'pos3',
      code: 'TOR2',
      dateheure: '2023-01-01T10:10:00Z',
      vitesse: 60,
      lat: 48.8586,
      lng: 2.3542
    },
    {
      id: 'pos4',
      code: 'TOR+',
      dateheure: '2023-01-01T10:15:00Z',
      vitesse: 35,
      lat: 48.8596,
      lng: 2.3552,
      code_capteurs: ['TOR1', 'TOR3']
    },
    {
      id: 'pos5',
      code: 'POS',
      dateheure: '2023-01-01T10:20:00Z',
      vitesse: 0,
      lat: 48.8606,
      lng: 2.3562
    }
  ]

  const mockSpeedFilters = {
    lessThan30: { condition: '<', value: 30 },
    lessThanOrEqual45: { condition: '<=', value: 45 },
    equal25: { condition: '==', value: 25 },
    greaterThanOrEqual35: { condition: '>=', value: 35 },
    greaterThan50: { condition: '>', value: 50 }
  }

  beforeEach(() => {
    // Reset service state before each test
    positionMarkersService.clear()
    
    // Reset mock speed filter before each test
    mockSpeedFilter.value = null
    mockSpeedFilter.condition = null
    
    // Override the store for testing
    positionMarkersService._setStoreForTesting(mockStore)
    
    // Disable cache logging for tests
    positionMarkersService.setCacheLogging(false)
  })

  afterEach(() => {
    // Clean up any listeners
    positionMarkersService.clear()
    
    // Reset store to original instance
    positionMarkersService._resetStore()
  })

  describe('Basic Position Management', () => {
    it('should initialize with empty positions', () => {
      expect(positionMarkersService.getPositions()).toEqual([])
      expect(positionMarkersService.getCount()).toBe(0)
    })

    it('should set and retrieve positions', () => {
      // Arrange
      const testPositions = mockPositions.slice(0, 2)

      // Act
      positionMarkersService.setPositions(testPositions)

      // Assert
      expect(positionMarkersService.getPositions()).toEqual(testPositions)
      expect(positionMarkersService.getCount()).toBe(2)
    })

    it('should handle null/undefined positions gracefully', () => {
      // Act & Assert
      positionMarkersService.setPositions(null)
      expect(positionMarkersService.getPositions()).toEqual([])
      expect(positionMarkersService.getCount()).toBe(0)

      positionMarkersService.setPositions(undefined)
      expect(positionMarkersService.getPositions()).toEqual([])
      expect(positionMarkersService.getCount()).toBe(0)
    })

    it('should clear all positions', () => {
      // Arrange
      positionMarkersService.setPositions(mockPositions)
      expect(positionMarkersService.getCount()).toBe(5)

      // Act
      positionMarkersService.clear()

      // Assert
      expect(positionMarkersService.getPositions()).toEqual([])
      expect(positionMarkersService.getCount()).toBe(0)
    })
  })

  describe('Speed Filtering', () => {
    beforeEach(() => {
      positionMarkersService.setPositions(mockPositions)
    })

    it('should return all positions when no speed filter is applied', () => {
      // Arrange
      mockSpeedFilter.value = null
      mockSpeedFilter.condition = null

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      expect(result).toEqual(mockPositions)
      expect(result.length).toBe(5)
    })

    it('should return all positions when speed filter has no value', () => {
      // Arrange
      mockSpeedFilter.condition = '<'
      mockSpeedFilter.value = null

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      expect(result).toEqual(mockPositions)
    })

    it('should filter positions with less than condition (<)', () => {
      // Arrange
      mockSpeedFilter.condition = '<'
      mockSpeedFilter.value = 30

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      const expectedPositions = mockPositions.filter(p => p.vitesse < 30)
      expect(result).toEqual(expectedPositions)
      expect(result.length).toBe(2) // pos1 (25) and pos5 (0)
    })

    it('should filter positions with less than or equal condition (<=)', () => {
      // Arrange
      mockSpeedFilter.condition = '<='
      mockSpeedFilter.value = 45

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      const expectedPositions = mockPositions.filter(p => p.vitesse <= 45)
      expect(result).toEqual(expectedPositions)
      expect(result.length).toBe(4) // All except pos3 (60)
    })

    it('should filter positions with equal condition (==)', () => {
      // Arrange
      mockSpeedFilter.condition = '=='
      mockSpeedFilter.value = 25

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      const expectedPositions = mockPositions.filter(p => p.vitesse === 25)
      expect(result).toEqual(expectedPositions)
      expect(result.length).toBe(1) // Only pos1 (25)
    })

    it('should filter positions with greater than or equal condition (>=)', () => {
      // Arrange
      mockSpeedFilter.condition = '>='
      mockSpeedFilter.value = 35

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      const expectedPositions = mockPositions.filter(p => p.vitesse >= 35)
      expect(result).toEqual(expectedPositions)
      expect(result.length).toBe(3) // pos2 (45), pos3 (60), pos4 (35)
    })

    it('should filter positions with greater than condition (>)', () => {
      // Arrange
      mockSpeedFilter.condition = '>'
      mockSpeedFilter.value = 50

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      const expectedPositions = mockPositions.filter(p => p.vitesse > 50)
      expect(result).toEqual(expectedPositions)
      expect(result.length).toBe(1) // Only pos3 (60)
    })

    it('should handle edge cases with zero and negative speeds', () => {
      // Arrange
      const edgeCasePositions = [
        { id: 'edge1', vitesse: -5, code: 'POS' },
        { id: 'edge2', vitesse: 0, code: 'POS' },
        { id: 'edge3', vitesse: 1, code: 'POS' }
      ]
      positionMarkersService.setPositions(edgeCasePositions)
      mockSpeedFilter.condition = '>'
      mockSpeedFilter.value = 0

      // Act
      const result = positionMarkersService.getFilteredPositions()

      // Assert
      expect(result.length).toBe(1) // Only edge3 (1)
      expect(result[0].id).toBe('edge3')
    })
  })

  describe('Position Code Filtering', () => {
    beforeEach(() => {
      positionMarkersService.setPositions(mockPositions)
    })

    it('should return all filtered positions when no code is specified', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode()

      // Assert
      expect(result).toEqual(mockPositions)
    })

    it('should return all filtered positions when code is null', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode(null)

      // Assert
      expect(result).toEqual(mockPositions)
    })

    it('should filter positions by POS code', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode('POS')

      // Assert
      const expectedPositions = mockPositions.filter(p => p.code === 'POS')
      expect(result).toEqual(expectedPositions)
      expect(result.length).toBe(2) // pos1 and pos5
    })

    it('should filter positions by TOR1 code', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode('TOR1')

      // Assert
      // Should include pos2 (TOR1) and pos4 (TOR+ with TOR1 sensor)
      expect(result.length).toBe(2)
      expect(result.some(p => p.id === 'pos2')).toBe(true)
      expect(result.some(p => p.id === 'pos4')).toBe(true)
    })

    it('should filter positions by TOR2 code', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode('TOR2')

      // Assert
      // Should only include pos3 (TOR2), pos4 doesn't have TOR2 sensor
      expect(result.length).toBe(1)
      expect(result[0].id).toBe('pos3')
    })

    it('should filter positions by TOR3 code', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode('TOR3')

      // Assert
      // Should only include pos4 (TOR+ with TOR3 sensor)
      expect(result.length).toBe(1)
      expect(result[0].id).toBe('pos4')
    })

    it('should filter positions by TOR+ code', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode('TOR+')

      // Assert
      // Should only include pos4 (TOR+)
      expect(result.length).toBe(1)
      expect(result[0].id).toBe('pos4')
    })

    it('should return empty array for non-existent code', () => {
      // Act
      const result = positionMarkersService.getPositionsByCode('NONEXISTENT')

      // Assert
      expect(result).toEqual([])
    })

    it('should combine speed filtering with code filtering', () => {
      // Arrange
      mockSpeedFilter.condition = '>'
      mockSpeedFilter.value = 30

      // Act
      const result = positionMarkersService.getPositionsByCode('TOR1')

      // Assert
      // Should include pos2 (TOR1, speed 45) and pos4 (TOR+, speed 35)
      expect(result.length).toBe(2)
      expect(result.every(p => p.vitesse > 30)).toBe(true)
    })
  })

  describe('Common Positions Filtering', () => {
    beforeEach(() => {
      positionMarkersService.setPositions(mockPositions)
    })

    it('should return positions without code or with POS code', () => {
      // Arrange
      const mixedPositions = [
        { id: 'common1', code: 'POS', vitesse: 25 },
        { id: 'common2', code: null, vitesse: 30 },
        { id: 'common3', vitesse: 35 }, // no code property
        { id: 'sensor1', code: 'TOR1', vitesse: 40 }
      ]
      positionMarkersService.setPositions(mixedPositions)

      // Act
      const result = positionMarkersService.getCommonPositions()

      // Assert
      expect(result.length).toBe(3) // common1, common2, common3
      expect(result.every(p => !p.code || p.code === 'POS')).toBe(true)
    })
  })

  describe('Event Listener System', () => {
    it('should add and notify listeners when positions change', () => {
      // Arrange
      const listener1 = jest.fn()
      const listener2 = jest.fn()
      positionMarkersService.addListener(listener1)
      positionMarkersService.addListener(listener2)

      // Act
      positionMarkersService.setPositions(mockPositions.slice(0, 2))

      // Assert
      expect(listener1).toHaveBeenCalledTimes(1)
      expect(listener2).toHaveBeenCalledTimes(1)
      expect(listener1).toHaveBeenCalledWith(mockPositions.slice(0, 2))
      expect(listener2).toHaveBeenCalledWith(mockPositions.slice(0, 2))
    })

    it('should remove listeners correctly', () => {
      // Arrange
      const listener1 = jest.fn()
      const listener2 = jest.fn()
      positionMarkersService.addListener(listener1)
      positionMarkersService.addListener(listener2)

      // Act
      positionMarkersService.removeListener(listener1)
      positionMarkersService.setPositions(mockPositions.slice(0, 1))

      // Assert
      expect(listener1).not.toHaveBeenCalled()
      expect(listener2).toHaveBeenCalledTimes(1)
    })

    it('should notify listeners when positions are cleared', () => {
      // Arrange
      const listener = jest.fn()
      positionMarkersService.setPositions(mockPositions)
      positionMarkersService.addListener(listener)
      listener.mockClear() // Clear previous calls

      // Act
      positionMarkersService.clear()

      // Assert
      expect(listener).toHaveBeenCalledTimes(1)
      expect(listener).toHaveBeenCalledWith([])
    })

    it('should handle listener errors gracefully', () => {
      // Arrange
      const errorListener = jest.fn(() => {
        throw new Error('Test error')
      })
      const normalListener = jest.fn()
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      positionMarkersService.addListener(errorListener)
      positionMarkersService.addListener(normalListener)

      // Act
      positionMarkersService.setPositions(mockPositions.slice(0, 1))

      // Assert
      expect(errorListener).toHaveBeenCalled()
      expect(normalListener).toHaveBeenCalled()
      expect(consoleSpy).toHaveBeenCalledWith('Error in position markers listener:', expect.any(Error))

      // Cleanup
      consoleSpy.mockRestore()
    })
  })

  describe('Cache Behavior and Memoization', () => {
    beforeEach(() => {
      positionMarkersService.setPositions(mockPositions)
    })

    it('should cache filtered positions results', () => {
      // Arrange
      Object.assign(mockSpeedFilter, mockSpeedFilters.lessThan30)

      // Act
      const result1 = positionMarkersService.getFilteredPositions()
      const result2 = positionMarkersService.getFilteredPositions()

      // Assert
      expect(result1).toBe(result2) // Same reference due to caching
      expect(result1).toEqual(result2)
    })

    it('should cache positions by code results', () => {
      // Act
      const result1 = positionMarkersService.getPositionsByCode('POS')
      const result2 = positionMarkersService.getPositionsByCode('POS')

      // Assert
      expect(result1).toBe(result2) // Same reference due to caching
      expect(result1).toEqual(result2)
    })

    it('should invalidate cache when positions change', () => {
      // Arrange
      const result1 = positionMarkersService.getFilteredPositions()

      // Act
      positionMarkersService.setPositions(mockPositions.slice(0, 2))
      const result2 = positionMarkersService.getFilteredPositions()

      // Assert
      expect(result1).not.toBe(result2) // Different references after cache invalidation
      expect(result1.length).not.toBe(result2.length)
    })

    it('should invalidate cache when speed filter changes', () => {
      // Arrange
      mockSpeedFilter.condition = '<'
      mockSpeedFilter.value = 30
      const result1 = positionMarkersService.getFilteredPositions()

      // Act
      mockSpeedFilter.condition = '>'
      mockSpeedFilter.value = 50
      const result2 = positionMarkersService.getFilteredPositions()

      // Assert
      expect(result1).not.toBe(result2) // Different references after filter change
      expect(result1.length).not.toBe(result2.length)
    })

    it('should invalidate cache when cleared', () => {
      // Arrange
      const result1 = positionMarkersService.getFilteredPositions()

      // Act
      positionMarkersService.clear()
      const result2 = positionMarkersService.getFilteredPositions()

      // Assert
      expect(result1).not.toBe(result2) // Different references after clear
      expect(result2).toEqual([])
    })

    it('should provide cache statistics', () => {
      // Arrange
      mockSpeedFilter.condition = '<'
      mockSpeedFilter.value = 30
      positionMarkersService.getFilteredPositions() // Populate cache
      positionMarkersService.getPositionsByCode('POS') // Populate cache

      // Act
      const stats = positionMarkersService.getCacheStats()

      // Assert
      expect(stats).toHaveProperty('filteredPositionsCache')
      expect(stats).toHaveProperty('positionsByCodeCache')
      expect(stats).toHaveProperty('currentSpeedFilterHash')
      expect(stats).toHaveProperty('lastSpeedFilterHash')
      expect(stats).toHaveProperty('cacheLoggingEnabled')
      expect(stats.filteredPositionsCache.size).toBeGreaterThan(0)
      expect(stats.positionsByCodeCache.size).toBeGreaterThan(0)
    })

    it('should manually invalidate caches', () => {
      // Arrange
      positionMarkersService.getFilteredPositions() // Populate cache
      let stats = positionMarkersService.getCacheStats()
      expect(stats.filteredPositionsCache.size).toBeGreaterThan(0)

      // Act
      positionMarkersService.invalidateCaches()
      stats = positionMarkersService.getCacheStats()

      // Assert
      expect(stats.filteredPositionsCache.size).toBe(0)
      expect(stats.positionsByCodeCache.size).toBe(0)
    })
  })

  describe('Cache Logging', () => {
    beforeEach(() => {
      positionMarkersService.setPositions(mockPositions)
    })

    it('should enable and disable cache logging', () => {
      // Arrange
      const consoleSpy = jest.spyOn(console, 'debug').mockImplementation()

      // Act
      positionMarkersService.setCacheLogging(true)

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('[PositionMarkersService] Cache logging enabled')

      // Cleanup
      consoleSpy.mockRestore()
    })

    it('should log cache hits and misses when enabled', () => {
      // Arrange
      const consoleSpy = jest.spyOn(console, 'debug').mockImplementation()
      positionMarkersService.setCacheLogging(true)

      // Act
      positionMarkersService.getFilteredPositions() // Cache miss
      positionMarkersService.getFilteredPositions() // Cache hit

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        '[PositionMarkersService] Cache MISS for getFilteredPositions:',
        expect.any(String)
      )
      expect(consoleSpy).toHaveBeenCalledWith(
        '[PositionMarkersService] Cache HIT for getFilteredPositions:',
        expect.any(String)
      )

      // Cleanup
      consoleSpy.mockRestore()
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle positions without required properties', () => {
      // Arrange
      const invalidPositions = [
        { id: 'invalid1' }, // Missing vitesse
        { vitesse: 25 }, // Missing id
        { id: 'valid1', vitesse: 30, code: 'POS' }
      ]
      positionMarkersService.setPositions(invalidPositions)
      Object.assign(mockSpeedFilter, mockSpeedFilters.greaterThan20)

      // Act & Assert - Should not throw errors
      expect(() => {
        const result = positionMarkersService.getFilteredPositions()
        expect(Array.isArray(result)).toBe(true)
      }).not.toThrow()
    })

    it('should handle TOR+ positions without code_capteurs property', () => {
      // Arrange
      const torPositions = [
        { id: 'tor1', code: 'TOR+', vitesse: 25 }, // Missing code_capteurs
        { id: 'tor2', code: 'TOR+', vitesse: 30, code_capteurs: null },
        { id: 'tor3', code: 'TOR+', vitesse: 35, code_capteurs: ['TOR1'] }
      ]
      positionMarkersService.setPositions(torPositions)

      // Act & Assert - Should not throw errors
      expect(() => {
        const result = positionMarkersService.getPositionsByCode('TOR1')
        expect(Array.isArray(result)).toBe(true)
        expect(result.length).toBe(1) // Only tor3
      }).not.toThrow()
    })

    it('should handle invalid speed filter conditions gracefully', () => {
      // Arrange
      positionMarkersService.setPositions(mockPositions)
      Object.assign(mockSpeedFilter, { condition: 'invalid', value: 30 })

      // Act & Assert - Should not throw errors
      expect(() => {
        const result = positionMarkersService.getFilteredPositions()
        expect(Array.isArray(result)).toBe(true)
      }).not.toThrow()
    })
  })

  describe('Performance and Determinism', () => {
    it('should produce consistent results across multiple runs', () => {
      // Arrange
      positionMarkersService.setPositions(mockPositions)
      mockSpeedFilter.condition = '<'
      mockSpeedFilter.value = 30

      // Act - Run multiple times
      const results = []
      for (let i = 0; i < 5; i++) {
        results.push(positionMarkersService.getFilteredPositions())
      }

      // Assert - All results should be identical
      results.forEach(result => {
        expect(result).toEqual(results[0])
        expect(result.length).toBe(results[0].length)
      })
    })

    it('should handle large datasets efficiently', () => {
      // Arrange
      const largeDataset = []
      for (let i = 0; i < 1000; i++) {
        largeDataset.push({
          id: `pos${i}`,
          code: i % 2 === 0 ? 'POS' : 'TOR1',
          vitesse: Math.floor(i / 10), // Predictable speeds
          dateheure: `2023-01-01T${String(i % 24).padStart(2, '0')}:00:00Z`
        })
      }

      // Act & Assert - Should complete without timeout
      positionMarkersService.setPositions(largeDataset)
      const startTime = Date.now()
      const result = positionMarkersService.getFilteredPositions()
      const endTime = Date.now()

      expect(result.length).toBe(1000)
      expect(endTime - startTime).toBeLessThan(100) // Should be fast
    })
  })
})

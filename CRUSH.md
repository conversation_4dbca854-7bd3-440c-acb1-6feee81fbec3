# CRUSH.md

This file provides guidance for agentic coding tools when working with code in this repository.

## Commands

- **Install Dependencies**: `npm custom-install`
- **Run Development Server**: `npm run dev` (or `npm run serve`)
- **Build for Production**: `npm run build`
- **Linting**: `npm run lint`
- **Code Formatting**: `npm run format`
- **Unit Tests**: `npm run test:unit`
- **E2E Tests**: `npm run test:e2e`
- **Watch Unit Tests**: `npm run test:unit:watch`
- **Run Single Unit Test**: `npm run test:unit -- testNamePattern="YourTestName"`
- **Run Single Test File**: `npm run test:unit -- testPathPattern="path/to/your/test.spec.js"`
- **Generate JSDoc Documentation**: `npm run jsdoc`

## Code Style Guidelines

### General
- Use single quotes for strings
- No semicolons at the end of statements
- 2-space indentation

### Naming Conventions
- **Components**: PascalCase (e.g., `MapIcon.vue`)
- **Assets and CSS selectors**: lowercase with underscores (e.g., `map_icon.svg`)
- **Routes**: lowercase with hyphens (e.g., `/client-details`)
- **CSS classes**: lowercase with underscores, double underscore for namespaces (e.g., `my_button__icon`)

### Imports
- Use absolute imports with aliases:
  - `@/` for `src/`
  - `@c/` for `src/components/`
  - `@desktop/` for `src/components/desktop/`
- Prefer destructuring for named exports
- Group imports in this order:
  1. External libraries
  2. Vue/Vuex/Vue Router
  3. Internal utilities
  4. Components
  5. Assets
  6. Styles

### TypeScript
- Use TypeScript for new files when possible
- Avoid `any` type when possible
- Use interfaces for object structures
- Use proper typing for Vue component props

### Error Handling
- Use try/catch for async operations
- Log errors appropriately with context
- Handle API errors gracefully

## Testing

### Patterns
- Follow the AAA pattern (Arrange-Act-Assert)
- Use descriptive test names that explain what is being tested
- Keep tests focused on a single behavior
- Mock external dependencies

### Running Tests
- Run all unit tests: `npm run test:unit`
- Run a specific test file: `npm run test:unit -- testPathPattern="path/to/test.spec.js"`
- Run tests matching a pattern: `npm run test:unit -- testNamePattern="ComponentName"`
- Watch mode: `npm run test:unit:watch`

## Build System
- Uses Vite with Vue 2
- Builds output to `dist/` directory
- Uses `vite-plugin-compression` for gzip/brotli compression
- Aliases configured in `vite.config.js`
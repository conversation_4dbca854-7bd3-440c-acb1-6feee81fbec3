{"time":"2025-08-04T14:33:58.902565421+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":113},"msg":"Getting live provider data"}
{"time":"2025-08-04T14:33:59.72418585+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/home/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-04T14:34:00.463424572+02:00","level":"INFO","msg":"OK   20250424200609_initial.sql (1.07ms)"}
{"time":"2025-08-04T14:34:00.463834217+02:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (382.96µs)"}
{"time":"2025-08-04T14:34:00.464128217+02:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (278.21µs)"}
{"time":"2025-08-04T14:34:00.464453342+02:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (312.81µs)"}
{"time":"2025-08-04T14:34:00.464460374+02:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-04T14:34:00.464712682+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-04T14:34:00.483635057+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T14:34:00.483723027+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T14:34:00.504764998+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T14:34:00.504841717+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T14:34:05.360599731+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T14:34:05.360665141+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T14:34:05.379886231+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T14:34:05.379942123+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T14:34:11.546059474+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_040b227fe0e945c081eb7ea4","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:34:14.984413136+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_29ef20679389f387","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:34:15.094092242+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_29ef20679389f387","error":"path does not exist: /home/<USER>/projets/geored-v3/.cursor"}
{"time":"2025-08-04T14:34:15.094168795+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_29ef20679389f387","error":"path does not exist: /home/<USER>/projets/geored-v3/.cursor"}
{"time":"2025-08-04T14:34:17.339815541+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_0660fcf238a3dbac","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:34:17.3427525+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_0660fcf238a3dbac","error":"path does not exist: /home/<USER>/projets/geored-v3/.github"}
{"time":"2025-08-04T14:34:17.34294761+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_0660fcf238a3dbac","error":"path does not exist: /home/<USER>/projets/geored-v3/.github"}
{"time":"2025-08-04T14:34:19.456118498+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_77d531fa9bf84885","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:34:21.649319908+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_a41bf47030b60aa9","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:34:38.639772928+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_2e4108b031e847f98a5b7c03","name":"glob","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:34:39.264953651+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/llm/tools.globFiles","file":"/home/<USER>/work/crush/crush/internal/llm/tools/glob.go","line":149},"msg":"Ripgrep execution failed, falling back to doublestar","error":"ripgrep: exit status 127\npyenv: rg: command not found\n\nThe `rg' command exists in these Python versions:\n  anaconda3-2023.03\n\nNote: See 'pyenv help global' for tips on allowing both\n      python2 and python3 to be found.\n"}
{"time":"2025-08-04T14:34:43.759595528+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_919d7d261fed1c8d","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:34:51.33692843+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_b1499dd1e1dc42d9aecbb5ca","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:03.113211763+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_bd2c0df6dc774e6db1f26c74","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:06.838552745+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_d668f521fe3eb164","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:10.639118774+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_8376e7ef6d23214b","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:21.841306959+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_4e8860ac1f214f4cac1528dc","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:22.222788777+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_4e8860ac1f214f4cac1528dc","error":"path does not exist: /home/<USER>/projets/geored-v3/.cursor"}
{"time":"2025-08-04T14:35:25.71055887+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_d78ea87127194b779d5511b2","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:25.880356864+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_d78ea87127194b779d5511b2","error":"path does not exist: /home/<USER>/projets/geored-v3/.cursor/rules"}
{"time":"2025-08-04T14:35:34.665868138+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_57de4ed421d344ef9004c8d6","name":"ls","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:34.761214136+02:00","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).streamAndHandleEvents","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":551},"msg":"Tool execution error","toolCall":"call_57de4ed421d344ef9004c8d6","error":"path does not exist: /home/<USER>/projets/geored-v3/.github"}
{"time":"2025-08-04T14:35:42.230098279+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_28411b27def144be957cb18d","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:35:49.910055343+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_f261aa9dc7344a0f9a2ff8e2","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:36:00.968715967+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_70ba68038c844395bbf551a1","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:36:10.287870429+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_08fedff8020e45869d9ba8f1","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:36:32.303814037+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_81a2b73404e8415b98e29ccc","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:36:36.297253481+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_debc874c51a1e5c2","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:36:40.337674272+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_2ffdacde0742f6de","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:36:44.693845247+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_4f3f9fc85a5f450ca2e590f6","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:36:45.756616383+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_2e9f757c7eb24d28a802d271","name":"view","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:38:32.829014845+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_72190649a8eb47c486be1f04","name":"write","input":"","type":"","finished":false}}
{"time":"2025-08-04T14:39:18.806632812+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_b0cb7091269f472fab955f77","name":"edit","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:05:47.643681635+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_8dd2bdf4730a7f7a","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:05:47.697905761+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show --stat 4abde24df","err":null}
{"time":"2025-08-04T15:05:47.701585072+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show --stat 4abde24df","err":null}
{"time":"2025-08-04T15:06:11.724667183+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_cda21d8a806c5063","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:06:24.817543327+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_60496b58f6c249419640b5a4","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:06:24.828371014+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show --stat b64d2de86","err":null}
{"time":"2025-08-04T15:06:38.436362091+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_4ed4e8bb1ae54223b0d47c7b","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:06:38.444222797+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show --stat 3ec6b68a1","err":null}
{"time":"2025-08-04T15:06:54.715048469+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_698661df8a01490fa62b89a8","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:06:54.723375294+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show --stat 982718c6c","err":null}
{"time":"2025-08-04T15:07:05.162923337+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_bbaafc3e097e48b092c1603b","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:07:05.169948762+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show --stat 8eacf43fd","err":null}
{"time":"2025-08-04T15:07:08.365631544+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_35512302b92248b5921094dc","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:07:08.417344561+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show --stat 1d24ec44b","err":null}
{"time":"2025-08-04T15:07:29.989212675+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_3cf12eab24964752bbf94fc5","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:07:30.025584207+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show 1d24ec44b src/store/location_module/history.js src/store/location_module/index.js src/store/location_module/main_search.js","err":null}
{"time":"2025-08-04T15:07:44.379812366+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_7f28293254af4654ba06c2da","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:07:44.393502377+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show 8eacf43fd src/components/location/LocationMap/LocationHistoryMap/LocationHistoryMap.vue src/components/shared/SimplicitiMap/SimplicitiMap.vue","err":null}
{"time":"2025-08-04T15:07:48.888292776+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_08a0c04ad12c41fdba5255ce","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:07:48.895243922+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show 982718c6c src/components/shared/SimplicitiMap/LeafletMap.vue src/components/shared/SimplicitiMap/draw-positions-markers-mixin.js src/store/simpliciti_map/index.js","err":null}
{"time":"2025-08-04T15:08:03.941119893+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_02680cc24b8b4e62877e319f","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:08:03.947591002+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show 3ec6b68a1 src/components/shared/SimplicitiMap/draw-single-polylines-mixin.js","err":null}
{"time":"2025-08-04T15:08:06.910365199+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_9dc43f219f9b45a9bf94ee1a","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:08:06.916371444+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show b64d2de86 src/components/shared/SimplicitiMap/draw-single-polylines-mixin.js","err":null}
{"time":"2025-08-04T15:08:20.632513926+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_6e0ca0792c194abc8bdb1a8f","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:08:20.64370618+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show 4abde24df src/components/shared/SimplicitiMap/LeafletMap.vue src/mixins/map.js","err":null}
{"time":"2025-08-04T15:08:32.305719335+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_00531c8a2f2449fc86465ab7","name":"bash","input":"","type":"","finished":false}}
{"time":"2025-08-04T15:08:32.31197461+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/shell.(*loggingAdapter).InfoPersist","file":"/home/<USER>/work/crush/crush/internal/shell/persistent.go","line":36},"msg":"POSIX command finished","command":"git show 4abde24df tests/unit/mixins.map.spec.js | head -50","err":null}
{"time":"2025-08-04T15:09:11.179632409+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processEvent","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":623},"msg":"Tool call started","toolCall":{"id":"call_ba6417b7c96b4076b3185e87","name":"write","input":"","type":"","finished":false}}

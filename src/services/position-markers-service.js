/**
 * @namespace Services
 * @category Services
 * @module position-markers-service
 *
 * Non-reactive position markers service to improve performance with large datasets.
 * Replaces Vuex-based vehiclePositionMarkers management.
 *
 * Features:
 * - Non-reactive storage for better performance with large position datasets
 * - Memoization for frequently called filtering methods
 * - Automatic cache invalidation on data or filter changes
 * - Event listener system for component reactivity
 * - Speed filtering with caching optimization
 * - Position code filtering with TOR sensor support
 *
 * Memoization:
 * - getFilteredPositions() results are cached based on speed filter configuration
 * - getPositionsByCode() results are cached based on code parameter and speed filter
 * - Caches are automatically invalidated when:
 *   - setPositions() is called with new data
 *   - Speed filter configuration changes
 *   - clear() is called
 * - Cache logging available in development mode for debugging
 */

// Allow store override for testing - use lazy access to avoid circular dependencies
let storeInstance = null
let originalStore = null

// Lazy store access function
function _getStore() {
  if (!storeInstance) {
    // In a real application, the store should be injected during service initialization
    // This error indicates that the service needs to be properly initialized
    throw new Error(
      'Store not available. Please ensure the store is properly initialized using _setStoreForTesting() or by injecting the store during service initialization.'
    )
  }
  return storeInstance
}

/**
 * Used by Location module.
 * Applies the speed filter from MapOptions into a single position.
 * @param {*} position
 * @param {*} speedFilter
 * @returns {boolean}
 */
function isSpeedFilterCriteriaMeet(position, speedFilter) {
  if (speedFilter.condition === '<') {
    return position.vitesse < speedFilter.value
  }
  if (speedFilter.condition === '<=') {
    return position.vitesse <= speedFilter.value
  }
  if (speedFilter.condition === '==') {
    return position.vitesse === speedFilter.value
  }
  if (speedFilter.condition === '>=') {
    return position.vitesse >= speedFilter.value
  }
  if (speedFilter.condition === '>') {
    return position.vitesse > speedFilter.value
  }
  return false
}

class PositionMarkersService {
  constructor() {
    this._positions = []
    this._listeners = new Set()

    // Memoization caches
    this._filteredPositionsCache = new Map()
    this._positionsByCodeCache = new Map()
    this._lastSpeedFilterHash = null

    // Debug logging flag
    this._enableCacheLogging = process.env.NODE_ENV === 'development'
  }

  /**
   * Set position markers data
   * @param {Array} positions - Array of position objects
   */
  setPositions(positions) {
    this._positions = positions || []
    this._invalidateAllCaches()
    this._notifyListeners()
  }

  /**
   * Override the store instance for testing
   * @param {Object} testStore - Mock store instance for testing
   */
  _setStoreForTesting(testStore) {
    storeInstance = testStore
  }

  /**
   * Initialize the service with the store instance
   * This should be called during application startup
   * @param {Object} store - Vuex store instance
   */
  initializeStore(store) {
    if (!storeInstance) {
      storeInstance = store
      // Keep reference to original store for reset functionality
      if (!originalStore) {
        originalStore = store
      }
    }
  }

  /**
   * Reset store to original instance (for cleanup after tests)
   */
  _resetStore() {
    // Ensure we have the original store reference
    if (!originalStore) {
      originalStore = _getStore()
    }
    storeInstance = originalStore
  }

  /**
   * Generate a hash for the current speed filter configuration
   * @private
   * @returns {string} Hash representing the current speed filter state
   */
  _getSpeedFilterHash() {
    const store = _getStore()
    const speedFilter = store.getters['map_options/speedFilter']
    if (!speedFilter || !speedFilter.value) {
      return 'no-filter'
    }
    return `${speedFilter.condition}-${speedFilter.value}`
  }

  /**
   * Invalidate all memoization caches
   * @private
   */
  _invalidateAllCaches() {
    this._filteredPositionsCache.clear()
    this._positionsByCodeCache.clear()
    this._lastSpeedFilterHash = null

    if (this._enableCacheLogging) {
      console.debug('[PositionMarkersService] All caches invalidated')
    }
  }

  /**
   * Invalidate caches if speed filter has changed
   * @private
   */
  _invalidateCachesIfSpeedFilterChanged() {
    const currentSpeedFilterHash = this._getSpeedFilterHash()

    if (this._lastSpeedFilterHash !== currentSpeedFilterHash) {
      this._filteredPositionsCache.clear()
      this._positionsByCodeCache.clear()
      this._lastSpeedFilterHash = currentSpeedFilterHash

      if (this._enableCacheLogging) {
        console.debug(
          '[PositionMarkersService] Caches invalidated due to speed filter change:',
          {
            from: this._lastSpeedFilterHash,
            to: currentSpeedFilterHash,
          }
        )
      }
    }
  }

  /**
   * Get all position markers
   * @returns {Array} Array of position objects
   */
  getPositions() {
    return this._positions
  }

  /**
   * Get filtered position markers with speed filter applied
   * Uses memoization to cache results based on speed filter configuration
   * @returns {Array} Filtered array of position objects
   */
  getFilteredPositions() {
    // Check if speed filter has changed and invalidate caches if needed
    this._invalidateCachesIfSpeedFilterChanged()

    const speedFilterHash = this._getSpeedFilterHash()

    // Check cache first
    if (this._filteredPositionsCache.has(speedFilterHash)) {
      if (this._enableCacheLogging) {
        console.debug(
          '[PositionMarkersService] Cache HIT for getFilteredPositions:',
          speedFilterHash
        )
      }
      return this._filteredPositionsCache.get(speedFilterHash)
    }

    // Cache miss - compute result
    if (this._enableCacheLogging) {
      console.debug(
        '[PositionMarkersService] Cache MISS for getFilteredPositions:',
        speedFilterHash
      )
    }

    let result
    const store = _getStore()
    const speedFilter = store.getters['map_options/speedFilter']

    if (
      speedFilter &&
      speedFilter.value !== null &&
      speedFilter.value !== undefined
    ) {
      result = this._positions.filter((p) =>
        isSpeedFilterCriteriaMeet(p, speedFilter)
      )
    } else {
      result = this._positions
    }

    // Cache the result
    this._filteredPositionsCache.set(speedFilterHash, result)

    return result
  }

  /**
   * Get positions filtered by code (e.g., 'POS', 'TOR1', etc.)
   * Uses memoization to cache results based on code parameter and speed filter
   * @param {string} code - Position code to filter by
   * @returns {Array} Filtered array of position objects
   */
  getPositionsByCode(code) {
    // Check if speed filter has changed and invalidate caches if needed
    this._invalidateCachesIfSpeedFilterChanged()

    const speedFilterHash = this._getSpeedFilterHash()
    const cacheKey = `${speedFilterHash}:${code || 'no-code'}`

    // Check cache first
    if (this._positionsByCodeCache.has(cacheKey)) {
      if (this._enableCacheLogging) {
        console.debug(
          '[PositionMarkersService] Cache HIT for getPositionsByCode:',
          cacheKey
        )
      }
      return this._positionsByCodeCache.get(cacheKey)
    }

    // Cache miss - compute result
    if (this._enableCacheLogging) {
      console.debug(
        '[PositionMarkersService] Cache MISS for getPositionsByCode:',
        cacheKey
      )
    }

    const filteredPositions = this.getFilteredPositions()

    let result
    if (!code) {
      result = filteredPositions
    } else {
      let filteredItems = filteredPositions.filter((i) => i.code === code)

      // Handle TOR+ positions containing specific TOR sensors
      if (code.startsWith('TOR') && code !== 'TOR+') {
        filteredItems = filteredItems.concat(
          filteredPositions.filter(
            (p) =>
              p.code === 'TOR+' &&
              !!p.code_capteurs?.find((torCode) => torCode === code)
          )
        )
      }

      result = filteredItems
    }

    // Cache the result
    this._positionsByCodeCache.set(cacheKey, result)

    return result
  }

  /**
   * Get common positions (without sensor data)
   * @returns {Array} Filtered array of common position objects
   */
  getCommonPositions() {
    const filteredPositions = this.getFilteredPositions()
    return filteredPositions.filter((i) => !i.code || i.code === 'POS')
  }

  /**
   * Clear all position markers
   */
  clear() {
    this._positions = []
    this._invalidateAllCaches()
    this._notifyListeners()
  }

  /**
   * Get the count of position markers
   * @returns {number} Number of position markers
   */
  getCount() {
    return this._positions.length
  }

  /**
   * Add a listener for position changes
   * @param {Function} callback - Callback function to call when positions change
   */
  addListener(callback) {
    this._listeners.add(callback)
  }

  /**
   * Remove a listener
   * @param {Function} callback - Callback function to remove
   */
  removeListener(callback) {
    this._listeners.delete(callback)
  }

  /**
   * Get cache statistics for debugging and monitoring
   * @returns {Object} Cache statistics including hit rates and sizes
   */
  getCacheStats() {
    return {
      filteredPositionsCache: {
        size: this._filteredPositionsCache.size,
        keys: Array.from(this._filteredPositionsCache.keys()),
      },
      positionsByCodeCache: {
        size: this._positionsByCodeCache.size,
        keys: Array.from(this._positionsByCodeCache.keys()),
      },
      currentSpeedFilterHash: this._getSpeedFilterHash(),
      lastSpeedFilterHash: this._lastSpeedFilterHash,
      cacheLoggingEnabled: this._enableCacheLogging,
    }
  }

  /**
   * Manually invalidate all caches (useful for debugging)
   */
  invalidateCaches() {
    this._invalidateAllCaches()
  }

  /**
   * Enable or disable cache logging
   * @param {boolean} enabled - Whether to enable cache logging
   */
  setCacheLogging(enabled) {
    this._enableCacheLogging = enabled
    if (enabled) {
      console.debug('[PositionMarkersService] Cache logging enabled')
    }
  }

  /**
   * Notify all listeners of position changes
   * @private
   */
  _notifyListeners() {
    this._listeners.forEach((callback) => {
      try {
        callback(this._positions)
      } catch (error) {
        console.error('Error in position markers listener:', error)
      }
    })
  }
}

// Create singleton instance
const positionMarkersService = new PositionMarkersService()

window.positionMarkersService = positionMarkersService

export default positionMarkersService

import i18n from '@/i18n'
import { ComputedRef } from 'vue'

interface SectionItem {
  visible?: () => boolean
  name: string
  label: string
}

interface SectionRow {
  visible?: () => boolean
  twoColumns: SectionItem[]
}

interface UseWashSectionsOptions {
  isWasher: ComputedRef<boolean>
  isSweeper: ComputedRef<boolean>
  isSinglePosition: ComputedRef<boolean>
}

export function getWashAndSweepRows({
  isWasher,
  isSweeper,
  isSinglePosition,
}: UseWashSectionsOptions): SectionRow[] {
  return [
    {
      visible: () => isWasher.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'washPressure',
          label: i18n.t('diagnostics.chart.category.washPressure') as string,
        },
        {
          visible: () => isSinglePosition.value,
          name: 'washLevel',
          label: i18n.t('diagnostics.chart.category.washLevel') as string,
        },
      ],
    },
    {
      visible: () => isWasher.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'washFlowRateFrontL',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeFrontL'
          ) as string,
        },
        {
          visible: () => isSinglePosition.value,
          name: 'washFlowRateFrontR',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeFrontR'
          ) as string,
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'washFlowRangeFrontLAvg',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeFrontLAvg'
          ) as string,
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'washFlowRangeFrontRAvg',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeFrontRAvg'
          ) as string,
        },
      ],
    },
    {
      visible: () => isWasher.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'washFlowRateBackL',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeBackL'
          ) as string,
        },
        {
          visible: () => isSinglePosition.value,
          name: 'washFlowRateBackR',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeBackR'
          ) as string,
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'washFlowRangeBackLAvg',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeBackLAvg'
          ) as string,
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'washFlowRangeBackRAvg',
          label: i18n.t(
            'diagnostics.chart.category.washFlowRangeBackRAvg'
          ) as string,
        },
      ],
    },
    {
      visible: () => isWasher.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'washFlowRatePole',
          label: 'diagnostics.chart.category.washFlowRangePole',
        },
        {
          visible: () => isSinglePosition.value,
          name: 'washFlowRateRamp',
          label: 'diagnostics.chart.category.washFlowRangeRamp',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'washFlowRangePoleAvg',
          label: 'diagnostics.chart.category.washFlowRangePoleAvg',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'washFlowRangeRampAvg',
          label: 'diagnostics.chart.category.washFlowRangeRampAvg',
        },
      ],
    },
    {
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'sweepHopRaised',
          label: 'diagnostics.chart.category.sweepHopRaised',
        },
        {
          visible: () => isSinglePosition.value,
          name: 'sweepPump',
          label: 'diagnostics.chart.category.sweepPump',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepHopRaisedCount',
          label: 'diagnostics.chart.category.sweepHopRaisedCount',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepPumpCount',
          label: 'diagnostics.chart.category.sweepPumpCount',
        },
      ],
    },
    {
      visible: () => !isSinglePosition.value,
      twoColumns: [
        {
          name: 'sweepFanCount',
          label: 'diagnostics.chart.category.sweepFanCount',
        },
        {
          name: 'sweepRearDoorCount',
          label: 'diagnostics.chart.category.sweepRearDoorCount',
        },
      ],
    },
    {
      visible: () => isSinglePosition.value,
      twoColumns: [
        {
          name: 'sweepFan',
          label: 'diagnostics.chart.category.sweepFan',
        },
        {
          name: 'sweepFanSp',
          label: 'diagnostics.chart.category.sweepFanSp',
        },
      ],
    },
    {
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'sweepHpPump',
          label: 'diagnostics.chart.category.sweepHpPump',
        },
        {
          visible: () => isSinglePosition.value,
          name: 'sweepLevel',
          label: 'diagnostics.chart.category.sweepLevel',
        },
      ],
    },
    {
      visible: () => isSinglePosition.value,
      twoColumns: [
        {
          name: 'sweepRearDoor',
          label: 'diagnostics.chart.category.sweepRearDoor',
        },
        {
          visible: () => isSweeper.value,
          name: 'sweepBrushSp',
          label: 'diagnostics.chart.category.sweepBrushSp',
        },
      ],
    },
    {
      visible: () => isSweeper.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'sweepBrushCentral',
          label: 'diagnostics.chart.category.sweepBrushCentral',
        },
        {
          visible: () => isSinglePosition.value,
          name: 'sweepBrushLeft',
          label: 'diagnostics.chart.category.sweepBrushLeft',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepBrushCentralCount',
          label: 'diagnostics.chart.category.sweepBrushCentralCount',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepBrushLeftCount',
          label: 'diagnostics.chart.category.sweepBrushLeftCount',
        },
      ],
    },
    {
      visible: () => isSweeper.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'sweepBrushRight',
          label: 'diagnostics.chart.category.sweepBrushRight',
        },
        {
          visible: () => isSinglePosition.value,
          name: 'sweepWaterCentral',
          label: 'diagnostics.chart.category.sweepWaterCentral',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepBrushRightCount',
          label: 'diagnostics.chart.category.sweepBrushRightCount',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepWaterCentralCount',
          label: 'diagnostics.chart.category.sweepWaterCentralCount',
        },
      ],
    },
    {
      visible: () => isSweeper.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'sweepWaterFront',
          label: 'diagnostics.chart.category.sweepWaterFront',
        },
        {
          visible: () => isSinglePosition.value,
          name: 'sweepWaterRight',
          label: 'diagnostics.chart.category.sweepWaterRight',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepWaterFrontCount',
          label: 'diagnostics.chart.category.sweepWaterFrontCount',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepWaterRightCount',
          label: 'diagnostics.chart.category.sweepWaterRightCount',
        },
      ],
    },
    {
      visible: () => isSweeper.value,
      twoColumns: [
        {
          visible: () => isSinglePosition.value,
          name: 'sweepWaterLeft',
          label: 'diagnostics.chart.category.sweepWaterLeft',
        },
        {
          visible: () => isSinglePosition.value,
          name: 'sweepWaterLaterals',
          label: 'diagnostics.chart.category.sweepWaterLaterals',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepWaterLeftCount',
          label: 'diagnostics.chart.category.sweepWaterLeftCount',
        },
        {
          visible: () => !isSinglePosition.value,
          name: 'sweepWaterLateralsCount',
          label: 'diagnostics.chart.category.sweepWaterLateralsCount',
        },
      ],
    },
  ]
}

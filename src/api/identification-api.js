/**
 * @namespace api
 * @category api
 * @module identification-api
 * */
import Vue from 'vue'
import moment from 'moment'
import { getNestedValue } from '@/utils/object'
import searchService from '@/services/search-service'
import { normalizeBinCollectionItem } from '@/services/entities/bin-collection-entity'
import api from '@/api'
import { apiPlatformResourcesConfiguration } from '@/config/simpliciti-apis'
import { sortByDate } from '@/utils/array'
import { hasInvalidCoords } from '@/utils/map'
/**
 *
 * @param {*} item Synthesis per circuit (api v3 service_lift_bin_collection -> synthesis)
 * @returns {Object}
 */
export function normalizeLeveesSynthesisItem(item) {
  return {
    circuitId: getNestedValue(item, 'roundId'),
    circuitName: getNestedValue(item, 'roundShortName'),
    leveesTotal: getNestedValue(item, 'totalLift', 0),
    leveesWeightTotal: getNestedValue(item, 'totalWeight', 0),
    identifiedTotal: getNestedValue(item, 'nbIdentified', 0),
    unidentifiedTotal: getNestedValue(item, 'nbNotIdentified', 0),
    authorizedTotal: getNestedValue(item, 'nbAuthorized', 0),
    unauthorizedTotal: getNestedValue(item, 'nbNotAuthorized', 0),
    collectedTotal: getNestedValue(item, 'nbCollected', 0),
    uncollectedTotal: getNestedValue(item, 'nbNotCollected', 0),
    highPointTotal: getNestedValue(item, 'nbHighPoint', 0),
    nonHighPointTotal: getNestedValue(item, 'nbNotHighPoint', 0),
    stoppedTotal: getNestedValue(item, 'nbStopped', 0),
    blacklistedTotal: getNestedValue(item, 'nbBlacklisted', 0),
    aboveWeightTotal: getNestedValue(item, 'nbWeightOnLoad', 0),
    underWeightTotal: getNestedValue(item, 'nbWeightUnderLoad', 0),
  }
}

/**
 * @todo Move to entity
 * @param {*} item
 * @returns {Object}
 */
export function normalizeLeveeDetailsItem(item) {
  let datetime = adjustDateWithTimezone(getNestedValue(item, 'dateheure'))
  let newItem = {
    id: getNestedValue(item, 'levee_id'),
    timestamp: datetime._d.getTime(),
    datetime: datetime._d,
    formattedDate: Vue.$date.formatDate(datetime),
    formattedTime: Vue.$date.formatTimeWithSeconds(datetime),
    timeUnix: moment()
      .hour(datetime.hour())
      .minute(datetime.minute())
      .second(datetime.second()),
    vehicleName: getNestedValue(item, 'vehicule_nom'),
    circuitId: getNestedValue(item, 'circuit_id'),
    circuitName: getNestedValue(item, 'circuit_nom_court'),
    puceNumber: getNestedValue(item, 'puce_fondeur'),
    memoryPuceNumber: getNestedValue(item, 'puce_memoire'),
    formattedAddress: getFormattedAddress(item, {
      streetNumber: 'num_rue',
      streetAddress: 'rue',
      zipCode: 'cp',
      city: 'ville',
    }),
    chairType: getNestedValue(item, 'chaise'),
    isIdentified: getNestedValue(item, 'identifie', false) ? true : false,
    isStopped: getNestedValue(item, 'stoppe', false) ? true : false,
    //Test column filter
    //isHighPoint: Math.random() > 0.5 ? 'n/c' : Math.random() > 0.5 ? true : false,
    isHighPoint: getNestedValue(item, 'point_haut', false, {
      transform(value, apiValue) {
        if (apiValue === '') return 'n/c' //TODO: Does n/c needs translation?
        return !!value //0/1 = false/true
      },
    }),
    isBlacklisted: getNestedValue(item, 'blackliste', false) ? true : false,
    weight: getNestedValue(item, 'poids'),
    lat: getNestedValue(item, 'latitude'),
    lng: getNestedValue(item, 'longitude'),
  }
  newItem.hasInvalidCoords = hasInvalidCoords({
    lat: newItem.lat,
    lng: newItem.lng,
  })
  return newItem
}

/**
 *
 * @param {*} date
 * @param {*} vehicleId
 * @param {*} circuitId
 * @returns
 */
export async function fetchLeveesDetails(
  date,
  vehicleId,
  circuitId,
  options = {}
) {
  const fetchBinCollectionFunction =
    options.fetchBinCollection || fetchBinCollection
  const res = await fetchBinCollectionFunction(
    date,
    vehicleId,
    circuitId,
    ['lift'],
    options
  )

  let items =
    res.length && res[0]['vehicles'].length
      ? res[0]['vehicles'][0].binCollections
      : []
  if (items.length === 0) return []
  // FMA 20240318 https://easyredmine.simpliciti.fr/issues/42002
  let vehicle =
    res.length && res[0]['vehicles'].length ? res[0]['vehicles'][0].vehicle : {}
  if (Object.keys(vehicle).length === 0) return {}

  let arr = items.map(function (item) {
    return normalizeBinCollectionItem(vehicle, item)
  })
  return sortByDate(arr, 'datetime')
}

export async function fetchLeveesSynthesis(elementIds, date, options = {}) {
  let groups = 'synthesis'
  groups = options.groups || groups
  //Load data from APIV3, instead
  const res = await fetchBinCollection(
    date,
    options.elementType === 'circuit' ? null : elementIds,
    options.elementType === 'circuit' ? elementIds : null,
    [groups],
    options
  )
  return formatResultSynthesis(res)
}

/**
 * Format result APi for Use Synthesis
 * @param res
 * @returns {*[]}
 */
export function formatResultSynthesis(res) {
  let arr = []
  let vehicle = null
  let round = null

  !!res &&
    Object.keys(res).forEach((dateKey) => {
      let date = moment(res[dateKey].date, 'YYYY-MM-DD')._d
      res[dateKey].vehicles.forEach((vehicleData) => {
        vehicle = vehicleData.vehicle
        let rounds = Object.keys(vehicleData.synthesis).map((roundId) => {
          round = vehicleData.synthesis[roundId]
          return normalizeLeveesSynthesisItem(round.synthesis)
        })
        arr.push({
          date,
          timestamp: date.getTime(),
          dateFormatted: Vue.$date.formatDate(date),
          vehicleId: getNestedValue(vehicle, 'id'),
          vehicleName: getNestedValue(vehicle, 'name'),
          vehicleImmatriculation: getNestedValue(vehicle, 'registrationPlate'),
          vehicleCategory: getNestedValue(vehicle.category, 'label'),
          //Synthesis by circuit
          circuits: rounds,
          synthesis: rounds,
        })
      })
    })

  return arr
}

/**
 * Call ApiV3 service to get bin collection data/synthesis
 * @param date
 * @param vehicleId
 * @param roundId
 * @param groups
 * @param options
 * @returns {Promise<*>}
 */
export async function fetchBinCollection(
  date,
  vehicleId = null,
  roundId = null,
  groups = ['synthesis'],
  options = {}
) {
  const rangeDateTimes = searchService.getRangeTimeSelected(
    Array.isArray(date) ? date[0] : date
  )
  let queryParams = {
    collectedStartAt: rangeDateTimes.start,
    collectedEndAt: rangeDateTimes.end,
    groups: groups,
  }
  let paramsCheckSelected = [
    'identified',
    'stopped',
    'highPoint',
    'blacklisted',
  ]
  paramsCheckSelected.forEach((filter) => {
    if (
      !options.filters ||
      options.filters[filter] === undefined ||
      options.filters[filter] === ''
    ) {
      return
    }
    queryParams[filter] = Boolean(options.filters[filter])
  })

  if (options.filters && options.filters['puceNumber'] !== '') {
    queryParams['chipNumber'] = options.filters['puceNumber']
  }

  if (vehicleId !== null) {
    queryParams['vehicleId'] = vehicleId
  }

  if (roundId !== null) {
    queryParams['roundId'] = roundId
  }
  //Load data from APIV3, instead
  let result = await api.v3.get(
    apiPlatformResourcesConfiguration.serviceLiftBinCollection,
    queryParams
  )
  return result.data
}

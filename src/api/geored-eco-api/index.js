import { equals, identity, includes, pathSatisfies } from 'ramda'
import queryString from 'query-string'
import httpClient from '@/api/http-client'
import cache from '@/utils/cache'
import Helpers from '@/services/helpers'
import api from '@/api'

const REQUEST_TIMEOUT = 30000

const addAuthHeader = {
  fulfilled: async (request) => {
    const { url: requestUrl } = request
    const isPublicURL = includes('/public', requestUrl)
    let userInfos = await cache.getItem('user_infos')
    if (isPublicURL) return request

    const authToken = userInfos.toClientToken || userInfos.token
    request.headers['Authorization'] = `Bearer: ${authToken}`

    return request
  },
  rejected: identity,
}

const convertJsonBodyToQuery = {
  fulfilled: async (request) => {
    const { data: requestBody, headers: requestHeaders } = request
    const isXFormRequest = pathSatisfies(
      equals('application/x-www-form-urlencoded'),
      ['Content-Type']
    )(requestHeaders)

    if (isXFormRequest) {
      request.data = queryString.stringify(requestBody)
      return request
    }

    return request
  },
  rejected: identity,
}

const url = {
  defaultEndpoint: import.meta.env.VITE_API_GEORED_V3_HOST,
}

export default (
  baseUrl = `${url.endpoint || url.defaultEndpoint}`,
  headers = {}
) => {
  const baseHeaders = {
    Accept: 'application/hal+json',
  }
  const client = httpClient({
    baseUrl,
    /* withCredentials: true, */
    timeout: REQUEST_TIMEOUT,
    headers: Object.assign({}, baseHeaders, headers),
  })

  client.addRequestInterceptor(addAuthHeader)
  client.addRequestInterceptor(convertJsonBodyToQuery)

  const scope = {
    fetchPaginate: async function (onPaginate) {
      let firstResponse = await onPaginate(0)
      let records = []
      let helpers = Helpers()
      if (firstResponse.data._embedded && firstResponse.data._embedded.item) {
        records = records.concat(firstResponse.data._embedded.item)
      }
      if (firstResponse.data._links.last) {
        let lastRecords = firstResponse.data._links.last.href.split('&page=')
        let page = 1
        let totalPages = lastRecords[1]
        do {
          let response = await onPaginate(++page)
          if (response.data._embedded && response.data._embedded.item) {
            records = records.concat(response.data._embedded.item)
          }
        } while (page < totalPages)
        return records
      }
      return records
    },

    fetchVehicule: createVehicleDriverCircuitFetchFunction(
      'eco_driving_vehicles'
    ),
    fetchVehiculePaginate: createPaginateFunction('fetchVehicule'),
    fetchVehiculeNow: createVehicleDriverCircuitNowFetchFunction(
      'eco_driving_vehicles'
    ),

    fetchDriver: createVehicleDriverCircuitFetchFunction('eco_driving_drivers'),
    fetchDriverPaginate: createPaginateFunction('fetchDriver'),
    fetchDriverNow: createVehicleDriverCircuitNowFetchFunction(
      'eco_driving_drivers'
    ),

    fetchCircuit: createVehicleDriverCircuitFetchFunction('eco_driving_rounds'),
    fetchCircuitPaginate: createPaginateFunction('fetchCircuit'),
    fetchCircuitNow:
      createVehicleDriverCircuitNowFetchFunction('eco_driving_rounds'),
  }

  function createVehicleDriverCircuitFetchFunction(apiName) {
    return function (stringRangeDates, clientId, extraQuery, page) {
      console.log(`ecoconduite-api:ajax:${apiName}`, {
        stringRangeDates,
        clientId,
        extraQuery,
      })
      const route = `/${apiName}?date[]=${stringRangeDates}&clientId=${clientId}&${extraQuery}&itemsPerPage=1000&page=${
        page || 1
      }`
      return client.get(route, {})
    }
  }

  function createVehicleDriverCircuitNowFetchFunction(apiName) {
    return async function (arrayRangeDatesNow, clientId, extraQuery, page) {
      let name = ''
      switch (apiName) {
        case 'eco_driving_drivers':
          name = 'driverId'
          break
        case 'eco_driving_vehicles':
          name = 'vehicleId'
          break
        case 'eco_driving_rounds':
          name = 'roundId'
          break
      }
      let dataResponseNow = []
      for (let s in extraQuery) {
        for (let itemRangeDateNow in arrayRangeDatesNow) {
          const route = `/v2/eco_conduite/${apiName}?date=${
            arrayRangeDatesNow[itemRangeDateNow]
          }&clientId=${clientId}&${name}=${extraQuery[s]}&page=${page || 1}`
          let response = await api.v2.get(route)
          let data = response.data
          if (typeof data.totalItems == 'undefined') {
            for (let i in data) {
              dataResponseNow.push(data[i])
            }
          }
        }
      }
      return dataResponseNow
    }
  }

  function createPaginateFunction(scopeMethod) {
    return function (stringRangeDates, clientId, extraQuery) {
      return this.fetchPaginate(async (page) => {
        return await this[scopeMethod](
          stringRangeDates,
          clientId,
          extraQuery,
          page
        )
      })
    }
  }

  return scope
}

import axios from 'axios'
import api from '@/api'
export class HttpClient {
  static requestInterceptors = []
  static responseInterceptors = []

  constructor(config) {
    this.client = axios.create({
      baseURL: config.baseUrl,
      headers: config.headers,
      timeout: config.timeout || 0,
      withCredentials: config.withCredentials || false,
    })

    this.client.interceptors.response.use(
      (r) => r,
      (err) => {
        if (err.response) {
          api.bus.$emit(err.response.status.toString(), err)
        }
        return Promise.reject(err)
      }
    )

    HttpClient.requestInterceptors.forEach((interceptor) =>
      this.addRequestInterceptor(interceptor)
    )

    HttpClient.responseInterceptors.forEach((interceptor) => {
      this.addResponseInterceptor(interceptor)
    })
  }

  addRequestInterceptor = (interceptor) =>
    this.client.interceptors.request.use(
      interceptor.fulfilled,
      interceptor.rejected
    )

  addResponseInterceptor = (interceptor) =>
    this.client.interceptors.response.use(
      interceptor.fulfilled,
      interceptor.rejected
    )

  get(url, additionalHeaders = {}) {
    return this.client.get(url, { headers: additionalHeaders })
  }

  post(url, data = {}, additionalHeaders = {}) {
    return this.client.post(url, data, { headers: additionalHeaders })
  }

  put(url, data = {}, additionalHeaders = {}) {
    return this.client.put(url, data, { headers: additionalHeaders })
  }

  patch(url, data = {}, additionalHeaders = {}) {
    return this.client.patch(url, data, { headers: additionalHeaders })
  }

  delete(url, additionalHeaders = {}) {
    return this.client.delete(url, { headers: additionalHeaders })
  }

  request(config) {
    return this.client.request(config)
  }
}

export default function httpClient(config) {
  return new HttpClient(config)
}

/**
 * @namespace api
 * @category api
 * @module dashboard-api
 * */

import Vue from 'vue'
import { getQueryStringValue } from '@/utils/querystring'
import moment from 'moment'
import { getNestedValue } from '@/utils/object.js'
import store from '@/store'
import i18n from '@/i18n'
import APIUrls, { APIV2RequestDateFormat } from '@/config/simpliciti-apis.js'

/**
 * Required by Widget ANOMALIES_COUNT "Nb de signalements" (Top Anomalies)
 * @returns {Array}
 */
export async function getRealtimeTopAnomaliesDashboardData() {
  let isTestSabatierDev =
    getQueryStringValue('test') === '1' && Vue.$env.isDev()
  let date = `dates=${moment().format(APIV2RequestDateFormat)}`

  //Lazy load vehicle categories into search_module if not initialize yet
  if (!store.state.search_module.initialized) {
    await store.dispatch('search_module/initialize')
  }

  let vehicleCategories = `&vehicule_categorie_id=${store.state.search_module.vehicleCategories
    .map((i) => i.id)
    .join(',')}`
  if (isTestSabatierDev) {
    date = `dates=2021-02-02,2021-02-03,2021-02-08,2021-02-10`
    vehicleCategories = `&vehicule_categorie_id=9740,10854,9806,11092,11094,11096,133,165,282,10950,3497,11056,8635,8636,10918,1572,391,11070,8674,11098,2755,3269,9839,10938,10940,10942,10944,10946,3510,10890,10870,9841,9852,912,1006,7351,9738,9742,9812,10948,1217,10936,9734,2294,9744,9746,46,1759,10900,10902,10862,8012,8013,8014,11068,10899,6414,10466,9810,10448,8484,9878,10468,10464,1251,10920,3388,10420,1368,10460,10462,9297`
  }
  let args = `?`
  args += date + vehicleCategories
  args += getQueryStringValue('test') === '1' ? '&test=1' : ''
  let res =
    (await Vue.$api.v2.get(APIUrls.APIV2_EVENEMENTS_CLIENT_INDICATEURS + args))
      .data || {}

  if (Object.keys(res).length > 0) {
    let anomaliesFromFirstDate = res[Object.keys(res)[0]]
    return Object.keys(anomaliesFromFirstDate)
      .map((anomalyId) => {
        return {
          code: getNestedValue(anomaliesFromFirstDate, `${anomalyId}.code`),
          name: getNestedValue(anomaliesFromFirstDate, `${anomalyId}.libelle`),
          value: getNestedValue(anomaliesFromFirstDate, `${anomalyId}.nb`, 0),
          color: getNestedValue(
            anomaliesFromFirstDate,
            `${anomalyId}.couleurs`,
            'black'
          ),
        }
      })
      .sort((a, b) => {
        return a.value > b.value ? -1 : 1
      })
    //.slice(0, 3);
  } else {
    return []
  }
}

/**
 * Required by Multiple Widgets
 * ACTIVE_VEHICLES_COUNT
 * ACTIVE_CIRCUIT_COUNT
 * ACTIVE_CIRCUIT_PERC
 * VEHICLE_STATUS
 * NO_EMIT_VEHICLE_COUNT
 * FINISHED_CIRCUIT_PERC
 * @returns {Array}
 */
export async function getRealtimeDashboardDataByVehicle() {
  let args =
    getQueryStringValue('test') === '1' ? '?test=2021-10-22%2023%3A59%3A59' : ''
  return (
    (
      await Vue.$api.v2.get(
        APIUrls.APIV2_TEMP_REEL_INDICATEUR_BY_VEHICLE + args
      )
    ).data || {}
  )
}

/**
 * Required by widget NO_EMIT_VEHICLE_COUNT "Le nombre de véhicules sans émission depuis 72 heures"
 * @returns {NUmber}
 */
export async function getRealtimeNoEmitSevenTwoCount() {
  return getNestedValue(
    await getRealtimeDashboardDataByVehicle(),
    'status.contact_off_72.nb',
    0
  )
}

/**
 * Required by Widget VEHICLE_STATUS "Le statut des véhicules"
 * @returns {Object}
 */
export async function getRealtimeVehicleStatusStats() {
  function mapToEchartSeriesArray(item) {
    return {
      name: item.label,
      type: 'bar',
      emphasis: {
        focus: 'series',
      },
      label: {
        show: true,
      },
      stack: 'total',
      data: [item.value],
      itemStyle: {
        color: item.color,
      },
    }
  }

  let res = await getRealtimeDashboardDataByVehicle()
  return [
    {
      label: i18n.t('dashboard.widgets.realtime_vehicle_status.move'),
      value: getNestedValue(res, 'status.move.nb', 0),
      color: getNestedValue(res, 'status.move.color', '#70BD95'),
    },
    {
      label: i18n.t('dashboard.widgets.realtime_vehicle_status.stop'),
      value: getNestedValue(res, 'status.stop.nb', 0),
      color: getNestedValue(res, 'status.stop.color', '#F6AC59'),
    },
    {
      label: i18n.t('dashboard.widgets.realtime_vehicle_status.contact_off'),
      value: getNestedValue(res, 'status.contact_off.nb', 0),
      color: getNestedValue(res, 'status.contact_off.color', '#484848'),
    },
    {
      label: i18n.t('dashboard.widgets.realtime_vehicle_status.contact_off_72'),
      value: getNestedValue(res, 'status.contact_off_72.nb', 0),
      color: getNestedValue(res, 'status.contact_off_72.color', '#FF4545'),
    },
  ].map(mapToEchartSeriesArray)
}

/**
 * Required by Widget FINISHED_CIRCUIT_PERC "Taux de réalisation moyen des circuits terminés"
 * @returns
 */
export async function getFinishedCircuitsPercentage() {
  let res = await getRealtimeDashboardDataByVehicle()
  res = res.circuits
  return getNestedValue(res, 'vehicule_with_circuit_finished_today.taux', 0)
}

/**
 * Required by Widget ACTIVE_CIRCUIT_PERC "Taux de réalisation moyen des circuits en cours de réalisation"
 * @returns
 */
export async function getActiveCircuitsPercentage() {
  let res = await getRealtimeDashboardDataByVehicle()
  res = res.circuits
  return getNestedValue(res, 'vehicule_with_circuit_in_progress_today.taux', 0)
}

/**
 * Required by Widget ACTIVE_CIRCUIT_COUNT "Nombre de circuits en cours de réalisation"
 * @warn Unused
 * @returns
 */
export async function getActiveCircuits() {
  let res = await getRealtimeDashboardDataByVehicle()
  res = res.circuits
  return {
    activeCircuitsCount: getNestedValue(
      res,
      'vehicule_with_circuit_in_progress_today.nb',
      0
    ),
    plannedCircuitsCount: getNestedValue(
      res,
      'vehicule_with_circuit_planned_today.nb',
      0
    ),
  }
}

/**
 * Required by Widget ACTIVE_VEHICLES_COUNT "Nombre de véhicules actifs sur les dernières 24 heures"
 * @returns
 */
export async function getActiveGPS() {
  let res = await getRealtimeDashboardDataByVehicle()
  res = res.gps_on
  return Object.keys(res)
    .map((key) => {
      return {
        date: moment(key)._d.getTime(),
        label: res[key].lib,
        value: res[key].nb,
      }
    })
    .sort((a, b) => (a > b ? 1 : -1))
}

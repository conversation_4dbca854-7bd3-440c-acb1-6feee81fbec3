/**
 * @namespace api
 * @category api
 * @module datatable-api
 * */

import Vue from 'vue'
import api from './'
import * as R from 'ramda'
import { faker } from '@/utils/faker'

async function getParams(params = {}, fetchOptions = {}) {
  if (typeof params === 'function') {
    params = params(fetchOptions)
  }
  let p = {}
  for (var x = 0; x < Object.keys(params).length; x++) {
    let k = Object.keys(params)[x]
    if (typeof params[k] === 'function') {
      p[k] = params[k](fetchOptions)
      if (p[k] instanceof Promise) {
        p[k] = await p[k]
      }
      if (p[k] === undefined) {
        delete p[k]
      }
      if (p[k] === '') {
        delete p[k]
      }
    } else {
      p[k] = params[k]
    }
  }
  return p
}

var scope = (() => {
  /**
   *
   * @param {*} options
   * @returns {Object} {name, infos:{currentPage, lastPage}, items}
   */
  function createFetchTableItems(options = {}) {
    if (options.paginationHandler) {
      return options.paginationHandler
    }

    /**
     * @param {Object} fetchOptions {data:{}, page, itemsPerPage, infos:{currentPage}}
     */
    return async function (fetchOptions = {}) {
      let createData = await getParams(options.data, fetchOptions)
      let request = {
        url: createData._url || options.url,
        data: {
          ...R.pick(['page', 'itemsPerPage'], fetchOptions),
          ...(fetchOptions.data || {}),
          ...(await getParams(options.data, fetchOptions)),
        },
        options: {
          headers: {
            Accept: 'application/hal+json',
          },
        },
      }

      request.data.page =
        fetchOptions.page || fetchOptions.infos.currentPage || 1

      let result = {}

      console.debugVerbose(8, 'datatableApi.request.url', request.url)

      if (options.useFakeData && options.fakeData) {
        result = {
          data: options.fakeData({
            faker,
            R,
            request,
          }),
        }
      } else {
        api.bus.$emit('datatable_fetch', {
          options,
          fetchOptions,
          request,
        })
        result = await api.v3.get(request.url, request.data, request.options)
      }

      let resultData = result.data ? result.data : {}

      let items = []

      if (resultData._embedded && resultData._embedded.item) {
        let transformTable =
          options.transform !== undefined ? options.transform : null

        const filterTransformTable = (handler) => {
          return Object.keys(transformTable).reduce((carry, curr) => {
            if (handler(curr)) {
              carry[curr] = transformTable[curr]
            }
            return carry
          }, {})
        }

        const transformTablePrimitives = filterTransformTable(
          (key) => typeof transformTable[key] !== 'function'
        )
        const convertionTableFunctions = filterTransformTable(
          (key) => typeof transformTable[key] === 'function'
        )

        const mapItem = (item) => {
          let obj = {}

          R.uniq(Object.keys(transformTable).concat(Object.keys(item))).forEach(
            (key) => {
              if (transformTablePrimitives[key]) {
                obj[transformTablePrimitives[key]] = item[key]
                return
              }
              if (convertionTableFunctions[key]) {
                obj[key] = convertionTableFunctions[key](item)
                return
              }
              obj[key] = item[key]
            }
          )

          if (options.mapOmit) {
            obj = R.omit(options.mapOmit, obj)
          }

          return obj
        }

        if (options.getResponseArrayRoot) {
          items = options.getResponseArrayRoot(resultData).map(mapItem)
        } else {
          items = resultData._embedded.item.map(mapItem)
        }
      }
      let infos = R.omit(['_embedded'], resultData)
      try {
        infos.currentPage =
          parseInt(infos._links.self.href.split('page=')[1]) || 1
      } catch (err) {
        infos.currentPage = 1
        //No data available (0 results)
        //console.error(err);
        //throw new Error("FAILED_TO_SET_PAGINATION_INFOS");
      }
      try {
        infos.lastPage = parseInt(infos._links.last.href.split('page=')[1])
      } catch (err) {
        infos.lastPage = 1
        //Not available when available records are lower or equal to itemsPerPage
      }

      if (infos.totalItems === 0) {
        infos.currentPage = 1
        infos.lastPage = 1
      }
      let response = {
        name: options.name,
        infos: R.pick(['currentPage', 'lastPage'], infos),
        items,
      }
      console.debugVerbose(
        8,
        'datatableApi response',
        R.omit(['items'], response),
        {
          infos: R.omit(['_links'], infos),
        }
      )
      return response
    }
  }

  return {
    tables: {},
    registerTable(options = {}) {
      this.tables[options.name] = {
        fetchTableItems:
          options.fetchTableItems || createFetchTableItems(options),
      }
    },
    hasTable(name) {
      return !!this.tables[name]
    },
  }
})()

export default {
  registerTable(options = {}) {
    if (!options.name) {
      throw new Error('registerTable: options.name required')
    }
    console.log(
      'datatableApi.registerTable',
      options.name,
      'Registered?',
      scope.hasTable(options.name)
    )
    if (!scope.hasTable(options.name)) {
      return scope.registerTable(options)
    }
  },
  async fetchTableItems(options = {}) {
    if (!scope.hasTable(options.name)) {
      throw new Error('INVALID_COLLECTION')
    }
    return scope.tables[options.name].fetchTableItems(options)
  },
}

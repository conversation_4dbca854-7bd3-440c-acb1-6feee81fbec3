import { apiPlatformResourcesConfiguration } from '@/config/simpliciti-apis.js'
import { isTestMode } from '@/services/env-service'
import * as R from 'ramda'
const debug = false
const defaultPoolingItemsPerRequest = 500

const normalizationModules = import.meta.glob('/src/api/normalization/*.js', {
  eager: true,
})

async function transformPayloadToAPIPlatformFormat(resourceName, payload = {}) {
  try {
    let mod = await getNormalizeModule(resourceName)
    if (mod?.mutateNormalize) {
      payload = mod.mutateNormalize(payload)
      debug &&
        console.log('transformPayloadToAPIPlatformFormat', {
          payload,
        })
    }
  } catch (err) {
    console.log(err.stack)
    throw err
  }
  return payload
}

export function useApiPlatformResources({ apiWrapper, getAPIV3Pooling }) {
  let apiResources = {}
  for (let resourceName of Object.keys(apiPlatformResourcesConfiguration)) {
    let resourcePath = apiPlatformResourcesConfiguration[resourceName]
    apiResources[resourceName] = {
      async create(values = {}) {
        if (values.id) {
          return apiResources[resourceName].edit(values)
        }

        values = await transformPayloadToAPIPlatformFormat(
          resourceName,
          R.omit(['id'], values)
        )

        let r = await apiWrapper.v3.post(resourcePath, values)
        debug &&
          console.log(`${resourceName} create `, {
            r,
          })
        return await normalizeResourceApiResponse(resourceName, r.data || r)
      },
      async edit(values = {}, options = {}) {
        values = await transformPayloadToAPIPlatformFormat(resourceName, values)
        if (!values.id) {
          throw new Error('id property expected')
        }
        let r = await apiWrapper.v3.patch(
          `${resourcePath}/${values.id}`,
          R.omit(['id'], values),
          options
        )
        let normalizedRes = await normalizeResourceApiResponse(
          resourceName,
          r.data || r
        )
        debug &&
          console.log(`${resourceName} edit `, {
            r,
            normalizedRes,
          })
        return normalizedRes
      },
      async get(id, query = {}, options = {}) {
        if (!id) {
          throw new Error('id required')
        }
        let r = await apiWrapper.v3.get(`${resourcePath}/${id}`, query, {
          ...options,
          disableCache: true,
        })
        debug &&
          console.log(`${resourceName} edit `, {
            r,
          })
        return await normalizeResourceApiResponse(resourceName, r.data || r)
      },
      /**
       *
       * @param {Object} query Request querystring
       * @param {Object} [options] Operation options
       * @param {Boolean} [options.rawResponse] Return raw response
       * @returns
       */
      async getAll(query = {}, options = {}) {
        console.debugVerboseScope(8, 'api', 'getAll', {
          query,
          mockAPIHandler: options.mockAPIHandler,
          isTestMode: isTestMode(),
        })

        let r =
          options.mockAPIHandler && isTestMode()
            ? options.mockAPIHandler()
            : await apiWrapper.v3.get(resourcePath, query, options)
        return await normalizeResourceApiResponse(
          resourceName,
          options.rawResponse === true ? r : r.data || r,
          options
        )
      },
      /**
       *
       * @param {Boolean} options.freezeItems Applies Object.freeze() to all items
       * @returns
       */
      async getAllPooling(options = {}) {
        //Inject normalization
        let callback = !options.callback
          ? null
          : async (items, lastRes) => {
              return options.callback(
                await normalizeResourceApiResponse(
                  resourceName,
                  items,
                  options
                ),
                lastRes
              )
            }

        debug && console.log('getAllPooling:start', { options })
        let itemsPerPage =
          options.itemsPerPage ||
          options.poolingItemsPerRequest ||
          defaultPoolingItemsPerRequest
        if (options.itemsPerPage) {
          delete options.itemsPerPage
        }
        let r = await getAPIV3Pooling({
          uri: `${resourcePath}?page=1&itemsPerPage=${itemsPerPage}`,
          ...options,
          callback,
        })

        debug && console.log('getAllPooling:end', { options, r })

        return await normalizeResourceApiResponse(
          resourceName,
          r.data || r,
          options
        )
      },
      async delete(id) {
        if (!id) {
          throw new Error('id required')
        }
        let r = await apiWrapper.v3.delete(`${resourcePath}/${id}`)
        return r.data || r
      },
    }

    apiResources[resourceName].normalizeItem = (r) => r
    getNormalizeItemHandler(resourceName).then((handler) => {
      apiResources[resourceName].normalizeItem =
        handler || apiResources[resourceName].normalizeItem
    })
  }
  /*debug &&
    console.log(
      `The following api platform resources are available: `,
      Object.keys(apiResources).join(', ')
    )*/
  window.apiResources = apiResources
  return apiResources
}

function getNormalizeItemHandler(resourceName) {
  return new Promise((resolve) => {
    const modulePath = `/src/api/normalization/${resourceName}.js`
    const module = normalizationModules[modulePath]
    resolve(module?.default || null)
  })
}

function getNormalizeModule(resourceName) {
  return new Promise((resolve) => {
    const modulePath = `/src/api/normalization/${resourceName}.js`
    const module = normalizationModules[modulePath]
    resolve(module || null)
  })
}

function normalizeResourceApiResponse(resourceName, response, options = {}) {
  return new Promise(async (resolve, reject) => {
    if (typeof response !== 'object' && !(response instanceof Array)) {
      debug &&
        console.log('normalizeResourceApiResponse::skip', {
          response,
        })
      return resolveWithOptions(response)
    }

    function resolveWithOptions(res) {
      if (options.sort && res.sort) {
        res = res.sort(options.sort)
      }
      if ((options.first || options.single) && res instanceof Array) {
        return resolve(res.length >= 1 ? res[0] : null)
      }
      resolve(res)
    }

    function applyOptionsToSingleItem(item) {
      if (options.freezeItems) {
        item = Object.freeze(item)
      }
      return item
    }

    //Feat: Map entire array at once (For non-flatten/nested/complex API responses)
    if (options.normalizeArray) {
      let res = response
      let mod = null
      try {
        mod = await getNormalizeModule(resourceName)
        res = mod.normalizeArray(res)
      } catch (err) {
        console.warn('Unable to call normalizeArray on ', resourceName, {
          err,
        })
      } finally {
        resolveWithOptions(res)
      }
      return
    }

    getNormalizeItemHandler(resourceName)
      .then((handler = null) => {
        if (!handler) {
          return resolveWithOptions(response)
        }

        if (response instanceof Array) {
          resolveWithOptions(
            response.map((item) =>
              applyOptionsToSingleItem(handler(item, options))
            )
          )
        } else {
          resolveWithOptions(
            applyOptionsToSingleItem(handler(response, options))
          )
        }
      })
      .catch((err) => {
        console.error(err)
        reject(err)
      })
  })
}

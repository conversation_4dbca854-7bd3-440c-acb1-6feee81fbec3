# API Platform Resource normalization

Normalization functions under ./normalizations are used by api-platform-resources.js

## How we can use them?

### Import module

By importing the function directly from the eache module.

```javascript
import normalizeZoneItem from '@/api/normalization/zone'
```

### Use API Platform resource

By calling get/getAll on the api-platform resource.

```javascript
import api from '@/api'

api.zone.getAll().then(console.log)
```

/**
 * @namespace api
 * @category api
 * @module alerts-api
 * */

import store from '@/store'
import api from '@/api'
import { getNestedValue } from '@/utils/object'
import moment from 'moment'
import { getQueryStringValue } from '@/utils/querystring'
import APIUrls from '@/config/simpliciti-apis.js'
import { normalizeItem } from '@/api/normalization/alerts.js'

/**
 *
 * @param {*} messageId
 * @returns {Object}
 */
export async function acknowledgeAlert(messageId) {
  return await api.v2.post(
    `${APIUrls.APIV2_MESSAGE_ACK}?message_id=${messageId}`
  )
}

/**
 *
 * @returns {Array}
 */
export async function fetchAlertTypes() {
  return ((await api.v2.get(APIUrls.APIV2_CDM_TYPE_ALERTE)).data || [])
    .map((item) => ({
      id: getNestedValue(item, 'id'),
      label: getNestedValue(item, 'libelle'),
      description: getNestedValue(item, 'description'),
    }))
    .sort((a, b) => (a.label > b.label ? 1 : -1))
}

/**
 *
 * @param {*} vehicleIds
 * @param {*} fromDate
 * @param {*} toDate
 * @param {*} options
 * @returns {Array}
 */
export async function fetchAlerts(
  vehicleIds,
  fromDate = null,
  toDate = null,
  options = {}
) {
  let alertTypes = options.types || []
  let alertStatus = options.status || []

  alertTypes =
    alertTypes.length > 0 ? `&typealerte_id=${alertTypes.join(',')}` : ``
  alertStatus = alertStatus.length > 0 ? `&statut=${alertStatus.join(',')}` : ``
  let userId = getQueryStringValue('user_id')
    ? getQueryStringValue('user_id')
    : store.state.auth.user_infos.userId
  let arr = await api.v2.get(
    `${APIUrls.APIV2_MESSAGES_ALERTES}?vehicule_id=${vehicleIds}&dateheure_debut=${fromDate}&dateheure_fin=${toDate}&utilisateur_id=${userId}${alertTypes}${alertStatus}`
  )

  arr = (arr.data || []).map(normalizeItem)

  return arr
}

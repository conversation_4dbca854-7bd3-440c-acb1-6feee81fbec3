import { getNestedValue } from '@/utils/object'

export default function mapSinglebinTypeItem(item) {
  return {
    id: getNestedValue(item, 'id'),
    label: getNestedValue(item, 'label'),
    pictureSVG: getNestedValue(item, 'picture'),
  }
  /*
    {
  "label": "Corbeilles de rue",
  "volume": 100,
  "load": 100,
  "maker": "maker 37447",
  "type": "Corbeilles de rue",
  "model": "model77981",
  "picture": "",
  "id": 1,
  "updatedAt": "2024-02-13T09: 25: 03+01: 00",
  "createdAt": "2024-02-13T09: 25: 03+01: 00"
}
*/
}

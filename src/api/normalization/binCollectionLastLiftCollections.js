import { getNestedValue } from '@/utils/object'

export function normalizeArray(array) {
  let res = []
  for (let item of array) {
    for (let subItem of item.vehicles) {
      for (let collection of subItem.binCollections) {
        res.push({
          vehicleId: subItem.vehicle?.id || '',
          vehicleName: subItem.vehicle?.name || '',
          collectedAt: collection.collectedAt,
          circuitId: getNestedValue(collection, 'round.id'),
          circuitName: getNestedValue(collection, 'round.shortName'),
          isAuthorized: collection.isAuthorized,
          isBlacklisted: collection.isBlacklisted,
          isCollected: collection.isCollected,
          isHighPoint: collection.isHighPoint,
          isIdentified: collection.isIdentified,
          isStopped: collection.isStopped,
          eventBinCollections: (collection.eventBinCollections || []).map(
            (evt) => {
              return {
                id: evt.id,
                name: evt.name,
                images: evt.images, //[url,url,url]
              }
            }
          ),
        })
      }
    }
  }
  return res
}

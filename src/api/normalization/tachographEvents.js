import {
  formatTime,
  formatTimeWithSeconds,
  adjustDateWithTimezone,
} from '@/utils/dates'
import {
  epsilonRound,
  makePercentage,
  metersToKilometers,
} from '@/utils/number'
import {
  getChronoColorFromType,
  getChronoIconPath,
  getChronoItemType,
} from '@/services/chrono-service'
import i18n from '@/i18n'
import {
  formatObjectValue,
  getNestedValue,
  getNestedValueFloat,
} from '@/utils/object'

export default (data) => {
  if (Object.keys(data).length === 0) {
    return {}
  }

  return {
    synthesis: normalizeChronoResponseSynthesis(data),
    details: normalizeChronoResponseDetails(data),
  }
}

/**
 * @todo Replace in favor of getNestedValue
 */
function formatArrayValue(array, value) {
  if (typeof array[value] === 'number') {
    return array[value]
  }

  return array[value] ? array[value] : ''
}

function formatObjectAddressV3(item) {
  const address = item.hasOwnProperty('address') ? item['address'] : ''
  const zipCode = item.hasOwnProperty('zipCode') ? item['zipCode'] : ''
  const city = item.hasOwnProperty('city') ? item['city'] : ''

  return [address, zipCode, city].join(' ').trim()
}

export function normalizeChronoResponseSynthesis(item) {
  return {
    startDay: formatObjectValue(item, 'start'),
    endDay: formatObjectValue(item, 'end'),
    amplitude: formatTimeWithSeconds(formatObjectValue(item, 'time')),
    nbStop: formatObjectValue(item, 'stopNumber'),
    distance: mapChronoDistanceKmToMFromItem(item),
    speed: epsilonRound(formatObjectValue(item, 'speed')),
    periodService: mapChronoServiceTimeFromItem(item),
    periodDrive: formatTime(formatObjectValue(item, 'drivingTime')),
    percentDrive: makePercentage(formatObjectValue(item, 'drivingRate')),
    periodAvailable: formatTime(formatObjectValue(item, 'availabilityTime')),
    percentAvailable: makePercentage(
      formatObjectValue(item, 'availabilityRate')
    ),
    periodWork: formatTime(formatObjectValue(item, 'workTime')),
    percentWork: makePercentage(formatObjectValue(item, 'workRate')),
    periodWorkAvailable: formatTime(
      formatObjectValue(item, 'availabilityWorkTime')
    ),
    percentWorkAvailable: makePercentage(
      formatObjectValue(item, 'availabilityWorkRate')
    ),
    maxPeriodRest: formatTime(formatObjectValue(item, 'restMaxTime')),
    periodRest: formatTime(formatObjectValue(item, 'restTime')),
    percentRest: makePercentage(formatObjectValue(item, 'restRate')),
    maxPeriodStart: formatTime(
      adjustDateWithTimezone(formatObjectValue(item, 'restMaxDatetime'))
    ),
  }
}

function translateType(type) {
  return i18n.t('location_module.chrono.' + type)
}

export function normalizeChronoResponseDetails(item) {
  let details = []

  if (item && item.hasOwnProperty('imputations')) {
    item.imputations.forEach((imputation) => {
      const badgeOn = !(
        imputation.driverName === '' && imputation.driverBadge === BADGE_OFF_ID
      )

      /**
       * Next time, please use two words minimum for variable naming i.g serviceTime intead of sevice
       */
      details.push({
        startDate: formatArrayValue(imputation, 'start'),
        endDate: formatArrayValue(imputation, 'end'),
        period: formatTimeWithSeconds(getNestedValueFloat(imputation, 'time')),
        type: translateType(imputation.type),
        typeAlt: imputation.type,
        color: getChronoColorFromType(imputation.type),
        iconPath: getChronoIconPath(formatArrayValue(imputation, 'type')),
        distance: mapChronoDistanceKmToMFromItem(imputation),
        service: mapChronoServiceTimeFromItem(imputation),
        driverName: formatArrayValue(imputation, 'driverName'),
        badgeOn,
        address: formatObjectAddressV3(imputation),
        lat: formatArrayValue(imputation, 'latitude'),
        lng: formatArrayValue(imputation, 'longitude'),
        periodDrive: formatTimeWithSeconds(
          formatArrayValue(imputation, 'drivingTime')
        ),
        periodWork: formatTimeWithSeconds(
          formatArrayValue(imputation, 'workTime')
        ),
        periodRest: formatTimeWithSeconds(
          formatObjectValue(imputation, 'restTime')
        ),
        periodAvailable: formatTimeWithSeconds(
          formatObjectValue(imputation, 'availabilityTime')
        ),
        zone: formatObjectValue(imputation, 'areaName'),
        /**
         * Km/h
         */
        speed: parseFloat(getNestedValueFloat(imputation, 'speed').toFixed(2)),
      })
    })
  }

  return details
}

/**
 * 60km => 0.06m
 * @param {*} distance
 */
export function mapChronoDistanceKmToMFromItem(item) {
  return metersToKilometers(getNestedValueFloat(item, 'distance'))
}

/**
 *
 * @param {Object} item Chrono detail item {serviceTime}
 * @returns {String} service time i.g 00:00:53
 */
export function mapChronoServiceTimeFromItem(item) {
  return formatTimeWithSeconds(getNestedValueFloat(item, 'serviceTime'))
}

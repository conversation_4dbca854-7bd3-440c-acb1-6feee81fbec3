import { extractIdFromRefLink } from '@/services/api-platform-service.js'
import { geoJsonToLatLngs } from '@/services/blackspot-service.js'

/**
 * API -> FRONT
 */
export default (item) => {
  if (!item?.geometry && !item?._embedded?.segments) {
    console.warn('Blackspot normalization geometry/segments missing', { item })
  }

  if (item._embedded) {
    item = { ...item, ...item._embedded }
  }

  const latLngs = geoJsonToLatLngs(item?.geometry?.geojson) ?? []

  if (latLngs.some((latLngItem) => !latLngItem[0] || !latLngItem[1])) {
    console.log({
      item,
    })
    //throw new Error('INVALID')
    console.warn('Invalid black spot (geoJson)', {
      item,
    })
  }

  let segmentsArr = item?.segments ?? item?._embedded?.segments ?? []
  segmentsArr = segmentsArr.map((segment) => {
    if (segment?.geoJson !== segment?.geometry?.geojson) {
      //Invalid data? e.g segment.geometry field is missing.
      /*console.warn('Blackspot segment part geojson parse warning', {
        item,
        segmentPart: segment,
      })*/
    }
    return {
      ...segment,
      latLngs: geoJsonToLatLngs(segment?.geoJson || segment?.geometry?.geojson),
    }
  })

  return {
    ...item, //Ideally, map the minimum necessary above and remove this line

    id: item.id ?? null,
    name: item.name ?? '',
    lat: item?.geometry?.latitude ?? null,
    lng: item?.geometry?.longitude ?? null,
    shapeName: item?.geometry?.shape ?? '', //cercle , polygone, linestring (if segment)
    radius: item?.geometry?.firstAttribute || null,
    latLngs, //Polygon (Use to render on map)
    geoJson: item?.geometry?.geojson, //Used during mutation (update)
    type: item.type, //Z (polygon/circle) or S (segment)
    segments: segmentsArr,
    constraintId:
      item?.constraintType?.id || extractIdFromRefLink(item, 'constraintType'),
    constraintLabel: item?.constraintType?.label,
    categoryId: item?.category?.id || extractIdFromRefLink(item, 'category'),
    categoryLabel: item?.category?.label,
    speedLimitId:
      item?.speedLimit?.id || extractIdFromRefLink(item, 'speedLimit'),
    speedLimitSpeed: item?.speedLimit?.speed,
    enabled: item?.active,
  }
}

/**
 * FRONT - API
 * @param {*} item
 */
export function mutateNormalize(item) {
  return {
    id: item.id ?? undefined,
    name: item.name ?? '',
    description: item.description ?? '',
    type: item.type ?? 'Z', //['Z','S'] Z = Zone (Circle/Polygon) S = Segment
    speedWeighting: 1,
    category: `/geored/black_spot/black_spot_categories/${item.categoryId}`,
    constraintType: `/geored/black_spot/constraint_types/${item.constraintId}`,
    speedLimit: `/geored/black_spot/speed_limits/${item.speedLimitId}`,
    active: item.enabled ?? true,
    segments: item.segments ?? undefined,
    geoJson: item.geoJson,
    latitude: item.lat ?? 0,
    longitude: item.lng ?? 0,
    radius: parseInt(item.radius ?? 0) ?? undefined,
    shape: item.shapeName ?? 'cercle', //cercle , polygone, linestring (if segment)
    xMin: item.xMin ?? 0,
    yMin: item.yMin ?? 0,
    xMax: item.xMax ?? 0,
    yMax: item.yMax ?? 0,
  }
}

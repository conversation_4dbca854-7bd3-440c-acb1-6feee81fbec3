import { normalizeZoneItemPolygon } from '@/services/map-service.js'
import { getFormattedAddress } from '@/utils/address'
import { getNestedValue } from '@/utils/object'
/**
 * Server stores Lat/Lng coordinates as Integer values.
 * @param {*} latOrLng
 * @returns
 */
function normalizeLatLng(latOrLng) {
  return latOrLng / 100000
}
function normalizeLatLngBackToAPIV3Format(latOrLng) {
  return Math.round(latOrLng * 100000)
}

// categoryId and typeId are grab from API Platform embedded property (type/category) (Found under _links object if response format is hal+json)
const getValueFromEmbeddedLink = (item, resourcePath, propertyName) => {
  return getNestedValue(
    item,
    [propertyName, `_links.${propertyName}.href`],
    '',
    {
      transform(value, originalValue = '', rootValue) {
        return parseInt(originalValue.split(resourcePath).join(''))
      },
    }
  )
}

/**
 * Normalize Zone to APIV3 entity format
 * @param {*} item
 */
export function mutateNormalize(item) {
  return {
    id: item.id,
    name: item.name,
    type: item.geometryType,
    share: item.isShared,
    shareEdition: item.isSharedEdition,
    specificBehavior: item.isSpecificBehavior ? 1 : null,
    internal: item.isInternal,
    analysis: item.isAnalysis,
    radius: item.radius,
    latitude: normalizeLatLngBackToAPIV3Format(item.lat),
    longitude: normalizeLatLngBackToAPIV3Format(item.lng),
    accessLatitude: item.accessLat
      ? normalizeLatLngBackToAPIV3Format(item.accessLat)
      : item.accessLat,
    accessLongitude: item.accessLng
      ? normalizeLatLngBackToAPIV3Format(item.accessLng)
      : item.accessLng,
    areaType: `/geored/area/area_types/${item.typeId}`,
    category: `/geored/area_categories/${item.categoryId}`,
    polygon: (item.polygon || [])
      .map(
        (arr) =>
          `(${normalizeLatLngBackToAPIV3Format(
            arr[1]
          )},${normalizeLatLngBackToAPIV3Format(arr[0])})`
      )
      .join(':'),
  }
}

export default function normalizeZoneItem(item) {
  const color = item.typeColor
    ? '#' + item.typeColor.split('#').join('')
    : 'grey'
  return {
    id: item.id,
    name: getNestedValue(item, 'name'), //Table
    polygon: normalizeZoneItemPolygon(item.polygon),
    lat: normalizeLatLng(item.latitude),
    lng: normalizeLatLng(item.longitude),
    accessLat: normalizeLatLng(item.accessLatitude),
    accessLng: normalizeLatLng(item.accessLongitude),
    isPolygon: item.type === 'P', //Table
    geometryType: (item.type || '').toUpperCase() === 'E' ? 'P' : item.type, //Eclipse is not supported
    radius: item.radius,
    iconSrc: item.typePicture ? `/img/zones/${item.typePicture}` : null,
    isInternal: item.internal, //Table
    isShared: item.share, //Table
    isSharedEdition: item.shareEdition, //Allow shared zones to be edited
    isSpecificBehavior: !!item.specificBehavior, // Allow zone for specific application ( Citimission = 1  )
    categoryId: getValueFromEmbeddedLink(
      item,
      '/geored/area_categories/',
      'category'
    ),
    typeId: getValueFromEmbeddedLink(
      item,
      '/geored/area/area_types/',
      'areaType'
    ),
    typeName: getNestedValue(item, 'typeName', ''), //Table
    categoryName: item.categoryName, //Table
    color,
    isAnalysis: item.analysis, //Table
    userLogin: item.userLogin,
    formattedAddress: getFormattedAddress(item, {
      //Table
      streetNumber: 'streetNumber',
      streetAddress: `street`,
      zipCode: `zipCode`,
      city: `city`,
    }),
  }
}

import { extractIdFromRefLink } from '@/services/api-platform-service.js'
import {
  datetimeToTimestamp,
  formatDatetimeWithSeconds,
} from '@/utils/dates.js'
import moment from 'moment'
import api from '@/api'

export default function normalizeValidityPeriodItem(item) {
  let normalized = {
    id: item.id ?? null,
    blackSpotId: extractIdFromRefLink(item, 'blackSpot'),
    startAt: item.startAt ?? '',
    endAt: item.endAt ?? '',
    startAtTimestamp: datetimeToTimestamp(item.startAt),
    endAtTimestamp: datetimeToTimestamp(item.endAt),
    startAtFormatted: formatDatetimeWithSeconds(item.startAt),
    endAtFomatted: formatDatetimeWithSeconds(item.endAt),
    model: [moment(item.startAt)._d, moment(item.endAt)._d],
  }
  return normalized
}

export function mutateNormalize(item) {
  if (!item.blackSpotId) {
    throw new Error('Expects item.blackSpotId')
  }
  if ((!item.startAt || item.endAt) && (!item.model || item.model.length < 2)) {
    throw new Error('Expects item.model or item.startAt/endAt')
  }

  return {
    id: item.id ?? null,
    blackSpot: `/geored/black_spot/black_spots/${item.blackSpotId}`,
    startAt: moment(item.model[0]).format(api.APIV3RequestDatetimeFormat),
    endAt: moment(item.model[1]).format(api.APIV3RequestDatetimeFormat),
  }
}

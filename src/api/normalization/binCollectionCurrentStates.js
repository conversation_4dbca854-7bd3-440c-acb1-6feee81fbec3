/**
 * @namespace normalizations
 * @category normalizations
 * @subcategory containersModule
 * @module binCollectionCurrentStates
 *
 * @todo Try to convert this to .ts
 **/

import { getFormattedAddress } from '@/utils/address'
import { getNestedValue } from '@/utils/object'

/**
 * If APIV3 response is a single object (e.g hal+json or rawResponse used), this handler is called once.
 * @param {*} res
 * @returns
 */
export default function normalizeHalJsonResponse(res) {
  if (res?.data?._embedded?.item) {
    res.data._embedded.item = res.data._embedded.item.map(normalizeItem)
  }
  if (res.id) {
    return {
      ...normalizeItem(res, 0, true),
      original: res,
    }
  }
  return res
}

/**
 * Normalize each item in the response
 * @param {*} item
 * @returns
 */
export function normalizeItem(item, index, isDetails = false) {
  let r = {
    id: item.id,
    name: item.chipNumber,
    lat: item.latitude,
    lng: item.longitude,
    stateId: item.stateId || 4, //Defaults to 2
    typeId: getNestedValue(item, 'typeId'),
    binId: getNestedValue(item, 'binId'),
    collectedAt: getNestedValue(item, 'collectedAt'),
  }
  if (isDetails) {
    r = {
      ...r,
      tankNumber: item.tankNumber,
      chipNumber: item.chipNumber,
      address: getFormattedAddress(item),
      referenceRoundName: item.referenceRoundName,
      vehicleName: item.vehicleName,
    }
  }
  return Object.freeze(r)
}

/**
 * {
    "_links": {
        "self": {
            "href": "/bin_collection/current_states/1968099"
        }
    },
    "binCollectionId": 192395894,
    "collectedAt": "2023-02-24T05:17:54+01:00",
    "chipNumber": "1340000160F6C0AB",
    "weight": 0,
    "side": 1,
    "empty": 0,
    "code": 111,
    "buttons": "0000000000000000",
    "latitude": 44.05618,
    "longitude": 5.047263,
    "point": {
        "latitude": 44.05618,
        "longitude": 5.047263
    },
    "received_at": "2023-01-16T05:46:02+01:00",
    "flags1": 0,
    "flags2": 0,
    "flags3": 0,
    "flagsWeight": 0,
    "liftNumber": 0,
    "vehicleId": 37259,
    "vehicleName": "D215 - T01",
    "vehicleCategoryId": 10220,
    "vehicleCategoryName": "BOM",
    "clientId": 1032,
    "clientName": "Cove",
    "roundId": 31966,
    "roundName": "T01_-_LUNDI_OM",
    "roundCategoryId": 3320,
    "roundCategoryName": "cove",
    "roundExecutionId": 3502054,
    "areaId": 0,
    "streetNumber": "71",
    "roadNumber": "",
    "street": "Rue Porte d'Orange",
    "city": "Carpentras",
    "zipCode": "84200",
    "borough": "",
    "department": "Vaucluse",
    "region": "Provence-Alpes-Côte d'Azur",
    "country": "France",
    "longitudeMapmatch": 5.04729,
    "latitudeMapmatch": 44.05618,
    "pointMapmatch": {
        "latitude": 44.05618,
        "longitude": 5.04729
    },
    "collectionDate": "2023-02-24",
    "id": 1968099,
    "updatedAt": "2024-01-14T12:53:07+01:00",
    "createdAt": "2024-01-07T23:02:50+01:00",
    "authorized": true,
    "collected": true,
    "identified": true,
    "stopped": false,
    "blackListed": false,
    "weightUnderLoad": false,
    "weightOnLoad": false
}
 */

import { isIsoDate } from '@/utils/dates'

export default (templates) => {
  return templates.map((template) => {
    let t = {
      id: template.id,
      label: template.label,
      functionId: template.functionId,
    }

    if (template.data) {
      const selection = template.data.reduce((acc, curr) => {
        const [key, value] = curr.split(': ')

        acc[key] = JSON.parse(value, (_, v) => {
          // Handle selectedDateRanges
          return isIsoDate(v) ? new Date(v) : v
        })

        return acc
      }, {})

      const {
        selectedVehiclesIds = [],
        selectedDriversIds = [],
        selectedCircuitsIds = [],
        selectedDateRanges = [],
      } = selection

      t.data = {
        selectedVehiclesIds,
        selectedDriversIds,
        selectedCircuitsIds,
        selectedDateRanges,
      }
    }
    return t
  })
}

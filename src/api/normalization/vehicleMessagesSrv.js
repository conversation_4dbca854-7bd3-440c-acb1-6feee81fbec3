import { getNestedValue } from '@/utils/object'
import { getEnvValue } from '@/services/env-service.js'
import { formatDate } from '@/utils/dates'

let counter = -1
const isTestStatusMode = getEnvValue('messagesTestStatus') === '1'

export default function (item) {
  if (isTestStatusMode) {
    counter = counter === 8 ? 0 : counter + 1 // 0..10
  }
  return {
    id: getNestedValue(item, 'id'),
    messageId: getNestedValue(item, 'messageId'),
    message: getNestedValue(item, 'message'),
    status: isTestStatusMode
      ? counter
      : getNestedValue(item, 'status', 0, {
          allowZero: true,
        }),
    sender: getNestedValue(item, 'sender'),
    vehicleId: getNestedValue(item, 'vehicleId'),
    vehicleName: getNestedValue(item, 'vehicleName'),
    categoryId: getNestedValue(item, 'categoryId'),
    categoryName: getNestedValue(item, 'categoryName'),
    sendAt: formatDate(getNestedValue(item, 'sendAt')),
  }
}

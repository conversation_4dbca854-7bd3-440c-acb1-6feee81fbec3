import { getNestedValue } from '@/utils/object'
import moment from 'moment'
import { formatDatetime, adjustDateWithTimezone } from '@/utils/dates'
export default function normalizeHandler(item) {
  const datetime = getNestedValue(item, 'datetime', '', {
    transform: (v) => (moment(v).isValid() ? adjustDateWithTimezone(v) : ''),
  })
  return {
    vehicleId: getNestedValue(item, 'vehicleId'),
    vehicleCategoryId: getNestedValue(item, 'vehicleCategoryId'),
    vehicleName: getNestedValue(item, 'vehicleName'),
    vehicleCategoryName: getNestedValue(item, 'vehicleCategoryName'),
    registrationPlate: getNestedValue(item, 'registrationPlate'),
    datetime,
    datetimeStamp: datetime ? datetime.valueOf() : '',
    datetimeFormatted: datetime
      ? formatDatetime(adjustDateWithTimezone(datetime))
      : '',
  }
}

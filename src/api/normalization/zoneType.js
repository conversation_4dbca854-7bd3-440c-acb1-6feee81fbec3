import {
  zoneTypeIcons,
  normalizeZoneTypeIconItem,
} from '@/composables/zones-module.js'

function getIconKeyFromFileName(fileName = '') {
  return Object.keys(zoneTypeIcons).find((key) => fileName.includes(key))
}

export default function normalizeZoneType(item) {
  let hexColor = '#' + item.color.split('#').join('')

  let iconKey = getIconKeyFromFileName(item.picture)
  let iconItem = normalizeZoneTypeIconItem(iconKey)

  return {
    id: item.id,
    name: item.name,
    color: hexColor,
    isDumping: item.dumping === 1,
    iconSrc: item.picture ? `/img/zones/${item.picture}` : null,
    icon: iconItem,
  }
}

export function mutateNormalize(item) {
  if (!item.iconFileName) {
    console.warn('Missing item property (iconFileName', {
      item,
    })
  }
  return {
    id: item.id,
    name: item.name,
    color: (item.color || '#808080').split('#').join(''),
    dumping: item.isDumping ? 1 : 0,
    picture: item.icon?.iconFileName || 'icone_depot_16x16.gif',
    class: '',
  }
}

import { getNestedValue } from '@/utils/object'

export default function normalizeHalJsonResponse(res) {
  if (res?.data?._embedded?.item) {
    res.data._embedded.item = res.data._embedded.item.map(mapBinsItem)
  }
  if (res.id) {
    return {
      ...mapBinsItem(res, 0, true),
      original: res,
    }
  }
  return res
}

function mapBinsItem(item, index, details = false) {
  let r = {
    id: item.id,
    lat: item.latitude,
    lng: item.longitude,
    typeId: getNestedValue(item, 'typeId'),
    t: 'b', //bin
    stateId: 2, //same as not collected or not planned
  }
  if (details) {
    r = {
      ...r,
      volume: getNestedValue(item, 'volume'),
      tankNumber: item.tankNumber,
      chipNumber: item.chipNumber,
      state: item.state ?? null,
    }
  }
  return r
}

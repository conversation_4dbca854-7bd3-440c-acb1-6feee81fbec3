import { getNestedValue } from '@/utils/object'
import {
  APIV3RequestDatetimeFormat,
  APIV2RequestDatetimeFormat,
} from '@/config/simpliciti-apis.js'
import moment from 'moment'
import { getFormattedAddress } from '@/utils/address'

/**
 *
 * Transform to an APIV2 like structure (already in used by Location module in front-end)
 *
 * @param {*} item
 * @returns
 */
export default function normalizeItem(item) {
  const optionToFormatDatetimeFromAPIV3ToAPIV2Format = {
    transform(value) {
      if (!value) {
        return null
      }
      return moment(value, APIV3RequestDatetimeFormat).format(
        APIV2RequestDatetimeFormat
      ) //e.g 2023-06-26 23:59:54
    },
  }
  const getNestedDatetime = (item, path) =>
    getNestedValue(item, path, '', optionToFormatDatetimeFromAPIV3ToAPIV2Format)

  const getNestedAPIV3LinestringAsAPIV2Format = (item, path = 'linestring') =>
    getNestedValue(
      item,
      path,
      {},
      {
        transform(value) {
          if (value.coordinates) {
            return value.coordinates.map((coords) => coords.join(' ')).join(',') //APIV2 format: //'1.9061775925926 47.944810462963,1.9061775925926 47.944810462963,1.9062496296296 47.944877962963',
          }
          if (Object.keys(value).length === 0) {
            return '' //no linestring attr present in response
          }
          return value
        },
      }
    )

  const toFixedTwoOptions = {
    transform: (v) => v.toFixed(2),
  }

  return {
    véhicule: {
      id: getNestedValue(item, 'vehicleId'),
      nom: getNestedValue(item, 'vehicleName'),
      immatriculation: getNestedValue(item, 'registrationPlate'),
    },
    historique: {
      distance_troncons: getNestedValue(item, 'distance'),
      positions_nb: getNestedValue(item, 'positionsNumber'),
      vitesse: getNestedValue(item, 'speed', 0, toFixedTwoOptions),
      vitesse_max: getNestedValue(item, 'maxSpeed', 0, toFixedTwoOptions),
      duree_contact: getNestedValue(item, 'time'),
      duree_arret: getNestedValue(item, 'stopTime'),
      dh_debut_troncons: getNestedValue(item, 'start'),
      dh_fin_troncons: getNestedValue(item, 'stop'),
      dh_contact_on: getNestedDatetime(item, 'contactOn'),
      dh_contact_off: getNestedDatetime(item, 'contactOff'),
    },
    troncons: (item.sections || []).map((rawSection) => {
      return {
        //id_troncon: '485561687747769', //Unused
        date: getNestedValue(rawSection, 'date'),
        jour: getNestedValue(rawSection, 'weekDay'),
        semaine: getNestedValue(rawSection, 'weekNumber'),
        dh_contact_on: getNestedDatetime(rawSection, 'contactOn', null),
        dh_contact_off: getNestedDatetime(rawSection, 'contactOff', null),
        dh_trajet_deb: getNestedDatetime(rawSection, 'start'),
        dh_arret_deb: getNestedDatetime(rawSection, 'stopStart'),
        adresse_deb: getNestedValue(rawSection, 'startAddress'),
        cp_deb: getNestedValue(rawSection, 'startZipCode'),
        ville_deb: getNestedValue(rawSection, 'startCity'),
        longitude_deb: getNestedValue(rawSection, 'startLongitude'),
        latitude_deb: getNestedDatetime(rawSection, 'startLatitude'),
        zone_deb: getNestedValue(rawSection, 'areaNameStart', null),
        in_zoneinterne_deb: getNestedValue(rawSection, 'areaInternalStart'),
        dh_trajet_fin: getNestedDatetime(rawSection, 'end'),
        dh_arret_fin: getNestedDatetime(rawSection, 'stopEnd'),
        adresse_fin: getNestedValue(rawSection, 'endAddress'),
        cp_fin: getNestedValue(rawSection, 'endZipCode'),
        ville_fin: getNestedValue(rawSection, 'endCity'),
        longitude_fin: getNestedValue(rawSection, 'endLongitude'),
        latitude_fin: getNestedValue(rawSection, 'endLatitude'),
        zone_fin: getNestedValue(rawSection, 'areaNameEnd'),
        in_zoneinterne_fin: getNestedValue(rawSection, 'areaInternalEnd'),
        duree_arret: getNestedValue(rawSection, 'stopTime'),
        duree_trajet: getNestedValue(rawSection, 'time'),
        distance: getNestedValue(rawSection, 'distance', 0, {
          transform(value) {
            //M to Km
            return (value * 0.001).toFixed(2)
          },
        }),
        linestring: getNestedAPIV3LinestringAsAPIV2Format(
          rawSection,
          'linestring'
        ),
        linestring_mouvement: getNestedValue(
          rawSection,
          'movementLinestrings',
          [],
          {
            transform(value) {
              if (value instanceof Array && value.length > 0) {
                return value.map((subItem) => {
                  return {
                    linestring: getNestedAPIV3LinestringAsAPIV2Format(
                      subItem,
                      'linestring'
                    ),
                    couleur: getNestedValue(subItem, 'color'),
                  }
                })
              }
              return value
            },
          }
        ),
      }
    }),
  }
}

import { getNestedValue } from '@/utils/object'
import Vue from 'vue'
import { generateShortId } from '@/utils/crypto'
import { computeStatusLabel } from '@/services/alert-service'

export function normalizeItem(item = {}) {
  let isAck = !!getNestedValue(item, 'date_ack')
  const datetime = Vue.$date.adjustDateWithTimezone(
    getNestedValue(item, ['date_creation'])
  )
  let timestamp = datetime.valueOf()
  let vehicleName = getNestedValue(item, 'vehicule_nom')
  let vehicleCategory = getNestedValue(item, 'categorie_nom')

  return Object.freeze({
    id: generateShortId(),
    message_id: getNestedValue(item, ['message_id']),
    title: getNestedValue(item, ['alerte_titre']),
    vehicleName,
    vehicleCategory,
    type: getNestedValue(item, 'type'),
    message: getNestedValue(item, 'message'),
    address: getNestedValue(item, 'adresse'),
    lat: getNestedValue(item, 'latitude'),
    lng: getNestedValue(item, 'longitude'),
    isAck,
    timestamp,
    statusLabel: computeStatusLabel(isAck),
    datetimeFormatted: Vue.$date.formatDatetime(datetime),
    dateFormatted: Vue.$date.formatDate(datetime),
    timeFormatted: Vue.$date.formatTimeWithSeconds(datetime),
    vehicleLabel: vehicleCategory
      ? `${vehicleName} (${vehicleCategory})`
      : vehicleName,
  })
}

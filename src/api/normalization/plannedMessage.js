import api from '@/api'
import moment from 'moment'
import { formatDate, formatTime } from '@/utils/dates'
export default function normalizeItem(item) {
  const formatDateOptions = {
    inputFormat: api.APIV3RequestDatetimeFormat,
  }

  return {
    id: item.id,
    vehicleIds: [],
    vehicleNames: '',
    message: item?.message || item?.predefinedMessage?.message || '',
    startAt: moment(item.startAt, api.APIV3RequestDatetimeFormat)._d,
    endAt: moment(item.endAt, api.APIV3RequestDatetimeFormat)._d,
    hour: moment(item.hour, api.APIV3RequestDatetimeFormat)._d,
    startAtFormatted: formatDate(item.startAt, formatDateOptions),
    endAtFormatted: formatDate(item.endAt, formatDateOptions),

    hourFormatted: formatTime(item.hour, formatDateOptions),
    frequencyId: item?.frequency?.id,
    frequencyCode: item?.frequency?.code,
    client: item.client,
    dayId: item?.day?.id,
    dayCode: item?.day?.code,
    predefinedMessage: item?.predefinedMessage,
    updatedAt: moment(
      item.updatedAt,
      api.APIV3RequestDatetimeFormat
    )._d.getTime(),
  }
}

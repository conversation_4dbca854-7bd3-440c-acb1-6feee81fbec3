/**
 * @namespace api
 * @category api
 * @module history-api
 * @todo Refactor/Move all the logic into history-service
 * */

import moment from 'moment'
import { getEnvValue } from '@/services/env-service.js'
import api from '@/api'
import Vue from 'vue'
import { getFormattedAddress } from '@/utils/address'
import APIUrls, {
  APIV2RequestDatetimeFormat,
  APIV2RequestDateFormat,
  APIV3RequestDatetimeFormat,
} from '@/config/simpliciti-apis.js'
import i18n from '@/i18n'
import normalizeTripHistoryItem from '@/services/entities/trip-history-item.js'
import { getNestedValue } from '@/utils/object'
import { isUnitTest } from '@/utils/unit.js'
import store from '@/store'
import searchService from '@/services/search-service'
import hash from 'object-hash'
import { splitDateRangeByDay } from '@/utils/dates'

/**
 * @todo Those functions should be consumed/proxied-by by history-service only
 */
const scope = {
  getVehiclePositionsV2,
  getVehiclePositionsV3,
  getPositionsFromVehicle,
  getVehicleHistoryInfosFromAPIV2,
  getChartTripHistoryFromVehicleId,
  getTripHistoryFromVehicleId,
}

export default scope

/**
 * @todo Normalize API response
 * @param {*} vehicleId
 * @param {*} date
 * @param {*} options
 * @returns
 */
export async function getVehiclePositionsV3(vehicleId, date, options = {}) {
  const t = (d, h = 0, m = 0, s = 0) =>
    moment(date).hour(h).minute(m).second(s).format(APIV3RequestDatetimeFormat)
  const startDatetime = options.startDate
    ? moment(options.startDate).format(APIV3RequestDatetimeFormat)
    : t(date)
  const endDatetime = options.endDate
    ? moment(options.endDate).format(APIV3RequestDatetimeFormat)
    : t(date, 23, 59, 59)
  console.log('getVehiclePositionsV3', {
    vehicleId,
    date,
    startDatetime,
    endDatetime,
  })

  if (isUnitTest()) return []

  //Get date chunks to make one api call per day (case of circuits occurring on multiple days)
  const dateChunks = splitDateRangeByDay(
    startDatetime,
    endDatetime,
    APIV3RequestDatetimeFormat
  )

  const results = []

  for (const [chunkStart, chunkEnd] of dateChunks) {
    try {
      const response = await api.v3.get(APIUrls.APIV3_GET_SENSORS, {
        vehicleId,
        startDatetime: chunkStart,
        endDatetime: chunkEnd,
        positions: true,
        ...(options.queryParams || {}),
      })

      const data = response?.data?.[0] ?? null

      if (data) {
        results.push(data)
      }
    } catch (error) {
      console.error(`Error fetching chunk ${chunkStart} - ${chunkEnd}`, error)
    }
  }

  // Merge all results into a single response object
  const mergedResponse = {}

  for (const chunk of results) {
    for (const key in chunk) {
      const value = chunk[key]

      if (Array.isArray(value)) {
        mergedResponse[key] = mergedResponse[key] || []
        mergedResponse[key].push(...value)
      } else if (typeof value === 'object' && value !== null) {
        mergedResponse[key] = {
          ...(mergedResponse[key] || {}),
          ...value,
        }
      } else {
        mergedResponse[key] = value
      }
    }
  }

  return mergedResponse
}

/**
 * deprecated
 async function getVehiclePositionsSensorsV2(vehicleId, date, options = {}) {
   let startDate = moment(date)
   .hour(0)
   .minute(0)
   .second(0)
   .format(APIV2RequestDatetimeFormat)
   let endDate = moment(date)
   .hour(23)
   .minute(59)
   .second(59)
   .format(APIV2RequestDatetimeFormat)
   return ((
     await api.v2.post(
       `${APIUrls.APIV2_POSITIONS_GPS}?dateheure_debut=${startDate}&dateheure_fin=${endDate}&vehicule_id=${vehicleId}&groups=infos_complementaires,capteurs,capteurs_details,can`
       )
       ).data || [null])[0]
      }
*/

/**
 *
 * Used by Location module (Trip history details - table) to load positions and sensors configuration
 *
 * @param {*} vehicleId
 * @param {*} date
 * @param {Object} options.startDate
 * @returns {Array}
 */
async function getVehiclePositionsV2(vehicleId, date, options = {}) {
  const t = (d, h = 0, m = 0, s = 0) =>
    moment(date).hour(h).minute(m).second(s).format(APIV2RequestDatetimeFormat)

  let startDate = t(date)
  let endDate = t(date, 23, 59, 59)

  if (options.startDate) {
    startDate = moment(options.startDate).format(APIV2RequestDatetimeFormat)
  }
  if (options.endDate) {
    endDate = moment(options.endDate).format(APIV2RequestDatetimeFormat)
  }

  console.log('getVehiclePositionsV2', {
    vehicleId,
    date,
    startDate,
    endDate,
  })

  /*   return ((await sensorsService.getSensors(
    vehicleId,
    options.startDate,
    options.endDate
  )) || [null])[0] */

  return ((
    await api.v2.post(
      `${APIUrls.APIV2_POSITIONS_CAPTEURS}?dateheure_debut=${startDate}&positions=true&capteurs=false&dateheure_fin=${endDate}&vehicule_id=${vehicleId}`
    )
  ).data || [null])[0]
}

/**
 * Retrieves vehicle positions from a date (00:00:00 to 23:59:59)
 * @param {*} vehicleId
 * @param {*} date
 * @returns
 */
async function getPositionsFromVehicle(
  vehicleId,
  date = new Date(),
  fromTime = '',
  toTime = ''
) {
  console.debugVerbose(
    8,
    'getPositionsFromVehicle',
    vehicleId,
    date,
    fromTime,
    toTime
  )
  date = moment(date).format(APIV2RequestDateFormat)
  fromTime =
    (fromTime && encodeURIComponent(` ${fromTime}`)) || '%2000%3A00%3A00'
  toTime = (toTime && encodeURIComponent(` ${toTime}`)) || '%2023%3A59%3A59'
  let res

  res = (
    await api.v2.get(
      `${APIUrls.APIV2_POSITIONS}/${vehicleId}/${date}${fromTime}/${date}${toTime}`
    )
  ).data

  let data = (res && res.length > 0 && res[0]) || {} //response comes in first item

  if (res.error && res.error[0]) {
    Vue.$log.error(res.error[0])
    throw new Error(res.error[0].message || 'unknown error')
  }
  return data.positions || []
}

/**
 * Used by Diagnostics module to display analysis overview (After search validation)
 *
 * Retrieves history infos (such as averange speed, distance) and positions from a vehicle, date
 * @returns {Object} Vehicle infos or an empty object
 */
async function getVehicleHistoryInfosFromAPIV2(
  vehicleId,
  dateOrDates,
  options = {}
) {
  let datesArr = dateOrDates instanceof Array ? dateOrDates : [dateOrDates]

  if (!vehicleId) {
    throw new Error('At least one vehicle must be specified')
  }

  if (datesArr.length === 0) {
    throw new Error('At least one date is required')
  }

  let formattedDates = datesArr.map((date) =>
    moment(date).format(APIV2RequestDateFormat)
  )
  let formattedDatesAsString = formattedDates.join(',')

  let startTimeAsString = moment(datesArr[0]).format('HH:mm')
  let endTimeAsString = moment(datesArr[datesArr.length - 1]).format('HH:mm')
  if (datesArr.length === 1) {
    endTimeAsString = '23:59' //If one date is selected, take the whole day
  }
  //Retrieve Hour/minutes selected in search Form
  let selectedTimeRangeStart = searchService.getRangeTimeSelected(
    datesArr[0]
  ).start
  let selectedTimeRangeEnd = searchService.getRangeTimeSelected(datesArr[0]).end
  if (selectedTimeRangeStart && selectedTimeRangeEnd) {
    startTimeAsString = moment(selectedTimeRangeStart).format('HH:mm')
    endTimeAsString = moment(selectedTimeRangeEnd).format('HH:mm')
    if (
      moment(selectedTimeRangeStart).format('YYYY-MM-DD HH:mm') ===
      moment(selectedTimeRangeEnd).format('YYYY-MM-DD HH:mm')
    ) {
      endTimeAsString = '23:59'
    }
  }

  let shouldApplyTheTimeToFirstAndLastDateOnly =
    options.shouldApplyTheTimeToFirstAndLastDateOnly !== undefined
      ? options.shouldApplyTheTimeToFirstAndLastDateOnly
      : true
  let data = (
    await api.v2.post(
      `${APIUrls.APIV2_HISTORIQUE_BY_VEHICLE_DATES}?vehicule_id=${vehicleId}&dates=${formattedDatesAsString}&linestring=true&heure_debut=${startTimeAsString}&heure_fin=${endTimeAsString}&premier_dernier_jour=${shouldApplyTheTimeToFirstAndLastDateOnly}`
    )
  ).data

  //Temporal fix: If multiple dates, cumulate results
  let cumulatedDataResult = null
  const cumulateData = (rawItem) => {
    console.log('cumulateData', {
      cumulatedDataResult: { ...cumulatedDataResult },
      rawItem,
    })
    if (!cumulatedDataResult) {
      cumulatedDataResult = rawItem
    } else {
      ;['duree_troncon', 'distance_troncon'].forEach((fieldToCumulate) => {
        cumulatedDataResult[fieldToCumulate] += rawItem[fieldToCumulate]
      })
      ;['dh_activ_fin', 'adresse_fin', 'cp_fin', 'ville_fin'].forEach(
        (fieldToAssign) => {
          cumulatedDataResult[fieldToAssign] = rawItem[fieldToAssign]
        }
      )
    }
  }

  let normalizedData = {}
  Object.keys(data || {}).forEach((d) => {
    //Temporal fix: If multiple dates, cumulate results
    if (datesArr.length > 1) {
      data[d].forEach((rawItem) => {
        cumulateData(rawItem)
      })
    }

    normalizedData[d] = (data[d] || []).map(normalizeTripHistoryItem)
  })

  /*console.log('getVehicleHistoryInfosFromAPIV2::normalizedData', {
    normalizedData,
  })*/

  let response = {}

  //Temporal fix: If multiple dates, cumulate results
  if (datesArr.length > 1) {
    response = normalizeTripHistoryItem(cumulatedDataResult || {})
    response.isCumulated = true
  } else {
    response =
      normalizedData[formattedDates[0]] &&
      normalizedData[formattedDates[0]].length > 0
        ? normalizedData[formattedDates[0]][0]
        : null
  }

  /*
  console.log('getVehicleHistoryInfosFromAPIV2::response', {
    response,
    cumulated: datesArr.length > 1,
    cumulatedDataResult,
  })*/

  return response
}

/**
 * Fetch trip history steps using APIV3.
 *
 * This function is used by the Location module and the Latest passed vehicles (TripHistoryList and Map).
 *
 * @param {number} vehicleId - The ID of the vehicle for which to fetch trip history steps.
 * @param {Date} date - The date for which to fetch trip history steps. Defaults to the current date.
 * @param startDate - Param to use as startDate if provided
 * @param endDate - Param to use as endDate if provided
 * @returns {Promise} A Promise that resolves with the trip history steps fetched from APIV3.
 */
export async function getTripHistoryFromVehicleId(
  vehicleId,
  date = new Date(),
  startDate = '',
  endDate = ''
) {
  console.debugVerboseScope(
    ['location_module', 'history'],
    5,
    'getTripHistoryFromVehicleId',
    {
      vehicleId,
      date,
    }
  )

  const APIV3RequestDatetimeFormat = 'YYYY-MM-DDTHH:mm:ss' //[Z]

  const apiv3InternalHandler = async () => {
    //Get start and end from startDate and endDate params if provided, otherwise from date
    const start =
      startDate && startDate !== ''
        ? moment(startDate).format(APIV3RequestDatetimeFormat)
        : searchService.getRangeTimeSelected(date).start
    const end =
      endDate && endDate !== ''
        ? moment(endDate).format(APIV3RequestDatetimeFormat)
        : searchService.getRangeTimeSelected(date).end

    const dateChunks = splitDateRangeByDay(
      start,
      end,
      APIV3RequestDatetimeFormat
    )

    let rawResponse = null

    for (const [start, end] of dateChunks) {
      try {
        const response = await api.historySectionsSrv.getAll({
          vehicleId,
          start,
          end,
          linestring: true,
          linestringMovements: true,
        })

        const firstItem = response?.[0]
        if (firstItem) {
          if (!rawResponse) {
            // First valid object becomes the base
            rawResponse = { ...firstItem }
          } else {
            // Merge arrays from firstItem into rawResponse
            for (const key of Object.keys(firstItem)) {
              if (Array.isArray(firstItem[key])) {
                rawResponse[key] = rawResponse[key] || []
                rawResponse[key] = rawResponse[key].concat(firstItem[key])
              } else {
                rawResponse[key] = rawResponse[key] ?? firstItem[key]
              }
            }
          }
        }
      } catch (error) {
        console.error(
          `Failed to fetch history for chunk ${start} - ${end}`,
          error
        )
      }
    }

    return rawResponse ? [rawResponse] : []
  }

  if (getEnvValue('use_apiv2') === '1') {
    throw new Error('APIV2 is no longer supported')
  }

  return await apiv3InternalHandler()
}

/**
 * Retrieves one vehicle trip history (normalized for a vertical chart)
 * @param {*} vehicleId
 * @param {*} date
 * @param startDate
 * @param endDate
 * @param data
 * @returns {Array} Normalized chart dataset
 * @todo Refactor: Instead of historyApi, Chart component (TripHistoryDetails) should be responsable of transofrming API response into chart normalized dataset
 */
export async function getChartTripHistoryFromVehicleId(
  vehicleId,
  date = new Date(),
  startDate = '',
  endDate = '',
  data = null
) {
  //  Helper functions:

  function pushItem(item) {
    item.id = hash(item)
    nodesGroupedByDate.push(item)
  }

  function createNode(tripStepNumber, trip, type = 'contact_on') {
    let label = ``,
      nodeDate = ``

    let prefix = ['contact_on', 'play'].includes(type) ? '_deb' : '_fin'

    let address = getFormattedAddress(trip, {
      streetAddress: `adresse${prefix}`,
      zipCode: `cp${prefix}`,
      city: `ville${prefix}`,
      useZone: trip[`in_zoneinterne${prefix}`],
      zone: `zone${prefix}`,
    })

    if (type === 'contact_on') {
      label = `${i18n.t('location.history_tab.item.contact_on')}
      <br/><br/>
      ${address}
      `
      nodeDate = Vue.$date.formatTimeWithSeconds(
        moment(trip.dh_contact_on, APIV2RequestDatetimeFormat)
      )
    }
    if (type === 'play') {
      label = `${i18n.t('common.Départ')} ${address}
      <br/><br/>
      ${i18n.t('location.details.circuit_tab.travel_time')} : ${getTimeDuration(
        trip.dh_trajet_deb,
        trip.dh_arret_deb
      )}  &nbsp;&nbsp;  ${i18n.t(
        'location.history_tab.item.traveled_distance'
      )} : ${trip.distance} Km
      `
      nodeDate = Vue.$date.formatTimeWithSeconds(
        moment(trip.dh_trajet_deb, APIV2RequestDatetimeFormat)
      )
    }

    if (type === 'flag') {
      nodeDate = Vue.$date.formatTimeWithSeconds(
        moment(trip.dh_trajet_fin, APIV2RequestDatetimeFormat)
      )
      label = `${i18n.t('common.Arrivée')} ${address}`
    }

    if (type === 'contact_off') {
      label = `${i18n.t('location.history_tab.item.contact_off')}
                <br/><br/>
                ${address}
                `
    }

    let object = {
      step: tripStepNumber,
      date: nodeDate,
      label: label,
      type,
      lng: trip[`longitude${prefix}`],
      lat: trip[`latitude${prefix}`],
      linestring: trip.linestring,
    }

    delete object.id
    object.id = hash(object)
    return object
  }

  async function parseItem(item) {
    let newItem = {
      nodes: [],
    }
    let trip

    for (let index in item.troncons) {
      trip = item.troncons[index]

      let title = Vue.$date.formatDate(
        moment(trip.date, APIV2RequestDateFormat)
      )

      if (newItem.title && newItem.title != title) {
        pushItem(newItem)
        newItem = {}
      }

      if (!newItem.title) {
        newItem.title = title

        //Trip history head infos (averages)

        newItem.fromDatetime = moment(
          getNestedValue(item, 'historique.dh_contact_on')
        )
        newItem.toDatetime = moment(
          getNestedValue(item, 'historique.dh_contact_off')
        )

        newItem.tronconDistance = getNestedValue(item, [
          'historique.distance_troncons', //invalid?
          'historique.distance_troncon',
        ])
        newItem.averageSpeed = item.historique.vitesse || ''
      }

      let stepNumber = parseInt(index) + 1

      if (trip.dh_contact_on) {
        newItem?.nodes?.push(createNode(stepNumber, trip, 'contact_on'))
      }

      newItem?.nodes?.push(createNode(stepNumber, trip, 'play'))

      newItem?.nodes?.push(createNode(stepNumber, trip, 'flag'))

      if (trip.dh_contact_off) {
        newItem?.nodes?.push(createNode(stepNumber, trip, 'contact_off'))
      }
    }
    pushItem(newItem)
  }

  //Get data
  data =
    data ||
    (await getTripHistoryFromVehicleId(vehicleId, date, startDate, endDate))

  let nodesGroupedByDate = []
  //Group data by date
  await Promise.all(data.map(parseItem))

  //Group data by steps
  //  nodesGroupedByDate = {title,nodes:{step}} ==> {title (date) ,steps:[{stepNumber,nodes:[]}]}
  nodesGroupedByDate.forEach((grouped) => {
    grouped.steps = []
    let stepGroup = {
      stepNumber: 1,
      nodes: [],
    }

    if (grouped.nodes) {
      grouped.nodes.forEach((node) => {
        if (stepGroup.stepNumber != node.step) {
          grouped.steps.push(stepGroup)
          stepGroup = {
            stepNumber: node.step,
            nodes: [],
          }
        }
        stepGroup.nodes.push(node)
      })
    }

    grouped.steps.push(stepGroup)
    delete grouped.nodes
  })

  return nodesGroupedByDate
}

function getTimeDuration(from, to) {
  let then = moment(from, APIV2RequestDatetimeFormat)._d
  let ms = moment(to, APIV2RequestDatetimeFormat).diff(moment(then))
  let d = moment.duration(ms)
  return Math.floor(d.asHours()) + moment.utc(ms).format('[h] mm[m] ss[s]')
}

<template lang="pug">
TLayout(
  :syncWithVuex="false",
  :sidebar="true",
  :menu="true",
  :menuCollapsed="menuCollapsed",
  :menuFullCollapse="true",
  :menuToggle="true",
  :rightMenu="true",
  :rightMenuBottom="!searchModuleSelectionView"

  @onMenuCollapsed="value=>menuCollapsed=value"
)
  template(v-slot:sidebar)
    Sidebar
  template(v-slot:menu)
    DisplayOptions(
      v-show="lateralMenuMode=='options'"
    )
    SearchWrapper(
      v-show="lateralMenuMode==='search'"
      :back-text="searchParams === null ? null : $t('search_module.back_to_location')"
      button-pressed-event-key="diagnostics-module"
      @search-input="onSearchModuleSelectionValidate",
      @search-view-change="onSearchModuleViewChange",
      @search-clear="onSearchModuleClearSelection"
    )
      template(v-slot:search-module-date-picker)
        label.date-picker-label
          span {{$t('diagnostics.search.datetimepicker_label')}}
          div(v-b-tooltip.hover.viewport="$t('diagnostics.datepicker_tooltip')")
            em.fa.fa-info-circle
        DiagnosticsDatePicker(
          @onDateSelection="onDateSelection"
          @onDateClear="onDateClear"
        )
      template(v-slot:search-module-results)
        .row.m-0.p-0
          .col-12(v-if="isLoading && !hasPositionAnalysisResults")
            Spinner
          .col-12(v-show="!isLoading && !isOverviewInformationAvailable  && positionsArray.length === 0")
            span {{ $t('diagnostics.no_results') }}

          .col-12(
            v-show="!isLoading && isOverviewInformationAvailable && !hasPositionAnalysisResults"
          )
            DiagnosticAnalysisOverview(
              :date="$date.formatDate(selectedDate)",
              :vehicleId="selectedVehicleId",
            )


          .col-12(
            v-if="positionsArray.length !== 0 && hasPositionAnalysisResults"
          )
            DiagnosticsAnalysis(
              :analysis="analysisResult",
              :isSinglePosition="!!selectedItem",
              :position="selectedItem"
            )
  template(v-slot:right_menu)
    SimplicitiMap
  template(v-slot:right_menu_bottom)
    DiagnosticsBottom(
      v-if="isBottomVisible"
      :vehicleName="vehicleName",
      :datesFormatted="datesFormatted",
      :date="selectedDate"
      @calendarClick="switchBackToSearchSelection",
      @onDayChange="onSearchModuleSelectionValidate"
    )
      DiagnosticsCharts(
          v-if="!!selectedVehicleId&&!!selectedDate"
          :vehicleId="selectedVehicleId"
          :date="selectedDate"
          :isTest="$router.currentRoute.name==='test'||isTest"
          @onSelection="onSelection"
        )
</template>

<script>
import { mapGetters } from 'vuex'
import Sidebar from '@c/shared/Sidebar/Sidebar.vue'
import TLayout from '@c/shared/TLayout/TLayout.vue'
import SearchWrapper from '@c/shared/SearchModule/SearchWrapper/SearchWrapper.vue'
import SimplicitiMap from '@c/shared/SimplicitiMap/SimplicitiMap.vue'
import DiagnosticsBottom from '@c/diagnostic/DiagnosticsBottom.vue'
import DiagnosticsAnalysis from '@c/diagnostic/DiagnosticsAnalysis.vue'
import DiagnosticAnalysisOverview from '@c/diagnostic/DiagnosticAnalysisOverview.vue'
import DiagnosticsCharts from '@c/diagnostic/DiagnosticsCharts.vue'
import DiagnosticsDatePicker from '@c/diagnostic/DiagnosticsDatePicker.vue'
import DisplayOptions from '@c/diagnostic/DisplayOptions.vue'
import Spinner from '@c/shared/Spinner.vue'
import { analyzeHistorySegment } from '@/services/history-service.js'
import { getQueryStringValue } from '@/utils/querystring'
import { createReplayController } from '@/services/diagnostics-service.js'
import { queueOperationOnce } from '@/utils/promise.js'
import moment from 'moment'
import i18n from '@/i18n'

function newState() {
  return {
    isSearchComingFromAnotherModule: false,
    onDateSelectionIgnoredTimes: 0,
    isBottomVisible: true,
  }
}

export default {
  name: 'DiagnosticModule',
  components: {
    Spinner,
    Sidebar,
    TLayout,
    SearchWrapper,
    SimplicitiMap,
    DiagnosticsBottom,
    DiagnosticsAnalysis,
    DiagnosticAnalysisOverview,
    DiagnosticsCharts,
    DiagnosticsDatePicker,
    DisplayOptions,
  },
  /**
   * @todo Add right check for vehicle tab
   */
  provide() {
    let searchFormTabs = ['disabled']

    searchFormTabs = [
      {
        label: i18n.t(`common.Véhicule`),
        value: 'vehicle',
      },
    ]

    return {
      //skipPositionMarkerPopupDetailsFetch: true,
      showPositionAnalysisButton: false,
      replayController: (window.replayController = createReplayController()),
      canOnlyCheckItemsSMTree: true,
      searchFormTabs,
      enableRenderVehiclePositionsMarkers: true,
      TLayoutIsResizeableExpandMode: true,
      showSaveSelectionButton: false,
    }
  },
  props: {
    searchParams: {
      type: Object,
      default: null,
    },
  },
  data() {
    return newState()
  },
  computed: {
    ...mapGetters({
      positions: 'diagnostics/positions',
    }),
    hasPositionAnalysisResults() {
      return (
        Object.keys(this.analysisResult || {}).length > 0 ||
        Object.keys(this.selectedItem || {}).length > 0
      )
    },
    searchSelection() {
      return this.$store.getters['search_module/getSelection']
    },
    /**
     * Single date (Today 00:00:00 as default)
     */
    selectedDate() {
      if (this.searchSelection.selectedDateRanges.length > 0) {
        return this.searchSelection.selectedDateRanges[0][0]
      }

      return moment(new Date()).hour(0).minute(0).second(0).toDate()
    },
    vehicleName() {
      let selectedVehicle =
        this.$store.getters['search_module/getSelectedVehicles'][0] || {}
      return selectedVehicle.name || ''
    },
    /**
     * Single vehicle id (Required)
     */
    selectedVehicleId() {
      if (this.isTest) {
        return 54466
      }
      return this.searchSelection.selectedVehiclesIds[0]
    },
    isTest() {
      return getQueryStringValue('test') === '1' && !this.$env.isProduction()
    },
    /**
     * Same as brushStartTimestamp
     */
    datetimeFrom() {
      let timestamp = this.$store.state.diagnostics.brushStartTimestamp

      if (
        this.positions.length > 0 &&
        timestamp < this.positions[0].timestamp
      ) {
        timestamp = this.positions[0].timestamp
      }

      return timestamp ? new Date(timestamp) : null //moment(this.selectedDate).hour(0).minute(0).second(0)._d.getTime()
    },
    /**
     * Same as brushEndTimestamp
     */
    datetimeTo() {
      let timestamp = this.$store.state.diagnostics.brushEndTimestamp
      if (
        this.positions.length > 0 &&
        timestamp > this.positions[this.positions.length - 1].timestamp
      ) {
        timestamp = this.positions[this.positions.length - 1].timestamp
      }
      return timestamp ? new Date(timestamp) : null //moment(this.selectedDate).hour(23).minute(59).second(59)._d.getTime()
    },
    datesFormatted() {
      let { startDatetime, endDatetime } = this.$store.getters[
        'diagnostics/getDatetimeRangeGivenSelectedDate'
      ](this.selectedDate)

      return `${this.$date.formatDatetime(
        this.datetimeFrom ? this.datetimeFrom : startDatetime
      )} - ${this.$date.formatDatetime(
        this.datetimeTo ? this.datetimeTo : endDatetime
      )}`
    },
    menuCollapsed: {
      set(value) {
        this.$store.state.diagnostics.menuCollapsed = value
      },
      get() {
        return this.$store.state.diagnostics.menuCollapsed
      },
    },
    analysisResult: {
      set(value) {
        this.$store.state.diagnostics.analysisResult = value
      },
      get() {
        return this.$store.state.diagnostics.analysisResult
      },
    },
    selectedItem: {
      set(value) {
        this.$store.state.diagnostics.chartSelectedItem = value
      },
      get() {
        return this.$store.state.diagnostics.chartSelectedItem
      },
    },
    lateralMenuMode: {
      set(value) {
        this.$store.state.diagnostics.lateralMenuMode = value
      },
      get() {
        return this.$store.state.diagnostics.lateralMenuMode
      },
    },
    isOverviewInformationAvailable() {
      return (
        Object.keys(this.$store.state.diagnostics.vehicleHistoryOverview || {})
          .length > 0 &&
        !!this.$store.state.diagnostics.vehicleHistoryOverview.vehicleName
      )
    },
    isLoading() {
      return !this.$store.state.diagnostics.isVehicleHistoryOverviewLoaded
    },
    /*isLoading: {
      set(value) {
        this.$store.state.diagnostics.isLoading = value
      },
      get() {
        return this.$store.state.diagnostics.isLoading
      },
    },*/
    positionsArray: {
      get() {
        return this.$store.state.diagnostics.positions
      },
      set(value) {
        this.$store.state.diagnostics.positions = value
      },
    },
    searchModuleSelectionView() {
      return this.$store.state.search_module.view === 'selection'
    },
  },
  watch: {
    /**
     * @warn Modifying state or dispatching actions from watches might have a negative performance impact
     */
    datetimeFrom() {
      queueOperationOnce(
        `diagnostics_${this._uid}_onSelectedDateRangeChange`,
        () => this.onSelectedDateRangeChange(true)
      )
    },
    /**
     * @warn Modifying state or dispatching actions from watches might have a negative performance impact
     */
    datetimeTo() {
      queueOperationOnce(
        `diagnostics_${this._uid}_onSelectedDateRangeChange`,
        () => this.onSelectedDateRangeChange(true)
      )
    },
    /** React to user choosing a position in the chart
     * @warn Modifying state or dispatching actions from watches might have a negative performance impact
     */
    selectedItem() {
      queueOperationOnce('onSelectedItemChange', () =>
        this.onSelectedItemChange()
      )
    },
  },
  created() {
    //This module reset and initialize search_module store because it make use of it

    this.$store.dispatch('search_module/resetStore', {
      origin: 'diagnostics',
    })
  },
  async mounted() {
    this.$store.dispatch('app/setIsLayoutTableMapResizeable', true)
    const self = (window._dm = this)

    // Test only: ?test=1 (berto provence)
    if (this.isTest) {
      await this.$store.dispatch('search_module/clearDateSelection')
      await this.$store.dispatch('search_module/selectItem', {
        type: 'date_range',
        value: [new Date('2022-03-04'), new Date('2022-03-04')],
        origin: 'DiagnosticsModule::mounted(testMode)',
      })
    }
    this.handleAutomaticSearchComingFromLocation()

    this.$store.dispatch('selection_templates/setSelectedTemplate', null)
  },
  destroyed() {
    this.$store.dispatch('app/setIsLayoutTableMapResizeable', false)
    this.$store.dispatch('diagnostics/resetStore')
    this.$store.dispatch('simpliciti_map/resetStore', null)
    this.$store.dispatch('search_module/resetStore', {
      origin: 'diagnostics/destroyed',
    })
    this.$store.dispatch('selection_templates/setSelectedTemplate', null)
  },
  methods: {
    handleAutomaticSearchComingFromLocation() {
      // Automatize search
      if (this.searchParams) {
        this.isSearchComingFromAnotherModule = true
        /* console.debugVerboseScope(
          5,
          'diags',
          'diagnostics created search-params',
          this.searchParams
        ) */

        const { vehicleId, selectedDate } = this.searchParams

        //this.$store.state.search_module.selectedVehiclesIds = [vehicleId]
        this.$store.dispatch('search_module/selectItem', {
          type: 'vehicle',
          value: vehicleId,
          origin: 'DiagnosticsModule::handleAutomaticSearchComingFromLocation',
        })
        this.$store.dispatch('search_module/selectItem', {
          type: 'date_range',
          value: [
            moment(new Date(selectedDate)).clone().startOf('day')._d,
            moment(new Date(selectedDate))
              .clone()
              .hour(23)
              .minute(59)
              .second(59)._d,
          ],
          origin: 'DiagnosticsModule::handleAutomaticSearchComingFromLocation',
        })

        this.$nextTick(() => {
          this.onSearchModuleSelectionValidate([selectedDate, selectedDate])
        })
      } else {
        /* console.debugVerboseScope(
          5,
          'diags',
          'diagnostics created no-search-params'
        ) */
      }

      // Capture back button press event
      this.$root.$once(
        'search-wrapper:diagnostics-module:back-button-pressed',
        () => this.backToLocation()
      )
    },
    backToLocation() {
      if (this.searchParams === null) {
        return
      }

      /* console.debugVerbose(
        8,
        'Attempting to navigate to location_module with params:',
        this.searchParams
      ) */

      this.$router
        .push({
          name: 'location_module',
          params: {
            searchParams: {
              vehicleId: this.searchParams.vehicleId,
              selectedDate: this.searchParams.selectedDate,
            },
          },
        })
        .catch((err) => {
          console.error('Navigation error:', err)
        })
    },
    /**
     * Open the left menu on single/range selection
     */
    onSelection() {
      this.menuCollapsed = false
    },
    onSelectedItemChange() {
      let wasItemSelectedByChartClick =
        this.$store.state.diagnostics.chartSelectedItemTrigger === 'chart'

      //Handle item deselect
      if (!this.selectedItem) {
        this.onSelectedDateRangeChange(false)
        //Close opened popups on map
        this.closePositionPopups()
      }

      if (wasItemSelectedByChartClick && this.selectedItem) {
        //Close opened popups on map
        this.closePositionPopups()

        this.showPositionMarker()
        this.lateralMenuMode = 'search'

        //Update replay index
        if (
          this.selectedItem?.id !=
          this.$store.state.diagnostics.replayPosition?.id
        ) {
          this.$store.state.diagnostics.replayPosition = null
          let selectedItemIndex = this.positions.findIndex(
            (p) => p.id == this.selectedItem.id
          )

          this.$store.state.diagnostics.replayPositionIndex = selectedItemIndex
          this.$store.state.diagnostics.replayPositionEndIndex = -1

          replayController.jumpToIndex(selectedItemIndex)
        }
      }
    },
    onSelectedDateRangeChange(shouldSetMarkers) {
      const brushStartTimestamp =
        this.$store.state.diagnostics.brushStartTimestamp
      const brushEndTimestamp = this.$store.state.diagnostics.brushEndTimestamp

      if (brushStartTimestamp && brushEndTimestamp) {
        this.highlightTripHistoryPolylines(true)

        //Update replay indexes
        let range = this.$store.getters['diagnostics/selectedPositions']
        let positions = this.$store.state.diagnostics.positions
        let riStart = positions.findIndex((p) => p.id === range[0].id)
        let riEnd = positions.findIndex(
          (p) => p.id === range[range.length - 1].id
        )
        this.$store.state.diagnostics.replayPositionIndex = riStart
        this.$store.state.diagnostics.replayPositionEndIndex = riEnd
      } else {
        this.highlightTripHistoryPolylines(false)
      }

      //Set vehicle position markers
      if (shouldSetMarkers) {
        this.setVehiclePositionMarkers()
      }

      this.updateAnalysisViewData()
      this.lateralMenuMode = 'search'
    },
    /**
     * Highlight partial trip polyline (Given a timestamp range)
     */
    highlightTripHistoryPolylines(shouldHightlight) {
      const brushStartTimestamp =
        this.$store.state.diagnostics.brushStartTimestamp
      const brushEndTimestamp = this.$store.state.diagnostics.brushEndTimestamp
      if (shouldHightlight) {
        let polylineItem = null
        const polylinesData = this.positions.reduce((a, v) => {
          if (
            v.timestamp >= brushStartTimestamp &&
            v.timestamp <= brushEndTimestamp
          ) {
            if (!polylineItem) {
              polylineItem = {
                polyline: [],
                smoothFactor: 0.5,
                weight:
                  parseInt(import.meta.env.VITE_LOCATION_MAP_POLYLINE_WEIGHT) ||
                  6,
                color: this.$map.defaultHighlightColor,
              }
              a.push(polylineItem)
            }
            polylineItem.polyline.push([v.lat, v.lng])
          }
          return a
        }, [])
        if (this.$map.getLeafletWrapperVM()) {
          this.$map.getLeafletWrapperVM().drawPolylines(polylinesData, {
            layer: 'diagnosticsPolylineHighlight',
            layerOptions: {
              zIndex: 1000,
            },
            after: ({ latLngs }) => {
              if (latLngs.length > 0) {
                this.$map.fitBounds(latLngs)
              }
            },
          })
        }
      } else {
        if (this.$map.getLeafletWrapperVM()) {
          this.$map.getLeafletWrapperVM().drawPolylines([], {
            layer: 'diagnosticsPolylineHighlight',
            visible: false,
          })
        }
      }
    },
    async showPositionMarker() {
      this.$nextTick(() => {
        setTimeout(() => {
          // Find the selected marker in the existing ones
          let positionLayer = this.$map
            .getLeafletWrapperVM()
            .layerGroups.positions.getLayers()
            .find((layer) => {
              const markerLatLng = layer.getLatLng()
              return (
                markerLatLng.lat === this.selectedItem.lat &&
                markerLatLng.lng === this.selectedItem.lng
              )
            })

          if (positionLayer) {
            // Center the map of selected marker
            this.$map
              .getLeafletWrapperVM()
              .map.setView(positionLayer.getLatLng(), 18, {
                animate: true,
              })

            this.$map.getSimplicitiMapVM().$refs.mapOptions.positions = true

            setTimeout(() => {
              //Open popup on selected marker
              positionLayer.openPopup()
            }, 600)
          } else {
            console.error('Marker not found')
          }
        }, 500)
      })
    },
    onDateClear() {
      this.$store.dispatch('search_module/clearDateSelection')
    },
    onDateSelection(selectedDate) {
      if (
        this.isSearchComingFromAnotherModule &&
        this.onDateSelectionIgnoredTimes < 1
      ) {
        this.onDateSelectionIgnoredTimes++
        return
      }

      this.onDateClear()
      /*  console.debugVerboseScope(5, 'diags', 'onDateSelection', {
        selectedDate,
      }) */
      if (selectedDate) {
        this.$store.dispatch('search_module/selectItem', {
          value: [selectedDate, selectedDate],
          type: 'date_range',
          origin: 'DiagnosticsModule::onDateSelection',
        })
      } else {
        this.$store.dispatch('search_module/clearDateSelection')
      }
    },
    /*
    unused?
    async syncSensorsConfig() {
      await this.$store.dispatch('map_options/syncSensorConfig', {
        vehicleId: this.selectedVehicleId,
      })
      this.isSensorConfigSync = true
    },*/
    /**
     * - Fetch analysis result data
     * - Fetch chrono data (vuex)
     */
    async updateAnalysisViewData() {
      queueOperationOnce(
        'diagnostics_updateAnalysisViewData',
        async () => {
          if (!this.datetimeFrom || !this.datetimeTo) {
            /* console.debugVerbose(
              8,
              'updateAnalysisViewData skip (invalid start/end dates)',
              {
                datetimeFrom: this.datetimeFrom,
                datetimeTo: this.datetimeTo,
              }
            ) */
            return
          }

          let hideLoader =
            (this.$loader && this.$loader.showAlternative()) || (() => ({}))

          let resolvedPromises = await Promise.all([
            analyzeHistorySegment(
              this.selectedVehicleId,
              this.datetimeFrom,
              this.datetimeTo
            ),
            this.$store.dispatch('diagnostics/updateChronoData', {
              vehicleId: this.selectedVehicleId,
              startDate: this.datetimeFrom,
              endDate: this.datetimeTo,
              onlySynthesis: true,
            }),
          ])

          let result = resolvedPromises[0] || {}
          this.analysisResult = Object.freeze(result)
          this.selectedItem = null
          hideLoader()

          if (this.$store.state.diagnostics.isChronoDataLoading === true) {
            this.$store.state.diagnostics.isChronoDataLoading = false
          }
        },
        1000,
        {
          clearPreviousTimeout: true,
        }
      )
    },
    switchBackToSearchSelection() {
      this.$store.state.search_module.view = 'selection'
      this.lateralMenuMode = 'search'
    },
    /**
     * Load overview information
     * Toggle results view (search module)
     */
    async onSearchModuleSelectionValidate() {
      this.resetModuleState()
      //await this.$store.dispatch('diagnostics/resetStore')
      this.$nextTick(async () => {
        await this.$store.dispatch('search_module/setHasResults', true)

        if (!!this.selectedVehicleId && !!this.selectedDate) {
          this.$store.dispatch('diagnostics/updateVehicleHistoryOverview', {
            vehicleId: this.selectedVehicleId,
            date: this.selectedDate,
          })
        }
      })
    },
    onSearchModuleViewChange(view) {
      //React to search module view changes (selection / results)
    },
    /**
     * Clear custom filters and datasets
     */
    onSearchModuleClearSelection() {
      this.positionsArray = []
      this.resetModuleState()
    },
    resetLocalState() {
      Object.assign(this.$data, newState())
    },
    resetModuleState() {
      this.resetLocalState()
      this.$store.dispatch('diagnostics/resetStore')
    },
    /**
     * Sets vehicle positions markers on map
     */
    setVehiclePositionMarkers() {
      let positions = this.$store.state.diagnostics.positions

      this.$store.dispatch('simpliciti_map/setDataset', {
        type: 'vehiclePositionMarkers',
        data: positions,
      })

      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$map.getLeafletWrapperVM().layerGroups.positions) {
            let positionLayer = this.$map
              .getLeafletWrapperVM()
              .layerGroups.positions.getLayers()[0]
          }

          this.$map.getSimplicitiMapVM().$refs.mapOptions.positions = true
        }, 500)
      })
    },
    closePositionPopups() {
      let leafletWrapperVM = this.$map.getLeafletWrapperVM()

      if (leafletWrapperVM) {
        let positionLayers = leafletWrapperVM.layerGroups.positions.getLayers()

        if (positionLayers && positionLayers.length > 0) {
          positionLayers.forEach((layer) => {
            if (layer.getPopup()) {
              layer.closePopup()
            }
          })
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.date-picker-label {
  span {
    font: normal normal 14px/18px Open Sans;
    letter-spacing: 0px;
    color: var(--color-tundora);
  }
  display: flex;
  column-gap: 10px;
  align-items: center;
  em {
    color: var(--color-main);
  }
}
</style>

/**
 * @namespace Utils
 * @category Utils
 * @module String*/

import i18n from '@/i18n'

/**
 * Remove french accents
 * @param {*} strAccents
 * @returns {String} Normalized string
 */
export function removeAccents(strAccents) {
  strAccents = strAccents.split('')
  var strAccentsOut = new Array()
  var strAccentsLen = strAccents.length
  var accents = 'ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØòóôõöøÈÉÊËèéêëðÇçÐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž'
  var accentsOut =
    'AAAAAAaaaaaaOOOOOOOooooooEEEEeeeeeCcDIIIIiiiiUUUUuuuuNnSsYyyZz'
  for (var y = 0; y < strAccentsLen; y++) {
    if (accents.indexOf(strAccents[y]) != -1) {
      strAccentsOut[y] = accentsOut.substr(accents.indexOf(strAccents[y]), 1)
    } else strAccentsOut[y] = strAccents[y]
  }
  strAccentsOut = strAccentsOut.join('')

  return strAccentsOut
}

export function normalizeString(input, options = {}) {
  const {
    replaceSpaces = false,
    removeParentheses = false,
    removeAccents = false,
    prefix = '',
  } = options

  const convertToLowercase =
    options.convertToLowercase === undefined ? true : options.convertToLowercase

  let normalized = input

  if (replaceSpaces) {
    normalized = normalized.replace(/ /g, '_')
  }

  if (convertToLowercase) {
    normalized = normalized.toLowerCase()
  }

  if (removeParentheses) {
    normalized = normalized.replace(/[()]/g, '')
  }

  if (removeAccents) {
    const accents = 'ÀÁÂÄÈÉÊËÌÍÎÏÒÓÔÖÙÚÛÜàáâäèéêëìíîïòóôöùúûü'
    const accentsOut = 'AAAAEEEEIIIIOOOOUUUUaaaaeeeeiiiioooouuuu'
    normalized = normalized.replace(
      new RegExp('[' + accents + ']', 'g'),
      function (c) {
        return accentsOut.charAt(accents.indexOf(c))
      }
    )
  }

  if (prefix) {
    normalized = prefix + normalized
  }

  return normalized
}

export function getEmptyStringIfStringIncludes(str, includesStr) {
  let finalStr = str
  finalStr = str.includes(includesStr) ? '' : finalStr
  return finalStr
}

/**
 * Check if a string is a numeric string
 * Used by events service to handle CitiPAV statuses
 * @param str
 * @returns {boolean}
 */
export function isNumericString(str) {
  return typeof str === 'string' && /^\d+$/.test(str)
}

export function camelToKebabString(str) {
  return str.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`)
}

/**
 * Normalize the value display by rounding it to 2 decimal places and appending the unit.
 *
 * @param {number} value - The value to be normalized.
 * @param {string} unit - The unit to be appended to the normalized value.
 * @return {string} The normalized value display with unit, or the translation for 'common.not_available' if value or unit is not provided.
 */
export function normalizeValueDisplay(value, unit) {
  if (value === undefined || value === null) {
    return i18n.t('common.not_available')
  }

  return (
    (value.toFixed(2) ?? 0) + '\u00a0' + unit ?? i18n.t('common.not_available')
  )
}

<template lang="pug">
.simpliciti_map(v-if="!!$store.getters['auth/clientId']")
  portal-target(name="simplicitiMap")
  slot(name="before_map", v-bind:map="this")
    MapOptions(
      v-if="!disableMapOptions",
      ref="mapOptions",
      v-model="mapOptions",
      @toggleLayer="toggleMapLayers",
      @toggleAllLayers="toggleAllMapLayers",
      @sensors="onSensorLayerToggleChange",
      :initialContactCheckboxesValue="showTripStepMarkersByDefault",
      :initialSensorsVisibilityValue="showPositionMarkersByDefault",
      @zonesMarkersToggle="(v) => (zonesMarkersToggle = v)"
    )
  LeafletMap(ref="map", @zoomend="onZoomEnd"
  @moveend="moveend"
  @init="onInit"
  )
  BaseMapsFloatingMenu
  MapBottomFloatingMenu(ref="bottomMenu")
  portal(to="MapToolboxPortalTarget")
    MapToolbox(:key="_uid")
  slot
</template>
<script>
import Vue, { ref } from 'vue'
import MapOptions from '@c/location/MapOptions/MapOptions.vue'
import LeafletMap from './LeafletMap.vue'
import L from 'leaflet'
import { mapGetters } from 'vuex'
import polylineZoomMixin from './polyline-zoom-mixin'
import drawSinglePolylinesMixin from './draw-single-polylines-mixin'
import drawEventsMarkersMixin from './draw-events-markers-mixin'
import drawAlertsMarkersMixin from './draw-alerts-markers-mixin'
import drawChronoMarkersMixin from './draw-chronomarkers-mixin'
import mittToMethodsMixin from './mitt-to-methods-mixin'
import genericZoomMixin from './generic-zoom-mixin'
import drawPositionsMarkersMixin from './draw-positions-markers-mixin'
import drawBacsMarkersMixin from './draw-bacs-markers-mixin'
import drawVehicleMarkersMixin from '@c/shared/SimplicitiMap/draw-vehicle-markers.js'
import drawZonesMixin from '@c/shared/SimplicitiMap/draw-zones-mixin.js'
import drawContainerMarkersMixin from '@/components/shared/SimplicitiMap/draw-container-markers-mixin'
import BaseMapsFloatingMenu from '@c/shared/SimplicitiMap/BaseMapsFloatingMenu.vue'
import { getFormattedAddress } from '@/utils/address.js'
import MapBottomFloatingMenu from '@c/shared/SimplicitiMap/MapBottomFloatingMenu.vue'
import { isMapSelectablePolylinesFeatureEnabled } from '@/services/feature-service.js'
import MapToolbox from '@c/shared/MapToolbox/MapToolbox.vue'
import useMapVM from '@/composables/map.js'
import { hasFeature, featureNames } from '@/config/features.js'
import blackSpotMixin from '@/mixins/blackspot-map-mixin'
import store from '@/store'
import { throttleSimple, throttleSmart } from '@/utils/async'
import mapLocDetailsHistoryMixin from '@/mixins/mapLocationDetailsHistoryMixin.js'

const map = ref(null)
const {
  toggleStepStartStopMarkers,
  changeLeafletPanesOpacity,
  moveLeafletGeometryIntoHighlightPane,
} = useMapVM(map)

/**
 * @namespace components
 * @category components
 * @subcategory shared
 * @module SimplicitiMap
 * @alias SimplicitiMapComponent
 * @description
 *  Note: LeafletMap communication: For performance reasons, we decide to use component programatically (i.g: $refs.map.drawGeometries) rather than representing leaflet geometries with vue components.
 *  Note: Z-Index: SimplicitiMap should be responsable of re-ordering layer types in the right order. I.g: Positions should always draw on top of linestrings.
 * @todo Methods should not be used directly (i.g $refs.map.drawPositionsMarkersFromData) but instead, called from inside this component based on store changes (simpliciti_map store)
 */
export default {
  name: 'SimplicitiMap',
  components: {
    MapToolbox,
    LeafletMap,
    MapOptions,
    BaseMapsFloatingMenu,
    MapBottomFloatingMenu,
  },
  mixins: [
    mapLocDetailsHistoryMixin,
    mittToMethodsMixin,
    polylineZoomMixin,
    drawSinglePolylinesMixin,
    drawEventsMarkersMixin,
    drawAlertsMarkersMixin,
    drawChronoMarkersMixin,
    genericZoomMixin,
    drawPositionsMarkersMixin,
    drawBacsMarkersMixin,
    drawVehicleMarkersMixin,
    drawZonesMixin,
    blackSpotMixin,
    drawContainerMarkersMixin,
  ],
  inject: {
    isLocationMainSearch: {
      default: false,
    },
    isLocationHistoryMap: {
      default: false,
    },
    isCircuitMap: {
      default: false,
    },

    isChronoMap: {
      default: false,
    },
    isZonesMap: {
      default: false,
    },
    simplicitiMapName: {
      default: '',
    },
  },
  props: {
    fitBounds: {
      type: Array,
      default: () => [],
    },
    disableMapOptions: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mapOptions: {
        positions: true,
      },
      zonesMarkersToggle: false,
    }
  },
  computed: {
    ...mapGetters({
      tripStepsmarkers: 'simpliciti_map/tripStepsmarkers',
      speedPolylines: 'simpliciti_map/speedPolylines',
      chronoMarkers: 'simpliciti_map/chronoMarkers',
      sensorsConfig: 'map_options/sensorsConfig',
      circuitExecutionsPolylines: 'simpliciti_map/circuitExecutionsPolylines',
      tripHistoryPolylines: 'simpliciti_map/tripHistoryPolylines',
      activeSearchFormTabName: 'search_module/activeSearchFormTabName',
    }),
    locationModulePolylines() {
      return this.circuitExecutionsPolylines.length > 0 &&
        this.activeSearchFormTabName === 'circuit'
        ? this.circuitExecutionsPolylines
        : this.tripHistoryPolylines
    },
    showTripStepMarkersByDefault() {
      return this.isTripHistoryMap
    },
    zoomLevel() {
      return this.$store.state.simpliciti_map.zoomLevel
    },
    moveendTimestamp() {
      return this.$store.state.simpliciti_map.moveendTimestamp
    },
  },

  watch: {
    fitBounds: {
      handler(bounds) {
        if (bounds.length > 0) {
          this.getMap().fitBounds(bounds)
        }
      },
      immediate: true,
    },
    /**
     * Used by:
     * Location - History by Vehicle,Driver, Circuit - Main search (Overview)
     *
     * Used for trip histories and circuit execution (if circuit tab)
     *
     * @todo: Extract if trip histories
     */
    locationModulePolylines: {
      handler(items = []) {
        let bounds = null
        if (this.isLocationHistoryMap && items.length > 0) {
          this.waitForMapRef(
            async () => {
              await this.drawPolylines(items, {
                layer: 'linestrings',
                itemOptions: (item) => ({
                  color: item.color || 'darkgray',
                  originalColor: item.color || 'darkgray',
                }),

                ...(isMapSelectablePolylinesFeatureEnabled()
                  ? {
                      onclick: (e) => {
                        this.$map.highlightPolylines(
                          e.target.options.stepNumber
                        )
                      },
                      popupOptions: {
                        style: 'min-width:267px;',
                      },
                      popup: () =>
                        /* webpackChunkName "main" */
                        import('@c/location/TripHistoryPolylinePopup.vue'),
                    }
                  : {}),
              })

              //Fit to bounds only if bounds length changes... (This will skip fit to bounds when highlighting elements)
              bounds = items.reduce((a, v) => {
                a = a.concat(v.polyline)
                return a
              }, [])
              if (this.locationModulePolylinesBoundsCount != bounds.length) {
                this.locationModulePolylinesBoundsCount = bounds.length
                this.getMap().fitBounds(bounds)
              }
            },
            {
              id: 'circuitExecutionsPolylines',
              metadata: { len: items.length },
            }
          )
        }
      },
      deep: true,
      immediate: true,
    },

    /**
     * Used by:
     * Location - Real-time by Vehicle/Driver/Circuit - Last circuit
     * Location - History by circuit - Main search (Details)
     */
    chronoMarkers: {
      handler(newValue) {
        this.waitForMapRef(() => this.drawChronoMarkers(newValue))
      },
      deep: true,
      immediate: true,
    },
    /**
     * Used by:
     * Location - Real-time by Vehicle/Driver/Circuit - Last trip history
     */
    speedPolylines: {
      handler(newValue) {
        if (this.isLocationMainSearch) {
          return
        }
        this.waitForMapRef(() => this.drawSpeedPolylines(newValue))
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.$store.dispatch('simpliciti_map/initialize', this.$store)
  },
  async mounted() {
    this.waitForMapRef(() => {
      map.value = this.$refs.map
      this.$map.registerVM(this.simplicitiMapName, this)
      this.bindReverseGeocodingOnClickFeature()
    })
    this.mapBoundsStoreBind = await this.bindMapViewportBoundstoStore()
  },
  beforeDestroy() {
    this.mapBoundsStoreBind && this.mapBoundsStoreBind.unbind()
  },
  destroyed() {
    this.$map.unregisterVM(this)
  },
  methods: {
    async bindMapViewportBoundstoStore() {
      console.debugVerbose(8, 'bindMapViewportBoundstoStore bind')
      const map = await this.getMapWhenReady()
      let handler = () => {
        if (this.getMap()) {
          this.$store.state.simpliciti_map.mapViewportBounds =
            this.getMap().getBounds()
        }
      }
      handler = throttleSimple(handler, 110)
      map.on('move', handler)
      this.getMapWhenReady().then(() => {
        handler()
      })
      return {
        unbind() {
          console.debugVerbose('bindMapViewportBoundstoStore unbind')
          map.off('move', handler)
        },
      }
    },
    onZoomEnd(v) {
      //this.$store.state.simpliciti_map.mapViewportBounds =this.getMap().getBounds()

      let layerGroups = this.getLayerGroups()
      this.$store.state.simpliciti_map.zoomLevel = v

      Object.keys(layerGroups).forEach((key) => {
        if (this['onZoomLevelChange_' + key]) {
          this['onZoomLevelChange_' + key](layerGroups[key].getLayers())
        }
      })
    },
    onInit() {
      //this.$store.state.simpliciti_map.mapViewportBounds = this.getMap().getBounds()
      //console.debugVerbose(8,'SimplicitiMap.init')
      this.$emit('init')
    },
    drawGeometries() {
      this.waitForMapRef((mapRef) => {
        if (mapRef) {
          mapRef.drawGeometries.apply(mapRef, arguments)
        }
      })
    },
    /**
     * Function that binds reverse geocoding service to Leaflet map on click events.
     * On single click it will return an address depending on the coordinates of the click, and on double click it will clear the timeout set by single click.
     */
    bindReverseGeocodingOnClickFeature() {
      let self = this
      let clickTimeout
      this.getLeafletMapWrapper().map.on('click', async (e) => {
        let enabled =
          self.$store.getters[
            'simpliciti_map/mapReverseGeocodingOnMouseClickEnabled'
          ]
        if (!enabled) {
          return //Skip reverse geocoding on map mouse click if not enabled
        }

        clearTimeout(clickTimeout)
        clickTimeout = setTimeout(async () => {
          e.originalEvent.stopPropagation()
          const coords = {
            latitude: e.latlng.lat,
            longitude: e.latlng.lng,
          }
          let resultMessage = this.$t('geocoding.not_address_found')
          if (e.latlng) {
            await this.$geocoding
              .reverseGeocoding(coords)
              .then(async (response) => {
                if (response.city) {
                  resultMessage = getFormattedAddress(response, {
                    country: true,
                  })
                  store.state.simpliciti_map.lastReverseGeocodingFormatedAdress =
                    {
                      formatted: resultMessage,
                      lat: coords.latitude,
                      lng: coords.longitude,
                    }
                }
              })
          }

          //Return if getLeafletMapWrapper is undefined
          if (!self.getLeafletMapWrapper()) {
            return
          }

          L.popup()
            .setLatLng(e.latlng)
            .setContent('<p class="px-2">' + resultMessage + '</p>')
            .openOn(self.getLeafletMapWrapper().map)
        }, 300) // set the timeout to 300ms
      })

      //Check if this.getLeafletMapWrapper() is defined
      if (this.getLeafletMapWrapper().map) {
        this.getLeafletMapWrapper().map.on('dblclick', () => {
          clearTimeout(clickTimeout)
        })
      }
    },

    moveend(e) {
      let layerGroups = this.getLayerGroups()

      //Trigger methods onMoveEnd_layergroupname (located within draw-single-polyline-mixin)
      Object.keys(layerGroups).forEach((key) => {
        if (this['onMoveEnd_' + key]) {
          this['onMoveEnd_' + key](layerGroups[key].getLayers())
        }
      })

      if (e.trigger !== 'pan') {
        return
      }

      let now = Date.now()
      this.$store.state.simpliciti_map.moveendTimestamp = now
      //this.$store.state.simpliciti_map.mapViewportBounds = this.getMap().getBounds()
    },
    withLeafletMapLayerGroup(layerGroupName, callback) {
      let layerGroups = this.getLayerGroups()
      if (layerGroups && layerGroups[layerGroupName]) {
        callback(layerGroups[layerGroupName])
      }
    },
    openLayerPopup({ groupName, layerId }) {
      let layerGroups = this.getLayerGroups()
      if (layerGroups && layerGroups[groupName]) {
        let layer = layerGroups[groupName]
          .getLayers()
          .find((layer) => layer.properties.id == layerId)
        if (!!layer && !layer.isPopupOpen()) {
          layer.openPopup()
        }
      }
    },
    /**
     * Get Leaflet map instance
     */
    getMap() {
      return this.$refs?.map?.getMap() || null
    },
    getMapWhenReady() {
      return new Promise((resolve) => {
        this.waitForMapRef(() => {
          resolve(this.$refs.map.getMap())
        })
      })
    },
    /**
     * Get LeafletMap component (child)
     */
    getLeafletMapWrapper() {
      return this.$refs.map
    },
    /**
     * Removes/Add all the available featureGroups (layers) from the map (LeafletMap::map) at once.
     */
    toggleAllMapLayers(value) {
      let layerGroups = this.getLayerGroups()

      if (layerGroups && this.getLeafletMapWrapper) {
        Object.keys(layerGroups).forEach((key) => {
          if (value) {
            layerGroups[key].addTo(this.getLeafletMapWrapper().map)
          } else {
            layerGroups[key].remove()
          }
        })
      }
    },

    /**
     * Toggles the specified layer groups from LeafletMap.
     * @param {Object} options The object containing layerName and enabled that determines which layer to toggle
     */
    toggleMapLayers(options) {
      let { layerName, enabled } = options

      let mapWrapper = this.getLeafletMapWrapper()
      if (!mapWrapper) {
        return
      }
      const layerGroups = mapWrapper.layerGroups
      if (!layerGroups) {
        console.warn('LeafletMap layerGroups is not available')
        return
      }
      let layerGroup = layerGroups && layerGroups[layerName]

      if (!layerGroup) {
        return
      }

      if (enabled) {
        layerGroup && layerGroup.addTo(mapWrapper.getMap())
      } else {
        layerGroup && layerGroup.remove()
      }
    },
    /**
     * @deprecated In favor of zIndex property when using drawGeometries (LeafletMap.vue)
     * After deprecation, ensure all the layers below (tripHistoryPolylines, speedPolylines, circuitPolylines, etc) uses zIndex property
     * Re-arrange the Z-index of map layers (predefined priority)

    sortMapLayers() {
      const layers = this.getLeafletMapWrapper().layerGroups

      if (layers.tripHistoryPolylines) {
        layers.tripHistoryPolylines.bringToFront()
      }
      if (layers.speedPolylines) {
        layers.speedPolylines.bringToFront()
      }
      if (layers.circuitPolylines) {
        layers.circuitPolylines.bringToFront()
      }
      if (layers.circuit_execution_arrows) {
        layers.circuit_execution_arrows.bringToFront()
      }

      Object.keys(layers).forEach((name) => {
        if (name.includes('position')) {
          layers[name].bringToFront()
          layers[name].getLayers().forEach((layer) => {
            layer.bringToFront()
          })
        }
      })
    },
    */
    /**
     * Vue DOM references might take some extra time to initialize
     * @todo: Refactor into re-usable helper (see LeafletMap::waitForProperty)
     */
    waitForMapRef(cb, options = {}, start = Date.now(), iterations = 0) {
      let waitTime = options.waitTime || 100
      let timeout = options.timeout || 10000

      if (Date.now() - start > timeout) {
        console.warn('waitForMapRef timeout')
        return
      }

      if (!!window._logoutAt && window._logoutAt > start) {
        console.warn('waitForMapRef skip because logout detected')
        return
      }

      //Ignore old calls if similar (only if options.id is supplied)
      if (options.id) {
        this._waitForMapRefTimeouts = this._waitForMapRefTimeouts || {}

        if (
          !!this._waitForMapRefTimeouts[options.id] &&
          this._waitForMapRefTimeouts[options.id] > start
        ) {
          //console.debugVerbose(8,`waitForMapRef::${options.id}::${start}::skipped`);
          return
        } else {
          /* console.debug(
            `waitForMapRef::${options.id}::${start}::start(${iterations})`,
            options.metadata || {},
            this._waitForMapRefTimeouts[options.id]
          );*/
        }

        if (
          !this._waitForMapRefTimeouts[options.id] ||
          this._waitForMapRefTimeouts[options.id] < start
        ) {
          this._waitForMapRefTimeouts[options.id] = start
        }
      }

      if (this.$refs.map) {
        if (options.id) {
          console.debugVerbose(
            8,
            `waitForMapRef::${options.id}::${start}::end`,
            options.metadata || {}
          )
        }

        if (this.$refs.map.waitForInitialization) {
          this.$refs.map.waitForInitialization().then(() => cb(this.$refs.map))
        } else {
          cb(this.$refs.map)
        }
      } else {
        setTimeout(
          () => this.waitForMapRef(cb, options, start, iterations + 1),
          waitTime
        )
      }
    },
    /**
     * Speed polylines will always start hidden (Toggleable from MapOptions)
     */
    drawSpeedPolylines(data) {
      console.log(`SimplicitiMap.vue drawSpeedPolylines`, { data: data.length })
      if (data.length > 0) {
        // Map colors to tooltip labels
        const colorToTooltip = {
          '#FF4545': this.$t(
            'location.history_tab.map.linestring_tooltip_stop'
          ),
          '#F6AC59': this.$t(
            'location.history_tab.map.linestring_tooltip_slow'
          ),
          '#70BD95': this.$t(
            'location.history_tab.map.linestring_tooltip_moving'
          ),
        }

        // Add tooltipContent based on color
        const enhancedData = data.map((item) => ({
          ...item,
          tooltipContent: colorToTooltip[item.color] || '',
        }))

        this.drawPolylines(enhancedData, {
          visible: false,
          leafletType: 'polyline',
          type: 'speedPolylines',
          layer: 'speedPolylines',
          zIndex: 2,
        })
      }
    },
    /**
     * Draw all Chrono Markers on the SimplicitiMap
     */
    drawChronoMarkers(chronoMarkersArray) {
      if (
        !Array.isArray(chronoMarkersArray) &&
        chronoMarkersArray.length === 0
      ) {
        return
      }
      this.drawChronoMarkersSteps(chronoMarkersArray)
    },

    /**
     * Draw Polylines (Linestrings) on the map
     * - Used by Location - History mode - History tab (Related history)
     * - Used by Location - History mode - Circuit tab (Related circuit exec)
     * - Used by Location - Realtime mode - History tab (Dernier historique)
     * - Used by Location - Realtime mode - Circuit tab (Dernier circuit exec)
     */
    drawPolylines(polylines = [], options = {}) {
      let self = this
      options.layer = options.layer || 'linestrings'
      options.zIndex = options.zIndex || 1
      if (!this.$refs.map) {
        console.debugVerbose(8, 'Map is not availalble. Unit-test?')
      }
      this.$refs.map &&
        this.$refs.map.drawPolylines(
          polylines.map((element) => {
            element = { ...element }
            element.color = element.highlightedColor || element.color

            // Add tooltipContent based on color if it doesn't exist
            if (!element.tooltipContent && element.color) {
              const colorToTooltip = {
                '#FF4545': 'arrêté',
                '#F6AC59': 'ralenti',
                '#70BD95': 'en mouvement',
              }
              element.tooltipContent = colorToTooltip[element.color] || ''
            }

            return element
          }),
          {
            ...options,
            after({ geometries }) {
              console.debugVerbose(8, 'simplicitmap.draw.after')
              self.updateHighlightedPolylineItemIfAny(polylines, geometries)
              options.after && options.after.apply(this, arguments)
            },
          }
        )
    },
    /**
     * Note: map polylines can be highlighted
     * Used by Location - Circuit tab
     */
    updateHighlightedPolylineItemIfAny(
      polylineItems,
      relatedLeafletGeometries
    ) {
      let highlightedPolylineItem = polylineItems.find(
        (d) => d.highlighted || d.highlightedColor
      )
      if (highlightedPolylineItem) {
        //Only show direction arrows on highlighted geometry
        /*
        relatedLeafletGeometries.forEach((g) => {
          g.deleteArrowheads && g.deleteArrowheads()
        })*/

        const item = relatedLeafletGeometries.find(
          (g) => g.externalId == highlightedPolylineItem.id
        )
        this.highlightSingleLeafletPolylineGeometry(item)

        if (
          hasFeature(
            featureNames.CIRCUIT_EXEC_STEP_HIGHLIGHT_SHOW_START_STOP_MARKERS
          )
        ) {
          //this.highlightPane = moveLeafletGeometryIntoHighlightPane(item)
          toggleStepStartStopMarkers(true, item)
          //changeLeafletPanesOpacity(1)

          var worldBounds = L.latLngBounds([-90, -180], [90, 180])
          this.highlightOverlay =
            this.highlightOverlay ||
            L.rectangle(worldBounds, {
              color: 'black',
              fillColor: 'black',
              fillOpacity: 0.3, // Adjust the opacity as needed
              interactive: false, // Make the overlay non-interactive
              zIndex: 1, // Set a very low zIndex to ensure it's rendered below other layers
            })
          this.highlightOverlay.addTo(this.getMap()).bringToBack()
        }
      } else {
        clearInterval(this.highlightSortInterval)
        if (
          hasFeature(
            featureNames.CIRCUIT_EXEC_STEP_HIGHLIGHT_SHOW_START_STOP_MARKERS
          )
        ) {
          this.highlightOverlay && this.highlightOverlay.remove()
          //this.highlightPane && this.highlightPane.remove()
          //this.highlightPane = null
          //changeLeafletPanesOpacity(1)
          toggleStepStartStopMarkers(false)
        }
      }
    },
    /**
     * Note: Highlighted polyline has also direction arrows
     * * Used by Location - Circuit tab
     */
    highlightSingleLeafletPolylineGeometry(polyline) {
      console.debugVerbose(8, 'highlight', polyline)
      //setTimeout(() => polyline.bringToFront(), 1000) //LeafletMap (Wrapper) takes 500ms to sort the layers
      polyline.setStyle({
        color: getComputedStyle(
          document.querySelector(':root')
        ).getPropertyValue('--color-polyline-highlight'),
        weight: 10,
      })
      polyline.bringToFront()

      clearInterval(this.highlightSortInterval)
      this.highlightSortInterval = setInterval(() => {
        polyline.bringToFront()
      }, 1000)

      import('leaflet-arrowheads')
        .then(() => {
          polyline.arrowheads({
            color: 'darkgray',
            fillColor:
              polyline.color ||
              polyline?.options?.color ||
              polyline?.options?.style?.color ||
              'darkgray',
            yawn: 40,
            //frequency: "180m",
            //frequency: 'allvertices',
            frequency: '200px',
            size: '10px',
            //fill: true,
            weight: 3,
            opacity: 0.8,
          })
        })
        .catch((error) => {
          console.error('Failed to load leaflet-arrowheads:', error)
        })
    },
    /**
     * Draw direction arrows (for single circuit execution)
     */
    drawCircuitArrowsFromPolylines(polylines = [], options = {}) {
      import('leaflet-arrowheads')
      this.$refs.map.drawGeometries({
        ...options,
        type: 'arrow',
        layer: 'circuit_execution_arrows',
        zIndex: 4,
        data: polylines.map((p) => ({
          ...p,
          color: 'transparent',
          arrowColor: p.color,
        })),
        generate(item) {
          return L.polyline([item.polyline], {
            arrowheads: true,
          }).arrowheads({
            color: item.arrowColor || 'darkgray',
            weight: 1,
            size: '12px',
            smoothFactor: 0.5,
            fill: true,
            yawn: 50,
            frequency: 'endonly',
          })
        },
        after() {
          options.after && options.after.apply(this, arguments)
        },
      })
    },
    getLayerGroups() {
      return (
        this.getLeafletMapWrapper &&
        this.getLeafletMapWrapper() &&
        this.getLeafletMapWrapper().layerGroups
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.simpliciti_map {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}
</style>

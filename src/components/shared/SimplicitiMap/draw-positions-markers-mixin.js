import colors from '@/styles/colors.js'
import positionMarkersService from '@/services/position-markers-service.js'

const torCodes = [
  'TOR1',
  'TOR2',
  'TOR3',
  'TOR4',
  'TOR5',
  'TOR6',
  'TOR7',
  'TOR8',
  'TOR+',
]

export default {
  inject: {
    /**
     * Used by TripHistoryMap and TripHistoryTableMap
     */
    isTripHistoryMap: {
      default: false,
    },
    /**
     * Used by Diagnostics module
     */
    enableRenderVehiclePositionsMarkers: {
      default: false,
    },
    isCircuitMap: {
      default: false,
    },
  },
  computed: {
    showPositionMarkersByDefault() {
      return (
        this.isCircuitMap ||
        this.isTripHistoryMap ||
        this.enableRenderVehiclePositionsMarkers
      )
    },
  },
  watch: {
    'mapOptions.positions': {
      handler() {
        this.onPositionsChange()
      },
    },
    // Position markers are now handled by the service with listeners
    // The onPositionsChange will be triggered by the service listener
  },
  mounted() {
    // Set up listener for position markers changes
    this._positionMarkersListener = () => {
      this.$nextTick(() => {
        window.setTimeout(() => {
          this.onPositionsChange()
        }, 500)
      })
    }
    positionMarkersService.addListener(this._positionMarkersListener)

    // Trigger initial load if positions already exist
    if (positionMarkersService.getCount() > 0) {
      this._positionMarkersListener()
    }
  },
  beforeDestroy() {
    // Clean up listener
    if (this._positionMarkersListener) {
      positionMarkersService.removeListener(this._positionMarkersListener)
    }
  },
  methods: {
    /**
     * Will be trigered any time positions data change or positions toggle button is pressed
     */
    onPositionsChange() {
      if (!this.isLocationMainSearch) {
        this.drawPositionsMarkers()

        this.waitForMapRef(this.drawSensorMarkers)
      }
    },
    /**
     * Draw sensor markers when user toggle layers
     */
    onSensorLayerToggleChange() {
      if (!(this.sensorsConfig && this.sensorsConfig.sensors)) {
        return
      }
      this.waitForMapRef(() => {
        //TOR 1..8 layers (bind to each button: i.g Tor1 enables Tor1 layers)
        this.sensorsConfig.sensors
          .filter((item) => item.code !== 'TOR+')
          .forEach((configItem) => {
            this.toggleMapLayers({
              layerName: `positions_${configItem.code}`, //i.g positions_TOR5
              enabled: configItem.showMarkers,
            })
            this.toggleMapLayers({
              layerName: `positions_multiple_tors_${configItem.code.toLowerCase()}`,
              enabled: configItem.showMarkers,
            })
          })

        //TOR+ layers
        const enableMultipleTors = (
          this.sensorsConfig.sensors.find((item) => item.code === 'TOR+') || {
            showMarkers: false,
          }
        ).showMarkers
        torCodes
          .filter((c) => c !== 'TOR+')
          .forEach((code) => {
            this.toggleMapLayers({
              layerName: `positions_multiple_tors_${code.toLowerCase()}`,
              enabled: enableMultipleTors,
            })
          })

        /*
        Re initializing leaflet layers is hardware intensive
        this.drawSensorMarkers({
          ignoreVisibilityByDefault: true,
        })
        */
      })
    },
    /**
     * Draw position markers on the map
     * - Waits until map ref is available
     * - Filters only common positions (without sensor data)
     */
    drawPositionsMarkers() {
      this.$nextTick(() => {
        this.waitForMapRef(() => {
          const commonPositions = positionMarkersService.getCommonPositions()
          drawPositionsMarkersFromData.apply(this, [commonPositions])
        })
      })
    },
    /**
     * Draw positions with sensor data using dataset from store
     *
     * Markers will be visible by default if each marker type has showMarkers equals true
     * Markers will not be visible if computed showPositionMarkersByDefault equals false
     * showPositionMakrersByDefault has no effect if ignoreVisibilityByDefault equals true
     */
    drawSensorMarkers(options = {}) {
      const getSensorConfig = (code) =>
        (this.sensorsConfig.sensors || []).find((i) => i.code === code) || null

      const shouldRenderMarkersFromType = (config) =>
        options.ignoreVisibilityByDefault
          ? config.showMarkers
          : this.showPositionMarkersByDefault
          ? config.showMarkers
          : false

      let configs = {}

      torCodes.forEach((code) => (configs[code] = getSensorConfig(code)))
      torCodes.forEach((code) => {
        let sensorConfig = configs[code]

        //Clear previous colored gps positions

        drawSensorMarkersFromData.apply(this, [
          [],
          {
            name: `multiple_tors_${code}`.toLowerCase(),
          },
        ])
        drawSensorMarkersFromData.apply(this, [
          [],
          {
            name: code,
          },
        ])

        if (!sensorConfig) {
          return
        }

        let filteredItems = positionMarkersService.getPositionsByCode(code)

        if (code === 'TOR+') {
          //Multiple activations of TORs will create a marker per valid TOR
          let multipleSensors = {}
          filteredItems.forEach((item) => {
            item.code_capteurs.forEach((torCode) => {
              if (configs[torCode]) {
                multipleSensors[torCode] = multipleSensors[torCode] || []
                multipleSensors[torCode].push(item)
              }
            })
          })
          Object.keys(multipleSensors).forEach((torCode) => {
            drawSensorMarkersFromData.apply(this, [
              multipleSensors[torCode],
              {
                name: `multiple_tors_${torCode}`.toLowerCase(),
                //Color will be the color from TOR+ (Black)
                color: configs[code].color,
                //i.g: TOR1 will be visible if TOR1 layer is enabled or if TOR+ layer is enabled
                visible: shouldRenderMarkersFromType(configs[torCode]), //||  shouldRenderMarkersFromType(configs["TOR+"]),
              },
            ])
          })
        } else {
          //Normal TORs (1,2,3,4,5,6,7,8) goes here
          drawSensorMarkersFromData.apply(this, [
            filteredItems,
            {
              name: code,
              color: sensorConfig.color,
              visible: shouldRenderMarkersFromType(sensorConfig),
            },
          ])
        }
      })
    },
    getPositionBorderColor() {
      // Border color: same as trip history polyline if available
      const color = this.$store?.state?.simpliciti_map?.tripHistoryPolylineColor
      return color || colors.color_dark_blue
    },
    drawSensorMarkersFromData(data = [], options = {}) {
      //console.debug('[SimplicitiMap] Drawing sensor markers:', data.length)

      if (!this.$refs?.map) {
        console.warn(
          '[SimplicitiMap] Map reference not ready, skipping sensor markers'
        )
        return
      }

      const self = this
      const drawOptions = {
        data,
        layer: options.name,
        type: 'positions',
        zIndex: options.zIndex || 1500,
        visible: options.visible,
        generate(item) {
          try {
            const circle = L.circle([item.lat, item.lng], {
              color: self.getPositionBorderColor(),
              fillColor: options.color,
              fillOpacity: 1,
              radius: 3,
              weight: 2,
            })
            circle.options.stepNumber = item.stepNumber
            return circle
          } catch (error) {
            console.error(
              '[SimplicitiMap] Error generating sensor marker:',
              error
            )
            return null
          }
        },
        ...this.getDrawGeometriesPopupOptions(options),
      }

      try {
        self.$refs.map.drawGeometries(drawOptions)
      } catch (error) {
        console.error('[SimplicitiMap] Error drawing sensor markers:', error)
      }
    },
    getDrawGeometriesPopupOptions(options = {}) {
      // Create a new object for popup options to avoid reference issues
      const popupProps = options.popupOptions?.props
        ? { ...options.popupOptions.props }
        : {}
      const popupOptions = options.popupOptions
        ? { ...options.popupOptions }
        : {}

      return {
        popup:
          options.popup ||
          (() =>
            /* webpackChunkName "shared_components" */
            import('./PositionMarkerPopup.vue')),
        popupOptions: {
          style: 'min-width:267px;',
          ...popupOptions,
          props: popupProps,
          bindItemToProperties: false, //For performance gains: Otherwise we have 'Maximum call stack size exceeded'
        },
      }
    },
    drawPositionsMarkersFromData(positions = [], options = {}) {
      console.debug(
        '[SimplicitiMap] Drawing positions markers:',
        positions.length
      )

      if (!this.$refs?.map) {
        console.warn(
          '[SimplicitiMap] Map reference not ready, skipping position markers'
        )
        return
      }

      let visible = true
      const color = options.color || this.getPositionBorderColor()

      if (!this.mapOptions?.positions) {
        visible = false
        console.debug('[SimplicitiMap] Positions hidden by map options')
      }

      // Filter out invalid positions
      const validPositions = positions.filter(
        (p) => p?.lat && (p?.lng || p?.lon)
      )
      const invalidCount = positions.length - validPositions.length
      if (invalidCount > 0) {
        console.warn('[SimplicitiMap] Found invalid positions:', invalidCount)
      }

      if (validPositions.length === 0) {
        console.debug('[SimplicitiMap] No valid positions to draw')
        return
      }
    },
  },
}

function getDrawGeometriesPopupOptions(options = {}) {
  return {
    popupOptions: {
      style: 'min-width:267px;',
    },
    popup:
      options.popup ||
      (() =>
        /* webpackChunkName "main" */
        import('./PositionMarkerPopup.vue')),
  }
}

function getPositionsClusterIcon(cluster, options = {}) {
  let style = `${
    options.backgroundColor
      ? 'background: ' + options.backgroundColor + ';'
      : ''
  }${options.color ? 'color: ' + options.color + ';' : ''}${
    options.borderColor ? 'border: 2px solid ' + options.borderColor + ';' : ''
  }`
  return L.divIcon({
    className: 'cluster_marker',
    html: `<div class="cluster_position_marker" style="${style}">${cluster.getChildCount()}</div>`,
  })
}

/**
 * Border color: same as trip history polyline if available
 *
 */
function getPositionBorderColor() {
  let color = colors.color_main

  let singlePolyline =
    (this.$store.getters['location_module/selectedItem'] || {})
      ?.tripHistoryPolyline || {}
  if (singlePolyline instanceof Array) {
    singlePolyline = singlePolyline[0]
  }

  if (singlePolyline.color) {
    color = singlePolyline.color
  }

  return color
}

/**
 * Draw common position markers (without sensor data)
 * Border: Same as trip history
 * Fill: #cccccc (Same as V2)
 */
function drawPositionsMarkersFromData(positions = [], options = {}) {
  let color = getPositionBorderColor.apply(this)

  let visible = this.showPositionMarkersByDefault

  //Update visible if layer is toggle off in map options
  if (!this.mapOptions.positions) {
    visible = false
    console.debugVerboseScope(8, 'map', 'Render positions hiding (map options')
  }

  let invalidPositions = positions.filter((p) => !p.lat || !p.lng)
  if (invalidPositions.length > 0) {
    console.warn('Invalid positions', {
      invalidPositions,
    })
  }

  this.$refs.map.drawCircles(positions, {
    ...options,
    circleStyle: {
      weight: 2,
      fillColor: '#cccccc',
      color,
      radius: 3,
    },
    /*clusterOptions: {
      iconCreateFunction: function (cluster) {
        return getPositionsClusterIcon(cluster, {
          borderColor: color,
        });
      },
    },*/
    layer: options.layer || 'positions',
    //Position markers should have higher zIndex than highlighted polylines
    zIndex: options.zIndex || 1500,
    type: 'positions',
    visible,
    ...this.getDrawGeometriesPopupOptions(options),
    after() {
      options.after && options.after.apply(this, arguments)
    },
  })
}

/**
 * There might be data with invalid lat/lng, which should be ignored
 * @todo Move to services/utils
 * @memberof SimplicitiMapComponent
 * @returns {Boolean}
 */
function validPositionsFilter(p) {
  return (
    (!!p.lat || !!p.latitude_mapmatch) &&
    (!!p.lng || !!p.lon || !!p.longitude_mapmatch)
  )
}

/**
 * Draw sensor markers (positions with sensor data) using LeafletMap::drawGeometries
 * Border: Trip history polyline color
 * Fill: Sensor color
 */
function drawSensorMarkersFromData(data = [], options = {}) {
  let self = this
  /*
  if (data.length === 0) {
    this.$refs.map.clearLayers &&
      this.$refs.map.clearLayers(`positions_${options.name}`)
    return
  }*/

  /* data = data.map((item) => {
    item.lng = item.lng || item.lon
    return item
  }) */

  const drawOptions = {
    ...options,
    leafletType: 'marker',
    visible: options.visible || false,
    type: `positions_${options.name}`,
    layer: `positions_${options.name}`,
    //Position markers should have higher zIndex than highlighted polylines
    zIndex: options.zIndex || 1500,
    /*clusterOptions: {
      iconCreateFunction: function (cluster) {
        return getPositionsClusterIcon(cluster, {
          borderColor: options.color,
        });
      },
    },*/
    data,
    ...this.getDrawGeometriesPopupOptions(options),
    generate(item) {
      try {
        const circle = L.circle([item.lat, item.lng], {
          color: getPositionBorderColor.apply(self),
          fillColor: options.color,
          fillOpacity: 1,
          radius: 3,
        })
        return circle
      } catch (err) {
        console.error({
          err,
          item,
        })
        return null
      }
    },
  }

  this.$refs.map.drawGeometries(drawOptions)
}

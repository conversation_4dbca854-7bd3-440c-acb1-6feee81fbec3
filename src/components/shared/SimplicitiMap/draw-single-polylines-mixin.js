import { mapGetters } from 'vuex'
import L from 'leaflet'
import { nextTick } from 'vue'
import { getArrowsSegmentsFromPolylines } from '@/utils/map'

export default {
  inject: {
    isEventsMap: {
      default: false,
    },
    isAlertsMap: {
      default: false,
    },
    /**
     * Disables:
     * Prevents big polylines on high zoom levels
     */
    disablePolylineDynamicWeightOnZoom: {
      default: false,
    },
  },
  computed: {
    ...mapGetters({
      singleCircuitExecPolylines: 'simpliciti_map/singleCircuitExecPolylines',
      singleTripHistoryPolylines: 'simpliciti_map/singleTripHistoryPolylines',
    }),
  },
  watch: {
    singleCircuitExecPolylines: createDrawPolylinesWatch(
      'singleCircuitExecPolylines'
    ),
    singleTripHistoryPolylines: createDrawPolylinesWatch(
      'singleTripHistoryPolylines'
    ),
  },
  methods: {
    onZoomLevelChange_tripHistoryPolylines(geometries) {
      if (this.disablePolylineDynamicWeightOnZoom) {
        return
      }
      geometries.forEach((geo) => {
        geo.setStyle({
          weight: (this.zoomLevel * 4) / 18,
        })
      })
    },
    onZoomLevelChange_circuitPolylines(geometries) {
      if (this.disablePolylineDynamicWeightOnZoom) {
        return
      }
      geometries.forEach((geo) => {
        if (geo.options.highligthed) {
          return
        }
        geo.setStyle({
          weight: (this.zoomLevel * 4) / 18,
        })
      })
    },
    onZoomLevelChange_tripHistoryPolylines_arrows(geometries) {
      this.setArrowheadsGeometriesOpacityDependingOnZoomLevel(geometries)
    },
    onMoveEnd_tripHistoryPolylines_arrows(geometries) {
      this.setArrowheadsGeometriesOpacityDependingOnZoomLevel(geometries)
    },
    setArrowheadsGeometriesOpacityDependingOnZoomLevel(geometries) {
      //Set opacity of arrows based on current zoom level
      geometries.forEach((geo) => {
        geo.setStyle({
          opacity: this.zoomLevel >= 15 ? 1 : 0,
          fillOpacity: this.zoomLevel >= 15 ? 1 : 0,
        })
      })
    },
  },
}

export function createDrawPolylinesWatch(name) {
  return {
    async handler(newValue, oldValue) {
      // Return a Promise that resolves after nextTick + async code inside
      return new Promise((resolve) => {
        nextTick(async () => {
          if (this.isLocationMainSearch) {
            resolve()
            return
          }

          let isVisible = true

          if (this.isEventsMap || this.isAlertsMap) {
            isVisible = false
          }

          if (
            this.isCircuitMap &&
            oldValue === undefined &&
            name === 'singleTripHistoryPolylines' &&
            this.$store.state.location_module
              .hideTripHistoryPolylinesIfCircuitTab
          ) {
            isVisible = false
          }

          if (
            !this.isCircuitMap &&
            oldValue === undefined &&
            name === 'singleCircuitExecPolylines'
          ) {
            isVisible = false
          }

          let self = this

          let layerName =
            name === 'singleCircuitExecPolylines'
              ? 'circuitPolylines'
              : 'tripHistoryPolylines'

          let zIndex = layerName === 'circuitPolylines' ? 3 : 1

          const originalPolylines = this[name] || []

          let arrowSegments = []

          if (originalPolylines.length > 0) {
            arrowSegments = getArrowsSegmentsFromPolylines(originalPolylines)
          } else {
            arrowSegments = []
          }

          await this.waitForMapRef(async () => {
            await this.drawPolylines(this[name], {
              zIndex,
              visible: isVisible,
              arrowheads: null,
              layer: layerName,
              after({ map, latLngs, geometries }) {
                if (
                  isVisible &&
                  !!latLngs &&
                  latLngs.length > 0 &&
                  latLngs.length != self[layerName + 'BoundsCount']
                ) {
                  self[layerName + 'BoundsCount'] = latLngs.length
                  map.fitBounds(latLngs)
                }
              },
            })

            if (this.isCircuitMap && name === 'singleCircuitExecPolylines') {
              this.drawCircuitArrowsFromPolylines(this[name])
            } else {
              if (arrowSegments.length > 0) {
                await this.drawPolylines(arrowSegments, {
                  zIndex,
                  visible: isVisible,
                  arrowheads:
                    name === 'singleTripHistoryPolylines'
                      ? {
                          color:
                            (!!this[name] &&
                              this[name].length > 0 &&
                              this[name][0].color) ||
                            'darkgray',
                          yawn: 60,
                          frequency: 'endonly',
                          size: '12px',
                        }
                      : null,
                  layer: layerName + '_arrows',
                  opacity: 0,
                  weight: 0,
                })
              }
            }
          })

          resolve()
        })
      })
    },
    deep: true,
    immediate: true,
  }
}

/**
 * Helper method to get mid-length arrow segments from polylines
 * @param polylines
 * @returns {*}
 */
export function getArrowSegments(polylines) {
  return polylines.flatMap((poly) => {
    const latlngs = poly.polyline || []
    const color = poly.color || 'darkgray'
    const segments = []

    for (let i = 0; i < latlngs.length - 1; i++) {
      const start = L.latLng(latlngs[i])
      const end = L.latLng(latlngs[i + 1])
      const mid = L.latLng((start.lat + end.lat) / 2, (start.lng + end.lng) / 2)

      segments.push({
        polyline: [start, mid],
        color,
      })
    }

    return segments
  })
}

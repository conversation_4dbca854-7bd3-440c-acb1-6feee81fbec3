<template lang="pug">
.identification_table(ref="root")
  PicturesGallery(ref="picg" v-show="pictures.length > 0" :pictures="pictures" @close="pictures=[]")
  .row.p-0.m-0
    .col-6.p-0.m-0.header-title-wrapper
      .header-title {{headerText}}
  DataTable(
    v-if="table"
    class="table_theme_1"
    ref="datatable",
    :ssrPaging="false",
    :ssrShowMultiColumnFilter="false",
    :searching="true",
    :paging="true",
    name="events",
    rowId="id",
    scrollY="250px",
    :columns="columns",
    :columnDefs="columnDefs",
    :language="$translations.datatable",
    :extraOptions="extraOptions || {}",
    :select="select",
    :defaultSortingColumn="2"
    :default-sorting-column-direction="'desc'"
    @select="onSelect"
    @deselect="onDeselect"
    :autoHeight="true"
    :autoHeightOffset="mode==='table'?0:0"
    @init="onTableInit"
  )
</template>
<script>
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import TableModesToolbar from '@c/shared/TableModesToolbar/TableModesToolbar.vue'
import { createSortableColumnDefinition } from '@/utils/datatable.js'
import PicturesGallery from '@/components/shared/PicturesGallery/PicturesGallery.vue'
import {
  fetchEventDetailsById,
  normalizeAPIV3Item,
} from '@/services/events-service.js'
import { getQueryStringValue } from '@/utils/querystring'
import { mapGetters } from 'vuex'

export default {
  name: 'EventsTable',
  components: {
    DataTable,
    TableModesToolbar,
    PicturesGallery,
  },
  mixins: [datatableMixin],
  props: {
    mode: {
      type: String,
      default: 'table+map',
      enum: ['table', 'table+map'],
    },
    headerText: {
      type: String,
      default: '',
    },
    showRoundColumns: {
      type: Boolean,
      default: false,
    },
    showBinColumns: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    var self = this

    let columns = [
      {
        data: 'zoomIcon',
        title: '',
        orderable: false,
        render: createComponentRender({
          name: 'ZoomActionButton',
          template: `<em @click="handleZoom" class="fas fa-search" style="cursor:pointer;"></em>`,
          methods: {
            handleZoom(e) {
              e.stopPropagation()
              self.$mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [
                [this.row.lat, this.row.lng],
              ])
            },
          },
        }),
      },
      {
        data: 'galleryIcon',
        title: '',
        orderable: false,
        render: createComponentRender({
          name: 'PhotoActionButton',
          template: `<em @click="openPhotoGallery" class="fas fa-camera" :style="iconStyle"></em>`,
          computed: {
            iconStyle() {
              return (this.row.photos !== undefined &&
                this.row.photos.length > 0) ||
                this.row.hasPhotos
                ? `cursor: pointer;`
                : `color: rgb(82 77 77 / 53%); cursor: not-allowed;`
            },
          },
          methods: {
            /**
             * Will retrieve event details from APIV3
             */
            async openPhotoGallery(e) {
              e.stopPropagation()

              if (getQueryStringValue('test') !== '1' && !this.row.hasPhotos) {
                return
              }

              let eventDetails = {
                ...(await fetchEventDetailsById(this.row.id)),
                ...(getQueryStringValue('test') === '1'
                  ? normalizeAPIV3Item({
                      filesNumber: 3,
                      files: [
                        {
                          url: 'https://mediamobile.geosab.eu/GEOTRANS/client_1038/evenement/1044299046/358205088115061-20220415100723-pict.jpg',
                          fileCreatedAt: '2022-04-15T10:07:23+02:00',
                        },
                        {
                          url: 'https://mediamobile.geosab.eu/GEOTRANS/client_1038/evenement/1044299046/358205088115061-20220415100729-pict.jpg',
                          fileCreatedAt: '2022-04-15T10:07:29+02:00',
                        },
                        {
                          url: 'https://mediamobile.geosab.eu/GEOTRANS/client_1038/evenement/1044299046/358205088115061-20220415100739-pict.jpg',
                          fileCreatedAt: '2022-04-15T10:07:39+02:00',
                        },
                      ],
                    })
                  : {}),
              }

              console.log({
                eventDetails,
              })

              self.pictures = eventDetails.photos
            },
          },
        }),
      },
      createSortableColumnDefinition(
        `timestamp`,
        `formattedDate`,
        this.$t('location_module.events.date')
      ),
      createSortableColumnDefinition(
        `timestamp`,
        `formattedTime`,
        this.$t('location_module.events.time')
      ),
      createSortableColumnDefinition(
        `vehicleName`,
        `vehicleName`,
        this.$t('location_module.events.vehicle')
      ),
      createSortableColumnDefinition(
        `label`,
        `label`,
        this.$t('location_module.events.name')
      ),
      createSortableColumnDefinition(
        `address`,
        `address`,
        this.$t('location_module.events.address')
      ),
      createSortableColumnDefinition(
        `city`,
        `city`,
        this.$t('location_module.events.city')
      ),
      createSortableColumnDefinition(
        `comment`,
        `comment`,
        this.$t('location_module.events.comment')
      ),
    ]
    if (
      this.$store.getters[
        'location_module/clientCanHaveOrderGeneratedFromEvents'
      ]
    ) {
      columns.push(
        createSortableColumnDefinition(
          `orderStateName`,
          `orderStateName`,
          this.$t('location_module.events.ot_status')
        )
      )
      columns.push(
        createSortableColumnDefinition(
          `orderNumber`,
          `orderNumber`,
          this.$t('location_module.events.ot_number')
        )
      )
    }
    if (this.showRoundColumns) {
      columns.push(
        createSortableColumnDefinition(
          `circuitName`,
          `circuitName`,
          this.$t('events.table_column.circuit_name')
        )
      )
    }
    if (this.showBinColumns) {
      columns.push(
        createSortableColumnDefinition(
          `puceNumber`,
          `puceNumber`,
          this.$t('events.table_column.puce_number')
        )
      )
      columns.push(
        createSortableColumnDefinition(
          `binCollectionSideLabel`,
          `binCollectionSideLabel`,
          this.$t('events.table_column.side')
        )
      )
    }

    columns.push(
      createSortableColumnDefinition(
        `isWorkDone`,
        `isWorkDone`,
        this.$t('events.events_table.work_done_label')
      )
    )

    return {
      pictures: [],
      isModalOpen: false,
      showInformationsPopup: false,
      table: false,
      select: {
        items: 'row',
        style: 'single',
        info: false,
      },
      columns,
      columnDefs: [],
      extraOptions: {
        ...this.configureColumnsFilters((filters) => {
          return [
            null,
            null,
            filters.createTextFilter({ column: 2 }),
            filters.createTextFilter({ column: 3 }),
            filters.createTextFilter({ column: 4 }),
            filters.createTextFilter({ column: 5 }),
            filters.createTextFilter({ column: 6 }),
            filters.createTextFilter({ column: 7 }),
            filters.createTextFilter({ column: 8 }),
            filters.createTextFilter({ column: 9 }),
            filters.createTextFilter({ column: 10 }),
            filters.createTextFilter({ column: 11 }),
            filters.createSelectFilter({
              column: 12,
              selectOptions: [
                {
                  text: this.$t('common.yes'),
                  value: this.$t('common.yes'),
                },
                {
                  text: this.$t('common.no'),
                  value: this.$t('common.no'),
                },
              ],
            }),
          ]
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      },
      selectedEventDisabled: false,
      hasDisabledScrollAndPagination: false,
      selectInProgress: false,
    }
  },
  computed: {
    shouldShowMemoryPuceColumn() {
      return !!this.items.find((item) => !!item.memoryPuceNumber)
    },
    ...mapGetters({
      selectedEvent: 'events/selectedEventItem',
      items: 'events/detailedItems',
    }),
    //Get table height
    tableHeight() {
      return this.$store.getters['app/layout'].rightMenuBottomCurrentHeight
    },
  },
  watch: {
    items() {
      this.updateTable()
      this.updateMemoryPuceColumnVisibility()
    },
    selectedEvent() {
      this.$trackWatch(1)

      if (this.selectedEventDisabled) {
        return
      }

      this.disableSelectListeners = true

      this.selectRowUsingAttribute(
        this.selectedEvent ? this.selectedEvent.id : null,
        'id',
        this.hasDisabledScrollAndPagination
      )

      this.$nextTick(() => {
        setTimeout(() => {
          this.disableSelectListeners = false
        }, 500)
      })
    },
    tableHeight(newVal, oldVal) {
      this.$trackWatch(1)

      this.hasDisabledScrollAndPagination = this.handleTableHeightWatcher(
        newVal,
        'id',
        this.selectedEvent ? this.selectedEvent.id : null,
        this.hasDisabledScrollAndPagination
      )
    },
  },
  mounted() {
    this.selectedEventDisabled = false

    this.disableSelectListeners = false

    this.$refs.picg.$el.parentNode.removeChild(this.$refs.picg.$el)
    this.$root.$el.appendChild(this.$refs.picg.$el)

    $.fn.dataTable.moment('DD/MM/YYYY HH:mm:ss')
    this.updateTable()
    setTimeout(() => {
      this.table = true
      setTimeout(() => {
        this.$refs.datatable.datatable.draw()
      }, 500)
    }, 200)
  },
  beforeDestroy() {
    this.$refs.picg.$el.parentNode.removeChild(this.$refs.picg.$el)
  },
  destroyed() {
    this.$store.commit('location_module/SET_IDENTIFICATION_BAC_MODAL', false)
  },
  methods: {
    onTableInit() {
      this.updateMemoryPuceColumnVisibility()
    },
    /**
     * @todo Refactor/Improve Toggling a column brokes column filters
     */
    updateMemoryPuceColumnVisibility() {
      /* this.$nextTick(() =>
        this.$refs.datatable.toggleColumnVisible(
          8,
          this.shouldShowMemoryPuceColumn
        )
      );*/
      /*
      //Hide column using pure javascript
      this.$nextTick(() => {
        document.querySelectorAll("table").forEach((t) =>
          t.querySelectorAll("tr").forEach((tr) => {
            tr.children[8].style.display = "none";
          })
        );
        this.$refs.datatable.datatable.draw(false);
      });*/
    },
    onDeselect() {
      if (this.disableSelectListeners || this.selectInProgress) {
        return
      }

      this.selectedEventDisabled = true

      this.$store.commit('events/resetSelectedEventItem')

      this.$mitt.emit(
        'SIMPLICITI_MAP_FIT_TO_BOUNDS',
        this.items.map((item) => [item.lat, item.lng])
      )

      //On deselect, close all opened popups
      let mapLayers = this.$map
        .getLeafletWrapperVM()
        .layerGroups.vehicle_events.getLayers()

      mapLayers.forEach((layer) => {
        if (layer._popup.isOpen()) {
          layer.closePopup()
        }
      })

      setTimeout(() => {
        this.selectedEventDisabled = false
      }, 500)
    },
    onSelect({ item }) {
      if (this.disableSelectListeners || this.selectInProgress) {
        return
      }

      this.selectedEventDisabled = true

      this.selectInProgress = true

      this.$store.commit('events/updateSelectedEventItem', item)

      this.$mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [[item.lat, item.lng]])

      try {
        let matchLayer = this.$map
          .getLeafletWrapperVM()
          .layerGroups.vehicle_events.getLayers()
          .find((t) => t.externalId === item.id)

        setTimeout(() => {
          try {
            //Open popup only if not already opened
            if (!matchLayer._popup.isOpen()) {
              matchLayer.openPopup()
            }
          } catch (err) {
            console.error('Fail to open related layer popup', err)
          }
        }, 600)
      } catch (err) {
        console.error('Fail to open related layer popup', err)
      }

      setTimeout(() => {
        this.selectedEventDisabled = false
        this.selectInProgress = false
      }, 500)
    },
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'events',
        items: this.items,
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.title,
.title span,
.title strong {
  font-size: 12px;
}
.identification_table {
  height: 100%;
  height: -moz-available; /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available; /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}
.table_toolbar {
  display: flex;
  align-items: center;
}
.header-title-wrapper {
  display: flex;
  align-items: center;
}
.header-title {
  font-size: 12px;
}
</style>

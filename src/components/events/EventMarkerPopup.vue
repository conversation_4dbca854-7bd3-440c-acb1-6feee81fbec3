<template>
  <MapPopup :title="$t('common.map.popup.event')">
    <Teleport to="body">
      <PicturesGallery
        v-show="photos.length > 0"
        ref="picgp"
        :pictures="photos"
        @close="photos = []"
      ></PicturesGallery>
    </Teleport>

    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>{{ $t('common.map.popup.received_infos') }}</div>
      <i
        :class="
          isLastInfosSectionCollapsed
            ? 'fas fa-chevron-up event-popup-chevron'
            : 'fas fa-chevron-down event-popup-chevron collapsed'
        "
        :aria-expanded="isLastInfosSectionCollapsed ? 'true' : 'false'"
        aria-controls="collapse-3"
        @click="isLastInfosSectionCollapsed = !isLastInfosSectionCollapsed"
      />
    </div>

    <b-collapse id="collapse-3" v-model="isLastInfosSectionCollapsed">
      <div class="row">
        <div class="col-12 pl-4 mb-2">
          <div class="my-1">
            <div class="row m-0">
              <strong> {{ $t('common.date') }} </strong>
            </div>
            <div class="row m-0">
              {{ itemFormattedDate }}
            </div>
          </div>

          <div class="my-1">
            <div class="row m-0">
              <strong> {{ $t('common.matriculation') }} </strong>
            </div>
            <div class="row m-0">
              {{ item.immat ? item.immat : '' }}
            </div>
          </div>

          <div class="my-1">
            <div class="row m-0">
              <strong> {{ $t('common.position') }} </strong>
            </div>
            <div class="row m-0">
              {{ item.fullAddress ? item.fullAddress : '' }}
            </div>
          </div>

          <div class="my-1">
            <div class="row m-0">
              <strong> {{ $t('events.reported_anomaly') }} </strong>
            </div>
            <div class="row m-0">
              {{ item.label ? item.label : '' }}
            </div>
          </div>

          <div v-if="item.orderStateName" class="my-1">
            <div class="row m-0">
              <strong> {{ $t('events.ot_status') }} </strong>
            </div>
            <div class="row m-0">
              <calendar-alert-icon
                :color="'#0b72b5'"
                :size="21"
              ></calendar-alert-icon>
              <div style="position: relative; height: 21px; width: 200px">
                <span style="position: absolute; bottom: 2px">{{
                  item.orderStateName ? item.orderStateName : ''
                }}</span>
              </div>
            </div>
          </div>

          <div v-if="item.orderNumber" class="my-1">
            <div class="row m-0">
              <strong> {{ $t('events.ot_number') }} </strong>
            </div>
            <div class="row m-0">
              {{ item.orderNumber ? item.orderNumber : '' }}
            </div>
          </div>
        </div>
      </div>
    </b-collapse>

    <div v-if="hasPhotos">
      <div
        class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
      >
        <div>Photos</div>
        <i
          :class="
            isPhotoSectionCollapsed
              ? 'fas fa-chevron-up event-popup-chevron'
              : 'fas fa-chevron-down event-popup-chevron collapsed'
          "
          :aria-expanded="isPhotoSectionCollapsed ? 'true' : 'false'"
          aria-controls="collapse-4"
          @click="isPhotoSectionCollapsed = !isPhotoSectionCollapsed"
        />
      </div>

      <b-collapse id="collapse-4" v-model="isPhotoSectionCollapsed">
        <div class="my-1">
          <div class="row justify-content-center text-center px-2">
            <div class="mb-2">
              <a
                v-for="(photo, index) in item.photos"
                :key="index"
                :hreff="photo"
                ttarget="_blank"
                href="#"
                class="mt-2"
                @click="selectPhoto(photo)"
              >
                <img
                  :src="photo"
                  class="img-thumbnail"
                  style="max-width: 30%"
                />
              </a>
            </div>
          </div>
        </div>
      </b-collapse>
    </div>
  </MapPopup>
</template>

<script>
import moment from 'moment'
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'
import {
  fetchEventDetailsById,
  normalizeAPIV3Item,
} from '@/services/events-service.js'
import { getQueryStringValue } from '@/utils/querystring'
import { getVehicleDetailsById } from '@/services/vehicle-service.js'
import PicturesGallery from '@/components/shared/PicturesGallery/PicturesGallery.vue'
import Teleport from 'vue2-teleport'
import switchablePopupsMixin from '@/mixins/switchable-popup-mixin.ts'
import { getEmptyStringIfStringIncludes } from '@/utils/string'
import CalendarAlertIcon from '@/components/shared/SearchResults/Submenu/assets/CalendarAlertIcon.vue'
import GeometryClickMixin from '@/components/shared/SimplicitiMap/geometry-click-mixin'
import {
  adjustDateWithTimezone,
  formatDatetimeWithSeconds,
} from '@/utils/dates.js'
export default {
  name: 'EventMarkerPopup',
  components: {
    MapPopup,
    PicturesGallery,
    Teleport,
    CalendarAlertIcon,
  },
  mixins: [switchablePopupsMixin, GeometryClickMixin],
  props: {
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    let spmLabelPrefix = getEmptyStringIfStringIncludes(
      this.$t('switchable_popups.label.events'),
      'switchable_popups'
    )
    return {
      spmLabelPrefix,
      spmLayerGroupName: 'vehicle_events',
      spmComputeNearbyLayersUsingLeafletLayerGroupLayersAccessor: true,
      isLastInfosSectionCollapsed: true,
      isPhotoSectionCollapsed: false,
      item: this.geometry.properties,
      photos: [],
    }
  },
  computed: {
    hasPhotos() {
      return Array.isArray(this.item.photos) && this.item.photos.length > 0
    },
    itemFormattedDate() {
      return this.item.date
        ? formatDatetimeWithSeconds(adjustDateWithTimezone(this.item.date))
        : ''
    },
  },
  async mounted() {
    if (this.item.isAPIV3Item) {
      this.item = {
        ...this.item,
        // ...(await fetchEventDetailsById(this.item.id)),
        // crée de la confusion entre les valeurs de datatable et celle de la pop up. /!\ principe single data source
        // Si des valeurs non contenues dans item son nécessaires, n'ajouter que ces valeurs, pas tout l'objet
        // author FMA 20240715 je n'ai pas trouvé de cas ou this.item est insuffisant.
        ...(getQueryStringValue('test') === '1'
          ? normalizeAPIV3Item({
              anomalyId: 73542,
              vehicleId: 49436,
              vehicleCategoryId: 15227,
              longitude: 2.31529,
              latitude: 49.0778,
              code: 5007,
              type: 0,
              streetCode: null,
              streetNumber: '19',
              address: 'Rue du Vieux Pavé',
              city: 'Maffliers',
              nud1: 0,
              nud2: 0,
              nud3: 0,
              nud4: 0,
              tud10: null,
              tud20: null,
              tud30: null,
              tud150: 'céramique toxique gazon verre',
              vehicleName: 'HAYON 3',
              vehicleCategoryName: 'TRI-OR',
              areas: [],
              comment: '',
              mapMatch: {
                longitude: 2.31528,
                latitude: 49.07782,
                streetNumber: '19',
                street: 'Rue du Vieux Pavé',
                roadNumber: null,
                zipCode: '95560',
                district: null,
                city: 'Maffliers',
                department: "Val-d\\'Oise",
                region: 'Île-de-France',
                country: 'France',
              },
              anomaliesLevels: [],
              configurations: [],
              filesNumber: 3,
              files: [
                {
                  id: 48,
                  externalId: 1100514,
                  name: '358205088115061-20220415100723-pict.jpg',
                  type: 'evenement_photo',
                  url: 'https://mediamobile.geosab.eu/GEOTRANS/client_1038/evenement/1044299046/358205088115061-20220415100723-pict.jpg',
                  fileCreatedAt: '2022-04-15T10:07:23+02:00',
                },
                {
                  id: 49,
                  externalId: 1100515,
                  name: '358205088115061-20220415100729-pict.jpg',
                  type: 'evenement_photo',
                  url: 'https://mediamobile.geosab.eu/GEOTRANS/client_1038/evenement/1044299046/358205088115061-20220415100729-pict.jpg',
                  fileCreatedAt: '2022-04-15T10:07:29+02:00',
                },
                {
                  id: 50,
                  externalId: 1100516,
                  name: '358205088115061-20220415100739-pict.jpg',
                  type: 'evenement_photo',
                  url: 'https://mediamobile.geosab.eu/GEOTRANS/client_1038/evenement/1044299046/358205088115061-20220415100739-pict.jpg',
                  fileCreatedAt: '2022-04-15T10:07:39+02:00',
                },
              ],
              anomalyLabel: 'PHOTO APRES PRESTATION',
              work: false,
            })
          : {}),
      }

      //If no registration plate, take it from APIV3 ref
      if (!this.item.immat) {
        this.item.immat = (
          await getVehicleDetailsById(this.item.vehicleId)
        ).matriculationNumber
      }
    }

    this.handleClickOnGeometry(this.item)
  },
  methods: {
    formatDate(date) {
      return moment(date).format('HH:mm:ss')
    },
    selectPhoto(photo) {
      this.photos = [photo, ...this.item.photos.filter((url) => url != photo)]
    },
  },
}
</script>

<style scoped>
.blue {
  color: var(--color-denim);
}

.small-text {
  font-size: 0.75rem;
}

.event-popup-chevron {
  position: absolute;
  right: 10px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
</style>

<template lang="pug">
.container
    .row
        .col-12.events-list--toolbar
          div(:title="$t('common.layout.modes.table_and_map_button_tooltip')" v-b-tooltip.hover.left.viewport)
            em.zoom-icon.fas.fa-search(:style="iconStyle" @click="()=>$emit('map')")
          slot
</template>
<script>
import colors from '@/styles/colors'
import MapSearchIcon from 'vue-material-design-icons/MapSearch.vue'
import TableIcon from 'vue-material-design-icons/Table.vue'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
export default {
  name: 'EventsListToolbar',
  components: {
    MapSearchIcon,
    TableIcon,
  },
  mixins: [removeNativeTooltipFromMaterialIconsMixin],
  props: {
    color: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      colors,
    }
  },
  computed: {
    iconColor() {
      return this.color || colors.color_denim
    },
    iconStyle() {
      return `color:${this.iconColor}`
    },
  },
}
</script>
<style lang="scss">
.events-list--toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  column-gap: 5px;
}
.events-list--toolbar .icon {
  cursor: pointer;
}

.zoom-icon {
  cursor: pointer;
  font-size: 15px;
  position: relative;
  top: 2px;
  color: var(--color-denim);
}
</style>

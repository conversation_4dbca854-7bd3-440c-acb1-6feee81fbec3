<template>
  <div>
    <div class="row m-0 p-0">
      <div
        v-for="(dateItem, index) in items"
        :key="dateItem.id"
        class="date-item col-12 m-0 p-0"
      >
        <!-- En-tête de l'élément date -->
        <!-- date-item collapse section -->
        <div
          class="date-item-header row m-0 d-flex justify-content-between align-items-center"
          :class="{ active: isDateActive(dateItem.id) }"
        >
          <div
            class="date-title-wrapper flex-grow-1"
            @click="delegateToolbarAction('map', 'date', { dateItem })"
          >
            <span class="date-title">{{ dateItem.label }}</span>
            <span class="date-subtitle"
              >({{ dateItemSubtitle[index].subtitle }})</span
            >
          </div>
          <div
            class="line-toolbar"
            style="cursor: pointer"
            @click="toggleSectionUnfoldState(dateItem.id)"
          >
            <b-badge class="results-badge">
              {{ dateItem.count }}
            </b-badge>
            <em
              class="date-item-icon fas"
              :class="{
                'fa-chevron-up': isSectionUnfolded(dateItem.id),
                'fa-chevron-down': !isSectionUnfolded(dateItem.id),
              }"
            ></em>
          </div>
        </div>

        <!-- Contenu des événements pour cette date -->
        <div v-show="isSectionUnfolded(dateItem.id)" class="row m-0 p-0">
          <div
            v-for="evtGroup in dateItem.items"
            :key="evtGroup.id"
            class="col-12 p-0 group-item"
            style="cursor: pointer"
          >
            <div
              class="row m-0 p-0 d-flex align-items-center"
              @click="
                delegateToolbarAction('map', 'date-aggregate', {
                  eventGroup: evtGroup,
                  dateItem,
                })
              "
            >
              <div class="col-3 m-0 p-0 d-flex align-items-center">
                <CalendarAlertIcon :color="'#0b72b5'" :size="28" />
              </div>
              <div class="col-9 m-0 p-0 d-flex align-items-center">
                <div class="vehicle-name">
                  {{ evtGroup.label }} ({{ evtGroup.count }})
                </div>
              </div>
            </div>
          </div>

          <!-- Groupes de véhicules/circuits -->
          <div
            v-for="subItem in dateItem.subItems"
            :key="subItem.id"
            class="vehicle-item col-12 p-0"
          >
            <div
              class="vehicle-item-header row m-0 d-flex justify-content-between align-items-center"
              :class="{ active: isSubItemActive(dateItem.id, subItem.id) }"
            >
              <div
                class="vehicle-title flex-grow-1"
                @click="
                  delegateToolbarAction('map', 'subitem', { subItem, dateItem })
                "
              >
                {{ subItem.label }}
              </div>
              <div
                class="line-toolbar"
                style="cursor: pointer"
                @click="toggleSectionUnfoldState(dateItem.id, subItem.id)"
              >
                <b-badge class="results-badge">
                  {{ subItem.count }}
                </b-badge>
                <em
                  class="fas left"
                  :class="{
                    'fa-chevron-up': isSectionUnfolded(dateItem.id, subItem.id),
                    'fa-chevron-down': !isSectionUnfolded(
                      dateItem.id,
                      subItem.id
                    ),
                  }"
                ></em>
              </div>
            </div>

            <!-- Détails des événements par sous-élément -->
            <div
              v-show="isSectionUnfolded(dateItem.id, subItem.id)"
              class="col-12 group-item"
            >
              <div
                v-for="eventGroup in subItem.items"
                :key="eventGroup.id"
                class="row m-0 p-0 d-flex align-items-center"
                style="cursor: pointer"
                @click="
                  delegateToolbarAction('map', 'aggregate', {
                    eventGroup,
                    subItem,
                    dateItem,
                  })
                "
              >
                <div class="col-3 m-0 p-0 d-flex align-items-center">
                  <CalendarAlertIcon :color="'#0b72b5'" :size="28" />
                </div>
                <div class="col-9 m-0 p-0 d-flex align-items-center">
                  <div class="vehicle-name">
                    {{ eventGroup.label }} ({{ eventGroup.count }})
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CalendarAlertIcon from '@/components/shared/SearchResults/Submenu/assets/CalendarAlertIcon.vue'
import moment from 'moment'
import { getFormattedSelectedTimeRangeStringByDate } from '@/utils/dates'
import { useDateItemSubtitle } from '@/composables/useDateItemSubtitle'

export default {
  name: 'EventsList',
  components: {
    CalendarAlertIcon,
  },
  props: {
    items: {
      type: Array,
      default: () => [],
    },
    /**
     * i.g
     *  total_x_x_x
     *  date_11/01/2022_x_x
     *  vehicle_11/01/2020_23_x
     *  circuit_11/01/2022_23_453
     */
    highlightedSelectionId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      unfoldStates: {},
      unfoldStatesUpdateCount: 0,
    }
  },
  computed: {
    /* itemsGroupedByDate() {
      let grouped = [];
      this.items.forEach((item) => {
        let match = grouped.find((g) => g.formattedDate == item.formattedDate);
        if (!match) {
          grouped.push({
            formattedDate: item.formattedDate,
            items: [],
          });
          match = grouped.find((g) => g.formattedDate == item.formattedDate);
        }
        match.items.push(item);
      });
      return grouped;
    },*/
    dateItemSubtitle() {
      return useDateItemSubtitle(this.items, 'label')
    },
  },
  watch: {
    highlightedSelectionId() {
      this.$forceUpdate()
    },
  },
  methods: {
    isDateActive(formattedDate = '') {
      let parts = this.highlightedSelectionId.split('_')
      return (
        ['date', 'vehicle', 'circuit'].includes(parts[0]) &&
        parts[1] == formattedDate
      )
    },
    /**
     * Should highlight vehicle/circuit row?
     */
    isSubItemActive(formattedDate = '', subItemId = '') {
      let parts = this.highlightedSelectionId.split('_')
      return (
        ['subitem', 'aggregate'].includes(parts[0]) &&
        parts[1] == formattedDate &&
        parts[2] == subItemId
      )
    },
    isSectionUnfolded(identifier, suffix = '') {
      return (
        this.unfoldStates[`section_${identifier}_${suffix}`] === true ||
        this.unfoldStates[`section_${identifier}_${suffix}`] === undefined
      )
    },
    toggleSectionUnfoldState(identifier, suffix = '') {
      if (this.unfoldStates[`section_${identifier}_${suffix}`] === undefined) {
        this.$set(this.unfoldStates, `section_${identifier}_${suffix}`, false)
      } else {
        this.$set(
          this.unfoldStates,
          `section_${identifier}_${suffix}`,
          !this.unfoldStates[`section_${identifier}_${suffix}`]
        )
      }
      this.unfoldStatesUpdateCount++
      this.$forceUpdate()
    },
    delegateToolbarAction(action, type, item) {
      this.$emit('toolbar', {
        action,
        type,
        item,
      })
    },
    getDateItemLabel(date) {
      let momentDate = moment(date).format('YYYY-MM-DD')

      return `${getFormattedSelectedTimeRangeStringByDate(momentDate)}`
    },
  },
}
</script>
<style lang="scss" scoped>
.date-item {
  background-color: var(--color-denim);
}
.date-item.active {
  background-color: #085587;
}
.date-item-header {
  border-bottom: 2px solid rgb(255 255 255 / 54%);
  background-color: var(--color-dark-blue);
}
.date-item-header.active {
  background-color: #3696d5;
}
.vehicle-item-header {
  box-shadow: 0px 2px 3px rgb(0 0 0 / 21%);
  background-color: var(--color-denim);
  border-bottom: 2px solid rgb(255 255 255 / 54%);
}
.date-item-header:hover,
.vehicle-item-header:hover {
  background-color: #3696d5;
}
.vehicle-item-header.active {
  background-color: #3696d5;
}

.date-item-header,
.vehicle-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px !important;
}

.vehicle-title {
  font: normal normal bold 14px/29px Open Sans;
  letter-spacing: 0px;
  padding: 0px 10px 0px 20px;
  cursor: pointer;
}

.date-title-wrapper {
  letter-spacing: 0px;
  color: var(--color-denim);
  cursor: pointer;
  column-gap: 5px;
  display: flex;
  align-items: center;
}
.date-title {
  font: normal normal bold 14px/29px Open Sans;
}
.date-subtitle {
  font: normal normal normal 12px/16px Open Sans;
}
.date-title-wrapper,
em,
.vehicle-title {
  color: white;
}
.date-title-wrapper {
  padding: 0px 10px;
}
.date-title-wrapper,
.date-item-icon {
  color: #ffffff;
}
.line-toolbar {
  display: flex;
  align-items: center;
  //padding-right: 15px;
  width: 35px;
  height: inherit;
  min-height: inherit;
  column-gap: 5px;
}
.vehicle-item {
  background-color: white;
}
.events-count {
  font: normal normal normal 11px/16px Open Sans;
  letter-spacing: 0px;
  color: #727272;
}
.vehicle-name {
  font: normal normal bold 12px/16px Open Sans;
  letter-spacing: 0px;
  color: #484848;
}
.group-item {
  background-color: white;
  padding: 10px;
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  user-select: none; /* Standard */
  .col-3 {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :hover {
    background-color: var(--color-concrete);
  }
}
</style>

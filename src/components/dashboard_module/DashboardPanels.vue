<template>
  <div>
    <DashboardRefresh @refresh="refreshWidgets" />

    <b-modal
      id="widget_info_modal"
      hide-footer
      size="lg"
      :title="widgetInfoItem.label"
      style="z-index: 11000"
    >
      <WidgetInfo :widget="widgetInfoItem" />
    </b-modal>
    <div class="DashboardPanels" :class="{ withWidgetList: showWidgetList }">
      <!-- Fixed layout -->
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-6 col-md-12 widgets-wrapper">
            <!-- WIDGET 1 
            <div
              class="widget_wrapper"
              :style="widgetStyle(items[0])"
            >
              <div
                v-b-tooltip.hover.bottom
                class="widget_title"
                :title="
                  items[0].legend || items[0].title || items[0].label || ''
                "
                v-text="items[0].title || items[0].label"
              />
              <component
                :is="getWidgetComponentName(items[0])"
                ref="widget"
                :meta="items[0].meta"
                :size="items[0].size"
                @dblclick="removeWidget(items[0])"
              />
            </div>
            -->
            <div class="row m-0 p-0">
              <div class="col-6 p-0 m-0">
                <!-- WIDGET 2 -->
                <div class="widget_wrapper" :style="widgetStyle(items[1])">
                  <div
                    v-b-tooltip.hover.bottom
                    class="widget_title"
                    :title="
                      items[1].legend || items[1].title || items[1].label || ''
                    "
                    v-text="items[1].title || items[1].label"
                  />
                  <component
                    :is="getWidgetComponentName(items[1])"
                    ref="widget"
                    :meta="items[1].meta"
                    :size="items[1].size"
                    @dblclick="removeWidget(items[1])"
                  />
                </div>
              </div>
              <div class="col-6 p-0 m-0">
                <!-- WIDGET 3 -->
                <div class="widget_wrapper" :style="widgetStyle(items[2])">
                  <div
                    v-b-tooltip.hover.bottom
                    class="widget_title"
                    :title="
                      items[2].legend || items[2].title || items[2].label || ''
                    "
                    v-text="items[2].title || items[2].label"
                  />
                  <component
                    :is="getWidgetComponentName(items[2])"
                    ref="widget"
                    :meta="items[2].meta"
                    :size="items[2].size"
                    @dblclick="removeWidget(items[2])"
                  />
                </div>
              </div>
            </div>

            <!-- WIDGET 4 -->
            <div class="widget_wrapper" :style="widgetStyle(items[3])">
              <div
                v-b-tooltip.hover.bottom
                class="widget_title"
                :title="
                  items[3].legend || items[3].title || items[3].label || ''
                "
                v-text="items[3].title || items[3].label"
              />
              <component
                :is="getWidgetComponentName(items[3])"
                ref="widget"
                :meta="items[3].meta"
                :size="items[3].size"
                @dblclick="removeWidget(items[3])"
              />
            </div>
          </div>

          <div class="col-lg-3 col-md-12 widgets-wrapper">
            <!-- WIDGET 1 -->
            <div class="widget_wrapper" :style="widgetStyle(items[4])">
              <div
                v-b-tooltip.hover.bottom
                class="widget_title"
                :title="
                  items[4].legend || items[4].title || items[4].label || ''
                "
                v-text="items[4].title || items[4].label"
              />
              <component
                :is="getWidgetComponentName(items[4])"
                ref="widget"
                :meta="items[4].meta"
                :size="items[4].size"
                @dblclick="removeWidget(items[4])"
              />
            </div>
          </div>
        </div>
      </div>

      <div v-if="false" class="WidgetListLayout">
        <em
          v-if="!showWidgetList"
          class="WidgetListOpenButton fas fa-cog"
          @click="openWidgetList"
        />
        <em
          v-if="showWidgetList"
          class="WidgetListOpenButton fas fa-times"
          @click="closeWidgetList"
        />

        <div v-if="showWidgetList" class="WidgetListWrapper">
          <widget-list @click="selectWidget" @showInfo="showWidgetInfo" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import $ from 'jquery'
///import Packery from "packery";
//import Draggabilly from "draggabilly";

import MockupWidget from './widgets/MockupWidget.vue'
import NumberWidget from './widgets/NumberWidget.vue'
import WidgetList from './WidgetList.vue'
import DashboardRefresh from './DashboardRefresh.vue'
//import PackeryStyles from "./PackeryStyles.vue";
import WidgetInfo from './WidgetInfo.vue'

import { createWidget as createActiveGPSWidget } from '@c/dashboard_module/widgets/ActiveGPSWidget.vue'
import { createWidget as createActiveCircuitsPercWidget } from '@c/dashboard_module/widgets/ActiveCicuitsPercWidget.vue'
import { createWidget as createFinishedCircuitsPercWidget } from '@c/dashboard_module/widgets/FinishedCicuitsPercWidget.vue'
import { createWidget as createVehicleStatusWidget } from '@c/dashboard_module/widgets/RealtimeVehicleStatusWidget.vue'
import { createWidget as createTopAnomaliesWidget } from '@c/dashboard_module/widgets/TopAnomaliesWidget.vue'

/*
<packery-styles/>
   <div v-if="false class="grid" ref="grid" :class="{ withWidgetList: showWidgetList }">
        <div class="grid-item" v-for="item in panel.widgets" :key="item.id">
          .container
            .row
              .col-4.offset-8.d-flex.align-items-center.justify-content-end
                em.fas.fa-info.widget_action_icon.widget_action_icon__info(@click="showWidgetInfo(item)")
                em(v-if="false" @click="removeWidget(item)" class="widget_action_icon removeIcon fas fa-trash-alt")
          
          <div class="widget_wrapper" :style="widgetStyle(item)">
            <div class="widget_title" :style="widgetTitleStyle(item)" v-show="item.title || item.label" v-b-tooltip.hover.bottom :title="item.legend||item.title||item.label||''">{{ item.title || item.label }}</div>
            <component ref="widget" @dblclick="removeWidget(item)" v-bind:is="getWidgetComponentName(item)"      :meta="item.meta" :size="item.size"></component>
          </div>

        </div>
      </div>
      */

export default {
  components: {
    WidgetInfo,
    //PackeryStyles,
    DashboardRefresh,
    MockupWidget,
    NumberWidget,
    WidgetList,
    ActiveGPSWidget: async () =>
      /* webpackChunkName: "main" */ import(
        '@c/dashboard_module/widgets/ActiveGPSWidget.vue'
      ),
    ActiveCicuitsPercWidget: async () =>
      /* webpackChunkName: "main" */ import(
        '@c/dashboard_module/widgets/ActiveCicuitsPercWidget.vue'
      ),
    RealtimeVehicleStatusWidget: async () =>
      /* webpackChunkName: "main" */ import(
        '@c/dashboard_module/widgets/RealtimeVehicleStatusWidget.vue'
      ),
    TopAnomaliesWidget: async () =>
      /* webpackChunkName: "main" */ import(
        '@c/dashboard_module/widgets/TopAnomaliesWidget.vue'
      ),
    FinishedCicuitsPercWidget: async () =>
      /* webpackChunkName: "main" */ import(
        '@c/dashboard_module/widgets/FinishedCicuitsPercWidget.vue'
      ),
  },
  data() {
    return {
      widgetInfoItem: {},
      showWidgetList: false,
      panel: {
        widgets: [
          createActiveGPSWidget(),
          createActiveCircuitsPercWidget(),
          createFinishedCircuitsPercWidget(),
          createVehicleStatusWidget(),
          createTopAnomaliesWidget(),
        ],
      },
    }
  },
  computed: {
    items() {
      return this.panel.widgets
    },
  },
  watch: {
    /**
     * POC: The specia grid needs to be updated if the list changed
     */
    'panel.widgets': {
      handler() {
        this.$nextTick(() => {
          this.refreshGrid()
        })
      },
    },
  },
  created() {
    this.pckryInit = false
  },
  async mounted() {
    //this.initializeGrid();
  },

  methods: {
    /**
     * Dashboard refresh feature: If enabled, widgets are refresh each X minutes.
     */
    refreshWidgets() {
      const widgets = Array.isArray(this.$refs.widget)
        ? this.$refs.widget
        : this.$refs.widget
        ? [this.$refs.widget]
        : []

      widgets.forEach((widget, index) => {
        console.log('REFRESH WIDGET', index + 1)
        widget.refresh && widget.refresh()
      })
    },
    /**
     * POC: Widget have a title
     */
    widgetTitleStyle(item) {
      return ((item.wrapper || {}).title || {}).style || ''
    },
    /**
     * Widgets share a base shell (style)
     */
    widgetStyle(item) {
      let baseStyles = ((item.wrapper || {}).style || '' + ';')
        .split(';;')
        .join(';')
      let sizeStyle =
        item.size.applyToWrapper !== false
          ? `height:${item.size.height}px` //width:${item.size.width}px;
          : ``
      return baseStyles + sizeStyle
    },
    /**
     * POC: Widgets have an information window
     */
    showWidgetInfo(item) {
      this.widgetInfoItem = item
      this.$nextTick(() => {
        this.$bvModal.show('widget_info_modal')
      })
    },
    /**
     * POC: Widgets can be removed
     * @deprecated
     */
    removeWidget(widget) {
      /*
      let index = this.panel.widgets.findIndex((w) => w.id == widget.id);
      if (index >= 0) {
        this.panel.widgets.splice(index, 1);
      }
      */
    },
    /**
     * POC: Used when the user select a widget from a list
     */
    selectWidget(widget) {
      if (this.panel.widgets.find((w) => w.id == widget.id)) {
        return
      }
      this.panel.widgets.push(widget)
      this.refreshGrid()
    },
    openWidgetList() {
      this.showWidgetList = true
      this.refreshGrid()
    },
    closeWidgetList() {
      this.showWidgetList = false
      this.refreshGrid()
    },
    /**
     * @deprecated
     */
    refreshGrid() {
      //setTimeout(() => this.pckry.layout(), 2000);
    },
    /**
     * @deprecated
     * The dashboard uses an special dragabble layout
     */
    initializeGrid() {
      if (this.pckryInit) {
        this.pckry.destroy()
        this.pckryInit = false
      }
      let el = this.$refs.grid
      var pckry =
        (this.pckry =
        window.pckry =
          new Packery(el, {
            itemSelector: '.grid-item',
            //columnWidth: 250,
            isHorizontal: false,
            gutter: 10,
          }))

      // make all grid-items draggable
      $(this.$refs.grid)
        .find('.grid-item')
        .each(function (i, gridItem) {
          var draggie = new Draggabilly(gridItem)
          // bind drag events to Packery
          pckry.bindDraggabillyEvents(draggie)
        })

      this.pckryInit = true
    },
    /**
     * Normalize component name
     * Used for mocks
     */
    getWidgetComponentName(item) {
      let table = {
        mockup: 'MockupWidget',
        number: 'NumberWidget',
      }
      return table[item.componentName] || item.componentName
    },
  },
}
</script>

<style lang="scss" scoped>
.grid {
  max-height: calc(100vh - 75px);
  min-height: calc(100vh - 75px);
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
}
.grid.withWidgetList {
  margin-right: 20px;
}
.grid-item {
  position: relative;
  min-width: 200px;
}
.DashboardPanels {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  background-color: transparent;
  padding: 15px;
}
.DashboardPanels.withWidgetList {
  grid-template-columns: 1fr 320px;
}
.WidgetListWrapper {
  display: flex;
  flex-direction: column;
}
.WidgetListCloseButton {
  color: var(--dashboard-color-main);
  font-size: 50px;
  display: flex;
  cursor: pointer;
  align-self: flex-end;
  font-family: system-ui;
}
.WidgetListOpenButton {
  position: absolute;
  top: 33px;
  right: 20px;
  color: var(--dashboard-color-main);
  width: 40px;
  display: flex;
  justify-content: center;
  font-size: 35px;
  cursor: pointer;
}
.removeIcon {
  color: red;
}
.widget_action_icon {
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  padding: 5px 10px;
}
.widget_action_icon:hover {
  background-color: rgba(108, 59, 59, 0.13);
}
.grid-item:hover .widget_action_icon {
  opacity: 1;
}
.widget_action_icon__info {
  color: var(--dashboard-color-main);
}

.widget_title {
  font-weight: bold;
  font-size: 12px;
  color: #333;
  text-align: left;
}
.widget_wrapper {
  background-color: white;
  padding: 10px;
  border: 1px solid #80808000;
  box-shadow: -1px 5px 5px -2px rgba(108, 59, 59, 0.51);
}
.widgets-wrapper {
  div {
    margin: 5px;
  }
}
</style>

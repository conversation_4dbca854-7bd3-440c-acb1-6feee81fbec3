<template>
  <div class="DashboardRefreshControl">
    {{ $t('dashboard.lastUpdateDatetime') }}&nbsp;: {{ lastUpdateDatetime }}

    <span class="refresh_control_wrapper" @click="toggleRefresh">
      <em v-show="!refreshing" class="fas fa-play refresh_icon" />
      <em v-show="refreshing" class="fas fa-stop refresh_icon" />
      <span v-show="refreshing" class="refresh_tooltip">Pause</span>
      <span v-show="!refreshing" class="refresh_tooltip">Play</span>
    </span>
  </div>
</template>
<script setup>
import i18n from '@/i18n'
//Inject
const $date = inject('$date')

//Emits
const emit = defineEmits(['refresh'])

//Consts
const intervalTime =
  import.meta.env.DASHBOARD_REFRESH_INTERVAL_MILLISECONDS || 1000 * 60 * 5
const refreshing = ref(true)
const lastUpdateDatetimeStamp = ref(Date.now())
const interval = ref(null)

//Computed
const lastUpdateDatetime = computed(() => {
  return i18n.t('dashboard.last_updated_at', {
    date: $date.formatDate(lastUpdateDatetimeStamp.value),
    time: $date.formatTime(lastUpdateDatetimeStamp.value),
  })
})

//Watchers
watch(refreshing, (curr, prev) => {
  if (refreshing.value) {
    refresh()
    startInterval()
  } else {
    clearInterval()
  }
})

//Lifecycle hooks
onBeforeMount(() => {
  setTimeout(() => {
    startInterval()
  }, intervalTime)
})

onUnmounted(() => {
  clearInterval()
})

//Methods
/**
 * This component is not aware about how widgets are refresh
 */
const refresh = () => {
  emit('refresh')
  lastUpdateDatetimeStamp.value = Date.now()
}

const startInterval = () => {
  interval.value = setInterval(() => {
    refresh()
  }, intervalTime)
}

const clearInterval = () => {
  interval.value && clearInterval(interval.value)
}

const toggleRefresh = () => {
  refreshing.value = !refreshing.value
}
</script>
<style lang="scss" scoped>
.refresh_icon {
  margin-left: 5px;
  color: white;
  font-size: 14px;
  position: relative;
  top: 0px;
}
.refresh_control_wrapper {
  cursor: pointer;
  border: 1px solid #0b72b5;
  border-radius: 10px;
  padding: 0px 6px;
  justify-content: center;
  align-items: center;
  background-color: #0b72b5;
  width: 100px;
  margin-left: 15px;
}
.refresh_tooltip {
  font-size: 9px;
  color: white;
  margin-left: 5px;
  font-weight: bold;
  position: relative;
  top: -2px;
}
.DashboardRefreshControl {
  font-size: 14px;
  padding: 25px 25px 0px 25px;
}
</style>

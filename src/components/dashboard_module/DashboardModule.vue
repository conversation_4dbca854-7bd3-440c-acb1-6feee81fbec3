<template>
  <div class="DashboardModule">
    <div v-if="isVisible" class="popupOverlay">
      <em
        class="closePopup fas fa-times"
        @click="() => $store.dispatch('app/setCurrentFloatingModule', '')"
      />
      <div class="popupShell">
        <dashboard-panels />
      </div>
    </div>
  </div>
</template>
<script>
import DashboardPanels from './DashboardPanels.vue'

export default {
  components: {
    DashboardPanels,
  },
  data() {
    return {
      isVisible: false,
    }
  },
  computed: {
    currentFloatingModule() {
      return this.$store.getters['app/currentFloatingModule']
    },
  },
  watch: {
    currentFloatingModule() {
      this.isVisible = this.currentFloatingModule === 'dashboard'
    },
  },
}
</script>
<style lang="scss" scoped>
.popupToggle {
  cursor: pointer;
  padding: 0.8rem;
  color: white;
  border-radius: 100px;
  background-color: #0b72b5;
  max-width: 40px;
  max-height: 40px;
}
.popupOverlay {
  position: fixed;
  top: 0px;
  left: 0px;
  width: calc(100vw);
  height: calc(100vh);
  z-index: 100000000;
  background-color: rgba(0, 0, 0, 0.2);
}
.popupShell {
  position: relative;
  margin-left: 120px;
  // border: 2px solid rgba(0, 0, 0, 0.082);
  min-height: calc(100vh);
  background-color: white;
  height: calc(100vh);
  max-height: calc(100vh);
  overflow-y: auto;
}
.closePopup {
  position: absolute;
  top: 50px;
  left: 100px;
  cursor: pointer;
  height: 40px;
  width: 40px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  border-radius: 50%;
  background-color: #0b72b5;
  z-index: 9;
}
</style>

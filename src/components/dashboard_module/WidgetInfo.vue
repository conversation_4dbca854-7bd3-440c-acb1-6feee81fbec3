<template lang="pug">
    .widget_info
        //p {{description}}
        a(:href="imageUrl" target="_blank")
            img(:src="imageUrl")
        //p {{randomText}}
</template>
<script>
import { faker } from '@/utils/faker'
export default {
  props: {
    widget: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    randomText() {
      return faker.lorem.paragraph(Math.round(Math.random() * 10))
    },
    description() {
      return (
        this.widget.description ||
        faker.lorem.paragraph(Math.round(Math.random() * 5))
      )
    },
    imageUrl() {
      return (this.widget.meta || {}).imageUrl || 'https://placeimg.com/640/480'
    },
  },
}
</script>
<style lang="scss" scoped>
img {
  max-width: 100%;
  max-height: 200px;
  margin-bottom: 20px;
}
</style>

<template>
  <div ref="root" class="WidgetList">
    <p class="title">Panneau de configuration</p>
    <p v-if="false" class="paragraph">Fréquence de rafraichissement</p>
    <select v-if="false" v-model="refreshTime" class="refresh_time__select">
      <option v-if="false" :value="30000">30 secondes</option>
      <option v-if="false" :value="60000">1 minute</option>
      <option :value="0" />
      <option :value="300000">5 minutes</option>
    </select>
    <p class="list_title title">Indicateurs disponibles</p>
    <div class="List">
      <div v-for="cat in categories" :key="cat.id">
        <div class="btn btn-block" @click="toggleCatItems(cat.id)">
          {{ cat.name }}
        </div>
        <div
          v-for="item in filterItems(cat.name)"
          v-show="cat.show"
          :key="item.id"
          class="item"
          :data-id="item.id"
        >
          <div class="item_title" @dblclick="selectWidget(item.id)">
            {{ item.label }}
            <em class="fas fa-info info_icon" @click="showInfo($event, item)" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import $ from 'jquery'
import Draggabilly from 'draggabilly'
import vehiclesByZoneChart from '@c/dashboard_module/widgets/assets/mockups/chart1.jpeg'
import acceptedAlertsChart from '@c/dashboard_module/widgets/assets/mockups/chart3.jpeg'
import realtimeMapChart from '@c/dashboard_module/widgets/assets/mockups/chart5.jpeg'
import puceReadingsChart from '@c/dashboard_module/widgets/assets/mockups/chart6.jpeg'
import commonReportsChart from '@c/dashboard_module/widgets/assets/mockups/chart9.jpeg'
export default {
  data() {
    return {
      refreshTime: '300000',
      categories: [
        {
          id: 1,
          name: 'Favoris',
          show: true,
        },
        {
          id: 2,
          name: 'Prédéfinis',
          show: true,
        },
      ],
      items: [
        {
          id: 'VEHICLES_BY_ZONE_COUNT',
          label: 'Nb de véhicules par type de zone',
          componentName: 'mockup',
          size: {
            width: 300,
            height: 200,
          },
          category: 'Prédéfinis',
          meta: {
            label: 'Nb de véhicules par type de zone',
            chartNumber: 1,
            imageUrl: vehiclesByZoneChart,
          },
        },
        {
          id: 'ACCEPTED_ALERTS',
          label: "Alertes à s'acquitter",
          componentName: 'mockup',
          size: {
            width: 300,
            height: 200,
          },
          category: 'Prédéfinis',
          meta: {
            label: "Alertes à s'acquitter : 4",
            chartNumber: 3,
            imageUrl: acceptedAlertsChart,
          },
        },
        {
          id: 'REALTIME_MAP',
          label: 'Carte temps réel',
          componentName: 'mockup',
          size: {
            width: 630,
            height: 475,
          },
          category: 'Favoris',
          meta: {
            chartNumber: 5,
            imageUrl: realtimeMapChart,
          },
        },
        {
          id: 'PUCE_READINGS',
          label: 'Lectures de puce',
          componentName: 'mockup',
          size: {
            width: 300,
            height: 200,
          },
          category: 'Prédéfinis',
          meta: {
            label: 'Lectures de puce',
            chartNumber: 6,
            imageUrl: puceReadingsChart,
          },
        },
        {
          id: 'COMMON_REPORTS',
          label: 'Rapports habituels',
          componentName: 'mockup',
          size: {
            width: 300,
            height: 200,
          },
          category: 'Prédéfinis',
          meta: {
            chartNumber: 9,
            imageUrl: commonReportsChart,
          },
        },
      ],
    }
  },
  watch: {
    items() {
      if (this.items.length > 0) {
        this.$nextTick(() => {
          this.installDraggieToList()
        })
      }
    },
  },
  mounted() {
    if (this.items.length > 0) {
      this.$nextTick(() => {
        this.installDraggieToList()
      })
    }
  },
  methods: {
    showInfo(e, item) {
      e.stopPropagation()
      e.preventDefault()
      this.$emit('showInfo', item)
    },
    toggleCatItems(id) {
      this.categories.find((cat) => cat.id === id).show = !this.categories.find(
        (cat) => cat.id === id
      ).show
    },
    filterItems(cat) {
      return this.items.filter((item) => item.category === cat)
    },
    selectWidget(id) {
      console.debugVerbose(8, 'selectWidget', id)
      this.$emit('click', {
        ...this.items.find((w) => w.id == id),
      })
    },
    installDraggie(elem) {
      var draggie = new Draggabilly(elem, {
        // options...
      })
      draggie.on('pointerUp', (e) => {
        if (e.layerX < -10) {
          this.selectWidget(elem.dataset.id)
        }
        this.reinstallDraggie(draggie, elem)
      })
      this.draggies = this.draggies || []
      this.draggies.push(draggie)
    },
    reinstallDraggie(draggie, elem) {
      draggie.destroy()
      this.installDraggie(elem)
    },
    installDraggieToList() {
      if (this.draggies) {
        this.draggies.forEach((d) => d.destroy())
      }
      $(this.$refs.root)
        .find('.item')
        .toArray()
        .forEach((el) => {
          this.installDraggie(el)
        })
    },
  },
}
</script>
<style lang="scss" scoped>
.title {
  font-size: 18px;
  color: #0b72b5;
  font-family: 'Open Sans', sans-serif;
  font-weight: bold;
  text-align: center;
}
.WidgetList {
  margin-top: 50px;
  background-color: #f9f9ff;
  position: relative;
  z-index: 10;
  padding: 10px;
}
.item {
  padding: 5px;
  cursor: pointer;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 3px #0000005c;
  border-radius: 4px;
  opacity: 1;
  margin-top: 10px;
}
.item_title {
  font-family: 'Open Sans', sans-serif;
  font-weight: bold;
  font-size: 14px;
}
.refresh_time__select {
  padding: 7px;
  border: 1px solid #707070;
  border-radius: 4px;
  background-color: #f9f9ff;
}
.list_title {
  margin-top: 15px;
}
.paragraph {
  font: var(--unnamed-font-style-normal) normal
    var(--unnamed-font-weight-normal) 12px/20px
    var(--unnamed-font-family-open-sans);
  letter-spacing: var(--unnamed-character-spacing-0);
  text-align: left;
  font: normal normal normal 12px/20px Open Sans;
  letter-spacing: 0px;
  color: #484848;
  opacity: 1;
}
.info_icon {
  opacity: 0.1;
  transition: opacity 0.2s;
  float: right;
  position: relative;
  top: 0px;
  color: var(--dashboard-color-main);
  padding: 5px 10px;
  border-radius: 50px;
}
.item:hover .info_icon {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}
</style>

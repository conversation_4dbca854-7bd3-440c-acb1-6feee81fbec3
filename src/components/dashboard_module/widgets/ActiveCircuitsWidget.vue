<template>
  <NumberWidget :value="value" />
</template>
<script>
import NumberWidget from '@c/dashboard_module/widgets/NumberWidget.vue'
import { getActiveCircuits } from '@/api/dashboardApi.js'

/**
 * @warn Unused
 * Nombre de véhicules actifs sur les dernières 24 heures
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module ActiveCircuitsWidget
 */
export default {
  components: {
    NumberWidget,
  },
  props: {
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      //Format: Active circuits/Planned circuits (i.g 5/10)
      value: '',
    }
  },
  async mounted() {
    this.refresh()
  },
  methods: {
    async refresh() {
      let res = await getActiveCircuits()
      this.value = `${res.activeCircuitsCount}/${res.plannedCircuitsCount}`
    },
  },
}
</script>

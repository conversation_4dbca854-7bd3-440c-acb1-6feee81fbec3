<template lang="pug">
    .active_gps_widget
        EchartsBar(:xAxis="xAxis" :yAxis="yAxis" :series="series")
</template>
<script>
import EchartsBar from './charts/EchartsBar.vue'
import { getRealtimeVehicleStatusStats } from '@/api/dashboardApi.js'
import i18n from '@/i18n'
import vehicleStatusWidget from '@c/dashboard_module/widgets/assets/vehicle-status-widget.jpg'

export function createWidget() {
  return {
    id: 'VEHICLE_STATUS',
    title: i18n.t('dashboard.widgets.realtime_vehicle_status.title'),
    legend: i18n.t('dashboard.widgets.realtime_vehicle_status.legend'),
    componentName: 'RealtimeVehicleStatusWidget',
    size: {
      width: 750,
      height: 400,
    },
    category: 'Prédéfinis',
    meta: {
      imageUrl: vehicleStatusWidget,
    },
  }
}

/**
 * VEHICLE_STATUS
 * Le statut des véhicules
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module RealtimeVehicleStatusWidget
 */
export default {
  components: {
    EchartsBar,
  },
  props: {
    size: {
      type: Object,
      default: () => ({
        width: 200,
        height: 200,
      }),
    },
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      xAxis: {
        type: 'value',
      },
      yAxis: {
        type: 'category',
        data: ['Today'],
        axisLabel: {
          show: false,
        },
      },
      series: [],
    }
  },
  mounted() {
    this.refresh()
  },
  methods: {
    async refresh() {
      this.series = [...(await getRealtimeVehicleStatusStats())]
    },
  },
}
</script>
<style lang="scss" scoped>
.active_gps_widget {
  min-height: 300px !important;
}
</style>

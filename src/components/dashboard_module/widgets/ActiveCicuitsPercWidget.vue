<template lang="pug">
  EchartsGauge(:series="series")
</template>
<script>
import EchartsGauge from './charts/EchartsGauge.vue'
import { getActiveCircuitsPercentage } from '@/api/dashboardApi.js'
import i18n from '@/i18n'
import activeCircuitImage from '@c/dashboard_module/widgets/assets/active-circuits-perc-widget.jpg'

export function createWidget() {
  return {
    id: 'ACTIVE_CIRCUIT_PERC',
    label: i18n.t('dashboard.widgets.active_circuits_perc.title'),
    legend: i18n.t('dashboard.widgets.active_circuits_perc.legend'),
    componentName: 'ActiveCicuitsPercWidget',
    size: {
      width: 400,
      height: 400,
    },
    meta: {
      imageUrl: activeCircuitImage,
    },
  }
}

/**
 * Name/Widget: Taux de réalisation moyen des circuits en cours de réalisation
 * Code: ACTIVE_CIRCUIT_PERC
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module ActiveCicuitsPercWidget
 */
export default {
  components: {
    EchartsGauge,
  },
  props: {
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      series: [
        {
          //name: "Taux de réalisation moyen des circuits en cours de réalisation",
          type: 'gauge',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false,
          },
          title: {
            borderWidth: 0,
            fontSize: 16,
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 0,
              //borderColor: "#464646",
            },
          },
          axisLine: {
            lineStyle: {
              width: 40,
            },
          },
          splitLine: {
            show: false,
            distance: 0,
            length: 10,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
            distance: 50,
          },
          data: [
            {
              value: 20,
              //name: "Réalisation Circuit",
              title: {
                offsetCenter: ['0%', '-20%'],
              },
              detail: {
                valueAnimation: true,
                offsetCenter: ['0%', '0%'],
              },
              itemStyle: {
                color: '#0b72b5',
              },
            },
          ],
          detail: {
            width: 50,
            height: 14,
            fontSize: 22,
            color: '#0b72b5',
            borderColor: 'auto',
            borderRadius: 20,
            borderWidth: 0,
            formatter: '{value}%',
          },
        },
      ],
    }
  },
  async mounted() {
    this.refresh()
  },
  methods: {
    async refresh() {
      this.series[0].data[0].value = await getActiveCircuitsPercentage()
    },
  },
}
</script>

<template>
  <NumberWidget color="white" :value="value" />
</template>
<script>
import NumberWidget from '@c/dashboard_module/widgets/NumberWidget.vue'
import { getRealtimeNoEmitSevenTwoCount } from '@/api/dashboardApi.js'
import noEmitVehicleChart from '@c/dashboard_module/widgets/assets/mockups/chart7.jpeg'

export function createWidget() {
  return {
    id: 'NO_EMIT_VEHICLE_COUNT',
    label: 'Le nombre de véhicules sans émission depuis 72 heures',
    componentName: 'NoEmitSevenTwoWidget',
    wrapper: {
      style: 'background-color:#FF4545;',
      title: {
        style: 'color:white',
      },
    },
    size: {
      width: 250,
      height: 200,
    },
    category: 'Prédéfinis',
    meta: {
      label: 'Le nombre de véhicules sans émission depuis 72 heures',
      imageUrl: noEmitVehicleChart,
    },
  }
}

/**
 * @warn Unused
 * NO_EMIT_VEHICLE_COUNT
 * Le nombre de véhicules sans émission depuis 72 heures
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module NoEmitSevenTwoWidget
 */
export default {
  components: {
    NumberWidget,
  },
  props: {
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      value: '',
    }
  },
  async mounted() {
    this.refresh()
  },
  methods: {
    async refresh() {
      this.value = `${await getRealtimeNoEmitSevenTwoCount()}`
    },
  },
}
</script>

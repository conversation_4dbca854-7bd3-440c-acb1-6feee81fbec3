<template lang="pug">
    .active_gps_widget
        EchartsBar(:yAxis="yAxis" :xAxis="null" :series="series")
</template>
<script>
import EchartsBar from './charts/EchartsBar.vue'
import { getActiveGPS } from '@/api/dashboardApi.js'

/**
 * ACTIVE_CIRCUIT_PERC_BY_VEHICLE
 * Taux de réalisation moyen des circuits en cours de réalisation par vehicule
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module ActiveCircuitsPercByVehicle
 */
export default {
  components: {
    EchartsBar,
  },
  props: {
    size: {
      type: Object,
      default: () => ({
        width: 200,
        height: 200,
      }),
    },
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      yAxis: {
        type: 'category',
        data: ['Camion 1'],
      },
      series: [
        {
          name: 'Taux de réalisation moyen des circuits en cours de réalisation par vehicule',
          type: 'bar',
          data: [30],
          emphasis: {
            label: {
              show: true,
            },
          },
        },
      ],
    }
  },
  mounted() {
    this.refresh()
  },
  methods: {
    /**
     * Fetch from API, normalize, and updates echarts dataset
     */
    async refresh() {
      let normalizedData = await getActiveGPS()
      this.yAxis.data = normalizedData.map((d) => d.label)
      this.series[0].data = normalizedData.map((d) => d.value)
    },
  },
}
</script>
<style lang="scss" scoped>
.active_gps_widget {
  min-height: 300px !important;
}
</style>

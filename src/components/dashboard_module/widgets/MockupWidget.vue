<template>
  <div class="MockupWidget" :style="style">
    <img :src="src" />
  </div>
</template>
<script>
var images = {}

;[1, 2, 3, 4, 5, 6, 7, 8, 9].forEach((n) => {
  let id = `./assets/mockups/chart${n}.jpeg`
  images[id] = import(`./assets/mockups/chart${n}.jpeg`)
})

/**
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module MockupWidget
 */
export default {
  props: {
    size: {
      type: Object,
      default: () => ({
        width: 200,
        height: 200,
      }),
    },
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    style() {
      return {
        width: `${this.size.width}px`,
        height: `${this.size.height}px`,
      }
    },
    src() {
      let id = `./assets/mockups/chart${this.meta.chartNumber || 1}.jpeg`
      return images[id]
    },
  },
}
</script>
<style lang="scss" scoped>
.MockupWidget {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding-top: 20px;
  background-color: white;
}
.title {
  font-weight: bold;
  font-size: 12px;
  color: #333;
  text-align: left;
}
img {
  max-width: 100%;
  max-height: 100%;
}
</style>

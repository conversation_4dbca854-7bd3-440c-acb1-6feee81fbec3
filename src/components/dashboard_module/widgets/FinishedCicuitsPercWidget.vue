<template lang="pug">
  EchartsGauge(:series="series")
</template>
<script>
import EchartsGauge from './charts/EchartsGauge.vue'
import { getFinishedCircuitsPercentage } from '@/api/dashboardApi.js'
import i18n from '@/i18n'
import Vue from 'vue'
import moment from 'moment'
import finishedCircuitsPercWidget from '@c/dashboard_module/widgets/assets/finished-circuits-perc-widget.jpg'
export function createWidget() {
  return {
    id: 'FINISHED_CIRCUIT_PERC',
    label: i18n.t('dashboard.widgets.finished_circuits_perc.title', {
      time: Vue.$date.formatTime(moment().minutes(0)),
    }),
    legend: i18n.t('dashboard.widgets.finished_circuits_perc.legend'),
    componentName: 'FinishedCicuitsPercWidget',
    size: {
      width: 400,
      height: 400,
    },
    category: 'Prédéfinis',
    meta: {
      imageUrl: finishedCircuitsPercWidget,
    },
  }
}

/**
 *  FINISHED_CIRCUIT_PERC
 * Taux de réalisation moyen des circuits terminés
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module ActiveGPSWidget
 */
export default {
  components: {
    EchartsGauge,
  },
  props: {
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      series: [
        {
          //name: "Taux de réalisation moyen des circuits terminés",
          type: 'gauge',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false,
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 0,
              //borderColor: "#464646",
            },
          },
          axisLine: {
            lineStyle: {
              width: 40,
            },
          },
          splitLine: {
            show: false,
            distance: 0,
            length: 10,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
            distance: 50,
          },
          data: [
            {
              value: 20,
              //name: "Réalisation Circuit terminé",
              title: {
                offsetCenter: ['0%', '-20%'],
              },
              detail: {
                valueAnimation: true,
                offsetCenter: ['0%', '0%'],
              },
              itemStyle: {
                color: '#0b72b5',
              },
            },
          ],
          title: {
            fontSize: 14,
          },
          detail: {
            width: 50,
            height: 14,
            fontSize: 22,
            color: '#0b72b5',
            borderColor: 'auto',
            borderRadius: 20,
            borderWidth: 0,
            formatter: '{value}%',
          },
        },
      ],
    }
  },
  async mounted() {
    this.refresh()
  },
  methods: {
    async refresh() {
      this.series[0].data[0].value = await getFinishedCircuitsPercentage()
    },
  },
}
</script>

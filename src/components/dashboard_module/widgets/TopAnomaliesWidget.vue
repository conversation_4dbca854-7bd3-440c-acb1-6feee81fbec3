<template lang="pug">
    .top_anomalies_widget
        EchartsPie(:series="series" :legend="false")
        .anomalies()
            .box(v-for="item in anomalies" :key="item.name" :style="'color:'+item.color")
                .number(:style="'color:'+item.color") {{item.value}}
                .title(:style="'color:'+item.color") {{item.name}}
</template>
<script>
import EchartsPie from './charts/EchartsPie.vue'
import { getRealtimeTopAnomaliesDashboardData } from '@/api/dashboardApi.js'
import i18n from '@/i18n'
import topAnomaliesWidget from '@c/dashboard_module/widgets/assets/top-anomalies-widget.jpg'

export function createWidget() {
  return {
    id: 'ANOMALIES_COUNT',
    title: i18n.t('dashboard.widgets.top_anomalies.title'),
    legend: i18n.t('dashboard.widgets.top_anomalies.legend'),
    componentName: 'TopAnomaliesWidget',
    size: {
      width: 400,
      height: 810,
    },
    category: 'Prédéfinis',
    meta: {
      imageUrl: topAnomaliesWidget,
    },
  }
}

/**
 * ANOMALIES_COUNT
 * Nombre d’anomalies signalées sur les dernières 24 heures
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module TopAnomaliesWidget
 */
export default {
  name: 'TopAnomaliesWidget',
  components: {
    EchartsPie,
  },
  props: {
    size: {
      type: Object,
      default: () => ({
        width: 200,
        height: 200,
      }),
    },
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      anomalies: [],
      series: [
        {
          name: 'Évènements',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '60%'],
          data: [],
          label: {
            show: false,
          },
          selected: {
            label: {
              show: false,
            },
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '10',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
        },
      ],
    }
  },
  mounted() {
    this.refresh()
  },
  methods: {
    /**
     * Fetch from API, normalize, and updates echarts dataset
     */
    async refresh() {
      this.anomalies = await getRealtimeTopAnomaliesDashboardData()
      this.series[0].data = this.anomalies.map((item) => ({
        ...item,
        itemStyle: {
          color: item.color,
        },
      }))
    },
  },
}
</script>
<style lang="scss" scoped>
.top_anomalies_widget {
  min-height: 300px !important;
}
.anomalies {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
}
.box {
  text-align: center;
  flex-basis: 33%;
}
.number {
  font: var(--unnamed-font-style-normal) normal bold 37px/20px
    var(--unnamed-font-family-open-sans);
  letter-spacing: var(--unnamed-character-spacing-0);
  color: var(--unnamed-color-969696);
  text-align: center;
  font: normal normal bold 37px/20px Open Sans;
  letter-spacing: 0px;
  color: #969696;
  opacity: 1;
  margin-top: 20px;
}
.title {
  font: var(--unnamed-font-style-normal) normal bold 12px/18px
    var(--unnamed-font-family-open-sans);
  letter-spacing: var(--unnamed-character-spacing-0);
  color: var(--unnamed-color-969696);
  text-align: center;
  font: normal normal bold 12px/18px Open Sans;
  letter-spacing: 0px;
  color: #969696;
  opacity: 1;
  margin-top: 15px;
}
</style>

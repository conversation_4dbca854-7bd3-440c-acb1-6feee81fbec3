<template lang="pug">
    .active_gps_widget
        EchartsBar(:xAxis="xAxis" :series="series")
</template>
<script>
import EchartsBar from './charts/EchartsBar.vue'
import { getActiveGPS } from '@/api/dashboardApi.js'
import i18n from '@/i18n'
import activeGpsWidget from '@c/dashboard_module/widgets/assets/active-gps-widget.jpg'

export function createWidget() {
  return {
    id: 'ACTIVE_VEHICLES_COUNT',
    title: i18n.t('dashboard.widgets.active_gps.title'),
    legend: i18n.t('dashboard.widgets.active_gps.legend'),
    componentName: 'ActiveGPSWidget',
    size: {
      width: 750,
      height: 400,
    },
    category: 'Prédéfinis',
    meta: {
      imageUrl: activeGpsWidget,
    },
  }
}

/**
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module ActiveGPSWidget
 */
export default {
  components: {
    EchartsBar,
  },
  props: {
    size: {
      type: Object,
      default: () => ({
        width: 200,
        height: 200,
      }),
    },
    meta: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      xAxis: {
        type: 'category',
        data: ['14h00'],
      },
      series: [
        {
          type: 'bar',
          data: [30],
          emphasis: {
            label: {
              show: true,
            },
          },
        },
      ],
    }
  },
  mounted() {
    this.refresh()
  },
  methods: {
    /**
     * Fetch from API, normalize, and updates echarts dataset
     */
    async refresh() {
      let normalizedData = await getActiveGPS()
      this.xAxis.data = normalizedData.map((d) => d.label)
      this.series[0].data = normalizedData.map((d) => d.value)
    },
  },
}
</script>
<style lang="scss" scoped>
.active_gps_widget {
}
</style>

# Dashboard Widgets

## Fixed dashboard widgets

### ACTIVE_VEHICLES_COUNT

- Nombre de véhicules actifs dans les dernières 24h - Nombre de véhicules actifs sur les dernières 24 heures

  Component: ActiveGPSWidget

### ACTIVE_CIRCUIT_PERC

- Taux de réalisation moyen des circuits en cours de réalisation (ex: Nombre de circuits en cours de réalisation)

  Component: ActiveCicuitsWidget

### FINISHED_CIRCUIT_PERC

- Taux de réalisation moyen des circuits terminés - (ex: Taux d’avancé des prestations de réalisation de circuit)

  Component: ActiveCicuitsPercWidget

### VEHICLE_STATUS

- Statut temps réel des véhicules - Le statut des véhicules

  Component: RealtimeVehicleStatusWidget

### NO_EMIT_VEHICLE_COUNT

- Le nombre de véhicules sans émission depuis 72 heures

  Component: NoEmitSevenTwoWidget

### ANOMALIES_COUNT

- Évènements déclarés dans les dernières 24h - (ex: Nombre d’anomalies signalées sur les dernières 24 heures)

  Component: TopAnomaliesWidget

### ACTIVE_CIRCUIT_PERC_BY_VEHICLE

- Taux de réalisation moyen des circuits en cours de réalisation

  Same as "Taux de réalisation moyen des circuits en cours de réalisation" but per vehicle
  API: /v2/temps_reel/indicateurs/by_vehicule - vehicule_with_circuit_finished_today.taux

  Component: TODO

  Notes: Use bar-y-category-stack (echarts)

## Widget Object example

```js
 let widget = {
    id: "realtime_no_emit_sevent_two_count_widget",
    label: "Le nombre de véhicules sans émission depuis 72 heures",
    componentName: "NoEmitSevenTwoWidget",
    size: {
        width: 250,
        height: 200,
    },
    category: "Prédéfinis",
    meta: {
        label: "Le nombre de véhicules sans émission depuis 72 heures",
        chartNumber: 7,
        imageUrl: import(`./widgets/assets/mockups/chart7.jpeg`),
    },
},
```

<template>
  <div class="root">
    <p :style="style">
      {{ meta.value || value || '999' }}
    </p>
  </div>
</template>
<script>
/**
 * @namespace components
 * @category components
 * @subcategory dashboard-module/widgets
 * @module NumberWidget
 */
export default {
  props: {
    meta: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: [String, Number],
      default: '',
    },
    color: {
      type: String,
      default: 'black',
    },
  },
  computed: {
    style() {
      return `color:${this.color}`
    },
  },
}
</script>
<style lang="scss" scoped>
p {
  font-family: 'Open Sans', sans-serif;
  letter-spacing: var(--unnamed-character-spacing-0);
  text-align: left;
  font-weight: bold;
  font-size: 72px;
  letter-spacing: 0px;
  color: #484848;
  opacity: 1;
  display: block;
  margin: 0 auto;
}
.root {
  display: flex;
  justify-items: center;
  align-items: center;
}
</style>

<template>
  <v-chart ref="chart" class="chart" :option="option" :autoresize="true" />
</template>

<script>
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON> } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
} from 'echarts/components'
import VChart, { THEME_KEY } from 'vue-echarts'

use([
  Canvas<PERSON><PERSON><PERSON>,
  <PERSON>C<PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
])

export default {
  name: 'EchartsPie',
  components: {
    VChart,
  },
  provide: {
    [THEME_KEY]: 'white',
  },
  props: {
    legend: {
      type: [Object, Boolean],
      default: () => ({
        top: '5%',
        left: 'center',
        padding: 0,
      }),
    },
    series: {
      type: Array,
      default: () => [
        {
          //name: "Traffic Sources",
          type: 'pie',
          radius: '55%',
          center: ['50%', '60%'],
          data: [
            { value: 335, name: 'Direct' },
            { value: 310, name: 'Email' },
            { value: 234, name: 'Ad Networks' },
            { value: 135, name: 'Video Ads' },
            { value: 1548, name: 'Search Engines' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    },
  },
  data() {
    return {
      option: {
        /*title: {
          text: "Traffic Sources",
          left: "center",
        },*/
        /*tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)",
        },*/
        legend: this.legend,
        series: this.series,
      },
    }
  },
  watch: {
    series: {
      handler() {
        this.option.series = this.series
        this.$nextTick(() => {
          this.$refs.chart.setOption(this.option, {})
        })
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.chart {
  min-height: 200px !important;
}
</style>

<style>
body {
  margin: 0;
}
</style>

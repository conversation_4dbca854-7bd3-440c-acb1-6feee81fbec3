<template>
  <v-chart ref="chart" class="chart" :option="option" :autoresize="true" />
</template>

<script>
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { Gauge<PERSON>hart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
} from 'echarts/components'
import VChart, { THEME_KEY } from 'vue-echarts'

use([
  Canvas<PERSON><PERSON><PERSON>,
  GaugeChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
])

export default {
  name: 'EchartsGauge',
  components: {
    VChart,
  },
  provide: {
    [THEME_KEY]: 'white',
  },
  props: {
    series: {
      type: Array,
      default: () => [
        {
          type: 'gauge',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false,
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              borderWidth: 1,
              borderColor: '#464646',
            },
          },
          axisLine: {
            lineStyle: {
              width: 40,
            },
          },
          splitLine: {
            show: false,
            distance: 0,
            length: 10,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
            distance: 50,
          },
          data: gaugeData,
          title: {
            fontSize: 14,
          },
          detail: {
            width: 50,
            height: 14,
            fontSize: 14,
            color: 'auto',
            borderColor: 'auto',
            borderRadius: 20,
            borderWidth: 1,
            formatter: '{value}%',
          },
        },
      ],
    },
  },
  data() {
    return {
      option: {
        series: this.series,
      },
    }
  },
  watch: {
    series: {
      handler() {
        this.option.series = this.series
        this.$nextTick(() => {
          this.$refs.chart.setOption(this.option, {})
        })
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.chart {
  min-height: 200px !important;
}
</style>

<style>
body {
  margin: 0;
}
</style>

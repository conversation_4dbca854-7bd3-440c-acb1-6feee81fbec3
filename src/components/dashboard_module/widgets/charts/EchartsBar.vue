<template>
  <v-chart ref="chart" class="chart" :option="option" :autoresize="true" />
</template>

<script>
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON> } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import VChart, { THEME_KEY } from 'vue-echarts'

use([
  GridComponent,
  Canvas<PERSON>enderer,
  <PERSON><PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
])

export default {
  name: 'EchartsBar',
  components: {
    VChart,
  },
  provide: {
    [THEME_KEY]: 'white',
  },
  props: {
    xAxis: {
      type: Object,
      default: () => ({
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      }),
    },
    yAxis: {
      type: Object,
      default: () => ({
        type: 'value',
      }),
    },
    series: {
      type: Array,
      default: () => [
        {
          data: [120, 200, 150, 80, 70, 110, 130],
          type: 'bar',
        },
      ],
    },
  },
  data() {
    return {
      option: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // Use axis to trigger tooltip
            type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
          },
        },
        legend: {},
        xAxis: this.xAxis,
        yAxis: this.yAxis,
        series: this.series,
      },
    }
  },
  watch: {
    xAxis: {
      handler() {
        this.option.xAxis = this.xAxis
        this.$nextTick(() => {
          this.$refs.chart.setOption(this.option, {})
        })
      },
      deep: true,
    },
    yAxis: {
      handler() {
        this.option.yAxis = this.yAxis
        this.$nextTick(() => {
          this.$refs.chart.setOption(this.option, {})
        })
      },
      deep: true,
    },
    series: {
      handler() {
        this.option.series = this.series
        this.$nextTick(() => {
          this.$refs.chart.setOption(this.option, {})
        })
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.chart {
  min-height: 300px !important;
}
</style>

<style>
body {
  margin: 0;
}
</style>

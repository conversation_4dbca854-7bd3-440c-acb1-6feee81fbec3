class TimeRangeSlider {
  constructor(element, options) {
    this.element = element
    this.options = Object.assign(
      {},
      {
        min: 0,
        max: 100,
        value: [0, 100],
        onChange: () => {},
      },
      options
    )

    this.handles = []
    this.range = null

    // Bind methods to maintain context
    this.handleDrag = this.handleDrag.bind(this)
    this.handleDragEnd = this.handleDragEnd.bind(this)
    this.handleClick = this.handleClick.bind(this)
    this.handleResize = this.handleResize.bind(this)

    this.init()
  }

  init() {
    if (!this.element) {
      console.error('TimeRangeSlider: Element is not defined')
      return
    }

    this.element.classList.add('time-range-slider')
    this.element.innerHTML = ''

    this.createHandles()
    this.createRange()
    this.updateRange()
    this.attachEvents()

    // Use a small delay to ensure the element is fully rendered
    setTimeout(() => {
      this.updateHandlePositions()
    }, 0)
  }

  createHandles() {
    for (let i = 0; i < 2; i++) {
      const handle = document.createElement('div')
      handle.classList.add('time-range-handle')
      handle.dataset.index = i.toString()
      this.element.appendChild(handle)
      this.handles.push(handle)
    }
    this.handles[0].classList.add('left')
    this.handles[1].classList.add('right')
    this.updateHandlePositions()
  }

  createRange() {
    // Create a thin line across the full width of the slider
    const track = document.createElement('div')
    track.style.position = 'absolute'
    track.style.top = '50%'
    track.style.left = '0'
    track.style.width = '100%'
    track.style.height = '2px'
    track.style.backgroundColor = '#e0e0e0'
    track.style.transform = 'translateY(-50%)'
    track.style.borderRadius = '1px'
    this.element.appendChild(track)

    // Create the colored range between handles
    this.range = document.createElement('div')
    this.range.classList.add('time-range-range')
    this.element.appendChild(this.range)
  }

  updateRange() {
    if (!this.range || !this.handles || this.handles.length < 2) return

    const left = this.handles[0].offsetLeft + this.handles[0].offsetWidth / 2
    const right = this.handles[1].offsetLeft + this.handles[1].offsetWidth / 2
    this.range.style.left = left + 'px'
    this.range.style.width = right - left + 'px'
  }

  updateHandlePositions() {
    if (!this.handles || this.handles.length < 2) return

    const { min, max, value } = this.options
    const elementWidth = this.element.offsetWidth

    if (elementWidth === 0) {
      // Element might not be visible or rendered yet
      return
    }

    this.handles.forEach((handle, index) => {
      const handleValue = value[index]
      const percentage = (handleValue - min) / (max - min)
      const handlePosition = percentage * elementWidth
      handle.style.left = handlePosition - handle.offsetWidth / 2 + 'px'
    })
    this.updateRange()
  }

  attachEvents() {
    // Bind handleDragStart for each handle
    this.handleDragStartBound = this.handleDragStart.bind(this)

    this.handles.forEach((handle) => {
      handle.addEventListener('mousedown', this.handleDragStartBound)
      handle.addEventListener('touchstart', this.handleDragStartBound, {
        passive: false,
      })
    })

    window.addEventListener('mousemove', this.handleDrag)
    window.addEventListener('touchmove', this.handleDrag, { passive: false })

    window.addEventListener('mouseup', this.handleDragEnd)
    window.addEventListener('touchend', this.handleDragEnd)

    this.element.addEventListener('click', this.handleClick)

    // Add resize listener to update positions when window size changes
    window.addEventListener('resize', this.handleResize)
  }

  destroy() {
    // Remove all event listeners
    this.handles.forEach((handle) => {
      handle.removeEventListener('mousedown', this.handleDragStartBound)
      handle.removeEventListener('touchstart', this.handleDragStartBound)
    })

    window.removeEventListener('mousemove', this.handleDrag)
    window.removeEventListener('touchmove', this.handleDrag)

    window.removeEventListener('mouseup', this.handleDragEnd)
    window.removeEventListener('touchend', this.handleDragEnd)

    window.removeEventListener('resize', this.handleResize)

    this.element.removeEventListener('click', this.handleClick)

    // Clear DOM elements
    this.element.innerHTML = ''
    this.element.classList.remove('time-range-slider')

    // Clear references
    this.handles = []
    this.range = null
  }

  handleResize() {
    // Debounce the resize event
    clearTimeout(this.resizeTimeout)
    this.resizeTimeout = setTimeout(() => {
      this.updateHandlePositions()
    }, 100)
  }

  handleDragStart(e) {
    this.draggingHandle = e.target
    const rect = this.element.getBoundingClientRect()
    this.elementRect = rect
    this.dragStartX = e.type.includes('touch')
      ? e.touches[0].clientX
      : e.clientX
    e.preventDefault()
  }

  handleDrag(e) {
    if (!this.draggingHandle) return

    e.preventDefault()

    const clientX = e.type.includes('touch') ? e.touches[0].clientX : e.clientX
    const { min, max } = this.options
    const elementWidth = this.element.offsetWidth
    const dragDistance = clientX - this.dragStartX
    const newHandlePosition = this.draggingHandle.offsetLeft + dragDistance
    let percentage =
      (newHandlePosition + this.draggingHandle.offsetWidth / 2) / elementWidth
    percentage = Math.max(0, Math.min(1, percentage))
    const newValue = min + (max - min) * percentage
    const index = parseInt(this.draggingHandle.dataset.index)

    const value = [...this.options.value]
    value[index] = Math.round(newValue)

    // Ensure minimum distance between handles
    if (Math.abs(value[0] - value[1]) < 1) {
      return
    }

    this.options.value = value.sort((a, b) => a - b)
    this.updateHandlePositions()
    this.options.onChange(this.options.value)

    this.dragStartX = clientX
  }

  handleDragEnd() {
    this.draggingHandle = null
  }

  handleClick(e) {
    // Only handle clicks directly on the track, not on handles
    if (e.target === this.element) {
      const { min, max } = this.options
      const elementWidth = this.element.offsetWidth
      const rect = this.element.getBoundingClientRect()
      const clickPosition = e.clientX - rect.left
      let percentage = clickPosition / elementWidth
      percentage = Math.max(0, Math.min(1, percentage))
      const newValue = min + (max - min) * percentage
      const value = [...this.options.value]

      // Move the closest handle to the click position
      const distance0 = Math.abs(newValue - value[0])
      const distance1 = Math.abs(newValue - value[1])
      if (distance0 < distance1) {
        value[0] = Math.round(newValue)
      } else {
        value[1] = Math.round(newValue)
      }

      // Ensure minimum distance between handles
      if (Math.abs(value[0] - value[1]) < 1) {
        return
      }

      this.options.value = value.sort((a, b) => a - b)
      this.updateHandlePositions()
      this.options.onChange(this.options.value)
    }
  }

  setValue(value) {
    this.options.value = value
    this.updateHandlePositions()
  }

  updateOptions(options) {
    this.options = Object.assign(this.options, options)
    this.updateHandlePositions()
  }
}

// Format minutes to HH:MM
export function formatValue(value) {
  let s = value * 60 * 1000
  var ms = s % 1000
  s = (s - ms) / 1000
  var secs = s % 60
  s = (s - secs) / 60
  var mins = s % 60
  var hrs = (s - mins) / 60

  if (hrs.toString().length === 1) {
    hrs = `0${hrs}`
  }

  if (mins.toString().length === 1) {
    mins = `0${mins}`
  }

  return hrs + ':' + mins
}

export default TimeRangeSlider

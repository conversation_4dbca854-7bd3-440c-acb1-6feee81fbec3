<template lang="pug">
.chart-buttons
    .chart-buttons--item(
        v-b-tooltip.hover.viewport
        :title="$t('diagnostics.chart.toolbar.zoomin')"
        @click="$emit('zoomin')"
    )
        Icon(icon="carbon:zoom-in" :style="{fontSize:'25px'}" color="var(--color-main)" )
    .chart-buttons--item(
        v-b-tooltip.hover.viewport
        :title="$t('diagnostics.chart.toolbar.zoomout')"
        @click="$emit('zoomout')"
    )
        Icon(icon="carbon:zoom-out" :style="{fontSize:'25px'}" color="var(--color-main)" )

    .chart-buttons--item(
        v-b-tooltip.hover.viewport
        :title="$t('diagnostics.chart.toolbar.zoomfit')"
        @click="$emit('zoomfit')"
    )
        Icon(icon="carbon:zoom-fit" :style="{fontSize:'25px'}" color="var(--color-main)" )

    .chart-buttons--item(
        v-b-tooltip.hover.viewport
        :title="$t('diagnostics.chart.toolbar.moveleft')"
        @click="$emit('moveleft')"
    )
        Icon(icon="carbon:chevron-left" :style="{fontSize:'25px'}" color="var(--color-main)" )

    .chart-buttons--item(
        v-b-tooltip.hover.viewport
        :title="$t('diagnostics.chart.toolbar.moveright')"
        @click="$emit('moveright')"
    )
        Icon(icon="carbon:chevron-right" :style="{fontSize:'25px'}" color="var(--color-main)" )
</template>
<script>
import { Icon } from '@iconify/vue2'

/**
 * Used by Diagnostic module to manipulate the charts using buttons:
 * Zoom-in, Zoom-out, Zoom-fit, Pan left and Pan rigth
 */
export default {
  components: {
    Icon,
  },
}
</script>
<style scoped>
.chart-buttons {
  display: flex;
  flex-direction: column;
  row-gap: 5px;
}
.chart-buttons--item {
  cursor: pointer;
  /*border: 1px solid var(--color-main);
  padding: 1px;*/
}
</style>

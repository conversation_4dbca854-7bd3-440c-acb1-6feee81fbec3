import { updateMapVehicleMarker } from './map-marker-update.js'

export function createReplayOptions() {
  return {
    replayOptions() {
      /* console.debugVerbose(8, 'computed::replayOptions') */
      const self = this

      let positions = self.$store.state.diagnostics.positions
      return {
        min: 0,
        max: positions.length - 1,

        animationOptions: {
          index: positions
            .map((pos, index) => {
              return {
                replayIndex: index,
              }
            })
            .filter((pos) => {
              return (
                pos.replayIndex >=
                  self.$store.state.diagnostics.replayPositionIndex &&
                (self.$store.state.diagnostics.replayPositionEndIndex === -1 ||
                  pos.replayIndex <=
                    self.$store.state.diagnostics.replayPositionEndIndex)
              )
            })
            .map((pos) => {
              return {
                value: pos.replayIndex,
                duration: self.replaySpeed,
              }
            }),
        },

        get currentIndex() {
          return self.$store.state.diagnostics.replayPositionIndex
        },
        set currentIndex(value) {
          self.$store.state.diagnostics.replayPositionIndex = value
          self.$store.state.diagnostics.replayPosition =
            self.positions[self.$store.state.diagnostics.replayPositionIndex]
        },
        onToggle(val) {
          self.isReplayPlaying = val
        },
        onDestroy() {
          /* console.debugVerbose(8, 'replay-mixin::options::onDestroy') */
          updateMapVehicleMarker(self.$map.getLeafletWrapperVM(), null, {
            visible: false,
          })
        },
        onAnimate(currentIndex) {
          // Hide position marker popup and remove (if available)
          let positionMarkerLayerGroup =
            self.$map.getLeafletWrapperVM().layerGroups?.positions
          if (positionMarkerLayerGroup) {
            let positionMarker = positionMarkerLayerGroup.getLayers()[0] || null
            if (positionMarker) {
              positionMarker.closePopup && positionMarker.closePopup()
            }
            positionMarkerLayerGroup.remove()
          }

          // This will open the position details on the left panel
          self.$store.state.diagnostics.chartSelectedItemTrigger = 'replay'
          self.$store.state.diagnostics.chartSelectedItem =
            self.$store.state.diagnostics.replayPosition

          if (!self.positions[currentIndex]) {
            /* console.debugVerbose(8, `onAnimate::Can't find position`, {
              currentIndex: currentIndex,
            }) */
            return
          }

          let currentPosition = self.positions[currentIndex]

          /* console.debugVerbose(8, 'onAnimate currentIndex:', currentIndex) */
          /* console.debugVerbose(8, 'Current position coords:', {
            lat: currentPosition.lat,
            lng: currentPosition.lng,
          }) */
          /* console.debugVerbose(8, 'Visibility before update:', {
            lastPosition: this.lastPosition,
            isMarkerVisible: !!currentPosition,
          }) */

          // Avoid drawing the lat/lng if the position hasn't changed
          if (
            self.lastPosition &&
            self.lastPosition.lat == currentPosition.lat &&
            self.lastPosition.lng == currentPosition.lng
          ) {
            return
          }

          updateMapVehicleMarker(
            self.$map.getLeafletWrapperVM(),
            currentPosition,
            {
              lastPosition: self.lastPosition,
              slideDuration: self.replaySpeed,
              vehicleName: self.vehicleName,
              vehicleClassName: self.vehicleClassName,
            }
          )
          self.lastPosition = currentPosition

          self.timesSinceZoom =
            self.timesSinceZoom === undefined
              ? self.updateZoomEveryPositions
              : self.timesSinceZoom

          let zoomLevel = self.$map.getSimplicitiMapVM().zoomLevel || 13

          switch (self.replaySpeed) {
            case 1000: {
              self.updateZoomEveryPositions = 1
              break
            }
            case 500: {
              self.updateZoomEveryPositions = 1
              break
            }
            case 200: {
              self.updateZoomEveryPositions = 1
              break
            }
            case 125: {
              self.updateZoomEveryPositions = 1
              break
            }
            default: {
              self.updateZoomEveryPositions = 0
              break
            }
          }

          if (self.timesSinceZoom >= self.updateZoomEveryPositions) {
            self.timesSinceZoom = 0
            self.$map
              .getLeafletWrapperVM()
              .map.setView(
                [currentPosition.lat, currentPosition.lng],
                zoomLevel
              )
          } else {
            self.timesSinceZoom++
          }
        },
      }
    },
  }
}

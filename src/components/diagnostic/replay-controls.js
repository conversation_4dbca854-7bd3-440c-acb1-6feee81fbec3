import { updateMapVehicleMarker } from './map-marker-update.js'

export function createReplayControls() {
  return {
    replayReset() {
      /* console.debugVerbose(8, 'method::replayReset') */
      if (this.replayController.currentAnimationPlaying) {
        this.replayController.destroyAnimation()
        this.replayPlayToggle()
      } else {
        this.replayController.destroyAnimation()
      }
    },
    replayPlayToggle() {
      /* console.debugVerbose(8, 'replayPlayToggle called') */
      /* console.debugVerbose(8, 'Current visibility state: ', {
        lastPosition: this.lastPosition,
        currentPosition:
          this.positions[this.replayController.currentAnimationIndex],
      }) */
      this.replayController.playToggle(this.replayOptions)
    },
    replayPrev() {
      this.replayController.prev(this.replayOptions)
    },
    replayNext() {
      this.replayController.next(this.replayOptions)
    },
    terminateReplay() {
      this.replayController.playToggle({
        ...this.replayOptions,
        value: false,
      })
      this.replayController.destroyAnimation()
      updateMapVehicleMarker(this.$map.getLeafletWrapperVM(), null, {
        visible: false,
      })
    },
  }
}

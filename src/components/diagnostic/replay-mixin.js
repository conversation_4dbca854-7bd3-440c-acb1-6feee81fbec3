/**
 *
 * @namespace mixins
 * @category mixins
 * @subcategory diagnostics
 * @module diagnostics-replay-mixin-module
 *
 **/

import { mapGetters } from 'vuex'
import { createReplayOptions } from './replay-options.js'
import { createReplayControls } from './replay-controls.js'

/**
 * @description Mixin for implementing replay functionality in the Diagnostic module.
 */
export default {
  /**
   * @property {Object} replayController
   * @see {@link /module-diagnostics-service#.createReplayController}
   */
  inject: {
    /**
     * @name replayController
     * @type {Object}
     * @description The replay controller object used to control the replay animation.
     */
    replayController: {
      default: () => ({}),
    },
  },
  /**
   * Data variables for the replay functionality.
   * @property {number} replaySpeed - The speed of the replay.
   * @property {number} updateZoomEveryPositions - The number of positions before updating the zoom level.
   * @property {boolean} isReplayPlaying - Flag indicating if the replay is currently playing.
   */
  data() {
    return {
      replaySpeed: 1000,
      updateZoomEveryPositions: 1,
      isReplayPlaying: false,
    }
  },
  computed: {
    ...mapGetters({
      positions: 'diagnostics/positions',
      vehicleClassName: 'diagnostics/vehicleClassName',
      replayPositionEndIndex: 'diagnostics/replayPositionEndIndex',
      chartSelectedItemTrigger: 'diagnostics/chartSelectedItemTrigger',
    }),
    ...createReplayOptions(),
  },
  methods: createReplayControls(),
  watch: {
    /**
     * @description Watcher for the replaySpeed property.
     * Resets the replay animation when the replay speed changes.
     */
    replaySpeed() {
      this.replayReset()
    },
    /**
     * @description Watcher for the replayPositionEndIndex property.
     * Resets the replay animation when the replay position end index changes.
     */
    replayPositionEndIndex() {
      /* console.debugVerbose(8, 'watch::replayPositionEndIndex') */
      this.replayReset()
    },
    /**
     * @description Watcher for the chartSelectedItemTrigger property.
     * Resets the replay animation when the chart selected item trigger changes to 'chart'.
     */
    chartSelectedItemTrigger() {
      /* if (newValue === 'chart' && newValue !== oldValue) {
        console.debugVerbose(8, 'watch::chartSelectedItemTrigger')
        this.replayReset()
      } */
    },
  },
  /**
   * @description Lifecycle hook invoked when the component is destroyed.
   * Terminates the replay animation.
   * @function destroyed
   */
  destroyed() {
    this.terminateReplay()
  },
}

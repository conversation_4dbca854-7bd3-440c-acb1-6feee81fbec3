<template lang="pug">
.diagnostics-analysis-consumption-gauge(ref="chart")
</template>
<script>
import * as echarts from 'echarts/core'
import { GaugeChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { computeStyleGlobalVariable } from '@/utils/styles.js'
import { queueOperationOnce } from '@/utils/promise.js'
import echartMixin from '@/mixins/echarts-mixin.js'
echarts.use([Gauge<PERSON>hart, CanvasRenderer])

function createEchartInstanceOptions(vm) {
  let colorGreen = computeStyleGlobalVariable('--color-green')

  return {
    series: [
      {
        type: 'gauge',
        axisLine: {
          lineStyle: {
            width: 5,
            color: [
              [vm.refConsumptionOver100km / 100, colorGreen],
              [1, computeStyleGlobalVariable('--color-red')],
            ],
          },
        },
        itemStyle: {
          color: colorGreen,
        },
        pointer: {
          show: true,
          itemStyle: {
            color: colorGreen,
          },
          //icon: 'arrow',
          length: '50%',
          offsetCenter: [0, '-5%'],
        },
        axisTick: {
          show: false,
        },
        anchor: {
          show: true,
          showAbove: true,
          icon: 'circle',
          size: 10,
          itemStyle: {
            borderWidth: 5,
            borderColor: colorGreen,
            color: 'white',
          },
        },
        splitLine: {
          length: 4,
          lineStyle: {
            color: colorGreen,
            borderColor: colorGreen,
            width: 2,
          },
        },
        axisLabel: {
          color: colorGreen,
          distance: 10,
          fontSize: 11,
        },

        detail: {
          valueAnimation: true,
          formatter: '{value} L/100km',
          color: colorGreen,
          fontSize: 12,
          offsetCenter: [0, '80%'],
        },
        data: [
          {
            value: vm.value,
          },
        ],
      },
    ],
  }
}

export default {
  mixins: [
    echartMixin(echarts, (vm) => vm.$refs.chart, {
      optionsHandler: createEchartInstanceOptions,
      renderOnMounted: true,
    }),
  ],
  props: {
    value: {
      type: Number,
      default: 0,
    },
    //Vehicle parameter
    refConsumptionOver100km: {
      type: Number,
      default: 38,
    },
  },
  watch: {
    value() {
      queueOperationOnce(
        `diagnostics_gauge_${this._uid}_update_on_value_change`,
        () => {
          this.renderChart()
        },
        1000
      )
    },
  },
}
</script>
<style lang="scss">
.diagnostics-analysis-consumption-gauge {
  min-height: 230px;
  min-width: 230px;
  canvas {
    left: -10px !important;
    width: 220px !important;
    height: 220px !important;
  }
}
</style>

<template lang="pug">
.diagnostics-analysis
  DynamicSections(v-model="dynamicSectionsState")
    template(v-slot:details_title="slotProps")
      SectionTitleWrapper(
        :icon="'radar'",
        :canCollapse="true",
        :collapsed="slotProps.isContentVisible",
        textMinWidth="240px"
      ) {{ slotProps.title }}

    template(v-slot:washing_title="slotProps")
      SectionTitleWrapper(
        :icon="'radar'"
        :canCollapse="true",
        :collapsed="slotProps.isContentVisible",
        textMinWidth="240px"
      ) {{ slotProps.title }}

    template(v-slot:consommation_title="slotProps")
      SectionTitleWrapper(
        :canCollapse="true",
        :collapsed="slotProps.isContentVisible",
        textMinWidth="240px"
      ) {{ slotProps.title }}
        template(v-slot:icon)
          em.fas.fa-gas-pump(style="font-size: 24px")

    template(v-slot:chrono_title="slotProps")
      SectionTitleWrapper(
        :canCollapse="true",
        :collapsed="slotProps.isContentVisible",
        textMinWidth="240px"
      ) {{ slotProps.title }}
        template(v-slot:icon)
          em.fas.fa-leaf(style="font-size: 24px")

    template(v-slot:sensors_title="slotProps")
      SectionTitleWrapper(
        :icon="'radar'",
        :canCollapse="true",
        :collapsed="slotProps.isContentVisible",
        textMinWidth="240px"
      ) {{ slotProps.title }}

    //Details - Both
    template(v-slot:vehicleName)
      span {{ isSinglePosition ? ( position?.vehicleName || positionDetails?.vehicleName) : analysis?.vehicleName }}
    template(v-slot:vehicleRegistrationPlate)
      span {{ isSinglePosition ? (position.vehicleRegistrationPlate || positionDetails.vehicleRegistrationPlate) : analysis.vehicleRegistrationPlate }}

    //Details - Position
    template(v-slot:datetime)
      span {{ datetimeFormatted }}
    template(v-slot:address)
      span {{ positionDetails.addressFormatted || singlePositionExtraDetails.addressFormatted }}
    template(v-slot:lat)
      span {{ position.lat }}
    template(v-slot:lng)
      span {{ position.lng }}
    template(v-slot:time)
      span {{ positionDetails.timeFormatted }}
    template(v-slot:speed data-field="vehicleSpeed")
      span {{ positionDetails.speedFormatted }}
    template(v-slot:hasContactOn)
      span {{ contactTimes.contactOn }}
    template(v-slot:hasContactOff)
      span {{ contactTimes.contactOff }}
    template(v-slot:moveDuration)
      span {{ moveDuration }}
    template(v-slot:contactOnDuration)
      span {{ contactTimes.contactOnDuration }}
    template(v-slot:fuelLevel)
      span {{ fuelLevel }}

    //Details - Analysis
    template(v-slot:startDatetime)
      span {{ analysis.startDatetimeFormatted }}
    template(v-slot:startAddress)
      span {{ startAddressFormatted }}
    template(v-slot:endDatetime)
      span {{ analysis.endDatetimeFormatted }}
    template(v-slot:endAddress)
      span {{ endAddressFormatted }}
    template(v-slot:duration)
      span {{ analysis.durationFormatted }}
    template(v-slot:effectiveDuration)
      span {{ analysis.effectiveDurationFormatted }}
    template(v-slot:distance)
      span {{ analysis.distanceFormatted }}
    template(v-slot:averageSpeed)
      span {{ analysis.speedFormatted }}

    //Details - Sensors config
    template(v-slot:sensorConfig)
      SensorsConfigInfos

    // Washing details (unique position)
    template(v-slot:washPressure) {{ wash.washPressure }}
    template(v-slot:washLevel) {{ wash.washLevel }}
    template(v-slot:washFlowRateFrontL) {{ wash.washFlowRateFrontL }}
    template(v-slot:washFlowRateFrontR) {{ wash.washFlowRateFrontR }}
    template(v-slot:washFlowRateBackL) {{ wash.washFlowRateBackL }}
    template(v-slot:washFlowRateBackR) {{ wash.washFlowRateBackR }}
    template(v-slot:washFlowRatePole) {{ wash.washFlowRatePole }}
    template(v-slot:washFlowRateRamp) {{ wash.washFlowRateRamp }}

    // Washing details (multiple positions selected)
    template(v-slot:washFlowRangeFrontLAvg) {{ wash.washFlowRateFrontL }}
    template(v-slot:washFlowRangeFrontRAvg) {{ wash.washFlowRateFrontR }}
    template(v-slot:washFlowRangeBackLAvg) {{ wash.washFlowRateBackL }}
    template(v-slot:washFlowRangeBackRAvg) {{ wash.washFlowRateBackR }}
    template(v-slot:washFlowRangePoleAvg) {{ wash.washFlowRatePole }}
    template(v-slot:washFlowRangeRampAvg) {{ wash.washFlowRateRamp }}

    // Sweep details (unique position)
    template(v-slot:sweepHopRaised)
      em.fa(:class="['fa', sweep.sweepHopRaised === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepPump)
      em.fa(:class="['fa', sweep.sweepPump === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepFan)
      em.fa(:class="['fa', sweep.sweepFan === 1 ? 'fa-check' : 'fa-times']")
    template(v-if="isSinglePosition" v-slot:sweepFanSp) {{ sweep.sweepFanSp ?? $t('common.not_available') }}
    template(v-if="isSinglePosition" v-slot:sweepHpPump) {{ sweep.sweepHpPump ?? $t('common.not_available') }}
    template(v-if="isSinglePosition" v-slot:sweepLevel) {{ sweep.sweepLevel ?? $t('common.not_available') }}
    template(v-slot:sweepRearDoor)
      em.fa(:class="['fa', sweep.sweepRearDoor === 1 ? 'fa-check' : 'fa-times']")
    template(v-if="isSinglePosition" v-slot:sweepBrushSp) {{ sweep.sweepBrushSp ?? $t('common.not_available') }}
    template(v-slot:sweepBrushCentral)
      em.fa(:class="['fa', sweep.sweepBrushCentral === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepBrushLeft)
      em.fa(:class="['fa', sweep.sweepBrushLeft === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepBrushRight)
      em.fa(:class="['fa', sweep.sweepBrushRight === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepWaterCentral)
      em.fa(:class="['fa', sweep.sweepWaterCentral === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepWaterFront)
      em.fa(:class="['fa', sweep.sweepWaterFront === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepWaterRight)
      em.fa(:class="['fa', sweep.sweepWaterRight === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepWaterLeft)
      em.fa(:class="['fa', sweep.sweepWaterLeft === 1 ? 'fa-check' : 'fa-times']")
    template(v-slot:sweepWaterLaterals)
      em.fa(:class="['fa', sweep.sweepWaterLaterals === 1 ? 'fa-check' : 'fa-times']")
    //Sweep details (multiple positions selected)
    template(v-slot:sweepHopRaisedCount) {{ sweep.sweepHopRaised }}
    template(v-slot:sweepPumpCount) {{ sweep.sweepPump }}
    template(v-slot:sweepFanCount) {{ sweep.sweepFan }}
    template(v-slot:sweepRearDoorCount) {{ sweep.sweepRearDoor }}
    template(v-slot:sweepBrushCentralCount) {{ sweep.sweepBrushCentral }}
    template(v-slot:sweepBrushLeftCount) {{ sweep.sweepBrushLeft }}
    template(v-slot:sweepBrushRightCount) {{ sweep.sweepBrushRight }}
    template(v-slot:sweepWaterCentralCount) {{ sweep.sweepWaterCentral }}
    template(v-slot:sweepWaterFrontCount) {{ sweep.sweepWaterFront }}
    template(v-slot:sweepWaterRightCount) {{ sweep.sweepWaterRight }}
    template(v-slot:sweepWaterLeftCount) {{ sweep.sweepWaterLeft }}
    template(v-slot:sweepWaterLateralsCount) {{ sweep.sweepWaterLaterals }}

    //Consumption
    template(v-slot:averageCo2)
      span {{ $transform.appendUnit('g/Km', analysis.averageCo2) }}
    template(v-slot:totalCo2)
      span {{ $transform.appendUnit('Kg', analysis.totalCo2) }}
    template(v-slot:gauge)
      DiagnosticAnalysisGauge(
        :value="analysis.averageConsumption"
        :refConsumptionOver100km="refConsumptionOver100km"
      )
    template(v-slot:gauge_right)
      .col-12
        .row.section_label 
          span {{ $t('diagnostics.analysis.totalConsumption') }}
        .row.section_value 
          span {{ analysis.totalConsumption }} {{$t('diagnostics.chart.units.liters')}}
        .row.section_label 
          span {{ $t('diagnostics.analysis.averageConsumption') }}
        .row.section_value 
          span {{ analysis.averageConsumption }} {{$t('diagnostics.chart.units.conso100km')}}
        .row.section_label 
          span {{ $t('diagnostics.analysis.referenceConsumption') }}
        .row.section_value 
          span {{ analysis.referenceConsumption }} {{$t('diagnostics.chart.units.conso100km')}}

    //Sensors -> Single positioin
    template(v-slot:sensors_content)
      SensorsInfos(:item="positionDetails" :removeMargin="true")

    //Chrono
    template(v-slot:chronoChart)
      DiagnosticsChronoChartAlternative.mt-2
</template>
<script>
import SectionTitleWrapper from '@c/shared/SectionTitle/SectionTitleWrapper.vue'
import SectionField from '@c/shared/SectionField.vue'
import DynamicSections from '@c/shared/DynamicSections.vue'
import DiagnosticAnalysisGauge from '@c/diagnostic/DiagnosticAnalysisGauge.vue'
import { queueOperationOnce } from '@/utils/promise.js'
import DiagnosticsChronoChartAlternative from '@c/diagnostic/DiagnosticsChronoChartAlternative.vue'
import {
  normalizeAnalyzeHistorySegment,
  getVehicleHistoryPositionDetailsFromDateLegacy,
} from '@/services/history-service.js'
import envService from '@/services/env-service.js'
import normalizePositionEntity from '@/services/entities/position-entity'
import SensorsInfos from '@/components/shared/SearchResults/Submenu/SensorsInfos.vue'
import SensorsConfigInfos from '@c/shared/SensorsConfigInfos.vue'
import { getQueryStringValue } from '@/utils/querystring'
import vehicleService from '@/services/vehicle-service.js'
import { isValidPositionId } from '@/services/position-service.js'
import { secondsToDuration } from '@/utils/dates'
import moment from 'moment'
import { getWashAndSweepRows } from '@/composables/useSweepAndWashSensors'
import { normalizeValueDisplay } from '@/utils/string'
import { getFormattedAddress } from '@/utils/address'

/**
 * Test dataset: DGD/ISOPROD/8151/2022-03-04
 */
export default {
  name: 'DiagnosticsAnalysis',
  components: {
    SectionTitleWrapper,
    SectionField,
    DynamicSections,
    SensorsInfos,
    SensorsConfigInfos,
    DiagnosticAnalysisGauge,
    DiagnosticsChronoChartAlternative,
  },
  props: {
    position: {
      type: Object,
      default: () => {
        return !envService.isProduction() && getQueryStringValue('test') === 1
          ? normalizePositionEntity({
              vehicleName: '1096',
              vehicleRegistrationPlate: 'XR230Z',
              datetime: '2022-03-04T05:30:08',
              vehicleId: 8151,
              boxNumber: 203,
              serverId: 34,
              latitude: 47.94443611111111,
              longitude: 1.906036111111111,
              hasContactOn: true,
              startupFlag: false,
              quality: 1,
              speed: 0,
              altitude: 0,
              washFlowRateFrontL: 0,
              washFlowRateFrontR: 0,
              washFlowRateBackL: 0,
              washFlowRateBackR: 0,
              washFlowRatePole: 0,
              washFlowRateRamp: 0,
              washFlowTotalFrontL: 5934,
              washFlowTotalFrontR: 4650,
              washFlowTotalBackL: 21577,
              washFlowTotalBackR: 21577,
              washFlowTotalPole: 215665,
              washFlowTotalRamp: 34669,
              fuelLevel: 94,
            })
          : {}
      },
    },
    analysis: {
      type: Object,
      default: () =>
        !envService.isProduction() && getQueryStringValue('test') === 1
          ? normalizeAnalyzeHistorySegment({
              vehicleId: 8151,
              clientId: 248,
              vehicleCategoryId: 99,
              vehicleName: '1096',
              clientName: 'DGD',
              vehicleRegistrationPlate: 'FX-578-NB',
              vehicleCategoryName: 'BOM',
              startDatetime: '2022-03-04T04:28:38',
              endDatetime: '2022-03-04T06:30:00',
              duration: 0,
              effectiveDuration: 46281,
              distance: 81810,
              speed: 9.09,
              totalCo2: 0,
              averageCo2: 0,
              totalConsumption: 0,
              averageConsumption: 0,
              referenceConsumption: 70,
              averageEngineSpeed: 0,
              startAddress: 'Rue du Faubourg Bannier',
              startZipCode: '45000',
              startCity: 'Orl\u00e9ans',
              startLongitude: 1.89815,
              startLatitude: 47.92017,
              endAddress: 'All\u00e9e Marcel Lerouge',
              endZipCode: '45770',
              endCity: 'Saran',
              endLongitude: 1.90618,
              endLatitude: 47.94483,
              tor: [
                { number: 1, name: 'Grand Bac', value: '98' },
                { number: 2, name: 'Bac gauche', value: '719' },
                { number: 3, name: 'Bac droit', value: '702' },
                { number: 4, name: 'Porte ouverte', value: '00:05' },
                { number: 5, name: 'Ripeur gauche', value: '08:16' },
                { number: 6, name: 'Ripeur droit', value: '08:11' },
                { number: 8, name: 'Marche arriere', value: '02:30' },
              ],
              ana: [],
              washFlowRateFrontL: 0,
              washFlowRateFrontR: 0,
              washFlowRateBackL: 0,
              washFlowRateBackR: 0,
              washFlowRatePole: 0,
              washFlowRateRamp: 0,
              washFlowRangeFrontL: 5934,
              washFlowRangeFrontR: 4650,
              washFlowRangeBackL: 21577,
              washFlowRangeBackR: 21577,
              washFlowRangePole: 215665,
              washFlowRangeRamp: 34669,
              fuelLevel: 94,
            })
          : {},
    },
  },
  data() {
    return {
      singlePositionExtraDetails: {},
      dynamicSectionsState: {
        sections: [
          // Relevé des informations
          {
            name: 'details',
            title: this.$t('diagnostics.analysis.title_details'),
            rows: [
              //Both modes
              {
                twoColumns: [
                  {
                    name: 'vehicleName',
                    label: this.$t('common.vehicule'),
                  },
                  {
                    name: 'vehicleRegistrationPlate',
                    label: this.$t('common.matriculation'),
                  },
                ],
              },

              //Position (Single position) ----------------------
              {
                visible: () => this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'datetime',
                    label: this.$t('common.from'),
                  },
                  {
                    name: 'address',
                    label: this.$t('common.position'),
                  },
                ],
              },
              {
                visible: () => this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'lat',
                    label: this.$t('common.lat'),
                  },
                  {
                    name: 'lng',
                    label: this.$t('common.lng'),
                  },
                ],
              },
              {
                visible: () => this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'time',
                    label: this.$t('common.hour'),
                  },
                  {
                    name: 'speed',
                    label: this.$t('common.Vitesse'),
                    dataField: 'vehicleSpeed',
                  },
                ],
              },

              //Analysis (Two positions) -----------------------
              //De datetime
              //Position address
              {
                visible: () => !this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'startDatetime',
                    label: this.$t('common.from'),
                  },
                  {
                    name: 'startAddress',
                    label: this.$t('common.position'),
                  },
                ],
              },
              //A datetime
              //Position address
              {
                visible: () => !this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'endDatetime',
                    label: this.$t('common.to'),
                  },
                  {
                    name: 'endAddress',
                    label: this.$t('common.position'),
                  },
                ],
              },
              //Duree //Duree effective
              {
                visible: () => !this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'duration',
                    label: this.$t('common.duree'),
                  },
                  {
                    name: 'effectiveDuration',
                    label: this.$t('diagnostics.analysis.effective_duration'),
                  },
                ],
              },
              //Distance Vitesse moyenne
              {
                visible: () => !this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'distance',
                    label: this.$t('common.distance'),
                  },
                  {
                    name: 'averageSpeed',
                    label: this.$t('common.average_speed'),
                    dataField: 'vehicleSpeed',
                  },
                ],
              },
              //Mise en route - Extinction
              {
                visible: () => !this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'hasContactOn',
                    label: 'diagnostics.analysis.contactOn',
                  },
                  {
                    name: 'hasContactOff',
                    label: 'diagnostics.analysis.contactOff',
                  },
                ],
              },
              {
                visible: () => !this.isSinglePosition,
                twoColumns: [
                  {
                    name: 'moveDuration',
                    label: 'diagnostics.analysis.moveDuration',
                  },
                  {
                    name: 'contactOnDuration',
                    label: 'diagnostics.analysis.contactOnDuration',
                  },
                ],
              },
              {
                twoColumns: [
                  {
                    visible: () => this.isSinglePosition,
                    name: 'fuelLevel',
                    label: 'diagnostics.analysis.fuelLevel',
                  },
                ],
              },
              //Sensors configuration (Last)
              {
                //visible:()=>this.isSinglePosition,
                name: 'sensorConfig',
                singleColumn: {
                  name: 'sensorConfig',
                },
              },
            ],
            isContentVisible: true,
          },
          // Informations nettoiement (rows set in useSweepAndWashSensors composable)
          {
            visible: () => this.isWasher || this.isSweeper,
            name: 'washing',
            title: this.$t('diagnostics.analysis.title_wash'),
            rows: [],
            isContentVisible: true,
          },
          //Informations Capteurs
          {
            name: 'sensors',
            title: this.$t('diagnostics.analysis.title_sensors'),
            rows: [
              //Analysis
              //Single position
            ],
            isContentVisible: true,
          },
          //Consommation
          {
            name: 'consommation',
            title: this.$t('diagnostics.analysis.title_consommation'),
            rows: [
              //Moyenne reject CO2 // Total reject CO2
              {
                twoColumns: [
                  {
                    label: this.$t('diagnostics.analysis.averageCo2'),
                    name: 'averageCo2',
                    className: 'col-8',
                  },
                  {
                    label: this.$t('diagnostics.analysis.totalCo2'),
                    name: 'totalCo2',
                    className: 'col-4',
                  },
                ],
              },
              {
                twoColumns: [
                  {
                    name: 'gauge',
                    label: false,
                    className: 'col-8',
                  },
                  {
                    name: 'gauge_right',
                    label: false,
                    className: 'col-4',
                    style: `display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    top: -10px;`,
                  },
                ],
              },
              //chart consommation //consommation total / moyenne / reference
            ],
            isContentVisible: true,
            visible: () =>
              !this.isSinglePosition && this.analysis?.averageConsumption,
          },
          //Chrono
          {
            name: 'chrono',
            title: this.$t('searchModule.tabs.chrono'),
            rows: [
              {
                name: 'chronoChart',
                singleColumn: {
                  name: 'chronoChart',
                },
              },
            ],
            isContentVisible: true,
            visible: () => this.isChronoChartVisible,
          },
        ],
      },
    }
  },
  computed: {
    datetimeFormatted() {
      return this.position?.datetime
        ? this.$date.formatDatetimeWithSeconds(
            this.$date.adjustDateWithTimezone(this.position?.datetime)
          )
        : ''
    },
    isSinglePosition() {
      return Object.keys(this.position || {}).length > 0
    },
    isChronoChartVisible() {
      let chronoData = this.$store.state.diagnostics.chronoData?.synthesis || {}
      let hasChronoData = false

      Object.keys(chronoData).forEach((key) => {
        if (
          chronoData[key] !== '' &&
          //Handle time formatting from APIV2 && APIV3
          chronoData[key] !== '00h00' &&
          chronoData[key] !== '00:00' &&
          chronoData[key] !== '00:00:00' &&
          chronoData[key] !== 0
        ) {
          hasChronoData = true
        }
      })

      return !this.isSinglePosition && hasChronoData
    },
    refConsumptionOver100km() {
      return this.$store.state.diagnostics?.vehicleConfiguration
        ?.vehicleGlobalConsumptionRef
    },
    positionDetails() {
      return {
        ...(this.$store.state.diagnostics?.vehicleHistoryOverview || {}), //vehicleName/vehicleRegistrationPlate
        ...(this.isSinglePosition ? this.position : this.analysis),
      }
    },
    isWasher() {
      const vehicle = this.$store.state.diagnostics?.vehicleConfiguration

      return vehicle && vehicle.vehicleCategoryName === 'Laveuses'
    },
    isSweeper() {
      const vehicle = this.$store.state.diagnostics?.vehicleConfiguration

      return vehicle && vehicle.vehicleCategoryName === 'Balayeuses'
    },
    simplePositionOrLastSelectedPosition() {
      let from = this.position

      if (!this.isSinglePosition) {
        const brushEndTime = this.$store.state.diagnostics.brushEndTimestamp
        const diagPositions = this.$store.state.diagnostics.positions

        if (diagPositions) {
          from = diagPositions.filter((p) => p.timestamp === brushEndTime)[0]
        }
      }

      return from
    },
    selectedPositions() {
      if (this.isSinglePosition) {
        return this.simplePositionOrLastSelectedPosition
      }

      const brushStartTime = this.$store.state.diagnostics.brushStartTimestamp
      const brushEndTime = this.$store.state.diagnostics.brushEndTimestamp
      const positions = this.$store.state.diagnostics.positions

      if (brushStartTime && brushEndTime) {
        const positionsInRange =
          positions.filter(
            (p) => p.timestamp >= brushStartTime && p.timestamp <= brushEndTime
          ) ?? []

        if (positionsInRange.length > 0) {
          const sortedPositionsByTimestamp = positionsInRange.sort((a, b) =>
            a.timestamp < b.timestamp ? -1 : 1
          )

          return sortedPositionsByTimestamp
        }

        return positionsInRange
      }

      return positions ?? []
    },
    wash() {
      const nvd = (value) => {
        return normalizeValueDisplay(value, 'l/min')
      }

      const getUsageAvgPerMinute = (key) => {
        const validPositions = this.positions.filter((pos) => pos[key] !== null)

        if (validPositions.length < 2) {
          return 0
        }

        const firstTotal = validPositions[0][key]
        const firstTime = validPositions[0].timestamp
        const lastTotal = validPositions[validPositions.length - 1][key]
        const lastTime = validPositions[validPositions.length - 1].timestamp

        const deltaTimeInMinute = Math.abs((lastTime - firstTime) / (1000 * 60))

        if (firstTotal - lastTotal === 0 || lastTime - firstTime === 0) {
          return 0
        }

        return (lastTotal - firstTotal) / deltaTimeInMinute
      }

      if (this.isSinglePosition) {
        return {
          washPressure:
            this.positions.washPressure ?? this.$t('common.not_available'),
          washLevel:
            this.positions.washLevel ?? this.$t('common.not_available'),
          washFlowRateFrontL: nvd(this.positions.washFlowRateFrontL),
          washFlowRateFrontR: nvd(this.positions.washFlowRateFrontR),
          washFlowRateBackL: nvd(this.positions.washFlowRateBackL),
          washFlowRateBackR: nvd(this.positions.washFlowRateBackR),
          washFlowRatePole: nvd(this.positions.washFlowRatePole),
          washFlowRateRamp: nvd(this.positions.washFlowRateRamp),
        }
      }

      const flowRangeFrontL = nvd(getUsageAvgPerMinute('washFlowTotalFrontL'))
      const flowRangeFrontR = nvd(getUsageAvgPerMinute('washFlowTotalFrontR'))
      const flowRangeBackL = nvd(getUsageAvgPerMinute('washFlowTotalBackL'))
      const flowRangeBackR = nvd(getUsageAvgPerMinute('washFlowTotalBackR'))
      const flowRangePole = nvd(getUsageAvgPerMinute('washFlowTotalPole'))
      const flowRangeRamp = nvd(getUsageAvgPerMinute('washFlowTotalRamp'))

      return {
        washFlowRateFrontL: flowRangeFrontL,
        washFlowRateFrontR: flowRangeFrontR,
        washFlowRateBackL: flowRangeBackL,
        washFlowRateBackR: flowRangeBackR,
        washFlowRatePole: flowRangePole,
        washFlowRateRamp: flowRangeRamp,
      }
    },
    sweep() {
      const getSweepSensorCount = (key) => {
        return this.positions.filter((pos) => pos[key] === 1).length
      }

      //If single position, return the value of the sensor
      if (this.isSinglePosition) {
        return {
          sweepBrushCentral: this.positions.sweepBrushCentral,
          sweepBrushLeft: this.positions.sweepBrushLeft,
          sweepBrushRight: this.positions.sweepBrushRight,
          sweepBrushSp: this.positions.sweepBrushSp,
          sweepFan: this.positions.sweepFan,
          sweepFanSp: this.positions.sweepFanSp,
          sweepHopRaised: this.positions.sweepHopRaised,
          sweepHpPump: this.positions.sweepHpPump,
          sweepLevel: this.positions.sweepLevel,
          sweepPump: this.positions.sweepPump,
          sweepRearDoor: this.positions.sweepRearDoor,
          sweepWaterCentral: this.positions.sweepWaterCentral,
          sweepWaterFront: this.positions.sweepWaterFront,
          sweepWaterLaterals: this.positions.sweepWaterLaterals,
          sweepWaterLeft: this.positions.sweepWaterLeft,
          sweepWaterRight: this.positions.sweepWaterRight,
        }
      }

      // Return the following if multiple positions
      const sweepBrushCentralCount = getSweepSensorCount('sweepBrushCentral')
      const sweepBrushLeftCount = getSweepSensorCount('sweepBrushLeft')
      const sweepBrushRightCount = getSweepSensorCount('sweepBrushRight')
      const sweepFanCount = getSweepSensorCount('sweepFan')
      const sweepHopRaisedCount = getSweepSensorCount('sweepHopRaised')
      const sweepPumpCount = getSweepSensorCount('sweepPump')
      const sweepRearDoorCount = getSweepSensorCount('sweepRearDoor')
      const sweepWaterCentralCount = getSweepSensorCount('sweepWaterCentral')
      const sweepWaterFrontCount = getSweepSensorCount('sweepWaterFront')
      const sweepWaterLateralsCount = getSweepSensorCount('sweepWaterLaterals')
      const sweepWaterLeftCount = getSweepSensorCount('sweepWaterLeft')
      const sweepWaterRightCount = getSweepSensorCount('sweepWaterRight')

      return {
        sweepBrushCentral: sweepBrushCentralCount,
        sweepBrushLeft: sweepBrushLeftCount,
        sweepBrushRight: sweepBrushRightCount,
        sweepFan: sweepFanCount,
        sweepHopRaised: sweepHopRaisedCount,
        sweepPump: sweepPumpCount,
        sweepRearDoor: sweepRearDoorCount,
        sweepWaterCentral: sweepWaterCentralCount,
        sweepWaterFront: sweepWaterFrontCount,
        sweepWaterLaterals: sweepWaterLateralsCount,
        sweepWaterLeft: sweepWaterLeftCount,
        sweepWaterRight: sweepWaterRightCount,
      }
    },
    contactTimes() {
      const getContactFormattedDatetime = (index) => {
        const positions = this.selectedPositions

        if (index >= 0) {
          return this.$date.formatDatetimeWithSeconds(
            this.$date.adjustDateWithTimezone(positions[index]?.datetime)
          )
        }

        return ''
      }

      const calculateContactOnDuration = (positions) => {
        const contactOnPositions = positions
          .reverse()
          .filter((p) => p.hasContactOn)

        let total = 0

        for (let i = 1; i < contactOnPositions.length; i++) {
          const curr = new Date(contactOnPositions[i].timestamp)
          const prev = new Date(contactOnPositions[i - 1].timestamp)
          const diff = moment.duration(moment(prev).diff(curr)).asSeconds()

          total += diff
        }

        return secondsToDuration(total)
      }

      let contactOn = ''
      let contactOff = ''
      let contactOnDuration = 0

      if (!this.isSinglePosition) {
        const onIndex = this.selectedPositions.findIndex((p) => p.hasContactOn)
        const offIndex = this.selectedPositions.length - 1

        contactOn = getContactFormattedDatetime(onIndex)
        contactOff = getContactFormattedDatetime(offIndex)
        contactOnDuration = calculateContactOnDuration(this.selectedPositions)
      }

      return {
        contactOn,
        contactOff,
        contactOnDuration,
      }
    },
    moveDuration() {
      const positions = this.selectedPositions
      const movingPositions = positions.filter((p) => p.speed > 0)

      let total = 0

      for (let i = 1; i < movingPositions.length; i++) {
        const curr = new Date(movingPositions[i].timestamp)
        const prev = new Date(movingPositions[i - 1].timestamp)
        const diff = moment.duration(moment(prev).diff(curr)).asSeconds()

        total += diff
      }

      return secondsToDuration(total)
    },
    fuelLevel() {
      if (!this.isSinglePosition) {
        return this.$t('common.not_available')
      }

      let from = this.simplePositionOrLastSelectedPosition

      return normalizeValueDisplay(from.sensorCanFuelLevel ?? null, '%')
    },
    positions() {
      return this.isSinglePosition
        ? this.selectedPositions
        : [...this.selectedPositions].sort((a, b) => a.timestamp - b.timestamp)
    },
    sweepAndWashRows() {
      return getWashAndSweepRows({
        isWasher: computed(() => this.isWasher),
        isSweeper: computed(() => this.isSweeper),
        isSinglePosition: computed(() => this.isSinglePosition),
      })
    },
    startAddressFormatted() {
      return (
        this.analysis?.startAddressFormatted ??
        getFormattedAddress(this.analysis, {
          streetNumber: '',
          streetName: 'startAddress',
          zipCode: 'startZipCode',
          city: 'startCity',
        })
      )
    },
    endAddressFormatted() {
      return (
        this.analysis?.endAddressFormatted ??
        getFormattedAddress(this.analysis, {
          streetNumber: '',
          streetName: 'endAddress',
          zipCode: 'endZipCode',
          city: 'endCity',
        })
      )
    },
  },
  watch: {
    position: {
      deep: true,
      handler() {
        //Delay two seconds if replay is playing to avoid calling API dozen of times to retrieve just the address
        if (
          this.$store.state.diagnostics.chartSelectedItemTrigger === 'replay'
        ) {
          queueOperationOnce(
            'diagnostics_analysis_api_fetch',
            () => {
              this.updateExtraPositionDetails()
            },
            {
              timeout: 1000,
              clearPreviousTimeout: true,
            }
          )
        } else {
          this.updateExtraPositionDetails()
        }
      },
    },
    sweepAndWashRows: {
      immediate: true,
      handler(newRows) {
        // Replace rows in the washing section dynamically
        const washingSection = this.dynamicSectionsState.sections.find(
          (section) => section.name === 'washing'
        )
        if (washingSection) {
          washingSection.rows = newRows
        }
      },
    },
  },
  mounted() {
    this.updateExtraPositionDetails()
  },
  methods: {
    /**
     * Used to retrieve formatted address, which is not present in the available selected position (single position)
     */
    async updateExtraPositionDetails() {
      if (
        !this.isSinglePosition ||
        !this.position.vehicleId ||
        !this.position.datetime
      ) {
        return
      }
      let hideLoader =
        (this.$loader && this.$loader.showAlternative()) || (() => ({}))

      if (isValidPositionId(this.position?.id)) {
        this.singlePositionExtraDetails = Object.freeze(
          (await vehicleService.getVehiclePosition(this.position.id)) || {}
        )
        hideLoader()
      } else {
        getVehicleHistoryPositionDetailsFromDateLegacy(
          this.position.vehicleId,
          this.position.datetime
        )
          .then((details) => {
            this.singlePositionExtraDetails = Object.freeze({
              addressFormatted: details.addressFormatted,
            })
          })
          .finally(() => {
            hideLoader()
          })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.section_label {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
.section_value {
  font: normal normal normal 12px/18px Open Sans;
  color: var(--color-tundora);
  em {
    font-size: 16px;
  }
}
.fa-times {
  color: var(--color-coral-red);
}
.fa-check {
  color: var(--color-silver-tree);
}
// je dois calculer entre l'activation du capteur entre le moment
// o`u le capteur de buse activé / arrêté (litrage début - fin * minutes) et
// s'il s'active plusieurs fois je dois en faire la moyenne de tout ça....

// buse = rampe
</style>

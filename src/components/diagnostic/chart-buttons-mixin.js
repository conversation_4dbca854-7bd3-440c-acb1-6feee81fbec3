const zoomPercentage = 0.2

/**
 * Used by Diagnostic module to control the chart pan/zoom using buttons.
 * requires positions mapped to store.state.diagnostics.positions
 */
export default {
  methods: {
    moveright() {
      if (!this.$store.getters['diagnostics/hasValidEchartDataZoom']) {
        return
      }
      let zoom = this.$store.state.diagnostics.echartDataZoom
      let perc = (zoom.end - zoom.start) * zoomPercentage
      let startZoomPerc = zoom.start + perc < 0 ? 0 : zoom.start + perc
      let endZoomPerc = zoom.end + perc > 100 ? 100 : zoom.end + perc
      this.$store.state.diagnostics.echartDataZoom = {
        ...this.$store.state.diagnostics.echartDataZoom,
        start: startZoomPerc,
        end: endZoomPerc,
      }
    },
    moveleft() {
      if (!this.$store.getters['diagnostics/hasValidEchartDataZoom']) {
        return
      }
      let zoom = this.$store.state.diagnostics.echartDataZoom
      let perc = (zoom.end - zoom.start) * zoomPercentage
      let startZoomPerc = zoom.start - perc < 0 ? 0 : zoom.start - perc
      let endZoomPerc = zoom.end - perc > 100 ? 100 : zoom.end - perc
      this.$store.state.diagnostics.echartDataZoom = {
        ...this.$store.state.diagnostics.echartDataZoom,
        start: startZoomPerc,
        end: endZoomPerc,
      }
    },
    zoomin() {
      if (!this.$store.getters['diagnostics/hasValidEchartDataZoom']) {
        return
      }
      let zoom = this.$store.state.diagnostics.echartDataZoom
      let perc = (zoom.end - zoom.start) * zoomPercentage
      let startZoomPerc = zoom.start + perc < 0 ? 0 : zoom.start + perc
      let endZoomPerc = zoom.end - perc > 100 ? 100 : zoom.end - perc
      if (startZoomPerc > endZoomPerc) {
        return
      }
      this.$store.state.diagnostics.echartDataZoom = {
        ...this.$store.state.diagnostics.echartDataZoom,
        start: startZoomPerc,
        end: endZoomPerc,
      }
    },
    zoomout() {
      if (!this.$store.getters['diagnostics/hasValidEchartDataZoom']) {
        return
      }
      let zoom = this.$store.state.diagnostics.echartDataZoom
      let perc = (zoom.end - zoom.start) * zoomPercentage
      let startZoomPerc = zoom.start - perc < 0 ? 0 : zoom.start - perc
      let endZoomPerc = zoom.end + perc > 100 ? 100 : zoom.end + perc
      this.$store.state.diagnostics.echartDataZoom = {
        ...this.$store.state.diagnostics.echartDataZoom,
        start: startZoomPerc,
        end: endZoomPerc,
      }
    },
    zoomfit() {
      if (!this.$store.getters['diagnostics/hasValidEchartDataZoom']) {
        return
      }
      this.$store.state.diagnostics.echartDataZoom = {
        ...this.$store.state.diagnostics.echartDataZoom,
        start: 0.1,
        end: 99.9,
      }
    },
  },
}

<template lang="pug">
.container
  .row.mt-2
    BackTitleButton(
        :title="$t('diagnostics.chart_settings_view.back_label')"
        style="margin-left: 10px"
        @click="lateralMenuMode='search'" color="var(--color-main)")
  .row.mt-3.pb-3
    .col-12(v-for="(category, index) in filteredCategories" :key="category.name")
      .category_block(:class="parseInt(index)>0?'mt-4':''" :data-index="index")
        .category_block_label
          Icon(ref="reload" :icon="category.icon" :style="{fontSize:'30px'}" :color="category.color||'var(--color-main)'" style="cursor:pointer")
          .category_label {{category.label ? $translation(category.label,category.label) : category.name}}
        .category_collapse
          em.fas.collapsable_toggle(
                :class="{'fa-chevron-up':category.showContent, 'fa-chevron-down':!category.showContent}"
                @click="category.showContent=!category.showContent")
      .row.m-0.p-0(v-if="category.showContent")
        .col-12.m-0.p-0.mt-2
          .option_block.mt-1(v-for="optionItem in getOptionItemsForCategory(category.name)" :key="optionItem.name") 
            .option_block_label
              .option_icon(v-show="optionItem.color!==undefined" :style="'background-color:'+optionItem.color||'black'")
              div() {{$translation(optionItem.label||optionItem.name,optionItem.label||optionItem.name)}}
            div()
            HorizontalToggleButton(
              v-model="optionItem.show"
              @input="saveOptionItemChoice(optionItem)"
              :disabled="optionItem.isToggleDisabled === true"
            )
</template>

<script>
import HorizontalToggleButton from '@/components/shared/HorizontalToggleButton.vue'
import {
  diagnosticsChartReferenceData,
  getFilterStateLocally,
  saveFilterStateLocally,
} from '@/services/diagnostics-service.js'
import { Icon } from '@iconify/vue2'
import BackTitleButton from '@c/shared/BackTitleButton.vue'
import { mergeArraysByKey } from '@/utils/array'

export default {
  name: 'DisplayOptions',
  components: {
    HorizontalToggleButton,
    Icon,
    BackTitleButton,
  },
  props: {
    isTest: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      categories: [
        {
          name: 'mandatory',
          icon: 'ant-design:lock-filled',
          color: 'var(--color-main)',
          label: 'diagnostics.chart_settings_view.mandatory_label',
          showContent: true,
        },
        {
          name: 'sweep_wash',
          icon: 'mdi:broom',
          color: 'var(--color-main)',
          label: 'diagnostics.chart_settings_view.sweep_wash',
          showContent: true,
        },
        {
          name: 'can',
          icon: 'ant-design:apartment-outlined',
          color: 'var(--color-main)',
          label: 'diagnostics.chart_settings_view.can_label',
          showContent: true,
        },
        {
          name: 'can_spe',
          icon: 'ant-design:warning-filled',
          color: 'var(--color-main)',
          label: 'diagnostics.chart_settings_view.can_specific_label',
          showContent: true,
        },
        {
          name: 'ana',
          icon: 'mdi:chart-bell-curve',
          color: 'var(--color-main)',
          label: 'diagnostics.chart_settings_view.ana',
          showContent: true,
        },
        {
          name: 'tor',
          icon: 'mdi:chart-scatter-plot',
          color: 'var(--color-main)',
          label: 'diagnostics.chart_settings_view.tor',
          showContent: true,
        },
        {
          name: 'chrono',
          icon: 'mdi:leaf',
          color: 'var(--color-main)',
          label: 'diagnostics.chart_settings_view.chrono',
          showContent: true,
        },
      ],
      referenceData: [
        //MANDATORY
        {
          ...diagnosticsChartReferenceData.normalGPS,
          isToggleDisabled: true,
        },
        {
          ...diagnosticsChartReferenceData.speed,
          isToggleDisabled: true,
        },
        /** CAN (7) */
        {
          ...diagnosticsChartReferenceData.canRPM,
        },
        {
          ...diagnosticsChartReferenceData.fuelLevelPerc,
        },
        {
          ...diagnosticsChartReferenceData.sensorCanConsumptionLiters,
        },
        {
          ...diagnosticsChartReferenceData.fuelLevelLiters,
        },

        {
          ...diagnosticsChartReferenceData.vehicleDistance,
        },
        {
          ...diagnosticsChartReferenceData.sensorCanBrakePedal,
        },
        {
          ...diagnosticsChartReferenceData.sensorCanBatteryPerc,
        },
        /** CAN SPE (4) */
        {
          ...diagnosticsChartReferenceData.speHarshBraking,
        },
        {
          ...diagnosticsChartReferenceData.speHarshAcceleration,
        },
        {
          ...diagnosticsChartReferenceData.speHarshCornering,
        },
        {
          ...diagnosticsChartReferenceData.speExcessiveSpeed,
        },
        /** TOR/ANA */
        ...diagnosticsChartReferenceData.torItems.map((i) => ({ ...i })),
        ...diagnosticsChartReferenceData.anaItems.map((i) => ({ ...i })),
        {
          name: 'chrono',
          label: this.$t('diagnostics.chart.category.chrono'),
          category: 'chrono',
        },
      ],
    }
  },
  computed: {
    lateralMenuMode: {
      set(value) {
        this.$store.state.diagnostics.lateralMenuMode = value
      },
      get() {
        return this.$store.state.diagnostics.lateralMenuMode
      },
    },
    chartsData: {
      set(value) {
        if (this.isTest) {
          this.referenceData = value
        } else {
          this.$store.state.diagnostics.chartsData = value
        }
      },
      get() {
        let chartData = this.isTest
          ? this.referenceData
          : this.$store.state.diagnostics.chartsData

        chartData.forEach((item) => {
          if (item.show === undefined) {
            this.$set(item, 'show', true)
          }
        })

        return chartData
      },
    },
    optionItems() {
      return this.chartsData.filter((item) => item.enabled !== false)
    },
    filteredCategories() {
      return this.categories.filter((cat) => {
        return this.optionItems.some((option) => option.category == cat.name)
      })
    },
  },
  watch: {
    optionItems(newValue, oldValue) {
      if (oldValue.length < 1 && newValue.length) {
        this.$nextTick(() => {
          this.applySavedOptionItemsState()
        })
      }
    },
  },
  methods: {
    async applySavedOptionItemsState() {
      let optionsItems = []

      for (let i = 0; i < this.optionItems.length; i++) {
        const item = this.optionItems[i]
        const savedState = await getFilterStateLocally(item.name, item.show)

        optionsItems.push({
          ...item,
          show: savedState,
        })
      }

      const newCharts = mergeArraysByKey(this.chartsData, optionsItems, 'name')

      if (this.isTest) {
        this.referenceData = newCharts
      } else {
        this.$store.state.diagnostics.chartsData = newCharts
      }
    },
    getOptionItemsForCategory(categoryName) {
      return this.optionItems.filter(
        (optionItem) => optionItem.category == categoryName
      )
    },
    saveOptionItemChoice(optionItem) {
      saveFilterStateLocally(optionItem.name, optionItem.show)
    },
  },
}
</script>

<style lang="scss" scoped>
.category_label {
  font: normal normal bold 18px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-main);
}
.category_block {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.category_collapse {
  cursor: pointer;
}
.category_block_label {
  display: flex;
  column-gap: 15px;
  align-items: center;
}
.option_block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.option_block_label {
  display: flex;
  column-gap: 15px;
  align-items: center;
  color: var(--color-tundora);
  font-family: 'Open Sans', sans-serif;
  font-size: 12px;
}
.option_icon {
  height: 12px;
  width: 12px;
  border-radius: 50px;
  background-color: black;
}
</style>

<template lang="pug">
span(:data-field="this.isSpeedChart ? 'vehicleSpeed' : ''")
    VChart.diagnostics-single-chart(:option="chartInstanceOptions" ref="vchart"
      :class="{'default-chart':this.isNormalGPSChart}"
      @click="onChartClick"
      @brushselected="brushselected"
      @datazoom="datazoom"
      :manual-update="true"
  )
</template>
<script>
import * as echarts from 'echarts/core'
import VChart, { THEME_KEY } from 'vue-echarts'
import moment from 'moment'
import {
  ToolboxComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BrushComponent,
  DataZoomComponent,
  MarkLineComponent,
  VisualMapComponent,
} from 'echarts/components'
import { CustomChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { ScatterChart, LineChart } from 'echarts/charts'
import { createEchartDateValueTooltipFormatter } from '@/utils/echarts.js'
import { queueOperationOnce } from '@/utils/promise.js'
import replayPositionClickTriggerMixin from '@c/diagnostic/replayPositionClickTriggerMixin.js'
import { useChartAxisPixelMap } from '@/composables/useChartAxisPixelMap'

echarts.use([
  ToolboxComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BrushComponent,
  DataZoomComponent,
  CustomChart,
  CanvasRenderer,
  LineChart,
  ScatterChart,
  MarkLineComponent,
  VisualMapComponent,
])

export default {
  name: 'DiagnosticsChart',
  components: {
    VChart,
  },
  mixins: [replayPositionClickTriggerMixin],
  provide: {
    [THEME_KEY]: 'white',
  },
  props: {
    name: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'plot',
      enum: ['plot', 'line', 'duration'],
    },
    color: {
      type: String,
      default: 'grey',
    },
    dataItems: {
      type: Array,
      default: () => [],
    },
    isTest: {
      type: Boolean,
      default: false,
    },
    /**
     * Options for configuring the chart behavior.
     *
     * @typedef {Object} ChartOptions
     * @default {Object} - defaults to an empty object with isEnableDisableMode set to false
     */
    options: {
      type: Object,
      default: () => ({}),
    },
    isFirstChart: {
      type: Boolean,
      default: false,
    },
    /**
     * Event bus for direct chart-to-chart communication
     * This bypasses Vue reactivity for faster synchronization
     */
    chartSyncBus: {
      // Using custom validator instead of type to handle EventTarget
      validator: function (value) {
        // Accept either null or anything with addEventListener method (EventTarget compatible)
        return (
          value === null ||
          (value && typeof value.addEventListener === 'function')
        )
      },
      required: false,
      default: null,
    },
  },
  data() {
    return {
      chartInstanceOptions: {},
      currentDataZoom: {
        type: 'dataZoom',
        dataZoomIndex: 0,
        start: 0,
        end: 100,
      },
      firstLoadSelectTimeout: null,
      processingExternalZoom: false, // Flag to prevent zoom event loops
      chartZoomHandler: null, // Store handler reference for cleanup
    }
  },
  computed: {
    analysisResult: {
      set(value) {
        this.$store.state.diagnostics.analysisResult = Object.freeze(value)
      },
      get() {
        return this.$store.state.diagnostics.analysisResult
      },
    },
    echartMarkLineData: {
      get() {
        return this.$store.state.diagnostics.echartMarkLineData
      },
      set(value) {
        this.$store.state.diagnostics.echartMarkLineData = value
      },
    },
    /**
     * Data items filtered using the time range selection
     */
    filteredDataItems() {
      return this.$store.getters[
        'diagnostics/filterChartDataItemsUsingSliderDataset'
      ](this.dataItems)
    },
    isNormalGPSChart() {
      return ['plot', 'line'].includes(this.type) && this.name === 'normalGPS'
    },
    isSpeedChart() {
      return ['line'].includes(this.type) && this.name === 'speed'
    },
    shouldRenderMarkLine() {
      return this.isNormalGPSChart
    },
    shouldUpdateTimeRangeOnBrushSelection() {
      return this.name === 'normalGPS'
    },
    menuCollapsed: {
      set(value) {
        this.$store.state.diagnostics.menuCollapsed = value
      },
      get() {
        return this.$store.state.diagnostics.menuCollapsed
      },
    },
    brushStartTimestamp: {
      set(value) {
        this.$store.state.diagnostics.brushStartTimestamp = value
      },
      get() {
        return this.$store.state.diagnostics.brushStartTimestamp
      },
    },
    brushEndTimestamp: {
      set(value) {
        this.$store.state.diagnostics.brushEndTimestamp = value
      },
      get() {
        return this.$store.state.diagnostics.brushEndTimestamp
      },
    },
    echartDataZoom: {
      set(value) {
        // Keep store updated for backward compatibility
        this.$store.state.diagnostics.echartDataZoom = value
        this.$store.state.diagnostics.echartDataZoomLastUpdateTime = +new Date()
      },
      get() {
        return this.$store.state.diagnostics.echartDataZoom
      },
    },
    chartInstance: {
      get() {
        return this.$refs.vchart.chart
      },
    },
    automaticSelectionDone() {
      return this.$store.state.diagnostics.automaticSelectionDone
    },
    selectedItem: {
      set(value) {
        this.$store.state.diagnostics.chartSelectedItem = value
      },
      get() {
        return this.$store.state.diagnostics.chartSelectedItem
      },
    },
    // We use this computed to detect if the screen is small (less than 1280px), to adjust grid left, otherwise yAxis labels are cut off
    isSmallScreen() {
      return window.innerWidth < 1280
    },
  },
  watch: {
    /**
     * Trigger automatic selection after data update (once)
     */
    automaticSelectionDone: {
      handler() {
        this.execAutomatedSelection()
      },
      immediate: true,
    },
    //Sync chart instance data zoom using centralized data zoom value (200ms)
    echartDataZoom() {
      if (!this.$store.getters['diagnostics/hasValidEchartDataZoom']) {
        return
      }

      const { updateStickyLabelPixelMap } = useChartAxisPixelMap()

      queueOperationOnce(
        `diagnostics_${this._uid}_sync_echart_data_zoom`,
        () => {
          if (
            !!this.echartDataZoom.start &&
            !!this.echartDataZoom.end &&
            (this.currentDataZoom.start != this.echartDataZoom.start ||
              this.currentDataZoom.end != this.echartDataZoom.end)
          ) {
            this.hasDispatchedDataZoomItself = true
            this.dispatchAction(this.echartDataZoom)

            updateStickyLabelPixelMap({
              chartInstance: this.chartInstance,
              isFirstChart: this.isFirstChart,
              store: this.$store,
            })
          }
        },
        {
          timeout: 300,
          clearPreviousTimeout: true,
        }
      )
    },
    filteredDataItems: {
      handler(newItems) {
        this.execAutomatedSelection()
        this.currentDataZoom.start = 0 //Reset zoom when data changes
        this.currentDataZoom.end = 100
        this.updateChart(`${this._uid}: dataItems change`)
      },
      //deep: true,
    },
    echartMarkLineData() {
      if (this.isNormalGPSChart) {
        /*  console.debugVerbose(
          8,
          'echartMarkLineData change',
          this.shouldRenderMarkLine
        ) */
      }
      if (this.shouldRenderMarkLine) {
        this.currentDataZoom.start = this.echartDataZoom.start
        this.currentDataZoom.end = this.echartDataZoom.end

        //Also clear brush selection
        if (this.$store.state.diagnostics.chartSelectedItem) {
          this.dispatchAction({
            type: 'brush',
            command: 'clear',
            areas: [],
          })
        }

        this.updateChart(`${this._uid}: echartMarkLineData change`)
      }
    },
    menuCollapsed() {
      if (!this._isDestroyed && !!this.chartInstance) {
        this.chartInstance.resize({
          width: 'auto',
          height: 'auto',
        })
      }
    },
    selectedItem() {
      queueOperationOnce('onSelectedItemChange', () =>
        this.onSelectedItemChange()
      )
    },
  },
  mounted() {
    // Set up direct chart-to-chart synchronization listener
    if (this.chartSyncBus) {
      // Store handler reference for removal during cleanup
      this.chartZoomHandler = (e) => {
        // Skip if this chart initiated the event
        if (e.detail.sourceChartId === this._uid) return

        // Set flag to prevent event loops
        this.processingExternalZoom = true

        // Apply zoom directly without going through store
        /* console.debug(`[DiagnosticsChart] ${this.name} received direct zoom sync, applying`, e.detail.zoomData); */
        this.dispatchAction(e.detail.zoomData)
      }

      // Add listener
      this.chartSyncBus.addEventListener('chart-zoom', this.chartZoomHandler)
    }
    this.$mitt.on('resizeable_stop', this.onResizeableStop)
    window.addEventListener('resize', this.onResizeableStop)

    this.updateChart(`${this._uid}: mounted`)

    if (this.isNormalGPSChart) {
      this.dispatchAction({
        type: 'takeGlobalCursor',
        key: 'brush',
        brushOption: {
          brushType: 'lineX',
          brushMode: 'single',
        },
      })
    }

    const { updateStickyLabelPixelMap } = useChartAxisPixelMap()

    updateStickyLabelPixelMap({
      chartInstance: this.chartInstance,
      isFirstChart: this.isFirstChart,
      store: this.$store,
    })

    //this.dispatchZoomUsingSelectedTimeRange()
  },
  beforeDestroy() {
    // Clean up event listener
    if (this.chartSyncBus && this.chartZoomHandler) {
      this.chartSyncBus.removeEventListener('chart-zoom', this.chartZoomHandler)
    }

    this.chartInstance = null
    this.chartInstanceOptions = null
  },
  methods: {
    /**
     * Handles the stop event of the resizable chart.
     * This method adjusts the chart's size and ensures proper rendering.
     *
     * The resizing logic includes multiple attempts because:
     * - Firefox may not register the resize immediately due to its rendering behavior.
     * - Using $nextTick ensures that the DOM updates are complete before resizing.
     * - requestAnimationFrame is utilized for smoother transitions in rendering.
     *
     * @param {number} attempt - The current attempt number for resizing.
     */
    onResizeableStop() {
      /* console.log('charts', 'adjust charts size') */
      const { updateStickyLabelPixelMap } = useChartAxisPixelMap()
      const resizeChart = (attempt = 0) => {
        if (attempt < 3) {
          this.$nextTick(() => {
            requestAnimationFrame(() => {
              this.chartInstance.resize({
                width: 'auto',
                height: 'auto',
              })
              this.updateChart('resize event')

              updateStickyLabelPixelMap({
                chartInstance: this.chartInstance,
                isFirstChart: this.isFirstChart,
                store: this.$store,
              }) // Resize external x-axis labels
            })
          })
          setTimeout(() => resizeChart(attempt + 1), 200) // Retry after 200ms
        }
      }
      resizeChart() // Start the resizing attempts
    },
    execAutomatedSelection() {
      if (
        this.isNormalGPSChart &&
        !this.automaticSelectionDone &&
        this.filteredDataItems.length > 0
      ) {
        this.selectAllRangesPerDefault()
        this.$store.state.diagnostics.automaticSelectionDone = true
      }
    },
    setOption(actionName, params) {
      this.$refs.vchart.setOption(actionName, params)
    },
    dispatchAction(actionName, params) {
      this.$nextTick(() => {
        this.$refs.vchart.dispatchAction(actionName, params)
      })
    },
    /**
     * Auto select using 0..N from available data
     */
    selectAllRangesPerDefault() {
      /* console.debugVerbose(
        8,
        'selectAllRangesPerDefault',
        this.filteredDataItems.length
      ) */
      let newItems = this.filteredDataItems
      clearInterval(this.firstLoadSelectTimeout)
      this.firstLoadSelectTimeout = setTimeout(() => {
        const startTimestamp = newItems[0]?.value[0] ?? -1
        const endTimestamp = newItems[newItems.length - 1]?.value[0] ?? -1

        if (startTimestamp === -1 || endTimestamp === -1) {
          return
        }

        this.selectRangeUsingTimestamps(startTimestamp, endTimestamp)
      }, 1000) // to prevent selection spamming
    },
    /**
     * Update centralized datazoom value when different
     * @param {*} evt
     */
    datazoom(evt) {
      /* console.debug(`[DiagnosticsChart] ${this.name} datazoom event`, evt); */

      // Skip if this event was triggered by sync from another chart
      if (this.processingExternalZoom) {
        this.processingExternalZoom = false
        return
      }

      // Also keep store updated (for backward compatibility)
      if (this.hasDispatchedDataZoomItself) {
        this.hasDispatchedDataZoomItself = false
        return
      }

      const { updateStickyLabelPixelMap } = useChartAxisPixelMap()

      // Get current zoom state
      const zoom = this.chartInstance.getOption().dataZoom[0]

      // Update local state
      this.currentDataZoom = {
        type: 'dataZoom',
        dataZoomIndex: 0,
        start: zoom.start,
        end: zoom.end,
      }

      // Performance: Direct sync with other charts via event bus
      if (this.chartSyncBus) {
        // Create custom event with zoom data
        const zoomEvent = new CustomEvent('chart-zoom', {
          detail: {
            sourceChartId: this._uid,
            zoomData: { ...this.currentDataZoom },
          },
        })

        // Dispatch immediately without debounce
        this.chartSyncBus.dispatchEvent(zoomEvent)
        /* console.debug(`[DiagnosticsChart] ${this.name} dispatched direct zoom sync event`, this.currentDataZoom); */
      }

      // Also update store for backward compatibility
      // Use a longer timeout since direct sync is handling immediate needs
      queueOperationOnce(
        `diagnostics_chart_update_store_datazoom`,
        () => {
          // Still update the store for backward compatibility with existing code
          if (
            this.echartDataZoom?.start != zoom.start ||
            this.echartDataZoom?.end != zoom.end
          ) {
            this.echartDataZoom = this.currentDataZoom
          }

          updateStickyLabelPixelMap({
            chartInstance: this.chartInstance,
            isFirstChart: this.isFirstChart,
            store: this.$store,
          })
        },
        {
          timeout: 200,
          clearPreviousTimeout: true,
        }
      )
    },
    /**
     * Range selection
     * Warning: Echarts trigger this event while dragging (CPU intensive)
     */
    brushselected(params) {
      if (!this.shouldUpdateTimeRangeOnBrushSelection) {
        return
      }
      queueOperationOnce(
        `diagnostics_chart_${this.name}_brushselected`,
        () => {
          /* console.debugVerbose(8, 'brushselected::queued-operation', {
            params,
          }) */

          let areas = params.batch[0]?.areas
          if (areas instanceof Array && areas.length === 0) {
            return //ignore extra event
          }

          let coordRange = params.batch[0]?.areas[0]?.coordRange || []

          if (coordRange.length === 2) {
            this.closePositionMakerPopup()
            this.selectRangeUsingTimestamps(coordRange[0], coordRange[1])
            this.$emit('onSelection')
          } else {
            if (
              this.brushStartTimestamp !== null ||
              this.brushEndTimestamp !== null
            ) {
              this.brushStartTimestamp = null
              this.brushEndTimestamp = null
            }
          }

          /*
          let selectedIndexes = params.batch[0]?.selected[0]?.dataIndex || []
          if (selectedIndexes.length > 0) {

            let startTimestamp = this.filteredDataItems[selectedIndexes[0]].value[0]

            let endTimestamp = this.filteredDataItems[
                selectedIndexes[selectedIndexes.length - 1]
              ].value[0]

            this.closePositionMakerPopup()
            this.selectRangeUsingTimestamps(startTimestamp, endTimestamp)
            this.$emit('onSelection')
          } else {

          }*/
        },
        {
          clearPreviousTimeout: true,
          timeout: 1000,
        }
      )
    },

    /**
     * Closes a previous single position popup if opened
     */
    closePositionMakerPopup() {
      try {
        let positionsLayerGroup =
          this.$map.getLeafletWrapperVM().layerGroups.positions
        if (positionsLayerGroup) {
          positionsLayerGroup.getLayers().forEach((l) => {
            if (l && typeof l.closePopup === 'function') {
              l.closePopup()
            } else {
              console.warn(
                'Layer is not defined or closePopup method is not available.'
              )
            }
          })
        } else {
          console.warn('Positions layer group is not defined.')
        }
      } catch (error) {
        console.error('Error in closePositionMakerPopup:', error)
      }
    },
    onChartClick(params) {
      /* console.debugVerbose(8, 'onChartClick', {
        params,
      }) */

      if (['custom', 'line', 'scatter'].includes(params.seriesType)) {
        let timestamp = params.value[0]
        this.selectChartItemByTimestamp(timestamp)
      } else {
        /* console.debugVerbose(8, 'Chart item selection skipped', {
          params,
        }) */
        this.$store.state.diagnostics.chartSelectedItem = null
        this.echartMarkLineData = []
      }
    },
    /**
     * Single item selection
     * @param {*} params
     */
    selectChartItemByTimestamp(timestamp) {
      this.$nextTick(() => {
        /* console.debugVerbose(8, 'selectChartItemByTimestamp', {
          timestamp,
        }) */

        /*
        //The user has clicked the previous selected position
        if (
          this.$store.state.diagnostics.chartSelectedItem &&
          this.echartMarkLineData.length > 0 &&
          [0, 1].includes(index)
        ) {
          this.$store.state.diagnostics.chartSelectedItem = null
          this.echartMarkLineData = []
          return
        }*/

        let positions = this.$store.state.diagnostics.positions

        const positionFindOffset = (offset = 0) =>
          positions.find((pos) => pos.timestamp === timestamp + offset)

        //Fix: Chono timestamp might not exist in normal positions dataset, so we try to offset left and right up to 3s to match a position.
        let selectedItem =
          positionFindOffset(0) ||
          positionFindOffset(1000) ||
          positionFindOffset(-1000) ||
          positionFindOffset(2000) ||
          positionFindOffset(-2000) ||
          positionFindOffset(3000) ||
          positionFindOffset(-3000) ||
          positionFindOffset(4000) ||
          positionFindOffset(-4000) ||
          positionFindOffset(5000) ||
          positionFindOffset(-5000) ||
          positionFindOffset(6000) ||
          positionFindOffset(-6000)
        null

        this.$store.state.diagnostics.chartSelectedItemTrigger = 'chart'

        this.$store.state.diagnostics.chartSelectedItem = selectedItem

        this.setPositionsChartVerticalLines(timestamp, timestamp)

        this.analysisResult = {}

        /* console.debugVerbose(8, 'selectChartItemByTimestamp', {
          selectedItem,
          timestamp,
        }) */
        this.$emit('onSelection')
      })
    },
    selectRangeUsingTimestamps(startTimestamp, endTimestamp) {
      /* console.debugVerbose(8, `${this._uid}: selectRangeUsingTimestamps`, {
        startTimestamp,
        endTimestamp,
      }) */
      if (startTimestamp && endTimestamp) {
        this.brushStartTimestamp = startTimestamp
        this.brushEndTimestamp = endTimestamp
        this.setPositionsChartVerticalLines(startTimestamp, endTimestamp)
      } else {
        this.brushStartTimestamp = null
        this.brushEndTimestamp = null
      }
    },
    /**
     * Vertical lines that delimit the selected range by brush (Mouse selection)
     */
    setPositionsChartVerticalLines(startTimestamp, endTimestamp) {
      this.echartMarkLineData = [
        { name: 'selection_start', xAxis: startTimestamp },
        { name: 'selection_end', xAxis: endTimestamp },
      ]
    },
    /**
     * Manually update the vchart component (:manual-update="true") for performance reasons
     */
    updateChart(origin = '') {
      /* console.debugVerbose(8, 'updateChart', {
        origin,
      }) */

      this.updateChartInstanceOptions()
      this.setOption(this.chartInstanceOptions)
    },
    /**
     * Manually re-compute chart options object
     */
    updateChartInstanceOptions() {
      let self = this
      let seriesItem = {
        name: this.name,
        // Enable large mode for better performance with large datasets
        large: true,
        // Enable progressive rendering to prevent UI freezes
        progressive: true,
        progressiveChunkSize: 1000, // Render 1000 points per frame
        // Disable animations for better performance
        animation: false,
        label: {
          //show: true,
        },
        tooltip: {
          // Default tooltip formatting will be used
        },
        itemStyle: {
          color: this.color,
        },
      }

      if (this.type === 'plot') {
        seriesItem = {
          ...seriesItem,
          symbol: 'rect',
          symbolSize: [3, 20],
          symbolKeepAspect: true,
          universalTransition: true,
          type: 'scatter',
        }
      } else {
        seriesItem.type = this.type
      }

      if (this.type === 'line') {
        seriesItem.yAxisIndex = 1
        seriesItem.smooth = this.isSpeedChart ? false : true
        /*seriesItem.lineStyle = this.options.lineStyle
          ? this.options.lineStyle
          : {
              color: this.color,
            }*/

        seriesItem.lineStyle = {
          color: this.color,
        }
        seriesItem.sampling = 'average' // Aggregate data points in dense areas
      }

      //TODO: Draw vertical lines
      if (this.shouldRenderMarkLine) {
        seriesItem.markLine = {
          lineStyle: {
            width: 5,
            color: getComputedStyle(
              document.querySelector(':root')
            ).getPropertyValue(
              'var(--color-denim-trans)'
                .split('var(')
                .join('')
                .split(')')
                .join('')
            ),
            type: 'solid',
          },
          label: {
            show: false,
          },
          symbol: 'none',
          data: this.echartMarkLineData,
          tooltip: {
            formatter(params) {
              return `${moment(params.data.value).format(
                'DD/MM/YYYY HH:mm:ss'
              )}`
            },
            extraCssText: 'margin-top:70px',
          },
        }
      }

      if (this.type === 'duration') {
        ;(seriesItem.tooltip = {
          extraCssText: 'margin-top:70px',
          formatter: (params) => {
            let start = moment(params.value[0]).format('DD/MM/YYYY HH:mm:ss')
            let end = moment(params.value[1]).format('DD/MM/YYYY HH:mm:ss')
            return `Start: ${start}
            <br/>
            End: ${end}`
          },
        }),
          (seriesItem.type = 'custom')
        seriesItem.dimensions = ['start', 'end', 'duration']
        seriesItem.renderItem = (params, api) => {
          var start = api.coord([api.value(0), 0])
          var end = api.coord([api.value(1), 0])
          var height = api.size([0, 1])[1] * 0.6
          var rectShape = echarts.graphic.clipRectByRect(
            {
              x: start[0],
              y: start[1] - height / 2,
              width: end[0] - start[0],
              height: height,
            },
            {
              x: params.coordSys.x,
              y: params.coordSys.y,
              width: params.coordSys.width,
              height: params.coordSys.height,
            }
          )
          return (
            rectShape && {
              type: 'rect',
              transition: ['shape'],
              shape: rectShape,
              style: api.style(),
            }
          )
        }
        seriesItem.encode = {
          x: ['start', 'end'],
        }
      }

      if (this.options?.tooltip?.formatter) {
        seriesItem.tooltip.formatter = this.options.tooltip.formatter
      }

      // Add performance monitoring
      /* console.debug(`[DiagnosticsChart] Updating chart options for ${this.name} with ${this.filteredDataItems?.length || 0} data points`); */

      this.chartInstanceOptions = {
        // Disable all animations at root level for better performance
        animation: false,
        grid: [
          {
            top: 0,
            bottom: 2,
            height: this.isNormalGPSChart ? 50 : 50,
            show: true,
            containLabel: false,
            left: this.isSmallScreen ? '25%' : '15%', //Gives some space to show labels (Long text can be cut off otherwise)
            right: '5%',
          },
        ],
        xAxis: [
          {
            position: 'top',
            show: true, //this.isNormalGPSChart,
            type: 'time',
            splitLine: {
              show: true,
            },
            boundaryGap: false,
            axisLabel: {
              formatter: function (value) {
                return moment(value).format('HH:mm')
              },
              show: false,
            },
            axisTick: {
              show: false,
            },
            min: function (value) {
              return self.$store.getters['diagnostics/positionsMinMaxDatetimes']
                .min
              //return moment(value.min).subtract(30, 'minute')._d.getTime() //.hour(0).minute(0).second(0)
            },
            max: function (value) {
              return self.$store.getters['diagnostics/positionsMinMaxDatetimes']
                .max
              //return moment(value.max).add(30, 'minute')._d.getTime() //.hour(23).minute(59).second(59)
            },
          },
        ],
        yAxis: [
          {
            type: 'category',
            position: 'left',

            data: [
              this.label ||
                this.$translation(
                  `diagnostics.chart.category.${this.name.toLowerCase()}`,
                  this.name
                ),
            ],
          },
          {
            type: 'value',
            position: 'right',
            splitNumber: 2,
            axisLabel: {
              fontSize: 10,
              margin: 8,
              verticalAlign: 'top',
            },
          },
        ],
        series: [
          {
            ...seriesItem,
            data: this.filteredDataItems,
          },
        ],
        ...(this.options.visualMap
          ? { visualMap: this.options.visualMap }
          : {}),
        tooltip: {
          type: 'item',
        },
        toolbox: this.options?.toolbox
          ? this.options?.toolbox
          : {
              show: false,
            },
        brush: {
          brushMode: 'single',
          transformable: false,
          xAxisIndex: 'all',
          brushLink: 'all',
          outOfBrush: {
            colorAlpha: 0.9,
          },
        },
        // Optimize dataZoom for performance
        dataZoom: [
          {
            type: 'inside',
            rangeMode: ['percent', 'percent'],
            xAxisIndex: [0, 1],
            start: this.currentDataZoom.start || 0,
            end: this.currentDataZoom.end || 100,
          },
        ],
      }
    },
    onSelectedItemChange() {
      //Handle item deselect
      if (!this.selectedItem) {
        this.onItemDeselect()
        return
      }

      //Get newly selected item timestamp
      let timestamp = this.selectedItem.timestamp

      if (!timestamp) {
        return
      }

      //Select new item in chart by timestamp
      this.selectChartItemByTimestamp(timestamp)
    },
    /**
     * Select previously selected range on item deselect
     */
    onItemDeselect() {
      this.selectRangeUsingTimestamps(
        this.brushStartTimestamp,
        this.brushEndTimestamp
      )
    },
  },
}
</script>

<style scoped>
.diagnostics-single-chart {
  width: 100%;
  min-height: 50px !important;
}
.diagnostics-single-chart.default-chart {
  min-height: 70px !important; /*110 */
}
</style>

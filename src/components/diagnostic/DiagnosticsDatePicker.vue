<template lang="pug">
date-picker(
    v-model="selectedValue"
    :lang="lang"
    type="datetime"
    :format="$date.getDatetimePattern({seconds:false})"
    @clear="$emit('onDateClear')"
    @change="handleChange"
    :open.sync="open"
    :showWeekNumber="true"
    :showSecond="false"
    :placeholder="$t('diagnostics.search.datetimepicker_label')"
)
      
</template>
<script>
/*
class="sm_datepicker"
    popup-class="sm_datepicker__popup"
    :range="false"
    :multiple="false"
    :confirm-text="$t('search_module.date_picker.validate')"
    :disabled="isSearchModuleDatePickerDisabledHandler()"
*/
import DatePicker from 'vue2-datepicker'
import 'vue2-datepicker/locale/fr'
import 'vue2-datepicker/index.css'
import { mapGetters } from 'vuex'
import moment from 'moment'
import i18nDatepickerMixin from '@/mixins/i18n-datepicker.js'

export default {
  components: {
    DatePicker,
  },
  mixins: [i18nDatepickerMixin],
  inject: {
    isSearchModuleDatePickerDisabledHandler: {
      default: () => () => {},
    },
  },
  data() {
    return {
      open: false,
      selectedValue: null,
    }
  },
  computed: {
    ...mapGetters({
      selectedDateRanges: 'search_module/getSelectedDateRanges',
    }),
  },
  watch: {
    selectedValue() {
      this.$emit('onDateSelection', this.selectedValue)
    },
    /**
     * Update selected value if store selected value has been updated and different
     */
    selectedDateRanges() {
      if (
        this.selectedDateRanges.length >= 1 &&
        this.selectedDateRanges[0].length >= 1
      ) {
        let storeDate = this.selectedDateRanges[0][0]
        /* console.log({
          storeDate,
          selectedValue: this.selectedValue,
        }) */
        if (
          moment(storeDate)._d.getTime() !=
          moment(this.selectedValue)._d.getTime()
        ) {
          this.selectedValue = storeDate
        }
      }

      if (this.selectedDateRanges.length === 0 && !!this.selectedValue) {
        this.selectedValue = null
      }
    },
  },
  mounted() {
    if (this.selectedDateRanges.length > 0) {
      this.selectedValue = this.selectedDateRanges[0][0]
    }
  },
  methods: {
    handleChange(value, type) {
      if (type === 'second') {
        this.open = false
      }
      /* console.log('handleChange: ', {
        value,
        type,
      }) */
    },
  },
}
</script>
<style lang="scss">
.mx-datepicker {
  width: 100%;
}
</style>

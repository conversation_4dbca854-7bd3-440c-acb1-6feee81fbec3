<template>
  <div class="time-range-wrapper">
    <div class="time-range-label-container">
      <svg
        class="time-icon"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width="16"
        height="16"
      >
        <path
          fill="#409eff"
          d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12.5,7H11V13L16.2,16.2L17,14.9L12.5,12.2V7Z"
        />
      </svg>
      <div class="time-range-labels">{{ formatValue(value[0]) }}</div>
    </div>
    <div ref="sliderContainer" class="time-range"></div>
    <div id="rightLabel" class="time-range-labels">
      {{ formatValue(value[1]) }}
    </div>
  </div>
</template>
<script>
import TimeRangeSlider from './TimeRangeSlider.js'

export default {
  props: {
    min: {
      type: Number,
      default: -1,
    },
    max: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      initialized: false,
      value: [0, 0],
      slider: null,
      debounceTimeout: null,
    }
  },
  computed: {
    isVisible() {
      return this.min !== -1 && this.max !== -1
    },
    normalizedMin() {
      return Math.round(this.min)
    },
    normalizedMax() {
      return Math.round(this.max)
    },
    selectedTimeRangeInMinutes: {
      set(value) {
        this.$store.state.diagnostics.selectedTimeRangeInMinutes = value
      },
      get() {
        return this.$store.state.diagnostics.selectedTimeRangeInMinutes
      },
    },
  },
  watch: {
    isVisible: {
      handler() {
        if (this.isVisible && !this.initialized) {
          this.value = [
            Math.floor(this.normalizedMin),
            Math.round(this.normalizedMax),
          ]
          this.initialized = true
          this.$nextTick(() => {
            this.initSlider()
          })
        }
      },
      immediate: true,
    },
    value: {
      handler(newValue) {
        // Update the slider if it exists and the change wasn't triggered by the slider
        if (this.slider && !this.updatingFromSlider) {
          this.slider.setValue(newValue)
        }

        // Debounce the store update (replacing queueOperationOnce)
        this.debounceStoreUpdate()
      },
      deep: true,
      immediate: true,
    },
    min() {
      this.updateSliderOptions()
    },
    max() {
      this.updateSliderOptions()
    },
  },
  mounted() {
    if (this.isVisible) {
      this.initSlider()
    }
  },
  beforeDestroy() {
    this.destroySlider()
  },
  methods: {
    initSlider() {
      /* console.log('TimeRangeSlider.vue initSlider', {
        min: this.normalizedMin,
        max: this.normalizedMax,
        value: this.value,
      }) */

      // Destroy existing slider if it exists
      this.destroySlider()

      // Create new slider instance
      const container = this.$refs.sliderContainer
      if (!container) return

      this.slider = new TimeRangeSlider(container, {
        min: this.normalizedMin,
        max: this.normalizedMax,
        value: [...this.value],
        onChange: this.handleSliderChange,
      })
    },

    destroySlider() {
      if (this.slider) {
        this.slider.destroy()
        this.slider = null
      }
    },

    updateSliderOptions() {
      if (this.slider && this.isVisible) {
        this.slider.updateOptions({
          min: this.normalizedMin,
          max: this.normalizedMax,
        })
      }
    },

    handleSliderChange(newValue) {
      /* console.log('TimeRangeSlider.vue handleSliderChange', { newValue }) */
      // Flag to prevent infinite loop with the watcher
      this.updatingFromSlider = true
      this.value = [...newValue]
      this.$nextTick(() => {
        this.updatingFromSlider = false
      })
    },

    debounceStoreUpdate() {
      // Clear any existing timeout
      if (this.debounceTimeout) {
        clearTimeout(this.debounceTimeout)
      }

      // Set a new timeout
      this.debounceTimeout = setTimeout(() => {
        /* console.log('TimeRangeSlider.vue debounceStoreUpdate', {
          value: this.value,
        }) */
        this.selectedTimeRangeInMinutes = this.value
      }, 1000)
    },

    formatValue(value) {
      let s = value * 60 * 1000
      var ms = s % 1000
      s = (s - ms) / 1000
      var secs = s % 60
      s = (s - secs) / 60
      var mins = s % 60
      var hrs = (s - mins) / 60

      if (hrs.toString().length === 1) {
        hrs = `0${hrs}`
      }

      if (mins.toString().length === 1) {
        mins = `0${mins}`
      }

      return hrs + ':' + mins // + ':' + secs + '.' + ms
    },
  },
}
</script>
<style scoped lang="scss">
.time-range-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  flex-wrap: nowrap;
  column-gap: 12px;
  justify-content: space-around;
}

.time-range-label-container {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  white-space: nowrap;
  margin-right: 10px;
}

.time-range-labels {
  font-weight: bold;
  white-space: nowrap;
  flex-shrink: 0;
}

.time-range {
  position: relative;
  height: 30px;
  flex-grow: 1;
  flex-shrink: 1;
  min-width: 80px;

  :deep(.time-range-slider) {
    width: 100%;
    height: 100%;
    position: relative;
    cursor: pointer;
  }

  :deep(.time-range-handle) {
    position: absolute;
    top: 50%;
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: 2px solid #409eff;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }

  :deep(.time-range-range) {
    position: absolute;
    top: 50%;
    height: 4px;
    background-color: #409eff;
    transform: translateY(-50%);
    z-index: 1;
  }
}
</style>

<template lang="pug">
.diagnostic-analysis-overview
  DynamicSections(v-model="dynamicSectionsState")
    template(v-slot:details_title="slotProps")
      SectionTitleWrapper(
        :icon="'radar'",
        :canCollapse="false",
        textMinWidth="240px"
      ) {{ slotProps.title }}
    template(v-slot:vehicleName) {{vehicleDetails.vehicleName}}
    template(v-slot:vehicleRegistrationPlate) {{vehicleDetails.vehicleRegistrationPlate}}
    template(v-slot:startDateFormatted) {{vehicleDetails.startDateFormatted}}
    template(v-slot:startAddress) {{vehicleDetails.startAddress}}
    template(v-slot:endDateFormatted) {{vehicleDetails.endDateFormatted}}
    template(v-slot:endAddress) {{vehicleDetails.endAddress}}
    template(v-slot:durationFormatted) {{vehicleDetails.durationFormatted}}
    template(v-slot:distanceKmFormatted) {{vehicleDetails.distanceKmFormatted}}
    template(v-slot:averageSpeedKmhFormatted) {{vehicleDetails.averageSpeedKmhFormatted}}
</template>
<script>
import DynamicSections from '@c/shared/DynamicSections.vue'
import SectionTitleWrapper from '@c/shared/SectionTitle/SectionTitleWrapper.vue'
export default {
  components: {
    DynamicSections,
    SectionTitleWrapper,
  },
  props: {
    vehicleId: {
      type: Number,
      default: 0,
    },
    date: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dynamicSectionsState: {
        sections: [
          {
            name: 'details',
            title: this.$t('diagnostics.analysis.title_details'),
            collapsible: false,
            rows: [
              //Both modes
              {
                twoColumns: [
                  {
                    name: 'vehicleName',
                    label: 'common.Véhicule',
                    //value: this.vehicleDetails.vehicleName,
                  },
                  {
                    name: 'vehicleRegistrationPlate',
                    label: 'common.matriculation',
                    //value: this.vehicleDetails.vehicleRegistrationPlate,
                  },
                ],
              },
              {
                twoColumns: [
                  {
                    name: 'startDateFormatted',
                    label: 'common.from',
                    //value: this.vehicleDetails.startDateFormatted,
                  },
                  {
                    name: 'startAddress',
                    label: 'common.position',
                    //value: this.vehicleDetails.startAddress,
                  },
                ],
              },
              {
                twoColumns: [
                  {
                    name: 'endDateFormatted',
                    label: 'common.to',
                    //value: this.vehicleDetails.endDateFormatted,
                  },
                  {
                    name: 'endAddress',
                    label: 'common.position',
                    //value: this.vehicleDetails.endAddress,
                  },
                ],
              },
              {
                twoColumns: [
                  {
                    name: 'durationFormatted',
                    label: 'common.duree',
                    //value: this.vehicleDetails.durationFormatted,
                  },
                  {
                    name: 'distanceKmFormatted',
                    label: 'common.Distance',
                    //value: this.vehicleDetails.distanceKmFormatted,
                  },
                ],
              },
              {
                twoColumns: [
                  {
                    name: 'averageSpeedKmhFormatted',
                    label: 'searchModule.items.vitesseMoyenne',
                    visible: () => this.vehicleDetails.isCumulated !== true,
                    dataField: 'vehicleSpeed',
                    //value: this.vehicleDetails.averageSpeedKmhFormatted,
                  },
                ],
              },
            ],
          },
        ],
      },
    }
  },
  computed: {
    vehicleItem() {
      return (
        this.$store.getters['search_module/getVehicleById'](this.vehicleId) ||
        {}
      )
    },
    vehicleName() {
      return this.vehicleDetails?.vehicleName || this.vehicleItem.name || ''
    },
    vehicleDetails: {
      get() {
        return {
          ...this.$store.state.diagnostics.vehicleHistoryOverview,
          ...this.$store.state.diagnostics.vehicleConfiguration,
          ...(this.$attrs || {}),
        }
      },
    },
  },
  watch: {},
}
</script>
<style lang="scss" scoped></style>

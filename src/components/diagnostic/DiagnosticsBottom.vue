<template>
  <div class="diagnostics-bottom">
    <div class="header">
      <div class="row">
        <div class="col-12 col-md-6 col-xl-8">
          <div class="flex-area" style="justify-content: flex-start">
            <span
              class="nowrap"
              v-html="$t('diagnostics.bottom.period_label')"
            ></span>
            <span class="nowrap format">{{ datesFormatted }}</span>
            <div
              v-b-tooltip.hover.viewport="
                $t('diagnostics.bottom.calendar_tooltip')
              "
              class="calendar-button"
              @click="() => $emit('calendarClick')"
            >
              <Icon icon="bx:calendar-event" />
            </div>
            <span class="nowrap">
              {{ $t('diagnostics.bottom.period_label_vehicle') }} :
              {{ vehicleName || '' }}
            </span>
            <div
              class="buttons-area"
              :style="'opacity:' + (areCalendarButtonsDisabled ? '0.5' : '1')"
            >
              <div
                v-b-tooltip.hover.viewport
                :title="$t('diagnostics.bottom.calendar_prev_day')"
                @click="!areCalendarButtonsDisabled && prevDay()"
              >
                <Icon icon="mdi:calendar-arrow-left" />
              </div>
              <div
                v-b-tooltip.hover.viewport="
                  $t('diagnostics.bottom.calendar_next_day')
                "
                class="next-day-btn"
                :class="{ greyout: isNextDayBtnDisabled }"
                @click="
                  !isNextDayBtnDisabled &&
                    !areCalendarButtonsDisabled &&
                    nextDay()
                "
              >
                <Icon icon="mdi:calendar-arrow-right" />
              </div>
            </div>
          </div>
        </div>

        <div v-show="positions.length > 0" class="col-12 col-md-6 col-xl-4">
          <div class="flex-area" style="justify-content: flex-end">
            <span>{{ $t('common.Vitesse') }} :</span>
            <div class="speed-control-cmp">
              <select v-model="replaySpeed">
                <option :value="1000">x1</option>
                <option :value="500">x2</option>
                <option :value="200">x4</option>
                <option :value="125">x8</option>
                <option :value="62.5">x16</option>
              </select>
            </div>
            <div class="buttons-area">
              <div
                v-b-tooltip.hover.viewport
                :title="$t('diagnostics.bottom.replay.prev')"
                @click="replayPrev"
              >
                <Icon icon="material-symbols:skip-previous" />
              </div>
              <div
                v-b-tooltip.hover.viewport
                :title="
                  $t(
                    isReplayPlaying
                      ? 'diagnostics.bottom.replay.stop'
                      : 'diagnostics.bottom.replay.play'
                  )
                "
                @click="replayPlayToggle"
              >
                <Icon
                  v-show="!isReplayPlaying"
                  icon="material-symbols:play-arrow"
                />
                <Icon v-show="isReplayPlaying" icon="material-symbols:pause" />
              </div>
              <div
                v-b-tooltip.hover.viewport
                :title="$t('diagnostics.bottom.replay.next')"
                @click="replayNext"
              >
                <Icon icon="material-symbols:skip-next" />
              </div>
            </div>
            <div
              class="buttons-area"
              @click="() => (lateralMenuMode = 'options')"
            >
              <div
                v-b-tooltip.hover.viewport
                :title="$t('diagnostics.bottom.chart_options_button_tooltip')"
              >
                <Icon icon="carbon:settings-adjust" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="isSliderEnabled" class="row mt-1">
        <div class="col-12 p-0">
          <div class="row p-0 m-0">
            <div class="col-12" style="display: flex; column-gap: 10px">
              <!-- Optional icon if you want it back
              <div class="time-range-slider-tooltip"
                v-b-tooltip.hover.viewport
                :title="$t('diagnostics.bottom.slider_tooltip')"
              >
                <Icon icon="ic:outline-access-time-filled" color="var(--color-main')" :style="{ fontSize: '16px' }" />
              </div>
              -->
              <TimeRangeSlider
                :min="sliderMin"
                :max="sliderMax"
                style="flex-grow: 1; flex-shrink: 0"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-1">
        <div class="col-12" style="position: relative; height: 20px">
          <div
            class="external-x-axis"
            style="position: absolute; top: 0; height: 20px"
          >
            <div
              v-for="(px, ts) in pixelMap"
              :key="ts"
              :style="{
                position: 'absolute',
                left: px + 'px',
                transform: 'translateX(-50%)',
                whiteSpace: 'nowrap',
                fontSize: '12px',
                color: '#6e7079',
              }"
            >
              {{ getFormattedPixelMap(ts) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12 charts-upper-wrapper">
        <div class="charts-wrapper mt-1">
          <slot></slot>
        </div>
        <ChartButtons
          v-if="!areCalendarButtonsDisabled && positions.length > 0"
          @zoomfit="zoomfit"
          @zoomout="zoomout"
          @zoomin="zoomin"
          @moveleft="moveleft"
          @moveright="moveright"
        />
      </div>
      <!-- Optional column
      <div class="col-1 p-0" v-if="!isLoading && positions.length > 0"></div>
      -->
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import TimeRangeSlider from '@/components/diagnostic/TimeRangeSlider.vue'
import replayMixin from '@/components/diagnostic/replay-mixin.js'
import moment from 'moment'
import { Icon } from '@iconify/vue2'
import ChartButtons from '@/components/diagnostic/ChartButtons.vue'
import ChartButtonsMixin from '@/components/diagnostic/chart-buttons-mixin.js'
import { adjustDateWithTimezone } from '@/utils/dates'

/**
 * @namespace mixins
 * @category mixins
 * @subcategory diagnostics
 * @module diagnostics-layout-bottom
 * @mixes diagnostics-replay-mixin
 **/
export default {
  name: 'DiagnosticsBottom',
  components: {
    TimeRangeSlider,
    Icon,
    ChartButtons,
  },
  mixins: [replayMixin, ChartButtonsMixin],
  props: {
    vehicleName: {
      type: String,
      default: '',
    },
    datesFormatted: {
      type: String,
      default: '',
    },
    isTest: {
      type: Boolean,
      default: false,
    },
    date: {
      type: [String, Date],
      default: '',
    },
  },

  computed: {
    ...mapGetters({
      positions: 'diagnostics/positions',
      doesPeriodContainsMultipleDays:
        'diagnostics/doesPeriodContainsMultipleDays',
      sliderMin: 'diagnostics/sliderMin',
      sliderMax: 'diagnostics/sliderMax',
      pixelMap: 'diagnostics/getPixelMap',
    }),
    isSliderEnabled() {
      return (
        !this.isLoading &&
        this.positions.length > 0 &&
        !this.doesPeriodContainsMultipleDays
      )
    },
    isNextDayBtnDisabled() {
      return moment(this.date).diff(moment(), 'day') === 0
    },
    isLoading: {
      set(value) {
        this.$store.state.diagnostics.isLoading = value
      },
      get() {
        return this.$store.state.diagnostics.isLoading
      },
    },
    lateralMenuMode: {
      set(value) {
        this.$store.state.diagnostics.lateralMenuMode = value
      },
      get() {
        return this.$store.state.diagnostics.lateralMenuMode
      },
    },
    areCalendarButtonsDisabled() {
      return this.$store.state.diagnostics.isChronoDataLoading || this.isLoading
    },
  },

  methods: {
    async prevDay() {
      /* console.debugVerboseScope(5, 'diags', 'prevDay') */
      this.terminateReplay()
      let date = moment(this.date).subtract(1, 'day')
      await this.$store.dispatch('search_module/clearDateSelection')
      await this.$store.dispatch('search_module/selectItem', {
        type: 'date_range',
        value: [date._d, date._d],
        origin: 'DiagnosticsBottom::prevDay',
      })
      await this.$store.dispatch('search_module/validateSearch') //Force overview info refresh
      this.$emit('onDayChange')
    },
    async nextDay() {
      /* console.debugVerboseScope(5, 'diags', 'nextDay') */
      this.terminateReplay()
      let date = moment(this.date).add(1, 'day')
      await this.$store.dispatch('search_module/clearDateSelection')
      await this.$store.dispatch('search_module/selectItem', {
        type: 'date_range',
        value: [date._d, date._d],
        origin: 'DiagnosticsBottom::nextDay',
      })
      await this.$store.dispatch('search_module/validateSearch') //Force overview info refresh
      this.$emit('onDayChange')
    },
    onRangeSelection(params) {
      this.$emit('onRangeSelection', params)
    },
    getFormattedPixelMap(value) {
      return adjustDateWithTimezone(Number(value)).format('HH:mm')
    },
  },
}
</script>
<style lang="scss" scoped>
.diagnostics-bottom {
  margin: 10px 15px 0 25px;
  position: relative;
  height: calc(100% - 10px);
  overflow-x: hidden;
  overflow-y: auto;

  svg {
    font-size: 30px;
  }

  .header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: white;
    width: 100%;
    box-sizing: border-box;
  }
}

.flex-area {
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 10px;
  flex-wrap: wrap;
}

.flex-area > * {
  min-width: 0;
  flex-shrink: 1;
}

span {
  font: normal normal normal 11px/13px Open Sans;
  text-align: left;
  letter-spacing: 0px;
  color: #000000;
  opacity: 1;
}
.nowrap {
  white-space: nowrap;
}

em {
  color: var(--color-main);
}
span.format {
  font: normal normal bold 11px/13px Open Sans;
  letter-spacing: 0px;
  color: var(--color-main);
}

.calendar-button {
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3px;
  color: var(--color-main);
}

button {
  float: right;
  border: 0px;
  font-weight: 500;
  font-size: 16px;
  background-color: white;
  border-radius: 5px;
}
.display-options-button {
  cursor: pointer;
}
.buttons-area {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 1px 1px #0000005c;
  opacity: 1;
  display: flex;
  align-items: center;
  column-gap: 0px;
  cursor: pointer;
  color: var(--color-tundora);

  & > div {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3px;
  }

  & > div:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }
}
.time-range-slider-tooltip {
  position: relative;
  top: -3px;
}
.charts-upper-wrapper {
  display: flex;
}
.charts-wrapper {
  width: calc(100% - 25px);
  /*width: calc(100% - 25px);
  max-height: 310px;
  overflow: auto;
  padding-bottom: 50px;*/
}

.next-day-btn.greyout {
  background-color: #8080802b;
  cursor: default;
}
</style>

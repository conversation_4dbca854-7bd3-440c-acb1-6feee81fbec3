<template lang="pug">
.diagnostics-chrono-chart-wrapper
    .diagnostics-chrono-chart(ref="chart")
        vc-donut(:sections="sections" :size="150" unit="px" @section-mouseover="onMouseHover" @section-mouseout="onMouseHoverEnd" @section-mouseleave="onMouseHoverEnd" :thickness="30")
            div(v-html="sections[0].html")
            div(v-html="sections[1].html")
            div(v-html="sections[2].html")
            div(v-html="sections[3].html")
    .dccw_tooltip(v-show="selectedSection?.value" :style="selectedSectionStyle")
        div(:style="'color:'+selectedSection?.color||'black'") {{selectedSection?.label}}
        div(style="text-align: center;")
            strong {{selectedSection?.value}} %
    .dcc-values
        .dcc-value-item {{$t('diagnostics.analysis.chrono.amplitudeLabel')}}: {{amplitudeFormatted}}
        .dcc-value-item {{$t('diagnostics.analysis.chrono.serviceTimeLabel')}}: {{serviceTimeFormatted}}
    </template>
<script>
import { getChronoColorFromType } from '@/services/chrono-service.js'
import Donut from 'vue-css-donut-chart'
import 'vue-css-donut-chart/dist/vcdonut.css'
import Vue from 'vue'

Vue.use(Donut)

export default {
  name: 'DiagnosticsChartAlt',
  props: {
    vehicleId: {
      type: [String, Number],
      default: '',
    },
    date: {
      type: [String, Date],
      default: '',
    },
    name: {
      type: String,
      default: 'chrono',
    },
    label: {
      type: String,
      default: 'Chrono',
    },
    type: {
      type: String,
      default: 'pie',
      enum: ['plot', 'line', 'duration', 'pie'],
    },
    color: {
      type: String,
      default: 'grey',
    },
    isTest: {
      type: Boolean,
      default: false,
    },
    /**
     * Used to set a custom formatter to the echart series
     */
    options: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      selectedSection: null,
    }
  },
  computed: {
    menuCollapsed: {
      set(value) {
        this.$store.state.diagnostics.menuCollapsed = value
      },
      get() {
        return this.$store.state.diagnostics.menuCollapsed
      },
    },
    /**
     * Temps de service
     */
    serviceTimeFormatted() {
      return this.$store.state.diagnostics.chronoData?.synthesis?.periodService
    },
    amplitudeFormatted() {
      return this.$store.state.diagnostics.chronoData?.synthesis?.amplitude
    },

    sections() {
      let data = {
        ...(this.$store.state.diagnostics.chronoData?.synthesis || {}),
      }

      //Sometimes API returns chrono percs where sums is greater than 100, which breaks the third party donuts chart
      let total =
        data.percentAvailable +
        data.percentWork +
        data.percentDrive +
        data.percentRest
      /* console.log('Chrono chart total', {
        total,
      }) */
      if (total > 100) {
        let diff = total - 100 + 1

        if (data.percentAvailable > diff) {
          data.percentAvailable -= diff
          diff = 0
        }
        if (data.percentWork > diff) {
          data.percentWork -= diff
          diff = 0
        }
        if (data.percentDrive > diff) {
          data.percentDrive -= diff
          diff = 0
        }
        if (data.percentRest > diff) {
          data.percentRest -= diff
          diff = 0
        }
        /* console.warn(
          'Chono chart: The sum of all chrono percentages cannot acceed 100%!',
          {
            chronoApiData: {
              ...(this.$store.state.diagnostics.chronoData?.synthesis || {}),
            },
          }
        ) */
      }

      return [
        {
          value: data.percentAvailable,
          label: this.$t('location_module.chrono.available'),
          color: getChronoColorFromType('available'),
          html: `<span style="color:${getChronoColorFromType('available')}">${
            data.periodAvailable
          }</span>`,
        },
        {
          value: data.percentWork,
          durationFormatted: data.periodWork,
          label: this.$t('location_module.chrono.working'),
          color: getChronoColorFromType('working'),
          html: `<span style="color:${getChronoColorFromType('working')}">${
            data.periodWork
          }</span>`,
        },
        {
          value: data.percentDrive,
          durationFormatted: data.periodDrive,
          label: this.$t('location_module.chrono.driving'),
          color: getChronoColorFromType('driving'),
          html: `<span style="color:${getChronoColorFromType('driving')}">${
            data.periodDrive
          }</span>`,
        },
        {
          value: data.percentRest,
          durationFormatted: data.periodRest,
          label: this.$t('location_module.chrono.resting'),
          color: getChronoColorFromType('resting'),
          html: `<span style="color:${getChronoColorFromType('resting')}">${
            data.periodRest
          }</span>`,
        },
      ]
    },
    selectedSectionStyle() {
      /* console.log('e', this.selectedSection?._e) */
      return `position:absolute;left: ${this.selectedSection?._e?.layerX}px;top: ${this.selectedSection?._e?.layerY}px;`
    },
  },
  watch: {
    menuCollapsed() {
      this.resizeHandler()
    },
  },
  mounted() {
    window.addEventListener('resize', this.resizeHandler)
    window.addEventListener('orientationchange', this.resizeHandler)
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeHandler)
    window.addEventListener('orientationchange', this.resizeHandler)
  },
  methods: {
    onMouseHoverEnd(section, e) {
      this.selectedSection = null
      /*console.log('onMouseHoverEnd', {
        section,
        e,
      })*/
    },
    onMouseHover(section, e) {
      /*console.log('onMouseHover', {
        section,
        e,
      })*/
      section._e = e
      this.selectedSection = section
    },
    resizeHandler() {
      if (!this._isDestroyed) {
        /* console.log('resizeHandler') */
      }
    },
  },
}
</script>

<style scoped>
.diagnostics-chrono-chart {
  width: 100%;
}
.dcc-values {
  display: flex;
  column-gap: 15px;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
}
.diagnostics-chrono-chart-wrapper {
  width: 100%;
  position: relative;
}
.dccw_tooltip {
  background: white;
  padding: 5px 10px;
  border-radius: 10px;
}

.dccw_tooltip {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
</style>

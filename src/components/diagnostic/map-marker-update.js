import { getBearingFromTwoLatAndLng } from '@/utils/map.js'
import { getVehicleIconHTML } from '@/services/vehicle-service.js'
import { queueOperationOnce } from '@/utils/promise.js'
import mapMixin from '@/mixins/map.js'

const L = window.L // Attention, Leaflet should not be a global variable

export function updateMapVehicleMarker(leafletVM, position, options = {}) {
  position = { ...position }
  let lastPosition = options.lastPosition

  // Calculate the bearing angle between the last position and the current position
  let degValue = lastPosition
    ? getBearingFromTwoLatAndLng(
        [lastPosition.lat, lastPosition.lng],
        [position.lat, position.lng]
      )
    : 90

  let vehicleClassName = options.vehicleClassName
  let vehicleName = options.vehicleName || 'Vehicle'
  let hasContactOn = position.hasContactOn

  // Create a custom icon for the marker
  const icon = L.divIcon({
    className: 'realtime__marker',
    html: `<div class="marker__content">
      <div>
        <img class="marker_icon"
          data-computed-degrees="${degValue}"
          style="transform:translate(-50%, -50%) rotate(${degValue}deg)"
          src="./lib/realtimeMap/assets/picto_pins/Pin.svg">
        ${getVehicleIconHTML(vehicleClassName, {
          flipHorizontally: degValue > 180,
        })}
        <img class="marker__contact_icon"
          style="background-color:var(--color-contact-${
            hasContactOn ? 'on' : 'off'
          })"
          src="./lib/realtimeMap/assets/picto_status/Contact.svg" />
      </div>
      <p class="marker__label">
        ${vehicleName}
      </p>
    </div>`,
  })

  position.positionId = position.id
  position.id = 'vehicle'

  let visible = options.visible !== undefined ? options.visible : true

  let popupOptions = {
    style: 'min-width:267px;',
  }

  // Import the popup component dynamically
  let popupComponentImport = () =>
    /* webpackChunkName "main" */
    import('@/components/shared/SimplicitiMap/PositionMarkerPopup.vue')

  let smoothUpdateOptions = visible
    ? {
        update(marker, newPos) {
          marker.setIcon(icon)
          marker.closePopup && marker.closePopup()

          // After one second: Update popup component
          queueOperationOnce(
            `diagnostics_replay_marker_popup_update`,
            () => {
              marker.properties = newPos
              mapMixin.methods.bindLeafletPopupComponent(
                marker,
                popupComponentImport,
                {
                  ...popupOptions,
                  parent: leafletVM,
                  props: {
                    map: leafletVM.map,
                    data: newPos,
                  },
                }
              )
            },
            {
              clearPreviousTimeout: true,
              timeout: 700, // This should save resources by avoiding instantiating multiple components in a very short amount of time (e.g., speed x16)
            }
          )

          // Smoothly slide the marker to the new position
          marker.slideTo([newPos.lat, newPos.lng], {
            duration: options.slideDuration || 1000, // Should be the same as replaySpeed
            keepAtCenter: false,
          })
        },
      }
    : {}

  let data = position ? [position] : []
  if (!visible) {
    data = []
  }

  if (!leafletVM || !leafletVM.drawGeometries) {
    /* console.debugVerbose(
      8,
      'replay-mixin leafletVM.drawGeometries not available'
    ) */
    return
  }

  // Draw the marker on the map
  leafletVM.drawGeometries({
    visible,
    data,
    layerOptions: {
      zIndex: 999,
    },
    layer: 'diagnosticsReplayMarker',
    popupOptions,
    popup: popupComponentImport,
    preserveExistingLayers: true,
    ...smoothUpdateOptions,
    generate(pos) {
      return L.marker([pos.lat, pos.lng], {
        icon,
        zIndexOffset: 3,
      })
    },
  })
}

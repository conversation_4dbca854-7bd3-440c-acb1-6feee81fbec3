/**
 * @name replayPositionClickTriggerMixin.js
 *
 * Simulates an ECharts chart click in the DiagnosticsChart component (chart wrapper).
 * The click will render a vertical line that helps the user identify the exact current replay position in the chart (xAxis timestamp).
 *
 * @mixin
 * @requires this.isNormalGPSChart
 * @requires this.chartInstance
 * @requires this.chartInstanceOptions
 */
export default {
  computed: {
    /**
     * Retrieves the current replay position index from the Vuex store.
     * @returns {number} The current replay position index.
     */
    replayPosition() {
      return this.$store.state.diagnostics.replayPosition
    },
  },
  watch: {
    /**
     * Watches for changes in the replayPosition and update the vertical line accordingly
     */
    replayPosition() {
      if (
        this.isNormalGPSChart &&
        this.$store.state.diagnostics.chartSelectedItemTrigger === 'replay'
      ) {
        if (this.replayPosition) {
          const timestamp = this.replayPosition.timestamp
          const markLine = {
            name: 'replay_highlight',
            xAxis: timestamp,
            lineStyle: {
              width: 3,
              color: this.$map.defaultHighlightColor,
              type: 'solid',
            },
          }
          this.echartMarkLineData = [
            ...this.echartMarkLineData.filter(
              (ml) => ml.name !== 'replay_highlight'
            ),
            markLine,
          ]
        } else {
          if (this.echartMarkLineData.length === 3) {
            this.echartMarkLineData.pop()
          }
        }
      }
    },
  },
}

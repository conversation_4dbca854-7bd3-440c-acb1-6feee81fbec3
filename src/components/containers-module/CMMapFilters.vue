<template>
  <div class="wrapper">
    <ButtonWrapper
      v-show="collapsed"
      type="secondary"
      icon="mdi:filter"
      @click="collapsed = false"
    >
      Filtrer</ButtonWrapper
    >
    <div v-show="!collapsed" class="filters-area">
      <div class="header">
        <div class="title">Filtrer l'affichage</div>
        <div class="close-wrapper">
          <div class="close" @click="collapsed = true">
            <Icon
              icon="mdi:close"
              color="var(--color-main)"
              :style="{ fontSize: '28.5px' }"
            ></Icon>
          </div>
        </div>
      </div>
      <!--body-->
      <div class="body mb-4 mt-2">
        <!-- Toggles -->
        <div>
          <div class="title sub mb-2">Filtrer par statut des contenants</div>
          <!-- Green (1/4) -->
          <div class="toggle">
            <div class="toggle_circle green"></div>
            <div class="toggle_label">
              {{ $t('containers_module.stats.collected_bacs') }}
            </div>
            <div class="switch_wrapper">
              <b-form-checkbox v-model="visibility.green" switch size="lg">
              </b-form-checkbox>
            </div>
          </div>
          <!-- ORANGE (2/4) -->
          <div class="toggle">
            <div class="toggle_circle orange"></div>
            <div class="toggle_label">
              {{ $t('containers_module.stats.to_be_collected_count') }}
            </div>
            <div class="switch_wrapper">
              <b-form-checkbox v-model="visibility.orange" switch size="lg">
              </b-form-checkbox>
            </div>
          </div>
          <!-- RED (3/4) -->
          <div class="toggle">
            <div class="toggle_circle red"></div>
            <div class="toggle_label">
              {{ $t('containers_module.stats.not_collected_count') }}
            </div>
            <div class="switch_wrapper">
              <b-form-checkbox v-model="visibility.red" switch size="lg">
              </b-form-checkbox>
            </div>
          </div>
          <!-- GREY (4/4) -->
          <div class="toggle">
            <div class="toggle_circle"></div>
            <div class="toggle_label">
              {{
                $t('containers_module.stats.not_collected_not_planned_count')
              }}
            </div>
            <div class="switch_wrapper">
              <b-form-checkbox
                v-model="visibility.grey"
                style="cursor: pointer"
                switch
                size="lg"
              >
              </b-form-checkbox>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { Icon } from '@iconify/vue2'
import store from '@/store'
const emit = defineEmits(['draw'])
const collapsed = ref(true)
const visibility = ref({
  grey: true,
  green: true,
  red: true,
  orange: true,
})
watch(
  () => visibility.value,
  () => {
    Object.assign(store.state.containersModuleStore.markersVisibility, {
      collected: visibility.value.green,
      toBeCollected: visibility.value.orange,
      notCollected: visibility.value.red,
      notCollectedNotPlaned: visibility.value.grey,
    })
    emit('draw')
  },
  {
    deep: true,
  }
)

watch(
  () => store.state.containersModuleStore.markersVisibility,
  (v) => {
    if (JSON.stringify(visibility.value) !== JSON.stringify(v)) {
      Object.assign(visibility.value, {
        green: v.collected,
        orange: v.toBeCollected,
        red: v.notCollected,
        grey: v.notCollectedNotPlaned,
      })
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

/**
 * The vue-bootstrap switch doesnt have cursor:pointer by default
 */
function injectCursorPointerToSwitches() {
  document.querySelectorAll('.switch_wrapper').forEach((el) => {
    applyPointerCursor(el)
  })
  function applyPointerCursor(element) {
    try {
      element.style.cursor = 'pointer'
      var children = element.children
      for (var i = 0; i < children.length; i++) {
        applyPointerCursor(children[i])
      }
    } catch (err) {
      console.warn('Fail to inject cursor pointer', err)
    }
  }
}

onMounted(() => {
  injectCursorPointerToSwitches()
})
</script>
<style lang="scss" scoped>
.wrapper {
  position: absolute;
  z-index: 99999;
  top: 12px;
  left: 90px;
}
.filters-area {
  border-radius: 5px;
  background-color: white;
  padding: 15px 10px 15px 10px;
  min-width: 300px;
  min-height: 50px;
}

.close {
  cursor: pointer;
  padding: 10px 0px 10px 10px;
}
.close-wrapper {
  display: flex;
  justify-content: flex-end;
  width: 50px;
}
.header {
  display: flex;
  flex-direction: row;
}
.title {
  display: flex;
  align-items: center;
  width: calc(100% - 50px);
  font: normal normal bold 16px/16px Open Sans;
  &.sub {
    font: normal normal bold 14px/16px Open Sans;
  }
}
.toggle {
  display: flex;
  align-items: center;
  column-gap: 5px;
  .toggle_label {
    font: normal normal normal 12px/16px Open Sans;
    max-width: 250px;
    min-width: 250px;
  }
  & > div {
    height: 25px;
    display: flex;
    align-items: center;
  }
  .toggle_circle {
    border-radius: 50px;
    background-color: grey;
    width: 15px;
    height: 15px;
    &.green {
      background-color: #4d7c0f;
    }
    &.orange {
      background-color: #f97316;
    }
    &.red {
      background-color: #ef4444;
    }
  }
}

.switch_wrapper {
  position: relative;
  top: -2px;
}

//header
//   title   close-wrapper
</style>

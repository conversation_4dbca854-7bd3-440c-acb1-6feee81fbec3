<template>
  <div class="CMMap">
    <SimplicitiMap v-if="visibleRef" ref="map" @init="onInit" />
    <!--
      @todo Uncomment when working on the map floating filters
    <portal to="simplicitiMap" v-if="isResultsView">
      <CMMapFilters @draw="drawMarkers" />
    </portal>
    -->
  </div>
</template>
<script setup lang="ts">
/* eslint-disable */
import store from '@/store'
import { ref, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import L from 'leaflet'
import useContainersModuleDataPooling, {
  resetFiltersInClientSideCache,
} from '@/composables/containers-module'
import i18n from '@/i18n'
import useToastComposable from '@/composables/toast'
import loader from '@/plugins/loader.js'
import {
  useDrawBinReferenceData,
  useDrawCurrentStatesData,
  useManualDraw,
  useAutoDrawOnMarkersVisibilityChange,
} from '@/composables/containers-map'
import { throttleSimple } from '@/utils/async'
import { generateUUID } from '@/utils/crypto'
import useLeafletLassoSelection from '@/composables/leaflet-lasso-selection'
import { Icon } from '@iconify/vue2'

const visibleRef = ref(false)
const name = ref(generateUUID('map-'))
const props = defineProps({
  isResultsView: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['init'])
const { showToast } = useToastComposable({
  store,
  i18n,
})

const totalAvailableItemsCount = computed(() => {
  return (
    store.state.containersModuleStore.stateItemsTotalAvailCount +
    store.state.containersModuleStore.binItemsTotalAvailableCount
  )
})

const { filterDataWithFilters, loadingRef, filters, clearFilters } =
  useContainersModuleDataPooling()
const map = ref<any>(null)
const searchHereButtonRef = ref(null)
let mapCenter = {}
let mapZoom = {}
configuresMapCenter()
configuresMapZoom()
provide('mapCenter', mapCenter)
provide('uniqueLeafletMap', true)

useDrawBinReferenceData(map)
useDrawCurrentStatesData(map)
const { drawMarkers } = useManualDraw(map)
useAutoDrawOnMarkersVisibilityChange(map)

const { cancel: cancelLasso } = useLeafletLassoSelection(map)

onBeforeUnmount(() => {
  //Handle logout behavior : remove layers only if map exists
  if (!map.value.$refs.map) {
    return
  }

  map.value.$refs.map.removeLayerGroupBy(() => true) //Remove layers
})

onMounted(() => {
  //@ts-ignore
  console.debugVerboseScope(8, 'containers', 'CMMap mounted', name.value)
})

defineExpose({
  getMap() {
    return getMap()
  },
  getSimplicitiMap() {
    return map.value
  },
})

/**
 * Get Leaflet map instance using the SimplicitiMap SFC VM ref.
 */
function getMap() {
  return map.value.getMap ? map.value.getMap() : null
}

/***
 * Provides mapCenter property to LeafletMap
 * Persist map center if change (Using settings store)
 */
async function configuresMapCenter() {
  await store.dispatch('settings/syncFromCache', {
    once: true,
  })
  Object.defineProperty(mapCenter, 'value', {
    enumerable: true,
    get: () => {
      let storeVal = store.getters['settings/getParameter'](
        'containersModuleMapCenter'
      )
      return storeVal instanceof Array && storeVal.length === 2
        ? storeVal
        : null
    },
  })

  visibleRef.value = true
}

/**
 * Injects a custom zoom level into the LeafletMap component
 */
function configuresMapZoom() {
  //Pseudo computed from settings store state
  Object.defineProperty(mapZoom, 'value', {
    enumerable: true,
    get: () => {
      let storeVal = store.getters['settings/getParameter'](
        'containersModuleMapZoom'
      )
      //console.log('cmmap mapZoom get ', storeVal)
      return typeof storeVal === 'number' ? storeVal : null
    },
  })
  provide('mapZoom', mapZoom)
}

/**
 * Stores map center coords and zoom level in settings (persistance)
 */
function watchMapCenterAndZoom() {
  console.log('watchMapCenterAndZoom')

  const storeMapCenterThrottled = throttleSimple(() => {
    if (
      map.value?.getMap &&
      store.state.simpliciti_map.mapViewportBounds !== null
    ) {
      console.log('watch trigger', name.value)
      const center = getMap().getCenter()
      //console.log('Store map center', [center.lat, center.lng])
      store.dispatch('settings/setParameterAndSave', {
        name: 'containersModuleMapCenter',
        value: [center.lat, center.lng],
      })
    }
  }, 200)

  const storeMapZoomThrottled = throttleSimple(() => {
    let zoomLevel = store.state.simpliciti_map.zoomLevel
    if (zoomLevel !== null) {
      //console.log('Store map zoom', zoomLevel)
      store.dispatch('settings/setParameterAndSave', {
        name: 'containersModuleMapZoom',
        value: zoomLevel,
      })
    }
  }, 200)

  //Update center when users pan
  watch(
    () => store.state.simpliciti_map.mapViewportBounds,
    storeMapCenterThrottled
  )
  //Update zoom setting on change
  watch(() => store.state.simpliciti_map.zoomLevel, storeMapZoomThrottled)
}

/**
 * Once Leaflet map is initialized or component activated (keep alive)
 */
function onInit() {
  console.log('CMMap.onInit')

  //Ensure simpliciti_store contains the map bounds (synced)
  store.state.simpliciti_map.mapViewportBounds = getMap().getBounds()
  store.dispatch(
    'containersModuleStore/prepareToFallbackToMapBoundsAsCoordsFilterValue'
  )

  configureSearchHereButton()

  if (props.isResultsView) {
    executeSearch()
  }

  nextTick(() => {
    watchMapCenterAndZoom()
  })

  //Used by selection view to enable lasso
  emit('init')
}

/**
 * User can search in a map region (after pan/zoom)
 */
function configureSearchHereButton() {
  console.log('CMMap configureSearchHereButton')
  let btnEl = document.querySelector('.search_here_btn')
  if (!!btnEl) {
    if (props.isResultsView) {
      btnEl.style.display = 'block'
    } else {
      btnEl.style.display = 'none'
    }
    console.log('CMMap configureSearchHereButton exists')
    return //already exists
  } else {
    if (!props.isResultsView) {
      console.log('CMMap configureSearchHereButton skip')
      return //not needed (selection view)
    }
  }

  let mapI = getMap()
  const btn = L.control({ position: 'topleft' })
  btn.onAdd = function () {
    // Create container div
    let div = L.DomUtil.create('div', 'leaflet-control')
    div.style.position = 'relative'

    // Mount a Vue component
    const SearchHereButton = Vue.extend({
      template: `
        <button 
          class="btn btn-primary search_here_btn" 
          style="padding: 9px;"
          :title="title"
          @click="handleClick"
        >
          <GlobalIcon icon="mdi:map-search" width="18" height="18" />
        </button>
      `,
      computed: {
        title() {
          return i18n.t('containers_module.map_search_here_button')
        },
      },
      setup(props, { emit }) {
        const handleClick = () => {
          clearFilters(true)
          cancelLasso()
          void resetFiltersInClientSideCache(true)
          executeSearch(true, true)
        }
        return { handleClick }
      },
    })

    const instance = new SearchHereButton().$mount()
    div.appendChild(instance.$el)

    searchHereButtonRef.value = div
    return div
  }
  mapI.addControl(btn)
}

/**
 * Entry point for fetching data
 * @param reset
 * @param isSearchHereMode
 */
async function executeSearch(reset = true, isSearchHereMode = false) {
  store.dispatch(
    'containersModuleStore/prepareToFallbackToMapBoundsAsCoordsFilterValue'
  )

  try {
    loader.show()
    await filterDataWithFilters(reset, {
      isSearchHereMode,
    })
  } catch (err) {
    console.log({
      stack: err.stack,
      message: err.message,
      code: err.code,
    })
    if (err.message.includes('TOO_MANY_ELEMENTS')) {
      console.log('Will show toast')
      let toast = {
        type: 'info',
        title: i18n.t('containers_module.generic_toast_title', {
          number: totalAvailableItemsCount.value,
        }),
        text: 'containers_module.search_too_many_elements_at_zoom_level',
        timeout: 3000,
        i18nOptions: {
          mapNotification: true,
        },
      }
      showToast(toast)
    } else {
      throw err
    }
  } finally {
    loader.hide()
  }
}

watch(
  () => store.state.containersModuleStore.mode,
  async () => {
    await store.dispatch(
      'containersModuleStore/prepareToFallbackToMapBoundsAsCoordsFilterValue'
    )

    await filterDataWithFilters(true, {
      isSearchHereMode: false,
    })
  }
)

/**
 * Hide search here button if search in progress

watch(
  () => loadingRef.value,
  () => {
    if (searchHereButtonRef.value.innerHTML) {
      searchHereButtonRef.value.style.display = loadingRef.value
        ? 'none'
        : 'block'
    }
  }
) */

/**
 * Center over address on change (selection view)
 */
watch(
  () => filters.value?.address,
  () => {
    let address = filters.value?.address?.lat ? filters.value?.address : null
    if (address) {
      getMap().setView([address.lat, address.lng])
    }
  }
)

/**
 *  Center over zone on change (selection view)
 */
watch(
  () => filters.value?.zone,
  () => {
    let zone = filters.value?.zone.id ? filters.value?.zone : null
    if (zone) {
      if (zone.polygon) {
        getMap().fitBounds(zone.polygon)
      } else {
        getMap().setView([zone.lat, zone.lng])
      }
    }
  }
)
</script>
<style lang="scss">
.CMMap {
  height: calc(100vh);

  .cont {
    background-color: rgb(255 255 255 / 74%);
    border-radius: 50px;
    &.updated {
      box-shadow: 0 0 10px rgb(21 114 247 / 50%);
    }
    &.custom {
      padding-left: 4px;
      padding-top: 2px;
    }
  }

  .containers-cluster-marker {
    background-color: var(--color-dark-blue);
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    min-width: 20px;
    padding: 4px;
    font-weight: bold;
    font-size: 10px;
    box-sizing: content-box;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>

<template>
  <select v-model="selectedValueId" class="form-control" @change="onInput">
    <option :value="-1"></option>
    <option
      v-for="item in items"
      :key="item.id"
      :value="item.id"
      v-text="item.name"
    ></option>
  </select>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { fetchPredefinedViews } from '@/services/predefined-views.js'
import { useVModelEmit } from '@/composables/useVModelEmit'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  value: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits([
  'input',
  'predefinedViewsLoaded',
  'update:modelValue',
])

const items: any = ref<any[]>([])
const selectedValueId = ref(-1)
const ready = ref(false)
const valueUpdated = ref(false)
const { vModelEmit } = useVModelEmit(emit)

onMounted(async () => {
  if (props.value.id) {
    selectedValueId.value = props.value.id
  }
  items.value = await fetchPredefinedViews()
  ready.value = true
})

watch([() => props.value, () => ready.value], ([_, ready]) => {
  if (valueUpdated.value || !ready) {
    return
  }

  selectedValueId.value = props.value.id
})

const onInput = (event) => {
  const selected = items.value.find(
    (item) => item.id === parseInt(event.target.value)
  )
  console.log('OnInput', {
    event,
    selected,
  })

  vModelEmit(selected)
}

watch(
  () => items.value,
  (newValue, oldValue) => {
    if (oldValue.length === 0 && newValue.length > 0) {
      emit('predefinedViewsLoaded')
    }
  },
  { deep: true }
)

window.pv = {
  props,
  items,
  selectedValueId,
}
</script>

<template>
  <div>
    <span class="d-block mt-3">
      {{ $t('containers_module.date_selected') }} : {{ collectionDate }}
    </span>

    <CMStats class="mt-3 mb-3" />

    <CMListExport v-if="hasBinsToExport" />

    <p v-if="false && showTestPanel" style="padding: 5px; margin-top: 15px">
      Fetched/Available:<br />
      - States: {{ $store.state.containersModuleStore.items.length }} /
      {{ $store.state.containersModuleStore.stateItemsTotalAvailCount }}
      <br />
      - Bins: {{ $store.state.containersModuleStore.binItems.length }} /
      {{ $store.state.containersModuleStore.binItemsTotalAvailableCount }}
      <br />
      Items per page:
      <input v-model="$store.state.containersModuleStore.itemsPerPage" /><br />
      Max allowed:
      <input v-model="$store.state.containersModuleStore.maxAllowedItems" />
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import store from '@/store'
import envService from '@/services/env-service'
import { formatDate } from '@/utils/dates'

/**
 * Itended to tweak some variables during development phase.
 */
const showTestPanel = computed(
  () =>
    (envService.isStagging() || envService.isBeta()) &&
    (envService.isTestMode() || envService.isLocalhost())
)

const collectionDate = computed(() => {
  return formatDate(new Date(), {})
})

const hasBinsToExport = computed(() => {
  return (
    store.state.containersModuleStore.binItemsTotalAvailableCount > 0 ||
    store.state.containersModuleStore.stateItemsTotalAvailCount > 0
  )
})

onMounted(async () => {
  await store.dispatch('containersModuleStore/updateReferential')
  store.state.containersModuleStore.items = []
})
</script>

<style lang="scss" scoped>
.scroll-wrapper {
  max-height: calc(50vh);
  overflow: auto;
}
ul {
  list-style: none;
  padding: 0;
}
span {
  font: normal normal 600 14px/20px Open Sans;
  letter-spacing: 0px;
  line-height: 15px;
}
</style>

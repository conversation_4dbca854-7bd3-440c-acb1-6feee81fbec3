<template>
  <div>
    <div class="list mt-3">
      <CMStatItem
        id="lessThan24h"
        :text="$t('containers_module.last_collection.less_than_24h')"
        :value="metrics.less_than_24h"
        color="#66cc33"
      />
      <CMStatItem
        id="between24hAnd48h"
        :text="$t('containers_module.last_collection.between_24h_and_48h')"
        :value="metrics.between_24h_and_48h"
        color="#ff9900"
      />
      <CMStatItem
        id="between48hAnd72h"
        :text="$t('containers_module.last_collection.between_48h_and_72h')"
        :value="metrics.between_48h_and_72h"
        color="#ff3333"
      />
      <CMStatItem
        id="moreThan72h"
        :text="$t('containers_module.last_collection.more_than_72h')"
        :value="metrics.more_than_72h"
        color="#6b7280"
      />
      <CMStatItem
        id="neverCollected"
        :text="$t('containers_module.last_collection.never')"
        :value="metrics.never"
        color="#2a303d"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import store from '@/store'

const metrics = computed(() => {
  return store.getters['containersModuleStore/lastCollectionMetrics']
})
</script>

<style lang="scss" scoped>
.list {
  display: grid;
  grid-column-gap: 16px;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  width: 100%;
}
</style>

<template>
  <MapPopup title="Details" :sections="popupSections" :loading="loading">
    <template #code="">
      <span>{{
        details.tankNumber || $t('containers_module.marker_popup.empty_value')
      }}</span>
    </template>
    <template #puce="">
      <span>{{
        details.chipNumber || $t('containers_module.marker_popup.empty_value')
      }}</span>
    </template>
    <template #type="">
      <span>{{
        getLabelFromId('typeItems', details.typeId) ||
        $t('containers_module.marker_popup.empty_value')
      }}</span>
    </template>
    <template #volume="">
      <span>{{
        details.volume || $t('containers_module.marker_popup.empty_value')
      }}</span>
    </template>
    <template #state="">
      <span>{{
        $t('containers_module.marker_popup.state_value_' + details.state)
      }}</span>
    </template>
    <template #address="">
      <span>{{
        details.address || $t('containers_module.marker_popup.empty_value')
      }}</span>
    </template>
    <template #commissioningDate="">
      <span>
        {{ formatDate(details.original.commissioningDate) }}
      </span>
    </template>

    <template #lastcollections="">
      <div v-if="lastCollections.length < 1">
        <span v-if="lastCollectionLoading">
          {{ $t('common.loading_in_progress') }}
        </span>
        <span v-else>
          {{ $t('containers_module.marker_popup.no_result_provided') }}
        </span>
      </div>
      <div v-else>
        <div v-for="coll in lastCollections" :key="coll.id">
          <span
            v-b-tooltip.viewport="
              $t(
                'containers_module.marker_popup.last_infos.last_five_collections.tooltip',
                {
                  datetime:
                    !!coll &&
                    formatDatetimeWithSeconds(
                      adjustDateWithTimezone(coll.collectedAt)
                    ),
                  circuitName:
                    coll?.circuitName ||
                    $t('containers_module.marker_popup.empty_value'),
                  vehicleName: coll?.vehicleName,
                }
              )
            "
          >
            <span>{{
              formatDatetimeWithSeconds(
                adjustDateWithTimezone(coll.collectedAt)
              )
            }}</span>
            <span>&nbsp;-&nbsp;</span>
            <span v-show="coll.circuitName">{{ coll.circuitName }}</span>
            <span v-show="coll.circuitName">&nbsp;-&nbsp;</span>
            <span>{{ coll.vehicleName }}</span>
          </span>
          <div
            v-for="evt in coll.eventBinCollections"
            :key="evt.id"
            style="cursor: pointer"
            @click="() => (photos = evt.images)"
          >
            <span>{{ evt.name }}&nbsp;</span>
            <span
              class="iconify"
              data-icon="mdi:camera"
              style="font-size: 28px"
              data-width="14"
              data-height="14"
            ></span>
          </div>
        </div>
      </div>
    </template>

    <textarea
      v-if="false"
      style="
        min-height: 300px;
        max-height: 400px;
        overflow-y: auto;
        border: 0;
        width: 100%;
      "
      readonly
      v-html="
        JSON.stringify(
          {
            simple: geometry.options.data || {},
            details: details,
          },
          null,
          4
        )
      "
    >
    </textarea>

    <template #bottom>
      <div v-if="!loading" class="bottom-buttons">
        <ButtonWrapper
          v-if="lastCollections.length > 1"
          icon="mdi:download"
          type="secondary"
          custom-style="color:var(--color-main)"
          @click="exportLiftHistory"
        >
          {{ $t('containers_module.marker_popup.export_buttons.lift_history') }}
        </ButtonWrapper>
        <!--
        TODO: Anomaly data not available at present. Once the data is available, uncomment this button and map the data in the “exportAnomaliesHistory” method.
        <ButtonWrapper
          icon="mdi:download"
          type="secondary"
          custom-style="color:var(--color-main)"
          @click="exportAnomaliesHistory"
        >
          {{
            $t('containers_module.marker_popup.export_buttons.anomalie_history')
          }}
        </ButtonWrapper>
        -->
      </div>
    </template>

    <Teleport to="body">
      <PicturesGallery
        v-show="photos.length > 0"
        :pictures="photos"
        @close="photos = []"
      ></PicturesGallery>
    </Teleport>
  </MapPopup>
</template>

<script>
import { getEnvValue } from '@/services/env-service.js'
import * as R from 'ramda'
import moment from 'moment'
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'
import { getEnvIntValue } from '@/services/env-service'
import {
  adjustDateWithTimezone,
  formatDatetimeWithSeconds,
  formatDate,
  getRandomPastDateTime,
} from '@/utils/dates.js'
import { APIV3RequestDatetimeFormat } from '@/config/simpliciti-apis.js'
import PicturesGallery from '@/components/shared/PicturesGallery/PicturesGallery.vue'
import Teleport from 'vue2-teleport'
import { exportToCSV } from '@/services/export-service.js'
import { getLabelFromId } from '@/composables/containers-module'
import popupSectionsVisibility from '@/mixins/popup-sections-visibility'

const nbCollect = getEnvIntValue('containersModulePopupLastColletionsNb', '5')
const nbCollectExport = getEnvIntValue(
  'containersModulePopupLastColletionsNbExport',
  '60'
)

/**
 * Popup feature
 * @namespace components
 * @category components
 * @subcategory location/identification
 * @module LocationIdentBacsPopup
 **/
export default {
  name: 'CMMapPopup',
  components: {
    MapPopup,
  },
  mixins: [popupSectionsVisibility],
  props: {
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: true,
      APIV3RequestDatetimeFormat,
      details: {
        tankNumber: '',
      },
      lastCollections: [],
      lastCollectionLoading: true,
      /**
       * Contains URLs of anomalies images if gallery is opened
       */
      photos: [],
      popupSections: [
        {
          title: this.$t('containers_module.marker_popup.infos.title'),
          rows: [
            {
              singleColumn: {
                name: 'code',
                label: this.$t(
                  'containers_module.marker_popup.infos.tankNumber'
                ),
              },
            },
            {
              singleColumn: {
                name: 'puce',
                label: this.$t(
                  'containers_module.marker_popup.infos.chipNumber'
                ),
              },
            },
            {
              twoColumns: [
                {
                  name: 'type',
                  label: this.$t('containers_module.marker_popup.infos.type'),
                  visible: false,
                  enabled: false,
                  hideIfNull: true,
                },
                {
                  name: 'volume',
                  label: this.$t('containers_module.marker_popup.infos.volume'),
                },
              ],
            },
            {
              twoColumns: [
                {
                  name: 'state',
                  label: this.$t('containers_module.marker_popup.infos.state'),
                  // visible: this.details?.typeId,
                  visible: false,
                  hideIfNull: true,
                },
                {
                  name: 'commissioningDate',
                  label: this.$t(
                    'containers_module.marker_popup.infos.commissioning_date'
                  ),
                  // visible: false,
                  visible: false,
                  hideIfNull: true,
                },
              ],
            },
          ],
          collapsed: true,
        },
        {
          title: this.$t('containers_module.marker_popup.last_infos.title'),
          rows: [
            {
              singleColumn: {
                name: 'address',
                label: this.$t(
                  'containers_module.marker_popup.last_infos.address'
                ),
              },
            },
            {
              singleColumn: {
                name: 'lastcollections',
                label: this.$t(
                  'containers_module.marker_popup.last_infos.last_five_collections'
                ),
              },
            },
          ],
          collapsed: true,
        },
      ],
    }
  },
  async mounted() {
    const id = this.geometry?.options?.data?.id
    const isBin = this.geometry?.options?.type === 'bin'

    console.log({
      id,
      isBin,
      geo: this.geometry,
    })

    if (id) {
      if (isBin) {
        this.details = await this.$api.bins.get(id)
      } else {
        let layerData = this.geometry?.options?.data || {}
        let stateData = await this.$api.binCollectionCurrentStates.get(id)

        let binData = {}

        let binId = stateData.binId

        //Try to match using binId
        if (binId) {
          console.info('Retrieving binData with binId')
          binData = await this.$api.bins.get(binId)
        } else {
          //Fallback to chipNumber
          let chipNumber = stateData.chipNumber
          if (chipNumber) {
            console.info('Retrieving binData with chipNumber')
            let binData = await this.$api.bins.getAll(
              {
                chipNumber,
              },
              {
                single: true,
              }
            )

            //Fallback to: Try to retrieve bin ref using chipNumber from state as bin->uhfNumber (This works for the Djibril consolidation script + first Rabat import)
            if (binData === null) {
              console.info('Retrieving binData with chipNumber/uhfNumber')
              binData = await this.$api.bins.getAll(
                {
                  uhfNumber: chipNumber,
                },
                {
                  single: true,
                }
              )
            }
          }
        }

        console.info({
          binData,
          stateData,
        })

        this.details = {
          ...stateData,
          ...binData,
          chipNumber: stateData.chipNumber || binData.chipNumber,
        }
      }
      // Show type column if property exists
      if (this.details?.typeId) {
        this.setPopupSectionVisibilityIfPropertyExists(
          'type',
          this.$data.popupSections
        )
      }
      // Show state column if property exists
      if (this.details?.state !== null) {
        this.setPopupSectionVisibilityIfPropertyExists(
          'state',
          this.$data.popupSections
        )
      }
      // Show commissioningDate column if property exists AND if state === 1 (en service)
      if (
        this.details?.original?.commissioningDate &&
        this.details?.state === 1 &&
        this.details?.original?.active === true
      ) {
        this.setPopupSectionVisibilityIfPropertyExists(
          'commissioningDate',
          this.$data.popupSections
        )
      }
    } else {
      console.warn('No id found', {
        geometry,
      })
    }

    this.loading = false

    //Last lifts
    this.lastCollections =
      await this.$api.binCollectionLastLiftCollections.getAll(
        {
          chipNumber: this.details.chipNumber,
          nbCollect,
          groups: ['lift', 'event'],
        },
        {
          mockAPIHandler() {
            return [
              {
                date: 'ASD',
                vehicles: [
                  {
                    vehicle: {
                      name: 'BOM 01',
                    },
                    binCollections: [
                      {
                        id: '123456789',
                        round: {
                          id: 1,
                          shortName: 'PAP3012',
                        },
                        collectedAt: getRandomPastDateTime(),
                        eventBinCollections: [
                          {
                            id: 1,
                            name: 'Damaged container',
                            images: [
                              'https://placehold.co/600x400',
                              'https://placehold.co/1920x1080',
                            ],
                          },
                        ],
                      },
                    ],
                  },
                  {
                    vehicle: {
                      name: 'BOM 02',
                    },
                    binCollections: [
                      {
                        id: '123456589',
                        round: {
                          id: 1,
                          shortName: 'PAP3012',
                        },
                        collectedAt: getRandomPastDateTime(),
                      },
                    ],
                  },
                ],
              },
            ]
          },
          normalizeArray: true,
        }
      )

    this.lastCollectionLoading = false
  },
  methods: {
    getLabelFromId,
    async exportAnomaliesHistory() {
      const data = await this.$api.eventAggregateEventBinCollections.getAll({
        chipNumber: this.details.chipNumber,
      })

      console.log('exportAnomaliesHistory', { data })
    },
    async exportLiftHistory() {
      const data = await this.$api.binCollectionLastLiftCollections.getAll(
        {
          chipNumber: this.details.chipNumber,
          nbCollect: nbCollectExport,
          groups: ['lift', 'export'],
        },
        {
          normalizeArray: true,
        }
      )

      const fileNameSuffix = moment().format('DD[_]MM[_]YYYY[_]HH[_]mm')

      const mappedData = data.map((item) => ({
        tankNumber: this.details.tankNumber,
        shipNumber: this.details.chipNumber,
        volume: this.details.volume,
        type: getLabelFromId('typeItems', this.details.typeId),
        state: getLabelFromId('stateItems', this.details.state),
        comissionningDate: this.details.original?.comissionningDate
          ? formatDate(this.details.original?.comissionningDate, {})
          : '',
        collectAddress: this.details.address,
        collectedAt: formatDatetimeWithSeconds(
          adjustDateWithTimezone(item.collectedAt)
        ),
        vehicleName: item.vehicleName,
        circuitName: item.circuitName,
      }))

      exportToCSV(mappedData, 'lift-bin-collection-history-' + fileNameSuffix, {
        columns: [
          this.$t(
            'containers.module.last_collection.export.file.column_title.tank_number'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.chip_number'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.bin_volume'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.bin_type'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.state'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.commissioning_date'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.collect_address'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.collected_at'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.vehicle_name'
          ),
          this.$t(
            'containers.module.last_collection.export.file.column_title.round_name'
          ),
        ],
      })

      console.log('exportLiftHistory', { mappedData })
    },
    formatDatetimeWithSeconds: (d) => formatDatetimeWithSeconds(d),
    adjustDateWithTimezone: (d) => adjustDateWithTimezone(d),
    formatDate: (d) => formatDate(d),
  },
}
</script>

<style lang="scss" scoped>
:deep {
  .bottom-buttons {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
    gap: 15px;
    padding: 0 15px;

    .button-wrapper {
      width: 100%;

      button {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        padding-left: 6px;

        svg {
          width: 23px;
          height: 23px;
        }
      }

      &:last-child {
        margin-bottom: 15px;
      }
    }
  }
}
</style>

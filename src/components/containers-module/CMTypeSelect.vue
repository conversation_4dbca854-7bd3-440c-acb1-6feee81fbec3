<template>
  <!--
  <select class="form-control" :value="value.id" @change="onInput">
    <option :value="-1"></option>
    <option
      v-for="item in items"
      :key="item.id"
      :value="item.id"
      v-text="item.label"
    ></option>
  </select>-->
  <BetterSelect v-model="modelValue" :options="items">
    <template #option="slotProps">
      <div class="option">
        <span v-html="slotProps.option.pictureSVG"></span>
        {{ slotProps.option.label }}
      </div>
    </template>
    <template #singleLabel="slotProps">
      <span v-html="slotProps.option.pictureSVG"></span>
      &nbsp;&nbsp;
      {{ slotProps.option.label }}
    </template>
  </BetterSelect>
</template>
<script setup lang="ts">
import { useVModelProp } from '@/composables/useVModelProp'
import { useVModelEmit } from '@/composables/useVModelEmit'

import BetterSelect from '@c/shared/BetterSelect.vue'

const props = defineProps({
  [useVModelProp()]: {
    type: Object,
    default: () => ({}),
  },
  items: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

const modelValue = computed({
  get() {
    return props[useVModelProp()]
  },
  set(newValue) {
    vModelEmit(newValue)
  },
})
</script>

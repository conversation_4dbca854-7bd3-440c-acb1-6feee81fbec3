<template>
  <div
    class="CMStatItem"
    :class="{ disabled: !checked, toggleable: !!id }"
    :style="style"
    @click="onClick"
  >
    <div class="text">
      {{ props.text }}
    </div>
    <div class="value">
      {{ props.value }}
    </div>
  </div>
</template>
<script setup lang="ts">
/**
 * @description Containers module - Metrics - Metric item
 * Optional feature: Toggleable - It toggles some store visibility variable (Enable by using a valid id prop)
 * @namespace components
 * @category components
 * @module containersModule
 */

import { computed } from 'vue'
import store from '@/store'

const props = defineProps({
  /**
   * See containers store "markersVisibility" properties
   */
  id: {
    type: String,
    default: '',
  },
  value: {
    type: Number,
    default: 0,
  },
  text: {
    type: String,
    default: 'Title',
  },
  color: {
    type: String,
    default: 'black',
  },
  bgColor: {
    type: String,
    default: '',
  },
})
const color = computed(() => props.bgColor || props.color)
const borderCssRule = computed(() => `2px solid ${props.color}`)

const checked = ref(true)
const style = computed(() => {
  const color = props.color

  if (props.bgColor) {
    return props.color
  }

  return `color:${checked.value ? 'white' : color}`
})

function onClick() {
  if (props.id) {
    checked.value = !checked.value
  }
}

watch(
  () => checked.value,
  () => {
    if (props.id) {
      store.state.containersModuleStore.markersVisibility[props.id] =
        checked.value
    }
  }
)

watch(
  () => store.state.containersModuleStore.markersVisibility,
  (v) => {
    if (props.id && checked.value !== v[props.id]) {
      checked.value = v[props.id]
    }
  },
  {
    deep: true,
  }
)

onMounted(() => {
  if (props.id) {
    checked.value =
      store.state.containersModuleStore.markersVisibility[props.id]
  }
})
</script>

<style lang="scss" scoped>
.CMStatItem {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 14px 16px 16px;
  box-shadow: 0px 2px 3px #0000005c;
  border: v-bind(borderCssRule);
  width: 100%;
  border-radius: 6px;
  gap: 8px;
  background-color: v-bind(color);
  min-height: 98px;

  &.disabled {
    background-color: transparent;
  }

  &.toggleable {
    cursor: pointer;
  }
}

.text {
  font: normal normal bold 14px/17px Open Sans;
}

.value {
  font: normal normal bold 32px/22px Open Sans;
}
</style>

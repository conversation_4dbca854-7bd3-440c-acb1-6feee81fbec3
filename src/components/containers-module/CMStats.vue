<template>
  <div class="list">
    <CMStatItem
      id="collected"
      :text="$t('containers_module.stats.collected_bacs')"
      :value="
        $store.state.containersModuleStore.metricsResult?.collectedNumber || 0
      "
      color="var(--color-container-state-collected)"
    />
    <CMStatItem
      id="toBeCollected"
      :text="$t('containers_module.stats.to_be_collected_count')"
      :value="
        $store.state.containersModuleStore.metricsResult?.toBeCollectedNumber ||
        0
      "
      color="var(--color-container-state-planned)"
    />
    <CMStatItem
      id="notCollected"
      :text="$t('containers_module.stats.not_collected_count')"
      :value="
        $store.state.containersModuleStore.metricsResult?.notCollectedNumber ||
        0
      "
      color="var(--color-container-state-no-collected)"
    />

    <CMStatItem
      id="notCollectedNotPlaned"
      :text="$t('containers_module.stats.not_collected_not_planned_count')"
      :value="
        $store.state.containersModuleStore.metricsResult
          ?.notCollectedNotPlannedNumber || 0
      "
      color="#6b7280"
    />

    <CMStatItem
      v-if="false"
      :text="$t('containers_module.stats.anomalies_count_in_zone')"
      :value="
        $store.state.containersModuleStore.metricsResult?.anomaliesNumber || 0
      "
      color="var(--color-container-state-no-planned)"
    />

    <CMStatItem
      :text="$t('containers_module.stats.total_containers_zone_count')"
      :value="
        $store.state.containersModuleStore.metricsResult?.totalNumber ||
        $store.state.containersModuleStore.binItemsTotalAvailableCount ||
        0
      "
      color="#1e3a8a"
    />

    <CMStatItem
      :text="$t('containers_module.stats.total_containers_count')"
      :value="
        $store.getters[
          'containersModuleStore/binItemsTotalAvailableClientCount'
        ] || 0
      "
      color="#014470"
      bg-color="##DADADA5B"
    />

    <!--
    <CMStatItem
      :text="$t('containers_module.stats.collected_columns')"
      :value="20"
      color="green"
    />

    <CMStatItem
      :text="$t('containers_module.stats.today_pending_collected_columns')"
      :value="2"
      color="grey"
    />
    <CMStatItem
      :text="$t('containers_module.stats.delayed_columns')"
      :value="8"
      color="red"
    />
    -->
  </div>
</template>
<style lang="css" scoped>
.list {
  display: grid;
  grid-column-gap: 16px;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  color: var(--color-tundora);
  width: 100%;
}
</style>

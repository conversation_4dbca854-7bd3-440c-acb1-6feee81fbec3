<template>
  <select class="form-control" :value="value.id" @change="onInput">
    <option :value="-1"></option>
    <option
      v-for="item in items"
      :key="item.id"
      :value="item.id"
      v-text="item.label"
    ></option>
  </select>
</template>
<script setup lang="ts">
const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
  items: {
    type: Array,
    default: () => [],
  },
})
const emit = defineEmits(['input'])

const onInput = (event) => {
  const selected = props.items.find(
    (item) => item.id === parseInt(event.target.value)
  )
  console.log('OnInput', {
    event,
    selected,
  })
  emit('input', { ...selected })
}
</script>

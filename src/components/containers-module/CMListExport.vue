<template>
  <div class="cm-list-export-button">
    <ButtonWrapper
      v-b-tooltip.hover.viewport="
        $t('containers_module.last_collection.export')
      "
      type="secondary"
      :disabled="loading === true"
      @click="exportContainers"
    >
      <b-spinner
        v-if="loading === true"
        class="loader"
        variant="info"
        style="width: 16px; height: 16px"
      />
      <VueIcon
        v-else
        icon="mdi:earth"
        color="var(--color-red)"
        :icon-style="{ fontSize: '16px' }"
      />
      <span class="ml-1">
        {{ $t('containers_module.last_collection.export') }}
      </span>
    </ButtonWrapper>
  </div>
</template>

<script setup lang="ts">
import {
  getAllBinsForExport,
  getLabelFromId,
} from '@/composables/containers-module'
import i18n from '@/i18n'
import store from '@/store'
import api from '@/api'
import {
  adjustDateWithTimezone,
  formatDatetimeWithSeconds,
  formatDate,
} from '@/utils/dates'
import VueIcon from '@/components/shared/VueIcon.vue'
import ButtonWrapper from '@/components/shared/ButtonWrapper.vue'
import { ref } from 'vue'
import moment from 'moment/moment'
import { exportToCSV } from '@/services/export-service.js'
import * as fs from 'node:fs'
import useToastComposable from '@/composables/toast'

const chipNumbers = ref([])

const loading = ref(false)

const { showToast } = useToastComposable({
  store,
  i18n,
})

async function exportContainers() {
  try {
    loading.value = true

    await getAllBinsForExport()

    updateBinsWithCurrentStatesFields()

    exportCsv()

    loading.value = false
  } catch (err) {
    console.error(err)
    showToast({
      title: 'module_names.containers_module',
      type: 'warning',
      text: 'containers_module.last_collection.export.fail_message',
      timeout: 2000,
    })
    loading.value = false
  }
}

function updateBinsWithCurrentStatesFields() {
  //Map bins with current states by chipNumber
  store.state.containersModuleStore.allBinItems =
    store.state.containersModuleStore.allBinItems.map((bin) => {
      const binCurrentState = findBinCurrentStateByChipNumber(bin.chipNumber)

      if (binCurrentState) {
        chipNumbers.value.push(bin.chipNumber)
      }

      return {
        ...bin,
        //Get state from currentState, otherwise fallback to default not collected nor planned value
        currentState: binCurrentState
          ? i18n.t(
              'containers_module.last_collection.export.current_state_value_' +
                binCurrentState.stateId
            )
          : i18n.t(
              'containers_module.last_collection.export.current_state_value_' +
                '2'
            ),
        //Get address by currentState, otherwise fallback to standard empty value
        address: binCurrentState
          ? binCurrentState.address
          : i18n.t('containers_module.marker_popup.empty_value'),
        //Get last collect value from currentState, otherwise empty
        collectedAt:
          binCurrentState && binCurrentState.collectedAt
            ? binCurrentState.collectedAt
            : '',
        //Get vehicleName value from currentState, otherwise empty
        vehicleName:
          binCurrentState && binCurrentState.vehicleName
            ? binCurrentState.vehicleName
            : '',
        //Get circuitName value from currentState, otherwise empty
        circuitName:
          binCurrentState && binCurrentState.referenceRoundName
            ? binCurrentState.referenceRoundName
            : '',
      }
    })
}

function findBinCurrentStateByChipNumber(chipNumber) {
  return store.state.containersModuleStore.items.find(
    (cs) => cs.chipNumber?.toUpperCase() === chipNumber?.toUpperCase()
  )
}

function exportCsv() {
  const filename = getCsvFilename()

  const mappedData = store.state.containersModuleStore.allBinItems.map(
    (bin) => ({
      tankNumber: bin.tankNumber ?? '',
      chipNumber: bin.chipNumber ?? '',
      type: getLabelFromId('typeItems', bin.typeId),
      volume: bin.volume ?? '',
      flux: bin.original.fluxMaterialLabel ?? '',
      state: getLabelFromId('stateItems', bin.state) ?? '',
      commissioningDate: bin.original?.commissioningDate
        ? formatDate(bin.original.commissioningDate, {})
        : '',
      collectAddress: bin.address ?? '',
      collectedAt: bin.collectedAt
        ? formatDatetimeWithSeconds(adjustDateWithTimezone(bin.collectedAt))
        : '',
      vehicleName: bin.vehicleName ?? '',
      circuitName: bin.circuitName ?? '',
      currentState: bin.currentState ?? '',
    })
  )

  exportToCSV(mappedData, filename, {
    columns: getCsvColumns(),
    addExtraLinesAfterRows: true,
    extraLines: getBinsAvailableItemsColumns(),
  })
}

function getCsvFilename() {
  let prefix = i18n.t('containers_module.last_collection.export_file_prefix')
  //Get client name from loginAs, fallback to auth/clientName
  let clientName =
    store.getters['auth/loginAs'].client || store.getters['auth/clientName']
  let formattedDate = moment().format('DD[_]MM[_]YYYY[_]HH[_]mm[_]ss')

  return prefix + '_' + clientName + '_' + formattedDate
}

function getCsvColumns() {
  return [
    i18n.t(
      'containers.module.last_collection.export.file.column_title.tank_number'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.chip_number'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.bin_type'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.bin_volume'
    ),
    i18n.t('containers.module.last_collection.export.file.column_title.flux'),
    i18n.t('containers.module.last_collection.export.file.column_title.state'),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.commissioning_date'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.collect_address'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.datetime_last_collect'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.vehicle_last_collect'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.round_last_collect'
    ),
    i18n.t(
      'containers.module.last_collection.export.file.column_title.collect_status'
    ),
  ]
}

function getBinsAvailableItemsColumns() {
  return [
    {
      label: i18n.t(
        'containers.module.last_collection.export.file.bin_zone_available_items'
      ),
      value:
        store.state.containersModuleStore.metricsResult?.totalNumber ||
        store.state.containersModuleStore.binItemsTotalAvailableCount,
    },
    {
      label: i18n.t(
        'containers.module.last_collection.export.file.bin_total_available_items'
      ),
      value:
        store.state.containersModuleStore.binItemsTotalAvailableClientCount,
    },
  ]
}
</script>

<style scoped lang="scss">
.cm-list-export-button {
  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  .icon-wrapper {
    cursor: pointer;
  }
}
</style>

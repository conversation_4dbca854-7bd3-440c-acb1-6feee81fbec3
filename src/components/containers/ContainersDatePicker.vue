<template>
  <date-picker
    v-model="selectedValue"
    :lang="lang"
    :type="selectedType"
    :format="formatDate"
    :show-week-number="true"
    :show-second="false"
    :placeholder="$t('messages_module.search_messages.datepicker.placeholder')"
    @clear="$emit('onDateClear')"
    @focus="open('')"
  >
    <template #footer>
      <div class="container">
        <div v-if="false" class="row" style="text-align: left">
          <div class="col">
            <b-form-group
              v-slot="{ ariaDescribedby }"
              :label="$t('searchModule.date_picker.selection_label')"
              label-class="date_picker_label"
            >
              <b-form-radio-group
                v-model="selectedType"
                class="date-picker-form-wrapper"
                :options="selectionOptions"
                :aria-describedby="ariaDescribedby"
                name="type-options"
              >
              </b-form-radio-group>
            </b-form-group>
          </div>
          <div class="col">
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    </template>
  </date-picker>
</template>
<script setup>
import DatePicker from 'vue2-datepicker'
import 'vue2-datepicker/locale/fr'
import 'vue2-datepicker/index.css'
import { getDatetimePattern, getDatePattern } from '@/utils/dates'
import { useVModelEmit } from '@/composables/useVModelEmit'
import { useVModelProp } from '@/composables/useVModelProp'
import i18n from '@/i18n'
import { useDatePickerLang } from '@/composables/useI18nDatepicker'

const { lang } = useDatePickerLang()

//Inject
const isSearchModuleDatePickerDisabledHandler = inject(
  'isSearchModuleDatePickerDisabledHandler',
  () => {}
)

//Props
const props = defineProps({
  [useVModelProp()]: {
    type: Date,
    default: () => new Date(),
  },
  type: {
    type: String,
    default: 'datetime',
  },
})

//Emits
const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

//Computed
const vModelProp = computed(() => useVModelProp())
const formatDate = computed(() => {
  switch (props.type) {
    case 'datetime':
      return getDatetimePattern({ seconds: false })
      break
    case 'date':
      return getDatePattern()
    default:
      return getDatetimePattern({ seconds: false })
  }
})

//Consts
const visible = ref(false)
const selectedValue = ref(props[vModelProp.value])
const selectedType = ref(props.type)
const selectionOptions = [
  {
    text: i18n.t('search_module.date_picker.mode_each_day'),
    value: 'each_day',
  },
  {
    text: i18n.t('search_module.date_picker.mode_range'),
    value: 'range',
  },
]

//Watchers
watch(selectedValue, () => {
  vModelEmit(selectedValue.value)
})
watch(selectedType, () => {
  visible.value = false
  nextTick(() => {
    visible.value = true
  })
})

//Lifecycle hooks
onMounted(() => {
  watch(
    () => props[vModelProp.value],
    (newValue) => {
      if (JSON.stringify(newValue) !== JSON.stringify(selectedValue.value)) {
        selectedValue.value = newValue
      }
    },
    {
      deep: true,
      immediate: true,
    }
  )
})

//Methods
const open = () => {
  visible.value = true
}
</script>
<style lang="scss">
.mx-datepicker {
  width: 100%;
}
</style>

<template>
  <div class="container mt-2 archive">
    <div class="row">
      <div class="col">
        <div v-show="isProcessing" class="col-12">
          <LoaderSpinner />
        </div>
        <div class="blueTitle">
          {{ $t('container.form.archive.title') }} {{ currentContainer.name }}
        </div>
        <form autocomplete="false" @submit="(e) => e.preventDefault()">
          <b-form-group class="fieldOuter">
            <label>{{ $t('container.form.archive.reason') }} *</label>
            <b-form-input
              v-model="deletedReason"
              class="form-control"
              :placeholder="
                $t('container.form.archive.placeholder', { max: maxCharacters })
              "
              :state="validateState('deletedReason')"
            />
            <b-form-invalid-feedback
              v-for="error of v$.$errors"
              id="input-1-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <ButtonWrapper
            class="float-right pl-2"
            :disabled="isProcessing"
            @click="archiveContainer"
          >
            {{ $t('container.buttons.valid') }}
          </ButtonWrapper>
          <ButtonWrapper
            class="float-right pl-2"
            type="secondary"
            :disabled="isProcessing"
            @click="cancelArchive()"
            >{{ $t('container.buttons.cancel') }}</ButtonWrapper
          >
        </form>
      </div>
    </div>
  </div>
</template>
<script setup>
import store from '@/store'
import { useVuelidate } from '@vuelidate/core'
import { required, maxLength, helpers } from '@vuelidate/validators'
import i18n from '@/i18n'

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
})
const maxCharacters = 35
const isProcessing = ref(false)
const currentContainer = ref({})
const deletedReason = ref('')
const emit = defineEmits(['submitted', 'cancel'])
const rules = computed(() => ({
  deletedReason: {
    required: helpers.withMessage(
      i18n.t('container.form.archive.error.required'),
      required
    ),
    maxLength: helpers.withMessage(
      i18n.t('container.form.archive.error.toolong', { max: maxCharacters }),
      maxLength(maxCharacters)
    ),
  },
}))
const v$ = useVuelidate(rules, { deletedReason })

watch(
  () => props.value,
  () => {
    currentContainer.value = props.value
  },
  {
    immediate: true,
  }
)

function validateState(item) {
  if (this.v$[item]) {
    const { $dirty, $error } = this.v$[item]
    return $dirty ? !$error : null
  }
  return
}

async function archiveContainer() {
  const isFormCorrect = await v$.value.$validate()
  if (isFormCorrect) {
    isProcessing.value = true
    let res = true
    let params = []
    params['id'] = props.value.id
    if (deletedReason.value) {
      currentContainer.value.deletedReason = deletedReason.value
      params['deletedReason'] = deletedReason.value
      res = await store.dispatch('containers/updateContainer', params)
      if (!res) {
        return false
      }
    }
    if (res) {
      res = await store.dispatch('containers/archiveContainer', params)
    }
    isProcessing.value = false
    emit('submitted')
    return res
  }
  return
}

function cancelArchive() {
  this.v$.$reset()
  emit('cancel')
}
</script>
<style scoped>
.blueTitle {
  color: var(--color-denim);
  font-weight: bold;
  margin-bottom: 15px;
}
.container.archive {
  margin-bottom: 25px;
}
</style>

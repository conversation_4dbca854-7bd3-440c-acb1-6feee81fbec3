<template lang="pug">
.collapsible-section-wrapper
    .collapsible-section(@click="input")
        .collapsable_title {{title}}
        em.fas.collapsable_toggle(
            :class="{'fa-chevron-up':showContent, 'fa-chevron-down':!showContent}"
            )
    .collapsible-section-content(v-show="showContent")
        slot
</template>
<script setup>
import { useVModelEmit } from '@/composables/useVModelEmit'
import { useVModelProp } from '@/composables/useVModelProp'

//Props
const props = defineProps({
  title: {
    type: String,
    default: 'Title',
  },
  [useVModelProp()]: {
    type: Boolean,
    default: false,
  },
})

//Emits
const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

//Computed
const vModelProp = computed(() => useVModelProp())

//Consts
const showContent = ref(props[vModelProp.value])

//Lifecycle hooks
onMounted(() => {
  watch(
    () => props[vModelProp.value],
    (newValue) => {
      if (newValue !== showContent.value) {
        showContent.value = newValue
      }
    }
  )
})

//Methods
const input = () => {
  showContent.value = !showContent.value

  vModelEmit(showContent.value)
}
</script>
<style lang="scss" scoped>
.collapsable_title {
  font: normal normal bold 14px/18px Open Sans;
  letter-spacing: 0px;
  color: white;
}
.collapsable_toggle {
  color: white;
}
.collapsible-section {
  height: 33px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 5px;
  cursor: pointer;
}
.collapsible-section-wrapper {
  background-color: var(--color-main);
}
.collapsible-section-content {
  background-color: white;
  padding: 10px;
}
</style>

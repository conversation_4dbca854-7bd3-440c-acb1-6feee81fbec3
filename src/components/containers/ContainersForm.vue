<template>
  <div class="container mt-2" style="margin-top: 1.6rem !important">
    <div class="row">
      <div class="col">
        <div v-show="isProcessing" class="col-12">
          <LoaderSpinner />
        </div>
        <form
          id="formContainer"
          autocomplete="false"
          @submit="(e) => e.preventDefault()"
        >
          <b-form-group class="fieldOuter">
            <label>{{ $t('container.form.name') }} *</label>
            <b-form-input
              v-model="name"
              type="text"
              class="form-control"
              :state="validateState('name')"
            />
            <b-form-invalid-feedback
              v-for="error of filterErrors('name')"
              id="input-name-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <b-form-group class="fieldOuter">
            <label>{{ $t('container.form.volume') }}</label>
            <b-form-input
              v-model="volume"
              type="number"
              class="form-control"
              :state="validateState('volume')"
            />
            <b-form-invalid-feedback
              v-for="error of filterErrors('volume')"
              id="input-code-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <div class="form-group">
            <Checkbox v-model="biComp">
              {{ $t('container.form.bicomp') }}
            </Checkbox>
          </div>
          <b-form-group class="fieldOuter">
            <label v-if="!biComp"> {{ $t('container.form.flow') }} * </label>
            <label v-else>{{ $t('container.form.flow1') }} *</label>
            <b-form-select
              key="321313"
              v-model="flux1"
              :loading="isFlowListLoading"
              :options="optionsFlows"
              track-by="value"
              :state="validateState('flux1')"
              value-field="value"
              text-field="name"
            >
              <template #first>
                <b-form-select-option :value="null" disabled>{{
                  $t('container.undefined')
                }}</b-form-select-option>
              </template>
            </b-form-select>
            <b-form-invalid-feedback
              v-for="error of filterErrors('flux1')"
              id="input-flux1-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <b-form-group v-if="biComp" key="951" class="fieldOuter">
            <label>{{ $t('container.form.flow2') }} *</label>
            <b-form-select
              key="11321"
              v-model="flux2"
              :loading="isFlowListLoading"
              :options="optionsFlows"
              track-by="value"
              :state="validateState('flux2')"
              value-field="value"
              text-field="name"
            >
              <template #first>
                <b-form-select-option :value="null" disabled>{{
                  $t('container.undefined')
                }}</b-form-select-option>
              </template>
            </b-form-select>
            <b-form-invalid-feedback
              v-for="error of filterErrors('flux2')"
              id="input-flux2-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <b-form-group class="fieldOuter">
            <label>{{ $t('container.form.code') }} *</label>
            <b-form-input
              v-model="code"
              type="text"
              class="form-control"
              :state="validateState('code')"
            />
            <b-form-invalid-feedback
              v-for="error of filterErrors('code')"
              id="input-code-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <b-form-group class="fieldOuter">
            <label>{{ $t('container.form.ext_ref') }}</label>
            <b-form-input
              v-model="reference"
              type="text"
              class="form-control"
              :state="validateState('reference')"
            />
            <b-form-invalid-feedback
              v-for="error of filterErrors('reference')"
              id="input-reference-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <b-form-group class="fieldOuter">
            <label
              >{{ $t('container.form.use') }}
              <span v-if="canEditStateAndUse()">*</span>
              <VueIcon
                v-if="!canEditStateAndUse()"
                v-b-tooltip.hover.viewport="
                  $i18n.t('container.form.restricted')
                "
                icon="mdi:help-circle-outline"
                color="var(--color-red)"
                :icon-style="{ fontSize: '16px' }"
              ></VueIcon>
            </label>
            <b-form-select
              v-model="use"
              :options="optionsUses"
              :state="validateState('use')"
              value-field="value"
              text-field="name"
              :disabled="!canEditStateAndUse()"
              @change="confirmChangeUse()"
            >
              <template #first>
                <b-form-select-option :value="null" disabled>{{
                  $t('container.undefined')
                }}</b-form-select-option>
              </template>
            </b-form-select>
            <b-form-invalid-feedback
              v-for="error of filterErrors('use')"
              id="input-use-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <b-form-group class="fieldOuter">
            <label
              >{{ $t('container.form.state') }}
              <span v-if="canEditStateAndUse()">*</span>
              <VueIcon
                v-if="!canEditStateAndUse()"
                v-b-tooltip.hover.viewport="
                  $i18n.t('container.form.restricted')
                "
                icon="mdi:help-circle-outline"
                color="var(--color-red)"
                :icon-style="{ fontSize: '16px' }"
              ></VueIcon>
            </label>
            <b-form-select
              v-model="state"
              :options="optionsStates"
              :state="validateState('state')"
              value-field="value"
              text-field="name"
              :disabled="!canEditStateAndUse()"
            >
              <template #first>
                <b-form-select-option :value="null" disabled>{{
                  $t('container.undefined')
                }}</b-form-select-option>
              </template>
            </b-form-select>
            <b-form-invalid-feedback
              v-for="error of filterErrors('state')"
              id="input-state-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2"
            >
              * {{ error.$message }}
            </b-form-invalid-feedback>
          </b-form-group>
          <div class="form-group">
            <label>{{ $t('container.form.commissioning_date') }} *</label>
            <ContainersDatePicker
              v-model="commissioningDatetime"
              type="date"
              :state="validateState('commissioningDatetime')"
            ></ContainersDatePicker>
            <div
              v-for="error of filterErrors('commissioningDatetime')"
              id="input-commissioningDatetime-live-feedback"
              :key="error.$uid"
              class="pl-2 pr-2 invalid"
            >
              * {{ error.$message }}
            </div>
          </div>
          <div class="form-group">
            <label
              >{{ $t('container.form.address') }}
              <span v-if="canEditAddress()">*</span></label
            >
            <div v-if="canEditAddress()">
              <ContainersAddressForm
                v-model="address"
                :filter-errors="filterErrors"
              ></ContainersAddressForm>
            </div>
            <div v-else>{{ $t('container.form.address.uneditable') }}</div>
          </div>
          <ButtonWrapper class="float-right pl-2" @click="createContainer">
            {{ $t('container.buttons.valid') }}
          </ButtonWrapper>
          <ButtonWrapper
            class="float-right pl-2"
            type="secondary"
            :disabled="isProcessing"
            @click="cancelForm"
            >{{ $t('container.buttons.cancel') }}</ButtonWrapper
          >
        </form>
      </div>
    </div>
  </div>
</template>
<script setup>
/**
 * Formulaire de création et de modification des conteneurs
 * Regles de gestion :
 * - Si l'utilisateur a les droits en super-utilisateur il peut intervenir sur tous les champs
 *   - Sinon Etat et Statut sont bloqués si le conteneur est en cours d'utilisation
 * - Si le conteneur est en cours d'utilisation et que le user veut changer l'état,
 */
import Checkbox from '@/components/shared/Checkbox.vue'
import BetterSelect from '@c/shared/BetterSelect.vue'
import store from '@/store'
import api from '@/api'
import ContainersDatePicker from '@/components/containers/ContainersDatePicker.vue'
import i18n from '@/i18n'
import AddressAutocomplete from '@c/shared/AddressAutocomplete/AddressAutocomplete.vue'
import { useVuelidate } from '@vuelidate/core'
import {
  required,
  maxLength,
  integer,
  requiredIf,
  alphaNum,
  helpers,
} from '@vuelidate/validators'
import { useRightsPlugin } from '@/mixins/rights-mixin'
import VueIcon from '@/components/shared/VueIcon.vue'
import moment from 'moment'
import ContainersAddressForm from '@/components/containers/form/ContainersAddressForm.vue'

const emit = defineEmits(['submited', 'cancel'])
const props = defineProps({
  value: {
    type: Object,
    default: () => ({
      name: '',
      volume: null,
      code: '',
      reference: null,
      biComp: false,
      commissioningDatetime: new Date(),
      lastStateLabel: null,
      lastStateId: null,
      lastUseLabel: null,
      lastUseId: null,
    }),
  },
  editing: {
    type: Boolean,
    default: false,
  },
})
const { hasFeatureRight } = useRightsPlugin()
const optionsFullUses = computed(
  () => store.getters['containers/dropDownUseList']
)
const name = ref(null)
const code = ref(null)
const volume = ref(null)
const reference = ref(null)
const optionsFlows = computed(
  () => store.getters['containers/dropDownFlowList']
)
const flux1Id = ref(null)
const flux2Id = ref(null)
const flux1 = ref(null)
const flux2 = ref(null)
const optionsUses = computed(() => {
  if (props.editing && hasFeatureRight('container_superuser')) {
    return store.getters['containers/dropDownUseList']
  } else {
    return store.getters['containers/dropDownUseLimitedList']
  }
})
const use = ref(null)
const optionsStates = computed(() => {
  if (props.editing && hasFeatureRight('container_superuser')) {
    return store.getters['containers/dropDownStateList']
  } else {
    return store.getters['containers/dropDownStateLimitedList']
  }
})
const state = ref(null)
const biComp = ref(props.value['biComp'])
const isFlowListLoading = ref(false)
const isProcessing = ref(false)
const value = {}
const commissioningDatetime = ref(new Date(props.value.commissioningDatetime))
const address = ref(null)

const maxCharacters = 35
const betterAlphaNumValidator = helpers.regex(/^[A-Za-z0-9À-ÖÙ-öù-ÿ_\-]*$/)
const notZero = (value) => 0 != parseInt(value)
const uniqCode = (value) => {
  // true : ok la validation passe.
  // false : déclenche erreur
  let containerWithThisCode =
    store.getters['containers/findCodeInContainers'](value)
  if (typeof containerWithThisCode != 'undefined') {
    if (!props.editing) {
      // je suis en création de conteneur et je trouve ce code
      return false
    } else {
      // je suis en édition, si c'est le conteneur que j'édite ça passe
      return containerWithThisCode.id == props.value.id
    }
  }
  return true
}
const rules = computed(() => ({
  name: {
    required: helpers.withMessage(
      i18n.t('container.form.create.error.required'),
      required
    ),
    maxLength: helpers.withMessage(
      i18n.t('container.form.create.error.toolong', { max: maxCharacters }),
      maxLength(maxCharacters)
    ),
    betterAlphaNumValidator: helpers.withMessage(
      i18n.t('container.form.create.error.alphanum'),
      betterAlphaNumValidator
    ),
  },
  volume: {
    integer: helpers.withMessage(
      i18n.t('container.form.create.error.integer'),
      integer
    ),
    noteZero: helpers.withMessage(
      i18n.t('container.form.create.error.notenull'),
      notZero
    ),
  },
  code: {
    required: helpers.withMessage(
      i18n.t('container.form.create.error.required'),
      required
    ),
    maxLength: helpers.withMessage(
      i18n.t('container.form.create.error.toolong', { max: maxCharacters }),
      maxLength(maxCharacters)
    ),
    betterAlphaNumValidator: helpers.withMessage(
      i18n.t('container.form.create.error.alphanum'),
      betterAlphaNumValidator
    ),
    uniqCode: helpers.withMessage(
      i18n.t('container.form.create.error.codeexist'),
      uniqCode
    ),
  },
  reference: {
    betterAlphaNumValidator: helpers.withMessage(
      i18n.t('container.form.create.error.alphanum'),
      betterAlphaNumValidator
    ),
  },
  flux1: {
    required: helpers.withMessage(
      i18n.t('container.form.create.error.required'),
      required
    ),
  },
  flux2: {
    requiredIfRef: requiredIf(biComp),
  },
  use: {
    required: helpers.withMessage(
      i18n.t('container.form.create.error.required'),
      required
    ),
  },
  state: {
    required: helpers.withMessage(
      i18n.t('container.form.create.error.required'),
      required
    ),
  },
  commissioningDatetime: {
    required: helpers.withMessage(
      i18n.t('container.form.create.error.required'),
      required
    ),
  },
  address: {
    required: helpers.withMessage(
      i18n.t('container.form.create.error.required'),
      requiredIf(canEditAddress())
    ),
  },
}))
const v$ = useVuelidate(rules, {
  name,
  code,
  volume,
  flux1,
  flux2,
  use,
  state,
  reference,
  commissioningDatetime,
  address,
})

function validateState(item) {
  if (this.v$[item]) {
    const { $dirty, $error } = this.v$[item]
    return $dirty ? !$error : null
  }
  return
}

watch(
  () => props.value,
  () => {
    if (props.editing) {
      name.value = props.value.name
      volume.value = props.value.volume
      code.value = props.value.code
      reference.value = props.value.reference
      if (typeof props.value.stateId !== 'undefined') {
        state.value = props.value.stateId ? parseInt(props.value.stateId) : null
      }
      if (typeof props.value.lastUseId !== 'undefined') {
        use.value = props.value.lastUseId
          ? parseInt(props.value.lastUseId)
          : null
      }
      if (
        !!props.value.address &&
        !!props.value.latitude &&
        !!props.value.longitude
      ) {
        address.value = {
          formatted: props.value.address,
          lat: props.value.latitude,
          lng: props.value.longitude,
        }
      } else {
        address.value = null
      }
      biComp.value = props.value['biComp']
      commissioningDatetime.value = new Date(
        props.value['commissioningDatetime']
      )
      flux1.value = null
      flux2.value = null
      if (
        typeof props.value['flux1Id'] != 'undefined' &&
        typeof props.value['flux1Label'] != 'undefined'
      ) {
        flux1.value = parseInt(props.value['flux1Id'])
      }
      if (
        typeof props.value['flux2Id'] != 'undefined' &&
        typeof props.value['flux2Label'] != 'undefined'
      ) {
        flux2.value = parseInt(props.value['flux2Id'])
      }
    } else {
      biComp.value = false
      commissioningDatetime.value = new Date()
      name.value = null
      volume.value = null
      code.value = null
      reference.value = null
      flux1Id.value = null
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

/**
 * If a superUser change state value while container is in use,
 * he has to confirm its changing
 */
function confirmChangeUse() {
  if (
    isInProgress() &&
    props.editing &&
    hasFeatureRight('container_superuser')
  ) {
    if (!confirm(i18n.t('container.form.warning_container_in_progress'))) {
      use.value = props.value.lastUseId
    }
  }
}

/**
 * Return true if container is in use ( in progress ) when component is loaded
 * @returns {boolean}
 */
function isInProgress() {
  let inProgressItem = optionsFullUses.value.find(
    (item) => item.code == 'in_progress'
  )
  if (inProgressItem) {
    return props.value.lastUseId === inProgressItem.value
  }
  return false
}

/**
 * I can't edit State and Use fields if I'm not superuser and container's in use
 * @returns {*|boolean}
 */
function canEditStateAndUse() {
  if (!props.editing) {
    return true
  }
  if (hasFeatureRight('container_superuser')) {
    return true
  } else {
    return hasFeatureRight('container_update') && !isInProgress()
  }
}

/**
 * I can edit only if the container isn't in use
 * @returns {boolean}
 */
function canEditAddress() {
  if (!props.editing) {
    return true
  }
  let inProgressId = optionsFullUses.value.find(
    (item) => item.code == 'in_progress'
  ).value
  return use.value != inProgressId
}

async function createContainer() {
  const isFormCorrect = await v$.value.$validate()
  if (isFormCorrect) {
    isProcessing.value = true
    let res = false
    volume.value = parseFloat(volume.value)
    let params = []
    params['name'] = name.value.trim()
    params['code'] = code.value.trim()
    params['reference'] = reference.value ? reference.value.trim() : null
    params['volume'] = parseFloat(volume.value)
    if (
      address.value != null &&
      typeof address.value.lat != 'undefined' &&
      typeof address.value.lng != 'undefined' &&
      typeof address.value.formatted != 'undefined' &&
      canEditAddress
    ) {
      params['latitude'] = address.value.lat
      params['longitude'] = address.value.lng
      params['address'] = address.value.formatted
      if (!!params['latitude'] && !!params['longitude']) {
        params['notGeolocated'] = false
      }
    }
    params['biComp'] = biComp.value
    params['commissioningDatetime'] = moment(
      commissioningDatetime.value
    ).format('YYYY-MM-DDTHH:mm:ss')
    if (canEditStateAndUse) {
      params['lastUse'] =
        api.APIUrls.APIV3_GEORED_CONTAINER_USES + '/' + use.value
      params['lastState'] =
        api.APIUrls.APIV3_GEORED_CONTAINER_STATES + '/' + state.value
    }
    if (typeof flux1.value != 'undefined' && flux1.value != null) {
      params['flux1'] =
        api.APIUrls.APIV3_GEORED_FLUX_MATERIALS + '/' + flux1.value
    } else if (props.editing) {
      params['flux1'] = null
    }
    if (
      biComp.value &&
      typeof flux2.value != 'undefined' &&
      flux2.value != null
    ) {
      params['flux2'] =
        api.APIUrls.APIV3_GEORED_FLUX_MATERIALS + '/' + flux2.value
    } else if (props.editing) {
      params['flux2'] = null
    }
    if (!props.editing) {
      res = await store.dispatch('containers/addContainer', params)
    } else {
      params['id'] = props.value.id
      res = await store.dispatch('containers/updateContainer', params)
    }
    isProcessing.value = false
    emit('submitted', res)
  }
}

function cancelForm() {
  emit('cancel')
}

/**
 * needed to display errors to the right field
 * @param item
 * @returns {*}
 */
function filterErrors(item) {
  return v$.value.$errors.filter((elt) => elt.$propertyPath === item)
}
</script>
<style lang="scss" scoped>
.form-group {
  font: normal normal normal 10px/14px Open Sans;
  letter-spacing: 0px;
  label {
    font-size: 14px;
  }
}
.group-title {
  font: normal normal 400 12px/18px Open Sans;
}
.invalid-feedback {
  font-size: 100% !important;
}
.invalid {
  border-top: thin solid #dc3545;
  color: #dc3545;
}
</style>
<style>
.containers-address input,
.address_autocomplete input,
.address_autocomplete input::placeholder,
.address_autocomplete .multiselect__placeholder {
  color: #495057 !important;
}
.afs-form {
  left: 0px !important;
}
</style>

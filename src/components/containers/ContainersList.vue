<template>
  <div>
    <div class="list-filters">
      <ContainersCollapsibleSection
        v-model="filterIsOpen"
        :title="$t('container.list_dropdown_filter_label')"
      >
        <form autocomplete="false" @submit="(e) => e.preventDefault()">
          <div class="container">
            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label class="col-form-label-sm">{{
                    $t('container.form.name')
                  }}</label>
                  <input
                    v-model="nameFilterRef"
                    type="text"
                    class="form-control form-control-sm"
                  />
                </div>
                <div class="form-group">
                  <label class="col-form-label-sm">{{
                    $t('container.form.code')
                  }}</label>
                  <input
                    v-model="codeFilterRef"
                    type="text"
                    class="form-control form-control-sm"
                  />
                </div>
                <div class="form-group">
                  <label>{{ $t('container.form.flow') }}</label>
                  <BetterSelect
                    v-model="flowFilterRef"
                    :loading="isFlowListLoading"
                    :options="optionsFlows"
                  ></BetterSelect>
                </div>
                <div class="form-group">
                  <label>{{ $t('container.form.state') }}</label>
                  <BetterSelect
                    v-model="stateFilterRef"
                    :loading="isStateListLoading"
                    :options="optionsStates"
                  ></BetterSelect>
                </div>
                <div class="form-group">
                  <label>{{ $t('container.form.use') }}</label>
                  <BetterSelect
                    v-model="useFilterRef"
                    :loading="isUseListLoading"
                    :options="optionsUses"
                  ></BetterSelect>
                </div>
                <div class="form-group">
                  <Checkbox v-model="archivedFilterRef">
                    {{ $t('container.form.archived') }}
                  </Checkbox>
                </div>
                <div class="form-group">
                  <Checkbox v-model="geolocatedFilterRef">
                    {{ $t('container.form.geolocated') }}
                  </Checkbox>
                </div>
              </div>
            </div>
          </div>
        </form>
      </ContainersCollapsibleSection>
    </div>
    <!-- LISTE DES CONTENEURS -->
    <div v-show="isSpinnerLoaderVisible" class="col-12">
      <LoaderSpinner />
    </div>
    <div ref="scrollWrapper" infinite-wrapper class="scroll-wrapper">
      <ul>
        <li
          v-for="container in containerFiltered"
          :key="container.id"
          class="item"
        >
          <div class="item-left">
            <div
              class="item-icon"
              :style="{ 'border-color': getColorIcon(container) }"
            >
              <VueIcon
                v-b-tooltip:hover.right.viewport.noninteractive
                icon="mdi:flag"
                :color="getColorIcon(container)"
                :icon-style="{ fontSize: '20px' }"
                :title="containerIconTitle(container)"
              ></VueIcon>
            </div>
          </div>
          <div class="item-right">
            <div class="item-infos" style="width: 100%">
              <div class="item-name">
                {{ container.name }}
              </div>
              <div style="width: 100%">
                <div
                  class="item-typename"
                  style="display: inline-block; width: 60%"
                >
                  <div
                    v-if="container.notGeolocated"
                    class="item-typename"
                    style="display: inline-block"
                  >
                    <VueIcon
                      icon="mdi:map-marker"
                      color="var(--color-zones)"
                      :icon-style="{ fontSize: '20px' }"
                      :title="$t('container.list.not_located')"
                    ></VueIcon>
                  </div>
                  {{ container.code }}
                </div>
                <div
                  class="item-typename"
                  style="display: inline-block; text-align: right; width: 30%"
                >
                  {{ container.lastUseLabel }}
                </div>
              </div>
            </div>
            <div class="item-toolbar container-action">
              <div
                v-if="container.latitude && container.longitude"
                class="item-action-button"
                @click="handleItemZoomClick(container)"
              >
                <VueIcon
                  icon="radix-icons:magnifying-glass"
                  color="var(--color-main)"
                  :style="{ fontSize: '16px' }"
                  clickable
                ></VueIcon>
              </div>
              <div
                v-show="canEditContainer(container)"
                :editing="true"
                class="item-action-button"
                @click="handleEditClick(container)"
              >
                <VueIcon
                  icon="material-symbols:edit"
                  color="var(--color-main)"
                  :icon-style="{ fontSize: '16px' }"
                  clickable
                ></VueIcon>
              </div>
              <div
                v-show="isArchivable(container)"
                class="item-action-button"
                @click="handleArchiveClick(container)"
              >
                <VueIcon
                  icon="mdi:archive-arrow-down"
                  color="var(--color-main)"
                  :icon-style="{ fontSize: '16px' }"
                  clickable
                ></VueIcon>
              </div>
              <div
                v-show="
                  container.deletedAt && hasFeatureRight('container_unarchive')
                "
                class="item-action-button"
                @click="handleUnarchiveClick(container)"
              >
                <VueIcon
                  icon="mdi:archive-arrow-up-outline"
                  color="var(--color-fern)"
                  :icon-style="{ fontSize: '16px' }"
                  clickable
                ></VueIcon>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <!-- FIN LISTE DES CONTENEURS -->
    <div v-if="emptyList" class="container">
      <div class="row">
        <div class="col-12 filter_res">
          {{ $t('container.list.no_item') }}
          <ul>
            <li v-if="archivedFilterRef">
              {{ $t('container.list.archived_containers') }}
            </li>
            <li v-if="geolocatedFilterRef">
              {{ $t('container.list.notgeolocated_containers') }}
            </li>
            <li v-if="nameFilterRef">
              {{ $t('container.form.name') }}: {{ nameFilterRef }}
            </li>
            <li v-if="codeFilterRef">
              {{ $t('container.form.code') }}: {{ codeFilterRef }}
            </li>
            <li v-if="flowFilterRef">
              {{ $t('container.form.flow') }}: {{ flowFilterRef.name }}
            </li>
            <li v-if="stateFilterRef">
              {{ $t('container.form.state') }}: {{ stateFilterRef.name }}
            </li>
            <li v-if="useFilterRef">
              {{ $t('container.form.use') }}: {{ useFilterRef.name }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import ContainersForm from '@/components/containers/ContainersForm.vue'
import store from '@/store'
import mitt from '@/plugins/mitt.js'
import vueLeafletMapPlugin from '@/plugins/vue-map.js'
import VueIcon from '@/components/shared/VueIcon.vue'
import ContainersCollapsibleSection from '@/components/containers/ContainersCollapsibleSection.vue'
import BetterSelect from '@/components/shared/BetterSelect.vue'
import Checkbox from '@/components/shared/Checkbox.vue'
import iconsMixins from '@/mixins/icons-mixin.js'
import i18n from '@/i18n'
import { useRightsPlugin } from '@/mixins/rights-mixin.js'

const props = defineProps({
  haveToReload: {
    type: Boolean,
    default: false,
  },
})
const { hasFeatureRight, rightsTable } = useRightsPlugin()

const isSpinnerLoaderVisible = ref(false)
const emit = defineEmits([
  'onEdit',
  'onArchive',
  'onUnarchive',
  'reloadList',
  'closepopup',
  'closeBoxShadow',
  'openFilters',
])
const containers = computed(() => store.getters['containers/allContainers'])
const emptyList = computed(
  () => !containers.value.length || !containerFiltered.value.length
)
const filterIsOpen = ref(false)
const creation = true
const isValidCoords = ref(false)
const nameFilterRef = ref('')
const codeFilterRef = ref('')
const flowFilterRef = ref('')
const isFlowListLoading = ref(false)
const stateFilterRef = ref('')
const isStateListLoading = ref(false)
const useFilterRef = ref('')
const isUseListLoading = ref(false)
const archivedFilterRef = ref(false)
const geolocatedFilterRef = ref(false)
const optionsFlows = computed(
  () => store.getters['containers/dropDownFlowList']
)
const optionsStates = computed(
  () => store.getters['containers/dropDownStateList']
)
const optionsUses = computed(() => store.getters['containers/dropDownUseList'])

const containerFiltered = computed(() => {
  return containers.value.filter((item) => {
    const isFilteredByName =
      nameFilterRef.value === '' ||
      (item.name || '')
        .toLowerCase()
        .includes((nameFilterRef.value || '').toLowerCase())
    const isFilteredByCode =
      codeFilterRef.value === '' ||
      (item.code || '')
        .toLowerCase()
        .includes((codeFilterRef.value || '').toLowerCase())
    const isFilteredByFlow =
      !flowFilterRef.value?.value ||
      item.flux1Id === flowFilterRef.value?.value ||
      item.flux2Id === flowFilterRef.value?.value
    const isFilteredByState =
      !stateFilterRef.value?.value ||
      item.stateId === stateFilterRef.value?.value
    const isFilteredByUse =
      !useFilterRef.value?.value || item.lastUseId === useFilterRef.value?.value

    return (
      isFilteredByName &&
      isFilteredByCode &&
      isFilteredByFlow &&
      isFilteredByState &&
      isFilteredByUse
    )
  })
})

watch(
  () => archivedFilterRef.value,
  () => reloadContainerList(),
  {
    deep: true,
  }
)
watch(
  () => geolocatedFilterRef.value,
  () => reloadContainerList(),
  {
    deep: true,
  }
)
watch(
  () => props.haveToReload,
  () => reloadContainerList()
)

function containerIconTitle(container) {
  if (archivedFilterRef.value) {
    return i18n.t('container.list.archived')
  }
  return container.lastStateLabel || i18n.t('container.list.unable_state')
}

function handleItemZoomClick(container) {
  // femer la boxshadow
  emit('closeBoxShadow')
  // recentre la carte sur le container
  mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [
    [container.latitude, container.longitude],
  ])
  // open the container popup
  let positionsLayerGroup = vueLeafletMapPlugin
    .getLeafletWrapperVM()
    .layerGroups.containers_markers.getLayers()
    .find((item) => item.externalId == container.id)
  if (positionsLayerGroup) {
    positionsLayerGroup.closePopup()
    positionsLayerGroup.openPopup()
  }
}

function handleEditClick(container) {
  filterIsOpen.value = false
  emit('onEdit', container)
}

function handleArchiveClick(container) {
  filterIsOpen.value = false
  emit('onArchive', container)
}

async function reloadContainerList() {
  emit('closepopup')
  isSpinnerLoaderVisible.value = true
  let options = {
    payload: {},
  }
  if (archivedFilterRef.value) {
    Object.assign(options.payload, {
      'exists[deletedAt]': archivedFilterRef.value,
    })
  }
  if (geolocatedFilterRef.value) {
    Object.assign(options.payload, { notGeolocated: geolocatedFilterRef.value })
  }
  await store.dispatch('containers/getContainers', options)
  isSpinnerLoaderVisible.value = false
}

async function handleUnarchiveClick(container) {
  filterIsOpen.value = false
  emit('onUnarchive', container)
}

function canEditContainer(container) {
  return hasFeatureRight('container_update')
}

function canArchiveContainer(container) {
  return hasFeatureRight('container_archive')
}

function getColorIcon(container) {
  let color = 'var(--color-main)'
  let code = store.getters['containers/getStateById'](container.stateId)
  if (!container.stateId) {
    color = 'var(--color-tundora)'
  } else {
    switch (code.code) {
      case 'empty':
        color = 'var(--color-fern)'
        break
      case 'reusable':
        color = 'var(--color-sunshade)'
        break
      case 'full':
        color = 'var(--color-thunderbird)'
        break
      default:
        color = 'var(--color-tundora)'
    }
  }
  if (archivedFilterRef.value) {
    color = '#7d4309'
  }
  return color
}

function isArchivable(container) {
  if (!hasFeatureRight('container_archive')) {
    return false
  }
  let res = false
  let depositedId = optionsUses.value.find(
    (item) => item.code == 'deposited'
  )?.value
  let emptyId = optionsStates.value.find((item) => item.code == 'empty')?.value
  res =
    !container.deletedAt &&
    emptyId == container.stateId &&
    depositedId == container.lastUseId
  return res
}
</script>
<style lang="scss" scoped>
ul {
  list-style: none;
  padding: 0px;
}
.scroll-wrapper {
  max-height: calc(55vh);
  overflow: auto;
}
.item {
  box-shadow: 0px 1px 2px #0000005c;
  min-height: 65px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;

  &.highlighted {
    box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.5);
    background-color: rgba(255, 255, 0, 0.05);
    transition: box-shadow 0.3s ease, outline 0.3s ease,
      background-color 0.3s ease;
  }

  .item-icon {
    width: 35px;
    height: 35px;
    border-radius: 50px;
    border: 2px solid grey;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      max-width: 100%;
      max-height: 20px;
    }

    outline: none;
    background-color: initial;
  }

  .item-left {
    display: flex;
    justify-content: center;

    flex-basis: 15%;
  }
  .item-right {
    flex-basis: 85%;
    display: flex;
    justify-content: space-between;

    .item-name {
      font: normal normal bold 12px/16px Open Sans;
      letter-spacing: 0px;
      color: var(--color-tundora);
    }
    .item-typename {
      font: normal normal normal 12px/16px Open Sans;
      letter-spacing: 0px;
      color: var(--color-tundora);
    }
    .item-toolbar {
      display: flex;
      align-items: center;
    }
  }
}

.list-filters label {
  font: normal normal normal 12px/16px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.item-action-button {
  cursor: pointer;
  padding: 0px 2px;
}

.container-action {
  min-width: 65px;
  justify-content: flex-end;
}
.filter_res ul li {
  margin-left: 35px;
  list-style: circle;
}
</style>

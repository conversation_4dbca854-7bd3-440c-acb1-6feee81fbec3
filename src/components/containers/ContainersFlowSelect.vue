<template>
  <BetterSelect
    v-model="modelValue"
    :loading="isFlowListLoading"
    :options="optionsFlows"
  ></BetterSelect>
</template>
<script setup>
import BetterSelect from '@/components/shared/BetterSelect.vue'
import { useVModelProp } from '@/composables/useVModelProp'
import { useVModelEmit } from '@/composables/useVModelEmit'

const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

const props = defineProps({
  [useVModelProp()]: {
    type: [Number, Object],
    default: () => null,
  },
})

const modelValue = computed({
  get() {
    return props[useVModelProp()]
  },
  set(newValue) {
    vModelEmit(newValue)
  },
})
</script>

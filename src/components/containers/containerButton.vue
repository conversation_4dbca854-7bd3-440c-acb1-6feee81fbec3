<template>
  <ButtonWrapper type="secondary" @click="() => emit('click')">
    <slot name="icon">
      <simple-svg
        v-if="icon"
        width="30px"
        height="30px"
        class="clock"
        :src="icon"
        fill="var(--color-main)"
        color="var(--color-main)"
      />
    </slot>

    <slot></slot>
  </ButtonWrapper>
</template>
<script setup>
import { Icon } from '@iconify/vue2'
import ButtonWrapper from '@c/shared/ButtonWrapper.vue'
import containerIcon from '@/components/shared/Sidebar/assets/container.svg'
const emit = defineEmits(['click'])
const props = defineProps({
  icon: {
    type: String,
    default: containerIcon,
  },
})
</script>
<style lang="scss" scoped>
button {
  font: normal normal bold 12px/20px Open Sans;
  letter-spacing: 0px;
  color: var(--color-main);
  background-color: transparent;
  border: 0px;
  display: flex;
  justify-content: flex-start;
  column-gap: 7px;
  align-items: center;
  border-radius: 5px;
  padding: 5px;
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transition: background-color 1s linear;
  }
  &:active,
  &:focus {
    outline: 0;
  }
}
</style>

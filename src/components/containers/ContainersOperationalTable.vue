<template lang="pug">
  .identification_table(ref="root")
    .row.p-0.m-0
      .col-6.p-0.m-0.header-title-wrapper
        .header-title {{headerText}}
    DataTable(
      v-if="table"
      class="table_theme_1"
      ref="datatable",
      :ssrPaging="false",
      :ssrShowMultiColumnFilter="false",
      :searching="true",
      :paging="true",
      name="containersOperationalTable",
      rowId="id",
      scrollY="250px",
      :columns="columns",
      :columnDefs="columnDefs",
      :language="$translations.datatable",
      :extraOptions="extraOptions || {}",
      :select="select",
      :defaultSortingColumn="0"
      :autoHeight="true"
      :autoHeightOffset="35"
      :autoHeightRefreshKey="autoHeightRefreshKey"
      @select="onSelect"
      @deselect="onDeselect"
    )
</template>
<script setup>
import Vue, { reactive, ref } from 'vue'
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import { createSortableColumnDefinition } from '@/utils/datatable.js'
import i18n from '@/i18n'
import store from '@/store'
import mitt from '@/plugins/mitt.js'
import { hasInvalidCoords } from '@/utils/map'
import { useRightsPlugin } from '@/mixins/rights-mixin'

const { hasFeatureRight, rightsTable } = useRightsPlugin()

//Props
const props = defineProps({
  mode: {
    type: String,
    default: 'table+map',
  },
  headerText: {
    type: String,
    default: '',
  },
  showRoundColumns: {
    type: Boolean,
    default: false,
  },
  showBinColumns: {
    type: Boolean,
    default: false,
  },
})

//Consts
const columns = ref([
  createSortableColumnDefinition(
    `name`,
    `name`,
    i18n.t('container.table.name')
  ),
  {
    data: 'reference',
    title: i18n.t('container.table.reference'),
    defaultContent: '',
  },
  {
    data: 'biComp',
    title: i18n.t('container.table.biComp'),
    defaultContent: i18n.t('common.no'),
    width: '50px',
  },
  {
    data: 'lastUseFlux1Label',
    title: i18n.t('container.table.flow1'),
    defaultContent: i18n.t('container.undefined'),
  },
  {
    data: 'lastUseFlux2Label',
    title: i18n.t('container.table.flow2'),
    defaultContent: i18n.t('container.undefined'),
  },
  {
    data: 'lastVehicleName',
    title: i18n.t('container.table.last_vehicle_name'),
    defaultContent: '',
  },
  {
    data: 'lastUseLabel',
    title: i18n.t('container.table.use'),
    defaultContent: '',
  },
  {
    data: 'lastStateLabel',
    title: i18n.t('container.table.state'),
    defaultContent: '',
  },
  {
    data: 'address',
    title: i18n.t('container.table.address'),
    defaultContent: '',
  },
  {
    data: 'comment',
    title: i18n.t('container.table.complement'),
    defaultContent: '',
  },
])

const name = ref('containers')
const isModalOpen = ref(false)
const showInformationsPopup = ref(false)
const autoHeightRefreshKey = ref(Date.now())
const datatable = ref(null)
const table = ref(false)
const select = reactive({
  items: 'row',
  style: 'single',
  info: false,
})
const columnDefs = ref([
  {
    target: 2,
    orderable: true,
    render: createComponentRender(
      {
        name: 'bicomp',
        template: `<div :data-value-encoded="bicompExport"><span v-if='row.biComp'>{{$t('container.yes')}}</span><span v-else>{{$t('container.no')}}</span></div>`,
        computed: {
          bicompExport() {
            if (this.row.bicomp) {
              return window.btoa(this.$t('container.yes'))
            }
            return window.btoa(this.$t('container.no'))
          },
        },
      },
      {
        filterBy(row) {
          if (row.biComp) {
            return i18n.t('container.yes')
          } else {
            return i18n.t('container.no')
          }
        },
      }
    ),
    width: '50px',
  },
  {
    target: 5,
    orderable: false,
    render: createComponentRender(
      {
        name: 'lastVehicleName',
        template: `<div :data-value-encoded="vehicleExport"><span v-if='row.lastVehicleName'>{{row.lastVehicleName}}</span> <span v-if='row.lastVehicleDate'>{{dateVehicule}}</span></div>`,
        computed: {
          dateVehicule() {
            return Vue.$date.formatDatetime(this.row.lastVehicleDate, {})
          },
          vehicleExport() {
            let res = ' '
            if (this.row.lastVehicleName) {
              res = this.row.lastVehicleName + ' '
            }
            if (this.row.lastVehicleDate) {
              res += Vue.$date.formatDatetime(this.row.lastVehicleDate, {})
            }
            return window.btoa(res)
          },
        },
      },
      {
        filterBy(row) {
          return row.lastVehicleName
        },
      }
    ),
  },
])
const extraOptions = reactive({
  ...datatableMixin.methods.configureColumnsFilters((filters) => {
    let columnFilters = []

    columns.value.forEach((def, index) => {
      columnFilters.push(filters.createTextFilter({ column: index }))
    })
    return columnFilters
  }),
  ...datatableMixin.methods.configureFooter(),
  dom: 'rtip',
  pageLength: 25,
  info: true,
})

//Computed
const items = computed(() => {
  return store.state.containers.containers
})

//Watchers
watch(items, () => {
  updateTable()
})

//Lifecycle hooks
onMounted(() => {
  $.fn.dataTable.moment('DD/MM/YYYY HH:mm:ss')
  updateTable()
  setTimeout(() => {
    table.value = true
    setTimeout(() => {
      datatable.value.datatable.draw()
    }, 500)
  }, 200)
})

//Methods
const updateTable = () => {
  store.dispatch('datatable/setTableItems', {
    name: 'containersOperationalTable',
    items: items.value,
  })
}
const onSelect = ({ item }) => {
  if (
    hasInvalidCoords({
      lat: item?.latitude ?? null,
      lng: item?.longitude ?? null,
    })
  ) {
    console.log('invalid coords : ', item)
    return
  }

  mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [[item.latitude, item.longitude]])
}

const onDeselect = () => {
  const validItems = items.value.filter(
    (item) =>
      !hasInvalidCoords({ lat: item?.lat ?? null, lng: item?.lng ?? null })
  )

  if (validItems.length === 0) return

  mitt.emit(
    'SIMPLICITI_MAP_FIT_TO_BOUNDS',
    validItems.map((item) => [item.lat, item.lng])
  )
}
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.title,
.title span,
.title strong {
  font-size: 12px;
}
.identification_table {
  height: 100%;
  height: -moz-available; /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available; /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}
.table_toolbar {
  display: flex;
  align-items: center;
}
.header-title-wrapper {
  display: flex;
  align-items: center;
}
.header-title {
  font-size: 12px;
}
</style>

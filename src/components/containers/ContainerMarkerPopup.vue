<template>
  <MapPopup :title="$t('container.map.popup.title')">
    <div class="container">
      <div
        class="row py-2 pl-4 black small-text align-items-center font-weight-bold"
      >
        <div class="chapters">
          <div>
            {{ $t('container.map.popup.name') }}<br />
            <span class="item_value">{{ geometry.item.name }}</span>
          </div>
          <div>
            {{ $t('container.map.popup.status') }}<br />
            <span class="item_value">{{ geometry.item.lastUseLabel }}</span>
          </div>
          <div>
            {{ $t('container.map.popup.flow') }}<br />
            <span class="item_value">
              {{ geometry.item.flux1Label
              }}<span v-if="geometry.item.flux2Label"
                >, {{ geometry.item.flux2Label }}</span
              >
            </span>
          </div>
          <div>
            {{ $t('container.map.popup.last_vehicle_name') }}<br />
            <span class="item_value">
              {{ geometry.item.lastVehicleName }} {{ vehicleDate }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </MapPopup>
</template>
<script>
import Vue from 'vue'
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'
export default {
  name: 'ContainerMarkerPopup',
  components: {
    MapPopup,
  },
  props: {
    geometry: {
      // objet leaflet
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      item: this.geometry.properties,
    }
  },
  computed: {
    vehicleDate() {
      return Vue.$date.formatDatetime(this.geometry.item.lastVehicleDate, {})
    },
    lastSpecificDatetime() {
      return Vue.$date.formatDatetime(
        this.geometry.item.lastSpecificDatetime,
        {}
      )
    },
  },
}
</script>
<style scoped>
.blue {
  color: var(--color-denim);
}

.small-text {
  font-size: 0.75rem;
}

.event-popup-chevron {
  position: absolute;
  right: 10px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
.item_value {
  font-weight: normal !important;
  font-weight: normal !important;
}
.chapters div {
  margin-bottom: 5px;
}
</style>

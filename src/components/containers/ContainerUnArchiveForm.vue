<template>
  <div class="container mt-2 archive">
    <div class="row">
      <div class="col">
        <div v-show="isProcessing" class="col-12">
          <LoaderSpinner />
        </div>
        <div class="blueTitle">
          {{ $t('container.form.unarchive.title') }} {{ currentContainer.name }}
        </div>
        <form autocomplete="false" @submit="(e) => e.preventDefault()">
          <b-form-group class="fieldOuter">
            <div>{{ $t('container.form.unarchive.address') }} *</div>
            <div>
              <ContainersAddressForm
                v-model="address"
                :filter-errors="filterErrors"
              ></ContainersAddressForm>
            </div>
          </b-form-group>
          <ButtonWrapper
            class="float-right pl-2"
            :disabled="isProcessing"
            @click="unArchiveContainer"
          >
            {{ $t('container.buttons.valid') }}
          </ButtonWrapper>
          <ButtonWrapper
            class="float-right pl-2"
            type="secondary"
            :disabled="isProcessing"
            @click="cancelArchive()"
            >{{ $t('container.buttons.cancel') }}</ButtonWrapper
          >
        </form>
      </div>
    </div>
  </div>
</template>
<script setup>
import store from '@/store'
import { useVuelidate } from '@vuelidate/core'
import { required, helpers } from '@vuelidate/validators'
import i18n from '@/i18n'
import $j from 'jquery'
import ContainersAddressForm from '@/components/containers/form/ContainersAddressForm.vue'

const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
})
const maxCharacters = 35
const isProcessing = ref(false)
const currentContainer = ref({})
const deletedReason = ref('')
const address = ref('')

const emit = defineEmits(['submitted', 'cancel'])
const rules = computed(() => ({
  address: {
    formatted: {
      required: helpers.withMessage(
        i18n.t('container.form.unarchive.error.required'),
        required
      ),
    },
  },
}))
const v$ = useVuelidate(rules, { address })

watch(
  () => props.value,
  () => {
    currentContainer.value = props.value
  },
  {
    immediate: true,
  }
)

function validateState(item) {
  if (this.v$[item]) {
    const { $dirty, $error } = this.v$[item]
    return $dirty ? !$error : null
  }
}

async function unArchiveContainer() {
  const isFormCorrect = await v$.value.$validate()
  if (isFormCorrect) {
    isProcessing.value = true
    let res = true
    let params = []
    params['id'] = props.value.id
    // filterIsOpen.value = false

    // archivedFilterRef.value = false

    currentContainer.value.deletedReason = null
    params['deletedReason'] = null
    params['address'] = address.value.formatted
    params['latitude'] = address.value.lat
    params['longitude'] = address.value.lng
    params['needToReload'] = false
    res = await store.dispatch('containers/updateContainer', params)
    if (!res) {
      return false
    } else {
      params['needToReload'] = true
      res = await store.dispatch('containers/unarchiveContainer', params)
    }

    if (!res) {
      return false
    }

    isProcessing.value = false
    address.value = null
    emit('submitted')
    return res
  } else {
    $j('#input-unarchive-live-feedback').css('display', 'block')
  }
  return
}

function cancelArchive() {
  this.v$.$reset()
  emit('cancel')
}

function filterErrors(item) {
  return v$.value.$errors.filter((elt) => elt.$propertyPath === item)
}
</script>
<style scoped>
.blueTitle {
  color: var(--color-denim);
  font-weight: bold;
  margin-bottom: 15px;
}
.container.archive {
  margin-bottom: 25px;
}
</style>
<style>
.afs-form {
  left: 0px !important;
}
</style>

<template>
  <div class="containers_tables_group">
    <nav class="header_nav" role="tablist">
      <a
        v-for="option in tabOptions"
        :key="option.name"
        :class="option.className"
        @click="currentTable = option.name"
        v-text="option.text"
      />
    </nav>

    <ContainersGeneralTable v-if="currentTable === 'general'" />
    <ContainersOperationalTable v-if="currentTable === 'operational'" />
  </div>
</template>
<script setup>
import ContainersGeneralTable from './ContainersGeneralTable.vue'
import ContainersOperationalTable from './ContainersOperationalTable.vue'
import i18n from '@/i18n'
import store from '@/store'

//Consts
const currentTable = ref('general')

//Computed
const tabOptions = computed(() => {
  const createTabOption = (name) => ({
    name: name,
    text: i18n.t(`containers.tabs.${name}`),
    className: { active: currentTable.value === name },
  })

  return [createTabOption('general'), createTabOption('operational')]
})

//Lifecycle hooks
onMounted(() => {
  store.dispatch('app/changeLayout', {
    origin: 'ContainersTableGroup.vue::mounted',
    currentLayoutName: 'CONTAINERS_MAIN_TABLE',
    menu_collapsed: true,
    menu_full_collapse: true,
    menu_toggle: true,
  })
})

onUnmounted(() => {
  store.dispatch('app/changeLayout', {
    origin: 'ContainersTableGroup.vue::destroyed',
    menu_collapsed: true,
    menu_full_collapse: false,
    menu_toggle: true,
  })
})

//Methods
const exportExcel = (event, id) => {
  $('#localisation').DataTable({
    retieve: false,
    searching: false,
    ordering: true,
    paging: true,
    info: false,
    responsive: true,
    scrollX: true,
    dom: 'Bfrtip',
    buttons: ['csv', 'excel', 'pdf'],
  })
  $('.dataTables_length').addClass('bs-select')
  $('.dt-buttons').css('display', 'none')
  if (id == 1) {
    $('.buttons-csv').trigger('click')
  } else if (id == 2) {
    $('.buttons-excel').trigger('click')
  } else {
    $('.buttons-pdf').trigger('click')
  }
}
</script>
<style lang="scss">
.location-table-address-column {
  min-width: 200px;
}

.containers_tables_group,
.containers_tables_group * {
  box-sizing: border-box !important;
}
.identification_table {
  height: calc(100% - 25px) !important;
}
.containers_tables_group {
  padding: 10px;
  height: 100%;

  td {
    font: normal normal normal 12px/18px Open Sans;
    letter-spacing: 0px;
    color: var(--color-tundora);
  }

  .header_nav {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0px;
    align-items: center;
  }

  .header_nav a {
    cursor: pointer;
    padding: 5px 10px;
    flex-shrink: 0;
    color: var(--color-dark-blue);
    margin: 0px 5px;
    text-align: center;
    font-weight: bold;
    border-bottom: 2px solid #01447000;
    text-decoration: none;
    font-size: 14px;

    &.active {
      border-bottom: 2px solid var(--color-dark-blue);
    }
    &:hover {
      text-decoration: none;
      opacity: 0.8;
    }
  }

  table.dataTable thead th,
  table.dataTable thead td {
    border-bottom: 0px;
  }

  table.dataTable thead th,
  table.dataTable tfoot th {
    font-weight: bold;
    font-size: 10px;
  }

  .dataTables_wrapper.no-footer .dataTables_scrollBody {
    border-bottom: 0px;
  }

  div.dataTables_wrapper div.dataTables_filter input {
    box-shadow: 0px 2px 3px #0000005b;
    border-radius: 4px;
    border: 0px;
  }
  div.dataTables_wrapper div.dataTables_filter input:focus {
    outline: 0px;
  }

  .dataTables_wrapper .dataTables_paginate .paginate_button:hover,
  .dataTables_wrapper .dataTables_paginate .paginate_button:active,
  .dataTables_wrapper .dataTables_paginate .paginate_button:focus {
    border: 1px solid rgba(0, 0, 0, 0);

    background: #80808030;
    color: black !important;
  }

  .btn-outline-info {
    color: var(--color-dark-blue);
    border: 0px;
    box-shadow: 0px 2px 3px #0000005c;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
  }
  .btn-outline-info:hover,
  .btn-outline-info:active,
  .btn-outline-info:focus,
  .btn-outline-info:not(:disabled):not(.disabled).active,
  .btn-outline-info:not(:disabled):not(.disabled):active,
  .show > .btn-outline-info.dropdown-toggle {
    background-color: #0000000a;
    color: var(--color-dark-blue);
  }

  .dataTables_wrapper .dataTables_paginate .paginate_button.current,
  .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: white;
    padding: 2px 5px;
  }

  .table_button__icon {
    color: var(--color-dark-blue);
    font-size: 15px;
    margin-right: 0px;
  }

  div.dataTables_wrapper div.dataTables_info {
    display: inline-block;
    float: inherit;
    padding: 13px ​10px 0px 10px;
  }
}
</style>

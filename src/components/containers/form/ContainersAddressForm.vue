<template>
  <div style="display: flex; align-items: center">
    <div style="flex: 0 1 35px" @click="activeGeoreverseHandler">
      <VueIcon
        v-b-tooltip.hover.viewport="$i18n.t('container.form.address_carto')"
        icon="mdi:map-marker-radius-outline"
        :color="iconMapReverseColor"
        :icon-style="{ fontSize: '30px' }"
        :clickable="true"
      ></VueIcon>
    </div>
    <div style="flex: auto">
      <AddressAutocomplete v-if="true" v-model="value" :skip-search="true">
      </AddressAutocomplete>
      <div
        v-for="error of filterErrors('address')"
        id="input-address-live-feedback"
        :key="error.$uid"
        class="pl-2 pr-2 invalid"
      >
        * {{ error.$message }}
      </div>
    </div>
  </div>
</template>
<script setup>
import AddressAutocomplete from '@/components/shared/AddressAutocomplete/AddressAutocomplete.vue'
import VueIcon from '@/components/shared/VueIcon.vue'
import store from '@/store'

const props = defineProps({
  value: {
    type: Object,
    default: null,
  },
  filterErrors: {
    type: Function,
    default: () => ({}),
  },
})
const activeReverseGeocoding = ref(false)
const iconMapReverseColor = ref('var(--color-main)')
const emit = defineEmits(['input'])

watch(
  () => store.state.simpliciti_map.lastReverseGeocodingFormatedAdress,
  (newValue, oldValue) => {
    if (newValue.formatted) {
      if (activeReverseGeocoding.value) {
        props.value = newValue
        emit('input', newValue)
        activeReverseGeocoding.value = false
        iconMapReverseColor.value = 'var(--color-main)'
      }
    }
  },
  {
    deep: true,
    immediate: true,
  }
)
watch(
  () => props.value,
  (newValue) => {
    emit('input', newValue)
  }
)

function activeGeoreverseHandler() {
  activeReverseGeocoding.value = true
  iconMapReverseColor.value = 'var(--color-ecoconduite)'
}
</script>
<style scoped lang="scss">
.invalid {
  border-top: thin solid #dc3545;
  color: #dc3545;
}
</style>

<template lang="pug">
.sorting_input.align-items.center
  p.m-0 {{$t('alerts.list_sorting.title')}}
  b-form-select(v-model="selected", :options="options", @input="onInput")
</template>
<script>
import i18n from '@/i18n'
export default {
  data() {
    return {
      selected: 1,
      options: [
        { value: 'NONE', text: i18n.t('alerts.list_sorting.none') },
        { value: 'RECENT', text: i18n.t('alerts.list_sorting.recent') },
        { value: 'ACK', text: i18n.t('alerts.list_sorting.ack') },
        { value: 'NOACK', text: i18n.t('alerts.list_sorting.noack') },
        { value: 'TYPE', text: i18n.t('alerts.list_sorting.type') },
        { value: 'NAME', text: i18n.t('alerts.list_sorting.name') },
        { value: 'VEHICLE', text: i18n.t('alerts.list_sorting.vehicle') },
        {
          value: 'VEHICLE_CATEGORY',
          text: i18n.t('alerts.list_sorting.vehicle_category'),
        },
      ],
    }
  },
  methods: {
    onInput(value) {
      this.$emit('input', value)
    },
  },
}
</script>
<style lang="scss" scoped>
.sorting_input {
  display: flex;
  justify-content: center;
  align-items: center;
}
p {
  white-space: pre;
  color: grey;
  font-size: 12px;
  display: flex;
  align-items: center;
  padding-right: 7px;
  align-self: center;
}
select {
  align-self: center;
  color: var(--color-black);
  font-weight: bold;
  font-size: 14px;
  border-color: rgba(47, 79, 79, 0.082);
  border: 0;
}
</style>

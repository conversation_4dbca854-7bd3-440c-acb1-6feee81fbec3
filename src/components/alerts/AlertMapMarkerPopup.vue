<template>
  <MapPopup :title="$t('common.map.popup.alert_title_module')">
    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>{{ $t('alerts.map_marker.title') }}</div>
      <i
        :class="
          !isAlertDetailsCollapsed
            ? 'fas fa-chevron-down event-popup-chevron'
            : 'fas fa-chevron-up event-popup-chevron collapsed'
        "
        :aria-expanded="!isAlertDetailsCollapsed ? 'true' : 'false'"
        aria-controls="collapse-alert-details"
        @click="() => (isAlertDetailsCollapsed = !isAlertDetailsCollapsed)"
      />
    </div>

    <b-collapse
      id="collapse-alert-details"
      v-model="isAlertDetailsCollapsed"
      :appear="true"
      :visible="true"
    >
      <div class="col-12 pl-4 pb-2">
        <div class="my-1">
          <div class="row">
            <strong data-i18n="alerts.map_maker.alert_name">
              {{ $t('alerts.map_maker.alert_name') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.title
                ? geometry.properties.title
                : $t('alerts.map_maker.no_value')
            }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong data-18n="alerts.map_maker.alert_date">
              {{ $t('alerts.map_maker.alert_date') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.datetimeFormatted
                ? geometry.properties.datetimeFormatted
                : $t('alerts.map_maker.no_value')
            }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('alerts.map_maker.alert_type') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.type
                ? geometry.properties.type
                : $t('alerts.map_maker.no_value')
            }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong data-i18n="alerts.map_maker.alert_vehicle">
              {{ $t('alerts.map_maker.alert_vehicle') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.vehicleLabel
                ? geometry.properties.vehicleLabel
                : $t('alerts.map_maker.no_value')
            }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong data-i18n="alerts.map_maker.alert_status">
              {{ $t('alerts.map_maker.alert_status') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.statusLabel
                ? geometry.properties.statusLabel
                : $t('alerts.map_maker.no_value')
            }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong data-i18n="alerts.map_maker.alert_address">
              {{ $t('alerts.map_maker.alert_address') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.address
                ? geometry.properties.address
                : $t('alerts.map_maker.no_value')
            }}
          </div>
        </div>
      </div>
    </b-collapse>
  </MapPopup>
</template>

<script>
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'
import switchablePopupsMixin from '@/mixins/switchable-popup-mixin.ts'
import { getEmptyStringIfStringIncludes } from '@/utils/string'

export default {
  name: 'AlertMapMarkerPopup',
  components: {
    MapPopup,
  },
  mixins: [switchablePopupsMixin],
  props: {
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    let spmLabelPrefix = getEmptyStringIfStringIncludes(
      this.$t('switchable_popups.label.alerts'),
      'switchable_popups'
    )
    return {
      spmLabelPrefix,
      spmLayerGroupName: 'vehicle_alerts_messages',
      isAlertDetailsCollapsed: true,
    }
  },
  computed: {
    formatDate() {
      if (this.geometry.properties.date_creation) {
        return this.$date.formatDatetime(this.geometry.properties.date_creation)
      }
      return ''
    },
  },
}
</script>

<style scoped>
.blue {
  color: var(--color-denim);
}

.small-text {
  font-size: 0.75rem;
}

.event-popup-chevron {
  position: absolute;
  right: 10px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
</style>

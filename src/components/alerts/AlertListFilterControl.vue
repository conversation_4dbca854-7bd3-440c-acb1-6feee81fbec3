<template>
  <div
    v-b-tooltip.hover.viewport
    class="alert-list-filter-control"
    :title="$t('common.results_toolbar.filter_button_tooltip')"
    @click="(e) => e.stopPropagation()"
  >
    <v-select
      ref="select"
      v-model="selectedValue"
      searcheable="false"
      :clearable="false"
      :append-to-body="true"
      :calculate-position="withPopper"
      :options="options"
      @input="onInput"
    >
      <template #search="{ attributes }">
        <FilterIcon
          class="icon"
          :fill-color="colors.color_denim"
          v-bind="attributes"
        />
      </template>
    </v-select>
  </div>
</template>

<script setup>
import { createPopper } from '@popperjs/core'
import colors from '@/styles/colors'
import FilterIcon from 'vue-material-design-icons/Filter.vue'
import { onBeforeUnmount } from 'vue'
import i18n from '@/i18n'

//Props
const props = defineProps({
  size: {
    type: Number,
    default: 16,
  },
  color: {
    type: String,
    default: '--color-black',
  },
  value: {
    type: String,
    default: '',
  },
})

//Emits
const emit = defineEmits(['input'])

//Consts
const options = [
  {
    label: i18n.t('alerts.main_search.results_filter.none'),
    value: 'NONE',
  },
  {
    label: i18n.t('alerts.main_search.results_filter.ack'),
    value: 'ACK',
  },
  {
    label: i18n.t('alerts.main_search.results_filter.noack'),
    value: 'NOACK',
  },
]
const select = ref(null)
const selectedValue = ref({})

//Computed
const style = computed(() => {
  return `font-size:${props.size}px; color: var(${props.color})`
})

//Lifecycle hooks
onMounted(() => {
  window.addEventListener('click', onClick)
})

onBeforeUnmount(() => {
  window.removeEventListener('click', onClick)
})

//Watchers
//Emit event each time selectedValue changes
// watch(selectedValue, (newValue, oldValue) => {
//   emit('input', newValue.value)
// })

//Methods
const onClick = () => {
  if (select.value) {
    select.value.open = false
  }
}

const withPopper = (dropdownList, component, { width }) => {
  /**
   * We need to explicitly define the dropdown width since
   * it is usually inherited from the parent with CSS.
   */
  dropdownList.style.width = 'fit-content'

  /**
   * Here we position the dropdownList relative to the $refs.toggle Element.
   *
   * The 'offset' modifier aligns the dropdown so that the $refs.toggle and
   * the dropdownList overlap by 1 pixel.
   *
   * The 'toggleClass' modifier adds a 'drop-up' class to the Vue Select
   * wrapper so that we can set some styles for when the dropdown is placed
   * above.
   */
  createPopper(component.$refs.toggle, dropdownList, {
    placement: 'right-end',
    modifiers: [
      {
        name: 'offset',
        options: {
          offset: [0, -1],
        },
      },
      {
        name: 'toggleClass',
        enabled: true,
        phase: 'write',
        fn({ state }) {
          component.$el.classList.toggle('drop-down', true)
        },
      },
    ],
  })
}

const onInput = (value) => {
  emit('input', value)
}
</script>
<style lang="scss">
.alert-list-filter-control em {
  color: var(--color-black);
  margin-top: 10px;
}
.alert-list-filter-control .vs__selected {
  font-size: 10px;
  display: none;
}
.alert-list-filter-control .vs__dropdown-toggle {
  border: 0px;
  width: fit-content;
  cursor: pointer;
  padding-bottom: 0px;
}
.alert-list-filter-control .vs__actions {
  display: none;
}
.alert-list-filter-control .vs--single.vs--open .vs__selected {
  display: none;
}
.alert-list-filter-control button {
  position: relative;
  bottom: -4px;
}
</style>

<template lang="pug">
    .alert-stats.container-fluid
        .row.m-0
            .col-12.alert-stats-items
                .alert-not-acknowledge.stat--item
                    .alert-not-acknowledge--title {{$t('alerts.stats.noack')}}
                    .alert-not-acknowledge--value {{notAckCount}}
                .alert-acknowledge.stat--item
                    .alert-acknowledge--title {{$t('alerts.stats.ack')}}
                    .alert-acknowledge--value {{ackCount}}
        .separator
</template>
<script>
export default {
  props: {
    items: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    ackCount() {
      return this.items.filter((item) => item.isAck).length
    },
    notAckCount() {
      return this.items.filter((item) => !item.isAck).length
    },
  },
}
</script>
<style lang="scss" scoped>
.alert-stats {
  margin: 20px 0px 0px 0px;
}
.alert-stats-items {
  display: flex;
  column-gap: 25px;
  padding: 0px 20px;
}
.stat--item {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
}
.alert-not-acknowledge--title {
  text-align: left;
  font: normal normal bold 14px/17px Open Sans;
  letter-spacing: 0px;
  color: #f6ac59;
  opacity: 1;
}
.alert-not-acknowledge--value {
  text-align: left;
  font: normal normal bold 32px/17px Open Sans;
  letter-spacing: 0px;
  color: #f6ac59;
  opacity: 1;
}
.alert-acknowledge--title {
  text-align: left;
  font: normal normal bold 14px/17px Open Sans;
  letter-spacing: 0px;
  color: #70bd95;
  opacity: 1;
}
.alert-acknowledge--value {
  text-align: left;
  font: normal normal bold 32px/17px Open Sans;
  letter-spacing: 0px;
  color: #70bd95;
  opacity: 1;
}
.separator {
  margin-top: 20px;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 2px 3px #0000005c;
  opacity: 1;
  height: 2px;
  margin-left: -15px;
  margin-right: -15px;
}
</style>

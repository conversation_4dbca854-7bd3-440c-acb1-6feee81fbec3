<template>
  <div ref="root" class="alert-table">
    <DataTable
      v-if="table"
      ref="datatable"
      class="table_theme_1"
      :ssr-paging="false"
      :ssr-show-multi-column-filter="false"
      :searching="true"
      :paging="true"
      name="alerts"
      row-id="id"
      :columns="columns"
      :column-defs="columnDefs"
      :language="datatableLanguage"
      :extra-options="extraOptions || {}"
      :select="select"
      :default-sorting-column="3"
      :auto-height="true"
      :auto-height-offset="mode === 'table' ? -60 : 0"
      @select="onSelect"
      @deselect="onDeselect"
    />
  </div>
</template>

<script setup>
/* --------------------------------------------------------------------------
 * Imports
 * -------------------------------------------------------------------------- */
import {
  inject,
  ref,
  reactive,
  computed,
  watch,
  nextTick,
  onMounted,
  onBeforeUnmount,
} from 'vue'
import DataTable from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import {
  createSortableColumnDefinition,
  createActionButtonDatatableColumn,
} from '@/utils/datatable.js'
import { shareAlertByEmail } from '@/services/alert-service.js'
import i18n from '@/i18n'
import store from '@/store'

//Inject
const acknowledgeAlert = inject('acknowledgeAlert', () => ({}))
const handleListItemZoomClick = inject('handleListItemZoomClick', () => ({}))
const $translations = inject('$translations')
const $date = inject('$date')

//Props
const props = defineProps({
  mode: {
    type: String,
    default: 'table+map',
    enum: ['table', 'table+map'],
  },
  items: {
    type: Array,
    default: () => [],
  },
  headerText: {
    type: String,
    default: '',
  },
})

//Emits
const emit = defineEmits(['SIMPLICITI_MAP_FIT_TO_BOUNDS'])

//Consts
const datatable = ref(null)
const table = ref(false)
const select = reactive({
  items: 'row',
  style: 'single',
  info: false,
})

const columns = ref([
  createActionButtonDatatableColumn({
    name: 'ShareColumn',
    components: {
      Icon: () => import('vue-material-design-icons/ShareVariant.vue'),
    },
    template: `<div v-b-tooltip.hover.viewport :title="$t('alerts.list_item.share_button_tooltip')">
                <Icon @click="handleClick" :fillColor="colors.color_denim" :size="16" style="cursor:pointer;"/>
              </div>`,
    methods: {
      handleClick() {
        shareAlertByEmail(this.row)
      },
    },
  }),
  createActionButtonDatatableColumn({
    name: 'AckColumn',
    components: {
      Icon: () => import('@c/alerts/AlertEmailIcon.vue'),
    },
    template: `<div v-if="!row.isAck" @click="handleClick" v-b-tooltip.hover.viewport :title="$t('alerts.list_item.ack_button_tooltip')">
                <Icon :fillColor="colors.color_denim" :size="16" style="cursor:pointer;"/>
              </div>`,
    methods: {
      handleClick() {
        acknowledgeAlert(this.row.message_id)
      },
    },
  }),
  createActionButtonDatatableColumn({
    name: 'ZoomColumn',
    template: `<em class="fas fa-search" @click="handleClick" :style="'cursor:pointer;color:'+colors.color_denim" v-b-tooltip.hover.viewport :title="$t('alerts.list_item.show_button_tooltip')"></em>`,
    methods: {
      handleClick() {
        handleListItemZoomClick(this.row.lat, this.row.lng)
      },
    },
  }),
  createSortableColumnDefinition(
    'timestamp',
    'dateFormatted',
    i18n.t('alerts.table_column.date')
  ),
  createSortableColumnDefinition(
    'timestamp',
    'timeFormatted',
    i18n.t('alerts.table_column.time')
  ),
  createSortableColumnDefinition(
    'title',
    'title',
    i18n.t('alerts.table_column.name')
  ),
  createSortableColumnDefinition(
    'vehicleName',
    'vehicleName',
    i18n.t('alerts.table_column.vehicle_name')
  ),
  createSortableColumnDefinition(
    'type',
    'type',
    i18n.t('alerts.table_column.type')
  ),
  createSortableColumnDefinition(
    'statusLabel',
    'statusLabel',
    i18n.t('alerts.table_column.status_column')
  ),
])

const columnDefs = ref([])

const extraOptions = reactive({
  ...datatableMixin.methods.configureColumnsFilters((filters) => [
    null,
    null,
    null,
    filters.createTextFilter({ column: 3 }),
    filters.createTextFilter({ column: 4 }),
    filters.createTextFilter({ column: 5 }),
    filters.createTextFilter({ column: 6 }),
    filters.createTextFilter({ column: 7 }),
    filters.createTextFilter({ column: 8 }),
  ]),
  ...datatableMixin.methods.configureFooter(),
  dom: 'rtip',
  pageLength: 25,
  info: true,
})

//Computed
const datatableLanguage = computed(() => ({
  ...$translations.datatable,
  emptyTable: i18n.t('alerts.no_results'),
}))

/* --------------------------------------------------------------------------
 * Functions
 * -------------------------------------------------------------------------- */
function updateTable() {
  return store.dispatch('datatable/setTableItems', {
    name: 'alerts',
    items: props.items,
  })
}

function redraw(newItems) {
  const tableEl = datatable.value?.$el?.querySelector('table')
  if (!tableEl) return
  const api = $(tableEl).DataTable()
  api.clear()
  api.rows.add(newItems)
  api.draw()
}

/* --------------------------------------------------------------------------
 * Watchers
 * -------------------------------------------------------------------------- */
watch(
  () => props.items,
  async (newItems) => {
    // met à jour le store (et retourne une Promise)
    await updateTable()

    // rafraîchit l'instance DataTables en place
    if (datatable.value) {
      await nextTick()
      redraw(newItems)
    }
  },
  { immediate: true, deep: true }
)

//Lifecycle hooks
onMounted(async () => {
  $.fn.dataTable.moment($date.getDatetimePattern())

  await updateTable()
  table.value = true
})

onBeforeUnmount(() => {
  const tableEl = datatable.value?.$el?.querySelector('table')
  if (tableEl) {
    $(tableEl).DataTable().destroy(true)
  }
})

/* --------------------------------------------------------------------------
 * Event handlers (placeholders)
 * -------------------------------------------------------------------------- */
const onDeselect = () => {}
const onSelect = ({ item }) => {}
</script>

<style lang="scss" scoped>
.text {
  font: normal normal bold 12px/17px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
  flex-basis: 350px;
  flex-shrink: 0;
}
.text,
.text span,
.text strong {
  font-size: 12px;
}

.table_toolbar {
  display: flex;
  align-items: center;
}
.alert-header-text {
  display: flex;
  column-gap: 5px;
  white-space: nowrap;
}
</style>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18.8"
    viewBox="0 0 16 12.8"
  >
    <path
      :style="style"
      d="M15.222,10.642a4.272,4.272,0,0,0-5.689,4.025,4.718,4.718,0,0,0,.057.711H2.422A1.422,1.422,0,0,1,1,13.956V5.422A1.417,1.417,0,0,1,2.422,4H13.8a1.422,1.422,0,0,1,1.422,1.422v5.22M17,13.244,13.444,16.8l-2.489-2.489,1.067-1.067,1.422,1.422,2.489-2.489L17,13.244M2.422,5.422V6.844L8.111,10.4,13.8,6.844V5.422L8.111,8.978Z"
      transform="translate(-1 -4)"
    />
  </svg>
</template>
<script>
export default {
  props: {
    fillColor: {
      type: String,
      default: 'black',
    },
  },
  computed: {
    style() {
      return this.fillColor ? `fill:${this.fillColor}` : ``
    },
  },
}
</script>

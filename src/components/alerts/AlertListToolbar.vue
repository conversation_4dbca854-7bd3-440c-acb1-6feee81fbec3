<template>
  <div class="container">
    <div class="row">
      <div class="col-lg-8 col-md-12">
        <AlertListSortingControl @input="(v) => $emit('sorting', v)" />
      </div>
      <div class="col-lg-4 col-md-12 alert-list-toolbar">
        <div
          v-b-tooltip.hover.viewport
          :title="$t('common.layout.modes.table_and_map_button_tooltip')"
        >
          <MapSearchIcon
            class="icon"
            :fill-color="colors.color_denim"
            @click="() => $emit('map')"
          />
        </div>
        <div
          v-b-tooltip.hover.viewport
          :title="$t('common.layout.modes.table_button_tooltip')"
        >
          <TableIcon
            class="icon"
            :fill-color="colors.color_denim"
            @click="() => $emit('table')"
          />
        </div>
        <AlertListFilterControl
          v-if="showFilter"
          @input="(v) => $emit('filter', v)"
        />
      </div>
    </div>
  </div>
</template>
<script>
import colors from '@/styles/colors'
import MapSearchIcon from 'vue-material-design-icons/MapSearch.vue'
import TableIcon from 'vue-material-design-icons/Table.vue'
import AlertListSortingControl from './AlertListSortingControl.vue'
import AlertListFilterControl from '@c/alerts/AlertListFilterControl.vue'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
export default {
  name: 'AlertListToolbar',
  components: {
    MapSearchIcon,
    TableIcon,
    AlertListSortingControl,
    AlertListFilterControl,
  },
  mixins: [removeNativeTooltipFromMaterialIconsMixin],
  props: {
    showFilter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      colors,
    }
  },
}
</script>
<style lang="scss">
.alert-list-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  column-gap: 5px;
}
.alert-list-toolbar .icon {
  cursor: pointer;
}
</style>

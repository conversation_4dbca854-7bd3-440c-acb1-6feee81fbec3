<template>
  <div class="alert-list-item container">
    <div class="row">
      <div class="col-2">
        <AlertCircleIcon fill-color="#f6ac59" />
      </div>
      <div class="col-7 p-0">
        <div class="alert-datetime">{{ item.datetimeFormatted }}</div>
        <div class="alert-title">{{ item.title }} ({{ item.type }})</div>
        <div class="alert-origin">Déclencheur : {{ item.vehicleLabel }}</div>
      </div>
      <div class="col-3 alert-actions-wrapper p-0">
        <!-- Share by email -->
        <div
          v-b-tooltip.hover.viewport
          :title="$t('alerts.list_item.share_button_tooltip')"
        >
          <ShareVariantIcon
            :fill-color="colors.color_denim"
            :size="16"
            @click="shareByEmail"
          />
        </div>

        <!-- ACK Alert -->
        <div
          v-if="!item.isAck"
          v-b-tooltip.hover.viewport
          :title="$t('alerts.list_item.ack_button_tooltip')"
          @click="handleAckAlert"
        >
          <AlertEmailIcon
            class="alert-email-icon"
            :fill-color="colors.color_denim"
          />
        </div>

        <!-- Zoom to item in the map -->
        <div
          v-b-tooltip.hover.viewport
          :title="$t('alerts.list_item.show_button_tooltip')"
          @click="handleListItemZoomClick(item.lat, item.lng)"
        >
          <MagnifyIcon :fill-color="colors.color_denim" :size="20" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
import AlertCircleIcon from 'vue-material-design-icons/AlertCircle.vue'
import ShareVariantIcon from 'vue-material-design-icons/ShareVariant.vue'
import MagnifyIcon from 'vue-material-design-icons/Magnify.vue'
import AlertEmailIcon from './AlertEmailIcon.vue'
import colors from '@/styles/colors'
import { shareAlertByEmail } from '@/services/alert-service.js'
export default {
  name: 'AlertListItem',
  components: {
    AlertCircleIcon,
    ShareVariantIcon,
    AlertEmailIcon,
    MagnifyIcon,
  },
  mixins: [removeNativeTooltipFromMaterialIconsMixin],
  inject: {
    acknowledgeAlert: {
      default: () => ({}),
    },
    handleListItemZoomClick: {
      default: () => ({}),
    },
  },
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      colors,
    }
  },
  methods: {
    handleAckAlert() {
      return this.acknowledgeAlert(this.item.message_id)
    },
    shareByEmail() {
      return shareAlertByEmail(this.item)
    },
  },
}
</script>
<style lang="scss" scoped>
.alert-datetime {
  font: normal normal normal 11px/16px Open Sans;
  letter-spacing: 0px;
  color: var(--color-metal-rock);
}
.alert-title {
  font: normal normal bold 12px/16px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.alert-origin {
  font: normal normal normal 12px/16px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.alert-actions-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 5px;
  span,
  svg {
    cursor: pointer;
  }
}
.alert-email-icon {
  position: relative;
  top: 3px;
}
.alert-list-item {
  padding-bottom: 10px;
  border-bottom: 2px solid #00000030;
  margin-bottom: 2px;
}
</style>

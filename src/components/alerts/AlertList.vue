<template>
  <div infinite-wrapper class="scroll-wrapper">
    <AlertListItem v-for="item in loadedItems" :key="item.id" :item="item" />

    <InfiniteLoading
      :identifier="infiniteLoadingIdentifier"
      force-use-infinite-wrapper
      @infinite="loadMoreItems"
    >
      <template #no-results><span></span></template>
      <template #no-more><span></span></template>
    </InfiniteLoading>
  </div>
</template>
<script setup>
import InfiniteLoading from 'vue-infinite-loading'
import { throttle } from '@/utils/async'
import { sortBy } from 'ramda'

const infiniteLoadingIdentifier = ref(Date.now())
const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const itemsPerLoad = 20
const loadedItems = ref([])
const filteredItems = computed(() => props.items)
const areFiltersPresent = computed(() => false)

/**
 * Restart the infinite list loader plugin but avoid multiple resets within 1s
 */
const throttleReset = throttle(() => {
  loadedItems.value = []
  infiniteLoadingIdentifier.value = Date.now()
}, 1000)

/**
 * Sort prop items into new var sortedItems but throttle 1s
 */
const throttleSort = throttle(() => {
  loadedItems.value = sortBy((item) => item.timestamp, loadedItems.value)
}, 1000)

watch(
  () => props.items,
  () => {
    throttleReset()
  },
  {
    immediate: true,
    deep: true,
  }
)

function loadMoreItems($state) {
  console.debugVerbose(2, 'loadMoreItems', {
    $state,
  })

  const itemsToLoad = Math.min(
    itemsPerLoad,
    filteredItems.value.length - loadedItems.value.length
  )

  if (itemsToLoad === 0) {
    $state && $state.complete()
    return
  }

  const existingItemIds = new Set(loadedItems.value.map((item) => item.id))
  const uniqueNewItems = []

  for (
    let i = loadedItems.value.length;
    i < filteredItems.value.length && uniqueNewItems.length < itemsToLoad;
    i++
  ) {
    const item = filteredItems.value[i]
    if (!existingItemIds.has(item.id)) {
      uniqueNewItems.push(item)
      existingItemIds.add(item.id)
    }
  }

  loadedItems.value = loadedItems.value.concat(uniqueNewItems)
  throttleSort()
  $state && $state.loaded()
}
</script>

<style scoped>
.scroll-wrapper {
  max-height: calc(80vh);
  overflow: auto;
}
</style>

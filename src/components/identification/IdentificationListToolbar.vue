<template>
  <div class="container">
    <div class="row">
      <div class="col-12 identification-list-toolbar">
        <div
          v-if="showSearchIcon"
          v-b-tooltip.hover.left.viewport
          :title="$t('common.layout.modes.table_and_map_button_tooltip')"
        >
          <em
            class="zoom-icon fas fa-search"
            :style="iconStyle"
            @click="() => $emit('map')"
          ></em>
        </div>
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
import colors from '@/styles/colors'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'

export default {
  name: 'IdentificationListToolbar',
  mixins: [removeNativeTooltipFromMaterialIconsMixin],
  props: {
    color: {
      type: String,
      default: '',
    },
    showSearchIcon: {
      // Nouvelle prop pour contrôler l'affichage de la loupe
      type: Boolean,
      default: true, // Par défaut, l'icône s'affiche
    },
  },
  data() {
    return {
      colors,
    }
  },
  computed: {
    iconColor() {
      return this.color || colors.color_denim
    },
    iconStyle() {
      return `color:${this.iconColor}`
    },
  },
}
</script>

<style lang="scss">
.identification-list-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  column-gap: 5px;
}
.identification-list-toolbar .icon {
  cursor: pointer;
}

.zoom-icon {
  cursor: pointer;
  font-size: 15px;
  position: relative;
  top: 2px;
  color: var(--color-denim);
}
</style>

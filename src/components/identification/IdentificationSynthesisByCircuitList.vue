<template>
  <div class="row">
    <div v-if="false" class="col-12">
      <SectionTitleWrapper
        class="align-items-center"
        :text="$t('identification.synthesis_section.title')"
      >
        <template #icon>
          <RoadVariantIcon class="mr-1" :fill-color="iconColor" :size="24" />
        </template>
      </SectionTitleWrapper>
    </div>

    <div v-if="synthesis.length === 0" class="col">
      <span>{{ $t('identification.no_results') }}</span>
    </div>

    <div v-if="synthesis.length > 0" class="col-12 pl-4">
      <div class="history-tl-container">
        <ul class="tl">
          <li
            v-for="(item, index) in synthesis"
            :key="index"
            class="tl-item"
            :class="getItemClass(item)"
            @click="handleItemClick(item, $event)"
          >
            <div class="tl-item-icon">
              <img
                :src="
                  item.circuitName
                    ? './lib/realtimeMap/assets/picto_identification_bac/circuit_identified.svg'
                    : './lib/realtimeMap/assets/picto_identification_bac/circuit_no_identified.svg'
                "
                width="24px"
              />
            </div>

            <div class="item-wrapper">
              <div class="row">
                <div class="col-12">
                  <SectionField
                    is-row
                    :title="$t('common.Circuit') + ' :'"
                    title-class="text-bold"
                    :value="item.circuitName | circuitName"
                    :tooltip="item.circuitName"
                    value-class="pl-1"
                    value-style="max-width:150px;line-height:12px;"
                  >
                    <IdentificationListToolbar :show-search-icon="false" />
                  </SectionField>
                </div>
              </div>
              <div class="row">
                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_levees_count'
                      ) + ' :'
                    "
                    :value="item.leveesTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_identified_levees_count'
                      ) + ' :'
                    "
                    :value="item.identifiedTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_not_identified_levees_count'
                      ) + ' :'
                    "
                    :value="item.unidentifiedTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_stoped_levees_count'
                      ) + ' :'
                    "
                    :value="item.stoppedTotal"
                    value-class="pl-1"
                  />
                </div>
                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_total_weight'
                      ) + ' :'
                    "
                    :value="item.leveesWeightTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_high_point_levees_count'
                      ) + ' :'
                    "
                    :value="item.highPointTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_underweight_total'
                      ) + ' :'
                    "
                    :value="item.underWeightTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_blacklisted_count'
                      ) + ' :'
                    "
                    :value="item.blacklistedTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    :title-style="titleStyle"
                    :value-style="valueStyle"
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_aboveweight_total'
                      ) + ' :'
                    "
                    :value="item.aboveWeightTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import RoadVariantIcon from 'vue-material-design-icons/TimelineClock.vue'
import SectionField from '@c/shared/SectionField.vue'
import SectionTitleWrapper from '@c/shared/SectionTitle/SectionTitleWrapper.vue'
import IdentificationListToolbar from '@c/identification/IdentificationListToolbar.vue'
import i18n from '@/i18n'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'

export default {
  name: 'IdentificationSynthesisByCircuitList',
  components: {
    RoadVariantIcon,
    SectionField,
    SectionTitleWrapper,
    IdentificationListToolbar,
  },
  filters: {
    circuitName(value) {
      if (!value) {
        return i18n.t('identification.synthesis_section.no_circuit')
      }
      if (value.length > 40) {
        return value.substring(0, 40) + '...'
      } else {
        return value
      }
    },
  },
  mixins: [removeNativeTooltipFromMaterialIconsMixin],
  props: {
    synthesis: {
      type: Array,
      //default: () => []
      default: () => [
        {
          circuitId: '',
          circuitName: '',
          leveesTotal: 3,
          leveesWeightTotal: 0,
          identifiedTotal: 2,
          unidentifiedTotal: 1,
          authorizedTotal: 1,
          unauthorizedTotal: 2,
          collectedTotal: 1,
          uncollectedTotal: 2,
          highPointTotal: 0,
          nonHighPointTotal: 3,
          stoppedTotal: 2,
          blacklistedTotal: 1,
        },
        {
          circuitId: '37908',
          circuitName: '1 TSH MICRO OM',
          leveesTotal: 350,
          leveesWeightTotal: 0,
          identifiedTotal: 349,
          unidentifiedTotal: 1,
          authorizedTotal: 347,
          unauthorizedTotal: 3,
          collectedTotal: 347,
          uncollectedTotal: 3,
          highPointTotal: 0,
          nonHighPointTotal: 350,
          stoppedTotal: 3,
          blacklistedTotal: 2,
        },
      ],
    },
    iconColor: {
      type: String,
      default: 'var(--color-dark-blue)',
    },
    highlightedCircuitId: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      titleStyle: 'width:100px',
      valueStyle: 'width: 20px; text-align:right;',
      activeItem: null,
    }
  },
  watch: {
    highlightedCircuitId() {
      this.$forceUpdate()
    },
  },
  methods: {
    getItemClass(item) {
      return `${
        (!!item.circuitId.toString() &&
          item.circuitId.toString() == this.highlightedCircuitId.toString()) ||
        (!item.circuitId.toString() &&
          this.highlightedCircuitId.toString() == 'x')
          ? 'active'
          : ''
      }`
    },
    handleItemClick(item, event) {
      // Emit event to parent
      this.$emit('map', { circuitId: item.circuitId, $event: event })
    },
  },
}
</script>

<style lang="scss" scoped>
.blue {
  color: #164470;
}

.small-text {
  font-size: 0.75rem;
}

.history-tl-container {
  font-family: 'Roboto', sans-serif;
  width: 100%;
  margin: auto;
  display: block;
  position: relative;
}

.history-tl-container ul.tl {
  margin: 20px 0;
  padding: 0;
  display: inline-block;
}

.history-tl-container ul.tl li {
  list-style: none;
  margin: auto;
  min-height: 50px;
  border-left: 2px solid grey;
  padding: 0 0 20px 30px;
  position: relative;
}

.history-tl-container ul.tl li:last-child {
  border-left: 2px solid white;
}

.history-tl-container ul.tl li:hover::before {
  border-color: grey;
  transition: all 1000ms ease-in-out;
}

.tl-item:hover,
.tl-item.active {
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 10px;
}

.tl-item-icon {
  width: 20px;
  position: absolute;
  left: -11px;
  top: -5px;
  display: flex;
  justify-content: center;
  background-color: white;
}

.chrono-title {
  width: 100%;
  display: flex;
}

.tl {
  width: 100%;
}

ul.tl li .item-detail {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
</style>

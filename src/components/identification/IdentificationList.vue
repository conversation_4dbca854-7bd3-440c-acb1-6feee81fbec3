<template>
  <div>
    <div class="row m-0 p-0">
      <div
        v-for="(item, index) in itemsGroupedByDate"
        :key="item.dateFormatted"
        class="date-item col-12 m-0 p-0"
        :class="{ active: isDateActive(item.dateFormatted) }"
        @click.stop="
          handleToolbarClick($event, 'map', 'date', item, {
            dateFormatted: item.dateFormatted,
          })
        "
      >
        <div
          class="date-item-header row m-0 p-0"
          style="cursor: pointer"
          @click="toggleSectionUnfoldState(item.dateFormatted)"
        >
          <div
            class="date-title-wrapper"
            :class="{ disabled: isFetchDetailsInProgress }"
          >
            <span class="date-title">{{ item.dateFormatted }}</span>

            <span class="date-subtitle"
              >({{ dateItemSubtitle[index].subtitle }})</span
            >
          </div>
          <div class="line-toolbar">
            <div v-if="clickedLine === item.dateFormatted" class="spinner">
              <b-spinner
                class="loader"
                variant="info"
                style="
                  width: 1rem;
                  height: 1rem;
                  margin: 0 auto;
                  display: block;
                  font-size: 14px;
                "
              />
            </div>
            <IdentificationListToolbar
              :show-search-icon="false"
              :color="'white'"
              @map="handleToolbarClick('map', 'date', item)"
              @table="handleToolbarClick('table', 'date', item)"
              @markers="handleToolbarClick('markers', 'date', item)"
            >
              <em
                class="date-item-icon fas"
                :class="{
                  'fa-chevron-up': isSectionUnfolded(item.dateFormatted),
                  'fa-chevron-down': !isSectionUnfolded(item.dateFormatted),
                }"
                style="top: 1px; position: relative"
              ></em>
            </IdentificationListToolbar>
          </div>
        </div>

        <div v-show="isSectionUnfolded(item.dateFormatted)" class="row m-0 p-0">
          <div
            v-for="vehicleItem in item.items"
            :key="vehicleItem.vehicleId"
            class="vehicle-item col-12 p-0"
            style="cursor: pointer"
            @click.stop="
              handleToolbarClick($event, 'map', 'vehicle', vehicleItem, {
                dateFormatted: item.dateFormatted,
              })
            "
          >
            <div
              class="vehicle-item-header row m-0 p-0"
              :class="{
                active: isVehicleActive(
                  item.dateFormatted,
                  vehicleItem.vehicleId
                ),
                disabled: isFetchDetailsInProgress,
              }"
            >
              <div
                class="vehicle-title"
                :class="{ disabled: isFetchDetailsInProgress }"
              >
                {{ vehicleItem.vehicleName }}
              </div>
              <div
                class="line-toolbar"
                :class="{ disabled: isFetchDetailsInProgress }"
              >
                <div
                  v-if="clickedLine === vehicleItem.vehicleId"
                  class="spinner"
                >
                  <b-spinner
                    class="loader"
                    variant="info"
                    style="
                      width: 1rem;
                      height: 1rem;
                      margin: 0 auto;
                      display: block;
                      font-size: 14px;
                    "
                  />
                </div>
                <IdentificationListToolbar :show-search-icon="false">
                  <em
                    class="fas"
                    :class="{
                      'fa-chevron-up': isSectionUnfolded(
                        vehicleItem.dateFormatted,
                        vehicleItem.vehicleId
                      ),
                      'fa-chevron-down': !isSectionUnfolded(
                        vehicleItem.dateFormatted,
                        vehicleItem.vehicleId
                      ),
                    }"
                    style="top: 1px; position: relative; cursor: pointer"
                    @click.stop="
                      toggleSectionUnfoldState(
                        vehicleItem.dateFormatted,
                        vehicleItem.vehicleId
                      )
                    "
                  ></em>
                </IdentificationListToolbar>
              </div>
            </div>

            <div
              v-show="
                isSectionUnfolded(item.dateFormatted, vehicleItem.vehicleId)
              "
              class="col-12"
            >
              <IdentificationSynthesisByCircuitList
                class="mt-2"
                :synthesis="vehicleItem.synthesis"
                :highlighted-circuit-id="
                  getHighlightedCircuitId(
                    item.dateFormatted,
                    vehicleItem.vehicleId
                  )
                "
                @map="
                  ({ circuitId, $event }) =>
                    delegateToolbarAction(
                      $event,
                      'map',
                      'circuit',
                      vehicleItem,
                      {
                        circuitId,
                        dateFormatted: item.dateFormatted,
                      }
                    )
                "
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import IdentificationListToolbar from '@c/identification/IdentificationListToolbar.vue'
import IdentificationSynthesisByCircuitList from '@c/identification/IdentificationSynthesisByCircuitList.vue'
import { mapGetters } from 'vuex'
import { getFormattedSelectedTimeRangeStringByDate } from '@/utils/dates'
import moment from 'moment'
import { useDateItemSubtitle } from '@/composables/useDateItemSubtitle'

export default {
  name: 'IdentificationList',
  components: {
    IdentificationListToolbar,
    IdentificationSynthesisByCircuitList,
  },
  props: {
    items: {
      type: Array,
      default: () => [],
    },
    /**
     * i.g
     *  total_x_x_x
     *  date_11/01/2022_x_x
     *  vehicle_11/01/2020_23_x
     *  circuit_11/01/2022_23_453
     */
    highlightedSelectionId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      unfoldStates: {},
      unfoldStatesUpdateCount: 0,
      clickedLine: null,
    }
  },
  computed: {
    itemsGroupedByDate() {
      let grouped = []
      this.items.forEach((item) => {
        let match = grouped.find((g) => g.dateFormatted === item.dateFormatted)
        if (!match) {
          grouped.push({
            dateFormatted: item.dateFormatted,
            items: [],
          })
          match = grouped.find((g) => g.dateFormatted === item.dateFormatted)
        }
        match.items.push(item)
      })
      return grouped
    },
    ...mapGetters({
      isFetchDetailsInProgress: 'identification/isFetchDetailsInProgress',
    }),
    dateItemSubtitle() {
      return useDateItemSubtitle(this.itemsGroupedByDate, 'dateFormatted')
    },
  },
  watch: {
    highlightedSelectionId() {
      this.$forceUpdate()
    },
    isFetchDetailsInProgress: function (newVal, oldVal) {
      if (newVal === false) {
        console.log('fetch is over')
        this.clickedLine = null
      }
    },
  },
  methods: {
    getFormattedSelectedTimeRangeStringByDate,
    isDateActive(formattedDate = '') {
      let parts = this.highlightedSelectionId.split('_')
      return (
        ['date', 'vehicle', 'circuit'].includes(parts[0]) &&
        parts[1] == formattedDate
      )
    },
    isVehicleActive(formattedDate = '', vehicleId = '') {
      let parts = this.highlightedSelectionId.split('_')
      return (
        ['vehicle', 'circuit'].includes(parts[0]) &&
        parts[1] == formattedDate &&
        parts[2] == vehicleId
      )
    },
    getHighlightedCircuitId(formattedDate = '', vehicleId = '') {
      let parts = this.highlightedSelectionId.split('_')

      return parts[0] === 'circuit' &&
        parts[1] === formattedDate &&
        parts[2] === vehicleId.toString()
        ? parts[3]
        : ''
    },
    isSectionUnfolded(identifier, suffix = '') {
      return this.unfoldStates[`section_${identifier}_${suffix}`] === true
    },
    toggleSectionUnfoldState(identifier, suffix = '') {
      this.$set(
        this.unfoldStates,
        `section_${identifier}_${suffix}`,
        !this.unfoldStates[`section_${identifier}_${suffix}`]
      )
    },
    handleToolbarClick(event, action, type, item, metadata = {}) {
      this.setClickedLine(type, item)
      this.delegateToolbarAction(event, action, type, item, (metadata = {}))
    },
    setClickedLine(type, item) {
      // Track clicked item
      this.clickedLine = type === 'date' ? item.dateFormatted : item.vehicleId
    },
    delegateToolbarAction(event, action, type, item, metadata = {}) {
      if (event?.stopPropagation) {
        event.stopPropagation()
      }

      this.$emit('toolbar', {
        action,
        type,
        dateFormatted: item.dateFormatted || metadata.dateFormatted,
        vehicleId: item.vehicleId || null,
        circuitId: item.circuitId || metadata.circuitId || '',
      })
    },
    getDateResultSubtitle(date) {
      let momentDate = moment(date).format('YYYY-MM-DD')

      return `(${getFormattedSelectedTimeRangeStringByDate(momentDate)})`
    },
  },
}
</script>

<style lang="scss" scoped>
.date-item {
  background-color: var(--color-denim);
}
.date-item.active {
  background-color: #085587;
}
.date-item-header {
  border-bottom: 2px solid rgb(255 255 255 / 54%);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vehicle-item-header {
  box-shadow: 0px 2px 3px rgb(0 0 0 / 21%);
}
.vehicle-item-header.active {
  background-color: rgb(0 0 0 / 7%);
}
.date-item-header,
.vehicle-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.vehicle-title {
  font: normal normal bold 14px/29px Open Sans;
  padding: 0 20px;
}
.date-title-wrapper {
  display: flex;
  justify-content: space-between;
  column-gap: 5px;
  align-items: center;
  padding: 0 10px;
}
.date-title {
  font: normal normal bold 14px/29px Open Sans;
}
.date-subtitle {
  font: normal normal normal 13px/29px Open Sans;
}
.date-title,
.date-subtitle,
.date-item-icon {
  color: #ffffff;
}
.line-toolbar {
  display: flex;
  align-items: center;
}
.vehicle-item {
  background-color: white;
  cursor: pointer;
}
.vehicle-item-header:hover,
.date-item-header:hover {
  background-color: rgb(0 0 0 / 7%);
}
.disabled {
  opacity: 0.6;
  cursor: auto;
}
</style>

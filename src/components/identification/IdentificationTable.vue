<template lang="pug">
  .identification_table(ref="root")
    .row.p-0.m-0
      .col-6.m-0.header-title-wrapper
        .header-title(v-html="headerText")
      .col-6.p-0.m-0
    LoaderSpinner.mt-2(v-show="!table")
    DataTable(
      v-if="table"
      class="table_theme_1"
      ref="datatable",
      :ssrPaging="false",
      :ssrShowMultiColumnFilter="false",
      :searching="true",
      :paging="true",
      name="identification",
      rowId="id",
      scrollY="250px",
      :columns="columns",
      :columnDefs="columnDefs",
      :language="$translations.datatable",
      :extraOptions="extraOptions || {}",
      :select="select",
      :defaultSortingColumn="2"
      :defaultSortingColumnDirection="'desc'"
      @select="onSelect"
      @deselect="onDeselect"
      :autoHeight="true"
      :autoHeightOffset="mode==='table'?0:0"
      @init="onTableInit"
    )
</template>
<script>
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import mapMixin from '@/mixins/map.js'
import TableModesToolbar from '@c/shared/TableModesToolbar/TableModesToolbar.vue'
import { createSortableColumnDefinition } from '@/utils/datatable.js'
import { mapGetters } from 'vuex'
import {
  getChairSideOptions,
  getHighpointOptions,
} from '@/composables/useIdentificationModule'

export default {
  name: 'IdentificationTable',
  components: {
    DataTable,
    TableModesToolbar,
  },
  mixins: [datatableMixin, mapMixin],
  provide() {
    return {
      /**
       * - Improves table loading performance if > 1k records.
       * - Shows a loader in the bottom-right corner
       */
      vueDataTableDrawOptions: {
        batchSize: 250,
        showLoader: false,
        cooldownTimeoutMs: 2000,
        asyncRender: {
          timeoutMs: 250,
          progresiveLoader: true,
        },
      },
    }
  },
  props: {
    mode: {
      type: String,
      default: 'table+map',
      enum: ['table', 'table+map'],
    },
    items: {
      type: Array,
      default: () => [],
    },
    headerText: {
      type: String,
      default: '',
    },
  },
  data() {
    var self = this
    return {
      isModalOpen: false,
      showInformationsPopup: false,
      table: false,
      select: {
        items: 'row',
        style: 'single',
        info: false,
      },
      columns: [
        {
          orderable: false,
          render: createComponentRender({
            name: 'bacIcon',
            template: `
              <div style="display: flex;justify-content: flex-start;align-items: center;column-gap: 3px;">
                <img style="width:18px;height:18px" v-if="!this.row.isStopped" src="/lib/realtimeMap/assets/picto_identification_bac/bac_identified.svg"/>
                <img style="width:18px;height:18px" v-if="this.row.isStopped" src="/lib/realtimeMap/assets/picto_identification_bac/bac_no_identified.svg"/>
                <div v-show="this.row.hasInvalidCoords"
                  v-b-tooltip.hover.viewport
                  :title="$t('common.invalid_position')"
                >
                  <em  style="color: var(--color-sandy-brown);" class="fa fa-exclamation-triangle"></em>
                </div>
              </div>
            `,
            /**template: `<div>
             <svg
             :fill="!this.row.isStopped ? '#014470' : 'red'"
             class="material-design-icon__svg"
             v-b-tooltip.hover.right
             :title="!this.row.isStopped ? $t('location_module.identification_bacs.bac_collected') : $t('location_module.identification_bacs.bac_uncollected')"
             :width="20"
             :height="20"
             viewBox="0 0 24 24">
             <path class="icon-svg" d="M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,12.5A1.5,1.5 0 0,1 10.5,11A1.5,1.5 0 0,1 12,9.5A1.5,1.5 0 0,1 13.5,11A1.5,1.5 0 0,1 12,12.5M12,7.2C9.9,7.2 8.2,8.9 8.2,11C8.2,14 12,17.5 12,17.5C12,17.5 15.8,14 15.8,11C15.8,8.9 14.1,7.2 12,7.2Z">
             </path>
             </svg>
             </div>`,**/
          }),
        },
        {
          orderable: false,
          render: createComponentRender({
            name: 'eyeIcon',
            template: `<em @click.self="showBacInformationModal" class="fas fa-eye" style="cursor:pointer;"></em>`,
            methods: {
              showBacInformationModal(e) {
                e.stopPropagation()
                self.showBacModal(true, this.row)
              },
            },
          }),
        },
        createSortableColumnDefinition(
          `timestamp`,
          `formattedDate`,
          this.$t('common.date')
        ),
        createSortableColumnDefinition(
          `timeUnix`,
          `formattedTime`,
          this.$t('common.hour')
        ),
        createSortableColumnDefinition(
          `vehicleName`,
          `vehicleName`,
          this.$t('common.Véhicule')
        ),
        createSortableColumnDefinition(
          `circuitName`,
          `circuitName`,
          this.$t('common.Circuit')
        ),
        createSortableColumnDefinition(
          `puceNumber`,
          `puceNumber`,
          this.$t('location_module.identification_bacs.table.puce')
        ),
        createSortableColumnDefinition(
          `memoryPuceNumber`,
          `memoryPuceNumber`,
          this.$t('location_module.identification_bacs.table.memory_puce')
        ),
        createSortableColumnDefinition(
          `weight`,
          `weight`,
          this.$t('location_module.identification_bacs.table.weight')
        ),
        createSortableColumnDefinition(
          `formattedAddress`,
          `formattedAddress`,
          this.$t('common.Adresse')
        ),
        createSortableColumnDefinition(
          `chairType`,
          `chairType`,
          this.$t('location_module.identification_bacs.table.chair_type')
        ),
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_identified'
          ),
          orderable: true,
          sort: 'isIdentified',
          render: createComponentRender(
            {
              name: 'BacIsIdentified',
              template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
              methods: {
                setIcon(item) {
                  return item.isIdentified ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isIdentified ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return row.isIdentified ? 'true' : 'false'
              },
            }
          ),
        },
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_stopped'
          ),
          orderable: true,
          sort: 'isStopped',
          render: createComponentRender(
            {
              name: 'BacIsStopped',
              template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
              methods: {
                setIcon(item) {
                  return item.isStopped ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isStopped ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return row.isStopped ? 'true' : 'false'
              },
            }
          ),
        },
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_highpoint'
          ),
          orderable: true,
          sort: 'isHighPoint',
          render: createComponentRender(
            {
              name: 'BacIsHighPoint',
              template: `<div>
                <em v-if="typeof this.row.isHighPoint==='boolean'" :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>
                <span v-if="typeof this.row.isHighPoint!=='boolean'">{{this.row.isHighPoint}}</span>
              </div>`,
              methods: {
                setIcon(item) {
                  return item.isHighPoint ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isHighPoint ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return typeof row.isHighPoint === 'boolean'
                  ? row.isHighPoint
                    ? 'true'
                    : 'false'
                  : row.isHighPoint
              },
            }
          ),
        },
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_blacklisted'
          ),
          orderable: true,
          sort: 'isBlacklisted',
          render: createComponentRender(
            {
              name: 'BacIsBlacklisted',
              template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
              methods: {
                setIcon(item) {
                  return item.isBlacklisted ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isBlacklisted ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return row.isBlacklisted ? 'true' : 'false'
              },
            }
          ),
        },
      ],
      columnDefs: [
        /* {
          targets: 8,
          visible: false, //This create problems with column filters
        },*/
      ],
      extraOptions: {
        ...this.configureColumnsFilters((filters) => {
          return [
            null,
            null,
            filters.createTextFilter({ column: 2 }),
            filters.createTextFilter({ column: 3 }),
            filters.createTextFilter({ column: 4 }),
            filters.createTextFilter({ column: 5 }),
            filters.createTextFilter({ column: 6 }),
            filters.createTextFilter({ column: 7 }),
            filters.createTextFilter({ column: 8 }),
            filters.createTextFilter({ column: 9 }),
            filters.createSelectFilter({
              column: 10, // chairType
              selectOptions: getChairSideOptions(),
            }),
            filters.createSelectFilter({
              column: 11,
              isBoolean: true,
            }),
            filters.createSelectFilter({
              column: 12,
              isBoolean: true,
            }),
            filters.createSelectFilter({
              column: 13,
              selectOptions: getHighpointOptions(),
            }),
            filters.createSelectFilter({
              column: 14,
              isBoolean: true,
            }),
          ]
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      },
    }
  },
  computed: {
    shouldShowMemoryPuceColumn() {
      return !!this.items.find((item) => !!item.memoryPuceNumber)
    },
    ...mapGetters({
      selectedItem: 'identification/identificationBacSelected',
    }),
  },
  watch: {
    items() {
      this.updateTable()
      this.updateMemoryPuceColumnVisibility()
    },
    selectedItem() {
      this.selectRowUsingAttribute(parseInt(this.selectedItem), 'id', false)
    },
  },
  mounted() {
    $.fn.dataTable.moment(this.$date.getDatetimePattern())
    this.updateTable()
    setTimeout(() => {
      this.table = true
      setTimeout(() => {
        if (this.$refs.datatable && this.$refs.datatable.datatable) {
          this.$refs.datatable.datatable.draw()
        }
      }, 500)
    }, 200)
  },
  destroyed() {
    this.$store.commit('location_module/SET_IDENTIFICATION_BAC_MODAL', false)
  },
  methods: {
    onTableInit() {
      this.updateMemoryPuceColumnVisibility()
    },
    /**
     * @todo Refactor/Improve Toggling a column brokes column filters
     */
    updateMemoryPuceColumnVisibility() {
      /* this.$nextTick(() =>
        this.$refs.datatable.toggleColumnVisible(
          8,
          this.shouldShowMemoryPuceColumn
        )
      );*/
      /*
      //Hide column using pure javascript
      this.$nextTick(() => {
        document.querySelectorAll("table").forEach((t) =>
          t.querySelectorAll("tr").forEach((tr) => {
            tr.children[8].style.display = "none";
          })
        );
        this.$refs.datatable.datatable.draw(false);
      });*/
    },
    onDeselect() {
      /*this.$mitt.emit(
        'SIMPLICITI_MAP_FIT_TO_BOUNDS',
        this.items.map((item) => [item.lat, item.lng])
      )*/
    },
    async onSelect({ item }) {
      await this.fitBoundsAndOpenPopup(
        { lat: item.lat, lng: item.lng },
        'bacs_markers',
        item.id
      )

      this.$store.dispatch(
        'identification/setIdentificationBacSelected',
        item.id
      )
    },
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'identification',
        items: this.items,
      })
    },
    showBacModal(value, item) {
      this.$store
        .dispatch('location_module/getIdentificationBacModalDetails', item)
        .then(() => {
          this.$store.commit(
            'location_module/SET_IDENTIFICATION_BAC_MODAL',
            value
          )
        })
        .catch((error) => {
          console.log(error)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.title,
.title span,
.title strong {
  font-size: 12px;
}
.identification_table {
  height: 100%;
  height: -moz-available; /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available; /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}
.table_toolbar {
  display: flex;
  align-items: center;
}
.header-title-wrapper {
  display: flex;
  align-items: center;
}
.header-title {
  font-size: 12px;
}
</style>

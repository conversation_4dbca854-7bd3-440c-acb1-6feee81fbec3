<template>
  <div v-if="getCircuitPolylines.error" class="alert-danger col-12">
    {{ $t('ecoconduite.error') }}
  </div>
  <div v-else class="col-12">
    <div class="row mt-3 mb-3">
      <div class="col-12">
        <!-- Map -->
        <h5 class="mt-4 ecoTitle">
          {{ $t('ecoconduite.map.title') }}
          <b-spinner v-if="loading" small label="Small Spinner" />
        </h5>
      </div>
    </div>

    <div v-if="empty" class="alert-warning">
      {{ $t('ecoconduite.map.empty') }}
    </div>
    <div v-else class="row mt-3 mb-3">
      <div id="ecoconduiteMap" ref="mapElement" class="col-12 ecoconduite_map">
        <div class="ecoconduite_map__overlay" />
        <LMap
          ref="map"
          style="height: 80%; width: 100%"
          class="ecoconduite_map__map"
          :zoom="zoom"
          :center="center"
          :bounds="bounds"
          :options="options"
          @update:center="centerUpdate"
          @update:zoom="zoomUpdate"
          @update:bounds="boundsUpdated"
          @ready="waitForMap"
        >
          <LTileLayer :url="url" :attribution="attribution" />
          <!--          <LMarker-->
          <!--            v-for="(marker, index) in markersList"-->
          <!--            ref="marker"-->
          <!--            :key="index"-->
          <!--            :lat-lng="marker.latlng"-->
          <!--            :icon="marker.icon"-->
          <!--          />-->
          <LControlZoom position="bottomright" />
          <LPolyline
            v-for="(polyline, index) in getCircuitPolylines.coords"
            :key="index"
            :lat-lngs="[
              [polyline.startLat, polyline.startLng],
              [polyline.endLat, polyline.endLng],
            ]"
            :color="polyline.color"
          />
          <LControl position="topleft">
            <div id="legende" class="container">
              <div class="row pt-2 pb-1">
                <div class="col-11 m-0 p-0">
                  <b>{{ $t('ecoconduite.map.reference') | capitalize }}</b>
                  {{
                    parseNullValue(infos.consumptionAverage)
                      | floor(1)
                      | pointToComma
                  }}l/100km (FMS)
                </div>
                <div v-b-toggle.collapse-1 class="col-1 m-0 p-0">
                  <span class="when-opened">
                    <i class="fas fa-chevron-up" />
                  </span>
                  <span class="when-closed">
                    <i class="fas fa-chevron-down" />
                  </span>
                </div>
              </div>
              <b-collapse id="collapse-1" visible class="pb-2">
                <div class="row">
                  <div class="col-12">
                    <h6>{{ $t('ecoconduite.map.legend') }}</h6>
                  </div>
                </div>
                <div class="row m-0">
                  <div class="col-1 m-0 p-0">
                    <i
                      class="fas fa-gas-pump pl-1 pr-1"
                      style="display: inline-block"
                    />
                  </div>
                  <div class="col-11 m-0 p-0">
                    <div class="container m-0">
                      <div class="row">
                        <div
                          v-for="(
                            value, index
                          ) in getCircuitPolylines.rangeValues"
                          :key="index"
                          class="legvalues col-2"
                        >
                          {{ value }}
                        </div>
                      </div>
                      <div class="row">
                        <div
                          v-for="(color, index) in colors"
                          :key="index"
                          class="legcolors col-2"
                          :style="{ background: color }"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </b-collapse>
            </div>
          </LControl>
        </LMap>
      </div>
      <!--      <div class="col-12 mt-5">-->
      <!--        Zoom: {{ currentZoom }} - Center : {{ currentCenter }}-->
      <!--        <div id="ecoconduiteMap" ref="mapElement" class="ecoconduite_map">-->
      <!--          <div class="ecoconduite_map__overlay" />-->
      <!--          <LMap-->
      <!--            ref="map"-->
      <!--            class="ecoconduite_map__map"-->
      <!--            :options="mapOptions"-->
      <!--            @update:center="centerUpdate"-->
      <!--            @update:zoom="zoomUpdate"-->
      <!--            @ready="waitForMap"-->
      <!--          >-->
      <!--            <LTileLayer :url="url" :attribution="attribution" />-->
      <!--            &lt;!&ndash;            <LMarker&ndash;&gt;-->
      <!--            &lt;!&ndash;              v-for="(marker, index) in markersList"&ndash;&gt;-->
      <!--            &lt;!&ndash;              ref="marker"&ndash;&gt;-->
      <!--            &lt;!&ndash;              :key="index"&ndash;&gt;-->
      <!--            &lt;!&ndash;              :lat-lng="marker.latlng"&ndash;&gt;-->
      <!--            &lt;!&ndash;              :icon="marker.icon"&ndash;&gt;-->
      <!--            &lt;!&ndash;            />&ndash;&gt;-->
      <!--          </LMap>-->
      <!--        </div>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script>
import {
  LMap,
  LTileLayer,
  LControlZoom,
  LPolyline,
  LControl,
} from 'vue2-leaflet'
import moment from 'moment'
import { mapGetters } from 'vuex'
import { APIV2RequestDatetimeFormat } from '@/config/simpliciti-apis.js'

export default {
  name: 'RankingDetailsMap',
  components: {
    LMap,
    LTileLayer,
    LControlZoom,
    LPolyline,
    LControl,
    // LMarker,
  },
  filters: {
    capitalize: function (value) {
      if (!value) return ''
      value = value.toString()
      return value.toUpperCase()
    },
    floor: function (value, nbDecimal) {
      if (!value) return 0
      if (!nbDecimal) nbDecimal = 0
      let fact = Math.pow(10, nbDecimal)
      value = value * fact
      value = Math.ceil(value)
      return value / fact
    },
    pointToComma(value) {
      return value.toString().split('.').join(',')
    },
  },
  props: {
    infos: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      zoom: 5,
      url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      center: [47.41322, -1.219482],
      bounds: null,
      attribution:
        '&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors',
      options: {
        zoomControl: false,
      },
      colors: ['#82b021', '#c2da32', '#f3d81e', '#ff9d0a', '#ff3203'],
      values: ['<23', '24', '25', '26', '>27'],
      loading: true,
      empty: false,
    }
  },
  computed: {
    ...mapGetters({
      // getCircuitPolylines:"ecoConduite/getResponseApi"
      getCircuitPolylines: 'ecoConduite/getCircuitConsumption',
    }),
  },
  watch: {
    getCircuitPolylines() {
      if (this.getCircuitPolylines.coords.length) {
        this.recenter()
      } else {
        this.empty = true
      }
      this.loading = false
    },
  },
  created() {
    // fetchCircuitsConsumption({ this.infos.vehicleId, driverId, })
    // console.log(this.infos);
    this.$store.dispatch('ecoConduite/fetchCircuitsConsumption', {
      clientId: this.infos.clientId,
      vehicleId: this.infos.vehicleId,
      driverId: this.infos.driverId,
      dhd: moment(String(this.infos.activityStartDate)).format(
        'APIV2RequestDatetimeFormat'
      ),
      dhf: moment(String(this.infos.activityEndDate)).format(
        'APIV2RequestDatetimeFormat'
      ),
      distance: 500,
    })
  },
  methods: {
    zoomUpdate(zoom) {
      this.zoom = zoom
    },
    centerUpdate(center) {
      this.center = center
    },
    boundsUpdated(bounds) {
      this.bounds = bounds
    },
    recenter() {
      this.$refs.map.mapObject.invalidateSize()
      this.$refs.map.mapObject.fitBounds([
        [
          this.getCircuitPolylines.minmax.latmax,
          this.getCircuitPolylines.minmax.lngmin,
        ],
        [
          this.getCircuitPolylines.minmax.latmin,
          this.getCircuitPolylines.minmax.lngmax,
        ],
      ])
    },
    waitForMap() {
      let waitForMap = (callback) => {
        if (this.$refs.map) {
          callback()
        } else {
          setTimeout(() => waitForMap(callback), 1000)
        }
      }
      this.$nextTick(() => {
        waitForMap(() => {
          this.$refs.map.mapObject.invalidateSize()
        })
      })
    },
    parseNullValue(value) {
      return value !== null ? value : 0
    },
  },
}
</script>
<style lang="scss">
.ecoconduite_map {
  position: relative;
  z-index: 0;
}
.ecoconduite_map__map {
  min-height: calc(100vh / 2);
}
.ecoconduite_map__map.leaflet-container {
  background-color: white;
}
.ecoconduite_map__overlay {
  overflow: hidden;
  z-index: 500;
  position: fixed;
  left: 0px;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
#legende {
  background-color: #ffffff;
  box-shadow: 0 1px 5px rgb(0 0 0 / 65%);
  border-radius: 4px;
  min-width: 268px;
}
#legende h6 {
  font-size: small;
}
#legende > div {
  margin: 0;
  padding: 0;
}

.legcolors,
.legvalues {
  border: none;
  font-size: x-small;
  margin: 0;
  padding: 0;
}
.legvalues {
  border-left: thin solid #ccc;
  padding-left: 3px;
}
.legcolors {
  height: 4px;
}
.collapsed > .when-opened,
:not(.collapsed) > .when-closed {
  display: none;
}
</style>

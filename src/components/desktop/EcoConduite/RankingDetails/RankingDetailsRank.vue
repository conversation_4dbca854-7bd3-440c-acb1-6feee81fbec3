<template>
  <div v-if="totalRating" class="col-12 ranking-details-rank">
    <div class="row d-flex flex-row mt-3 mb-3 w-100">
      <div class="col-12">
        <!-- Note -->
        <h5 class="mt-4 ecoTitle">
          {{ $t('ecoconduite.rank.title') }}
        </h5>
      </div>
      <div class="col-12 mt-3 mb-3 text-center justify-content-center">
        <table class="table table-bordered w-100">
          <thead>
            <tr class="table-success">
              <th>{{ $t('ecoconduite.rank.global') }}</th>
              <th id="stop_popover">
                {{ $t('ecoconduite.rank.stops') }}
                <sup><em class="far fa-question-circle" /></sup>
              </th>
              <th id="acc_popover">
                {{ $t('ecoconduite.rank.flex_acceleration') }}
                <sup><em class="far fa-question-circle" /></sup>
              </th>
              <th id="engineunder_popover">
                {{ $t('ecoconduite.rank.engine_under') }}
                <sup><em class="far fa-question-circle" /></sup>
              </th>
              <th id="engineover_popover">
                {{ $t('ecoconduite.rank.engine_over') }}
                <sup><em class="far fa-question-circle" /></sup>
              </th>
              <th id="anticipated_popover">
                {{ $t('ecoconduite.rank.anticipated') }}
                <sup><em class="far fa-question-circle" /></sup>
              </th>
              <th id="braking_popover">
                {{ $t('ecoconduite.rank.braking') }}
                <sup><em class="far fa-question-circle" /></sup>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                {{ parseNullValue(totalRating) }}
              </td>
              <td>
                {{ parseNullValue(infos.ratingIdleExcess) }}
              </td>
              <td>
                {{ parseNullValue(infos.ratingAccelerateExcess) }}
              </td>
              <td>
                {{ parseNullValue(infos.ratingEngineUnder) }}
              </td>
              <td>
                {{ parseNullValue(infos.ratingEngineOver) }}
              </td>
              <td>
                {{ parseNullValue(infos.ratingFreewheel) }}
              </td>
              <td>
                {{ parseNullValue(infos.ratingBrake) }}
              </td>
            </tr>
            <tr class="notes">
              <td class="reinitialise">
                <a href="#" @click="razCoef">{{
                  $t('ecoconduite.rank.initcoef')
                }}</a>
              </td>
              <td>
                <div class="form-group row">
                  <label class="col-sm-5" for="ratingIdleExcessRatio">{{
                    $t('ecoconduite.rank.coef')
                  }}</label>
                  <input
                    id="ratingIdleExcessRatio"
                    class="col-sm-5 form-control form-control-sm"
                    type="text"
                    :value="ratingIdleExcessRatio"
                    @keyup="calculTotalRating"
                  />
                </div>
              </td>
              <td>
                <div class="form-group row">
                  <label class="col-sm-5" for="ratingAccelerateExcessRatio">{{
                    $t('ecoconduite.rank.coef')
                  }}</label>
                  <input
                    id="ratingAccelerateExcessRatio"
                    class="col-sm-5 form-control form-control-sm"
                    type="text"
                    :value="ratingAccelerateExcessRatio"
                    @keyup="calculTotalRating"
                  />
                </div>
              </td>
              <td>
                <div class="form-group row">
                  <label class="col-sm-5" for="ratingEngineUnderRatio">{{
                    $t('ecoconduite.rank.coef')
                  }}</label>
                  <input
                    id="ratingEngineUnderRatio"
                    class="col-sm-5 form-control form-control-sm"
                    type="text"
                    :value="ratingEngineUnderRatio"
                    @keyup="calculTotalRating"
                  />
                </div>
              </td>
              <td>
                <div class="form-group row">
                  <label class="col-sm-5" for="ratingEngineOverRatio">{{
                    $t('ecoconduite.rank.coef')
                  }}</label>
                  <input
                    id="ratingEngineOverRatio"
                    class="col-sm-5 form-control form-control-sm"
                    type="text"
                    :value="ratingEngineOverRatio"
                    @keyup="calculTotalRating"
                  />
                </div>
              </td>
              <td>
                <div class="form-group row">
                  <label class="col-sm-5" for="ratingFreewheelRatio">{{
                    $t('ecoconduite.rank.coef')
                  }}</label>
                  <input
                    id="ratingFreewheelRatio"
                    class="col-sm-5 form-control form-control-sm"
                    type="text"
                    :value="ratingFreewheelRatio"
                    @keyup="calculTotalRating"
                  />
                </div>
              </td>
              <td>
                <div class="form-group row">
                  <label class="col-sm-5" for="ratingBrakeRatio">{{
                    $t('ecoconduite.rank.coef')
                  }}</label>
                  <input
                    id="ratingBrakeRatio"
                    class="col-sm-5 form-control form-control-sm"
                    type="text"
                    :value="ratingBrakeRatio"
                    @keyup="calculTotalRating"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div
          v-if="displayWarningSimulation"
          class="alert alert-light"
          role="alert"
        >
          Information : les modifications de coefficients ne sont pas
          enregistrées. Il s'agit d'une simulation.
        </div>
        <div v-if="!coefsExist" class="alert alert-light" role="alert">
          Les coefficients qui ont servi au calcul de la note ne sont pas
          disponibles.
        </div>
      </div>
    </div>
    <!-- Popup informative arrêts -->
    <b-popover
      ref="popover"
      target="stop_popover"
      triggers="hover"
      :title="stops_management"
      placement="right"
    >
      <p>{{ $t('ecoconduite.rank.infos.stops.desc') }}</p>
      <p class="mt-2">
        <em>{{ $t('ecoconduite.rank.infos.example') }}</em> <br />
        {{ $t('ecoconduite.rank.infos.stops.explain') }}<br />
        <b>{{ $t('ecoconduite.rank.infos.stops.example') }}</b>
      </p>
      <p class="mt-2">
        {{ $t('ecoconduite.rank.infos.stops.example_per_60') }}<br />
        {{ $t('ecoconduite.rank.infos.stops.example_per_90') }}
      </p>
    </b-popover>

    <!-- Popup informative flex. accélération -->
    <b-popover
      ref="popover"
      target="acc_popover"
      :title="acc_management"
      triggers="hover"
      placement="right"
    >
      <p>{{ $t('ecoconduite.rank.infos.accs.desc') }}</p>
      <p class="mt-2">
        <em>{{ $t('ecoconduite.rank.infos.example') }}</em> <br />
        {{ $t('ecoconduite.rank.infos.accs.explain1') }}<br />
        {{ $t('ecoconduite.rank.infos.accs.explain2') }}<br />
        {{ $t('ecoconduite.rank.infos.accs.explain3') }}<br />
        <b>{{ $t('ecoconduite.rank.infos.accs.explain4') }}</b
        ><br />
      </p>
    </b-popover>

    <!-- Popup informative surrégime -->
    <b-popover
      ref="popover"
      target="engineover_popover"
      :title="engine_over_management"
      triggers="hover"
      placement="right"
    >
      <p>{{ $t('ecoconduite.rank.infos.engine_over.desc') }}</p>
      <p class="mt-2">
        <em>{{ $t('ecoconduite.rank.infos.example') }}</em> <br />
        {{ $t('ecoconduite.rank.infos.engine_over.explain1') }}<br />
        {{ $t('ecoconduite.rank.infos.engine_over.explain2') }}<br />
        {{ $t('ecoconduite.rank.infos.engine_over.explain3') }}<br />
        <b>{{ $t('ecoconduite.rank.infos.engine_over.explain4') }}</b
        ><br />
      </p>
    </b-popover>

    <!-- Popup informative sous_régime -->
    <b-popover
      ref="popover"
      target="engineunder_popover"
      :title="engine_under_management"
      triggers="hover"
      placement="right"
    >
      <p>{{ $t('ecoconduite.rank.infos.engine_under.desc') }}</p>
      <p class="mt-2">
        <em>{{ $t('ecoconduite.rank.infos.example') }}</em> <br />
        {{ $t('ecoconduite.rank.infos.engine_under.explain1') }}<br />
        {{ $t('ecoconduite.rank.infos.engine_under.explain2') }}<br />
        {{ $t('ecoconduite.rank.infos.engine_under.explain3') }}<br />
        <b>{{ $t('ecoconduite.rank.infos.engine_under.explain4') }}</b
        ><br />
      </p>
    </b-popover>

    <!-- Popup informative anticipation -->
    <b-popover
      ref="popover"
      target="anticipated_popover"
      :title="anticipation_management"
      triggers="hover"
      placement="right"
    >
      <p>{{ $t('ecoconduite.rank.infos.anticipation.desc') }}</p>
      <p class="mt-2">
        <em>{{ $t('ecoconduite.rank.infos.example') }}</em> <br />
        {{ $t('ecoconduite.rank.infos.anticipation.explain1') }}<br />
        {{ $t('ecoconduite.rank.infos.anticipation.explain2') }}<br />
        <b>{{ $t('ecoconduite.rank.infos.anticipation.explain3') }}</b
        ><br />
      </p>
    </b-popover>

    <!-- Popup informative freinages -->
    <b-popover
      ref="popover"
      target="braking_popover"
      :title="brakes_management"
      triggers="hover"
      placement="right"
    >
      <p>{{ $t('ecoconduite.rank.infos.brakes.desc') }}</p>
      <p class="mt-2">
        <em>{{ $t('ecoconduite.rank.infos.example') }}</em> <br />
        {{ $t('ecoconduite.rank.infos.brakes.explain1') }}<br />
        <b>{{ $t('ecoconduite.rank.infos.brakes.explain2') }}</b
        ><br />
      </p>
    </b-popover>
  </div>
  <div v-else class="alert-danger col-12">
    {{ $t('ecoconduite.empty') }}
  </div>
</template>

<script>
export default {
  name: 'RankingDetailsRank',
  props: {
    infos: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      stops_management: this.$t('ecoconduite.rank.infos.stops.title'),
      acc_management: this.$t('ecoconduite.rank.infos.accs.title'),
      engine_over_management: this.$t(
        'ecoconduite.rank.infos.engine_over.title'
      ),
      engine_under_management: this.$t(
        'ecoconduite.rank.infos.engine_under.title'
      ),
      anticipation_management: this.$t(
        'ecoconduite.rank.infos.anticipation.title'
      ),
      brakes_management: this.$t('ecoconduite.rank.infos.brakes.title'),
      totalRating: this.infos.rating,
      ratingIdleExcessRatio: this.infos.altData.ratingIdleExcessRatio,
      ratingAccelerateExcessRatio:
        this.infos.altData.ratingAccelerateExcessRatio,
      ratingEngineUnderRatio: this.infos.altData.ratingEngineUnderRatio,
      ratingEngineOverRatio: this.infos.altData.ratingEngineOverRatio,
      ratingFreewheelRatio: this.infos.altData.ratingFreewheelRatio,
      ratingBrakeRatio: this.infos.altData.ratingBrakeRatio,
      displayWarningSimulation: false,
      coefsExist: Boolean(this.infos.altData.length),
    }
  },
  methods: {
    parseNullValue(value) {
      return value !== null ? value : 0
    },
    calculTotalRating() {
      this.displayWarningSimulation = true
      if (document.getElementById('ratingIdleExcessRatio').value != '') {
        this.ratingIdleExcessRatio = parseInt(
          document.getElementById('ratingIdleExcessRatio').value
        )
      } else {
        this.ratingIdleExcessRatio = 0
      }
      if (document.getElementById('ratingAccelerateExcessRatio').value != '') {
        this.ratingAccelerateExcessRatio = parseInt(
          document.getElementById('ratingAccelerateExcessRatio').value
        )
      } else {
        this.ratingAccelerateExcessRatio = 0
      }
      if (document.getElementById('ratingEngineUnderRatio').value != '') {
        this.ratingEngineUnderRatio = parseInt(
          document.getElementById('ratingEngineUnderRatio').value
        )
      } else {
        this.ratingEngineUnderRatio = 0
      }
      if (document.getElementById('ratingEngineOverRatio').value != '') {
        this.ratingEngineOverRatio = parseInt(
          document.getElementById('ratingEngineOverRatio').value
        )
      } else {
        this.ratingEngineOverRatio = 0
      }
      if (document.getElementById('ratingFreewheelRatio').value != '') {
        this.ratingFreewheelRatio = parseInt(
          document.getElementById('ratingFreewheelRatio').value
        )
      } else {
        this.ratingFreewheelRatio = 0
      }
      if (document.getElementById('ratingBrakeRatio').value != '') {
        this.ratingBrakeRatio = parseInt(
          document.getElementById('ratingBrakeRatio').value
        )
      } else {
        this.ratingBrakeRatio = 0
      }

      let diviseur =
        this.ratingIdleExcessRatio +
        this.ratingAccelerateExcessRatio +
        this.ratingEngineUnderRatio +
        this.ratingEngineOverRatio +
        this.ratingFreewheelRatio +
        this.ratingBrakeRatio
      if (!diviseur) {
        this.totalRating = 'Division par 0'
      } else {
        this.totalRating =
          Math.round(
            ((this.ratingIdleExcessRatio * this.infos.ratingIdleExcess +
              this.ratingAccelerateExcessRatio *
                this.infos.ratingAccelerateExcess +
              this.ratingEngineUnderRatio * this.infos.ratingEngineUnder +
              this.ratingEngineOverRatio * this.infos.ratingEngineOver +
              this.ratingFreewheelRatio * this.infos.ratingFreewheel +
              this.ratingBrakeRatio * this.infos.ratingBrake) /
              diviseur) *
              10
          ) / 10
      }
    },
    razCoef() {
      this.displayWarningSimulation = false
      this.totalRating = this.infos.rating
      if (this.infos.altData.ratingIdleExcessRatio != 'undefined') {
        this.ratingIdleExcessRatio = this.infos.altData.ratingIdleExcessRatio
        this.ratingAccelerateExcessRatio =
          this.infos.altData.ratingAccelerateExcessRatio
        this.ratingEngineUnderRatio = this.infos.altData.ratingEngineUnderRatio
        this.ratingEngineOverRatio = this.infos.altData.ratingEngineOverRatio
        this.ratingFreewheelRatio = this.infos.altData.ratingFreewheelRatio
        this.ratingBrakeRatio = this.infos.altData.ratingBrakeRatio
        // this.calculTotalRating();
      } else {
        this.ratingIdleExcessRatio = this.ratingAccelerateExcessRatio = null
        this.ratingEngineUnderRatio = this.ratingEngineOverRatio = null
        this.ratingFreewheelRatio = this.ratingBrakeRatio = null
      }
    },
  },
  // created() {
  //   if(this.infos.altData.length > 0 ) {
  //     this.calculTotalRating();
  //   }
  // },
}
</script>
<style lang="scss">
.popover-header {
  background-color: rgba(143, 209, 158, 0.5) !important;
  border-bottom: #8fd19e80 !important;
}
.popover-body {
  & em {
    color: mediumseagreen;
    font-weight: 550;
  }
  & b {
    color: mediumseagreen;
    font-weight: 550;
  }
}
.far.fa-question-circle {
  color: lightslategray;
}
#input__cell1,
#input__cell2,
#input__cell3,
#input__cell4,
#input__cell5,
#input__cell6 {
  max-width: 100px;
  text-align: center;
}

.ranking-details-rank .form-group input {
  margin: 0;
  border: 0;
  border-bottom: 1px solid #70bd95;
  text-align: center;
  display: inline;
  max-width: 30px;
  padding: 0;
  margin: 0;
  height: 100%;
}
.notes label {
  color: #9f9f9f;
  font-size: 0.875rem;
  margin: 0;
  padding: 0;
  height: 100%;
}
.table-bordered,
.table-bordered td {
  border: 0;
}
.table thead th {
  width: 14%;
  vertical-align: top;
  border: 0;
  background-color: white;
}
.table thead th:first-child {
  width: 16%;
}
.table-success {
  background-color: white;
  border: 0;
}
.notes {
  font-size: x-large;
}
.reinitialise {
  font-size: 0.875rem;
  max-width: 100px;
  display: block;
  text-align: center;
  margin: 0 auto;
  padding: 0;
}
#ranking_details_modal .alert-light {
  margin: 0;
}
#ranking_details_modal .tab-pane .ecoTitle {
  margin-top: 0 !important;
}
</style>

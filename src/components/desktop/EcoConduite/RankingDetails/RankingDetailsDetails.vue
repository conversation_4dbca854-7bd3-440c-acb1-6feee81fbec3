<template>
  <div class="col-12">
    <div class="row d-flex flex-row mt-3 mb-3 w-100">
      <div class="col-12">
        <!-- Durée -->
        <h5 class="mt-4 mb-3 ecoTitle">
          {{ $t('ecoconduite.details.elapsed_time') }}
        </h5>
        <div class="row d-flex justify-content-around mt-3 mb-4 ecoContent">
          <div class="col-4">
            <span>
              <!-- Durée totale -->
              {{ $t('ecoconduite.details.total') }} :
              {{
                parseNullValue(infos.durationTotal) | secondsToMinutes | toInt
              }}
              mn <br />
              <!-- Durée moteur tournant -->
              {{ $t('ecoconduite.details.engine_on') }} :
              {{
                parseNullValue(infos.durationEngineOn)
                  | secondsToMinutes
                  | toInt
              }}
              mn
              <br />
              <!-- Durée moteur arrêté -->
              {{ $t('ecoconduite.details.engine_off') }} :
              {{
                parseNullValue(infos.durationEngineOff)
                  | secondsToMinutes
                  | toInt
              }}
              mn
              <br />
              <!-- Arrêts moteur tournant -->
              {{ $t('ecoconduite.details.engine_on_off_total') }} :
              {{
                parseNullValue(infos.durationIdleExcess)
                  | secondsToMinutes
                  | toInt
              }}
              mn <br />
              <!-- Au ralenti -->
              {{ $t('ecoconduite.details.slowed_down') }} :
              {{
                parseNullValue(infos.durationIdle) | secondsToMinutes | toInt
              }}
              mn <br />
              <!-- Au travail -->
              {{ $t('ecoconduite.details.working') }} :
              {{
                parseNullValue(infos.durationWork) | secondsToMinutes | toInt
              }}
              mn
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Durée de déplacement -->
              {{ $t('ecoconduite.details.speed') }} :
              {{
                parseNullValue(infos.durationMove) | secondsToMinutes | toInt
              }}
              mn <br />
              <!-- Durée des freinages -->
              {{ $t('ecoconduite.details.engine_brakes') }} :
              {{
                parseNullValue(infos.durationBrake) | secondsToMinutes | toInt
              }}
              mn -
              {{
                toPercent(infos.durationBrake, infos.durationTotal)
                  | toDecimal
                  | pointToComma
              }}%
              <br />
              <!-- Durée des accélérations -->
              {{ $t('ecoconduite.details.speed_ups') }} :
              {{
                parseNullValue(infos.durationAccelerate)
                  | secondsToMinutes
                  | toInt
              }}
              mn -
              {{
                toPercent(infos.durationAccelerate, infos.durationTotal)
                  | toDecimal
                  | pointToComma
              }}%
              <br />
              <!-- Durée des accélérations excessives -->
              {{ $t('ecoconduite.details.excessive_speed_up') }} :
              {{
                parseNullValue(infos.durationAccelerateExcess)
                  | secondsToMinutes
                  | toInt
              }}
              mn -
              {{
                toPercent(infos.durationAccelerateExcess, infos.durationTotal)
                  | toDecimal
                  | pointToComma
              }}%
              <br />
              <!-- Durée en roue libre -->
              {{ $t('ecoconduite.details.free_wheel') }} :
              {{
                parseNullValue(infos.durationFreewheel)
                  | secondsToMinutes
                  | toInt
              }}
              mn -
              {{
                toPercent(infos.durationMove, infos.durationTotal)
                  | toDecimal
                  | pointToComma
              }}%
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Durée en conduite -->
              {{ $t('ecoconduite.details.while_driving') }}* :
              {{
                parseNullValue(infos.durationEngineTotal)
                  | secondsToMinutes
                  | toInt
              }}
              mn
              <br />
              <!-- Durée en sous régime -->
              {{ $t('ecoconduite.details.engine_rotation_under') }}* :
              {{
                parseNullValue(infos.durationEngineUnder)
                  | secondsToMinutes
                  | toInt
              }}
              mn -
              {{
                toPercent(infos.durationEngineUnder, infos.durationTotal)
                  | toDecimal
                  | pointToComma
              }}
              %
              <br />
              <!-- Durée en surrégime -->
              {{ $t('ecoconduite.details.engine_rotation_over') }}* :
              {{
                parseNullValue(infos.durationEngineOver)
                  | secondsToMinutes
                  | toInt
              }}
              mn -
              {{
                toPercent(infos.durationEngineOver, infos.durationTotal)
                  | toDecimal
                  | pointToComma
              }}
              %
              <br />
              <!-- Durée en conduite eco -->
              {{ $t('ecoconduite.details.engine_eco_drive') }}* :
              {{
                parseNullValue(infos.durationEngineEco)
                  | secondsToMinutes
                  | toInt
              }}
              mn -
              {{
                toPercent(infos.durationEngineEco, infos.durationTotal)
                  | toDecimal
                  | pointToComma
              }}
              %
              <br />
              <em>*{{ $t('ecoconduite.details.specific') }}</em>
            </span>
          </div>
        </div>
        <!-- Phases -->
        <h5 class="mt-5 ecoTitle">
          {{ $t('ecoconduite.details.phases.title') }}
        </h5>
        <div class="row mt-3 mb-4 ecoContent">
          <div class="col-4">
            <span>
              <!-- Arrêts -->
              {{ $t('ecoconduite.details.phases.stops') }} :
              {{ parseNullValue(infos.phaseStop) }} <br />
              <!-- Arrêts moteur tournant -->
              {{ $t('ecoconduite.details.phases.stops_engine_on') }} :
              {{ parseNullValue(infos.phaseIdleExcess) }} <br />
              <!-- Arrêts frein de parking -->
              {{ $t('ecoconduite.details.phases.stops_engine_braked') }} :
              {{ parseNullValue(infos.phaseParkingBrake) }}
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Débrayages -->
              {{ $t('ecoconduite.details.phases.declutch') }} :
              {{ parseNullValue(infos.phaseDeclutch) }} <br />
              <!-- Freinages -->
              {{ $t('ecoconduite.details.phases.brakes') }} :
              {{ parseNullValue(infos.phaseBrake) }} <br />
              <!-- Surrégime -->
              {{ $t('ecoconduite.details.phases.overload') }} :
              {{ parseNullValue(infos.phaseEngineOver) }}
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Accélérations -->
              {{ $t('ecoconduite.details.phases.accelerates') }} :
              {{ parseNullValue(infos.phaseAccelerate) }} <br />
              <!-- Roue libre -->
              {{ $t('ecoconduite.details.phases.free_wheel') }} :
              {{ parseNullValue(infos.phaseFreewheel) }}
            </span>
          </div>
        </div>
        <!-- Distances -->
        <h5 class="mt-5 ecoTitle">
          {{ $t('ecoconduite.details.distance.title') }}
        </h5>
        <div class="row mt-3 mb-4 ecoContent">
          <div class="col-4">
            <span>
              <!-- Distance totale -->
              {{ $t('ecoconduite.details.distance.total') }} :
              {{
                parseNullValue(infos.distanceTotal)
                  | toKilometers
                  | floor(1)
                  | pointToComma
              }}
              km<br />
              <!-- En freinage -->
              {{ $t('ecoconduite.details.distance.while_breaking') }} :
              {{
                parseNullValue(infos.distanceBrake)
                  | toKilometers
                  | floor(1)
                  | pointToComma
              }}
              km
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- En roue libre -->
              {{ $t('ecoconduite.details.distance.while_free_wheeling') }} :
              {{
                parseNullValue(infos.distanceFreewheel)
                  | toKilometers
                  | floor(1)
                  | pointToComma
              }}
              km
              <br />
              <!-- En débrayage -->
              {{ $t('ecoconduite.details.distance.while_declutching') }} :
              {{
                parseNullValue(infos.distanceDeclutch)
                  | toKilometers
                  | floor(1)
                  | pointToComma
              }}
              km
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- En accélération -->
              {{ $t('ecoconduite.details.distance.while_accelerating') }} :
              {{
                parseNullValue(infos.distanceAccelerate)
                  | toKilometers
                  | floor(1)
                  | pointToComma
              }}
              km
              <br />
              <!-- En travail -->
              {{ $t('ecoconduite.details.distance.while_working') }} :
              {{
                parseNullValue(infos.distanceWork)
                  | toKilometers
                  | floor(1)
                  | pointToComma
              }}
              km
            </span>
          </div>
        </div>
        <!-- Consommation carburant -->
        <h5 class="mt-5 ecoTitle">
          {{ $t('ecoconduite.details.fuel_consuming.title') }}
        </h5>
        <div class="row mt-3 mb-4 ecoContent">
          <div class="col-4">
            <span>
              <!-- Rejet CO2 -->
              {{ $t('ecoconduite.details.fuel_consuming.cO2_rejects') }} :
              {{ parseNullValue(infos.co2) | toKilograms | pointToComma }} Kg
              {{ $t('ecoconduite.details.fuel_consuming.c02_else') }}
              {{
                toPercent(infos.distanceTotal, infos.co2)
                  | toDecimal
                  | pointToComma
              }}
              g/km
              <br />
              <!-- Conso moyenne -->
              {{ $t('ecoconduite.details.fuel_consuming.average_consuming') }} :
              {{
                parseNullValue(infos.consumptionAverage)
                  | floor(1)
                  | pointToComma
              }}
              L/100 km
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Conso moyenne en travail -->
              {{
                $t('ecoconduite.details.fuel_consuming.while_working_consuming')
              }}
              :
              {{
                parseNullValue(infos.consumptionAverageWork)
                  | floor(1)
                  | pointToComma
              }}
              L
              <br />
              <!-- Conso perdue -->
              {{
                $t(
                  'ecoconduite.details.fuel_consuming.measured_lost_consumption'
                )
              }}
              <!-- Convert to L (mL) -->
              :
              {{
                parseNullValue(infos.consumptionIdle)
                  | mlToL
                  | floor(1)
                  | pointToComma
              }}
              L
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Conso totale -->
              {{ $t('ecoconduite.details.fuel_consuming.total') }} :
              {{
                parseNullValue(infos.consumptionTotal)
                  | mlToL
                  | floor(1)
                  | pointToComma
              }}
              L
              <br />
              <!-- Conso en mouvement -->
              {{
                $t(
                  'ecoconduite.details.fuel_consuming.consumption_while_moving'
                )
              }}
              :
              {{
                parseNullValue(infos.consumptionAverageMove)
                  | floor(1)
                  | pointToComma
              }}
              L/100 km
            </span>
          </div>
        </div>
        <!-- Vitesse et accélération -->
        <h5 class="mt-5 ecoTitle">
          {{ $t('ecoconduite.details.speed_accelerating.title') }}
        </h5>
        <div class="row mt-3 mb-4 ecoContent">
          <div class="col-4">
            <span>
              <!-- Vitesse moyenne -->
              {{ $t('ecoconduite.details.speed_accelerating.average_speed') }} :
              {{ parseNullValue(infos.speedAverage) | toInt }} km/h<br />
              <!-- Ecr. moyen accélérateur -->
              {{
                $t(
                  'ecoconduite.details.speed_accelerating.average_accelerating_pedal_push'
                )
              }}
              : {{ parseNullValue(infos.ratioAcceleratorPedal) }} %
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Ecr. moyen pédale frein -->
              {{
                $t(
                  'ecoconduite.details.speed_accelerating.average_braking_pedal_push'
                )
              }}
              : {{ parseNullValue(infos.ratioBrakePedal) }} %
              <br />
              <!-- Accélération kinetic moyenne -->
              {{
                $t(
                  'ecoconduite.details.speed_accelerating.average_acceleration'
                )
              }}
              :
              {{ parseNullValue(infos.kineticIndex) }}
            </span>
          </div>
          <div class="col-4">
            <span>
              <!-- Régime moteur moyen -->
              {{
                $t(
                  'ecoconduite.details.speed_accelerating.average_engine_rotation'
                )
              }}
              : {{ parseNullValue(infos.engineSpeed) }} tr/mn
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'RankingDetailsDetails',
  filters: {
    toDecimal: function (value) {
      if (!value) return 0
      value = Number.parseFloat(value).toFixed(2)
      return value
    },
    toInt: function (value) {
      return Number.parseInt(value).toFixed(0)
    },
    secondsToMinutes: function (value) {
      if (!value) return 0
      return value / 60
    },
    toKilometers: function (value) {
      if (!value) return 0
      return value / 1000
    },
    toKilograms: function (value) {
      if (!value) return 0
      return value / 1000
    },
    mlToL: function (value) {
      if (!value) return 0
      return value / 1000
    },
    floor: function (value, nbDecimal) {
      if (!value) return 0
      if (!nbDecimal) nbDecimal = 0
      let fact = Math.pow(10, nbDecimal)
      value = value * fact
      value = Math.ceil(value)
      return value / fact
    },
    pointToComma(value) {
      return value.toString().split('.').join(',')
    },
  },
  props: {
    infos: {
      type: Object,
      default: () => ({}),
    },
  },
  methods: {
    toPercent(value, totalValue) {
      return value !== null && totalValue !== null
        ? (value / totalValue) * 100
        : 0
    },
    parseNullValue(value) {
      return value !== null ? value : 0
    },
  },
}
</script>
<style>
#detailsTabs h5.ecoTitle:not(:first-child) {
  margin-top: 0rem !important;
}
#detailsTabs .ecoContent {
  margin-top: 0 !important;
  margin-bottom: 1rem !important;
}
</style>

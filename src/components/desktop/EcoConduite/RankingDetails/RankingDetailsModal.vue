<template>
  <div class="container">
    <div class="row">
      <div class="col-12">
        <!-- Tab Nav -->
        <b-tabs
          content-class="mt-3"
          active-nav-item-class="activeTab"
          align="end"
          fill
        >
          <b-tab
            v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_DETAILS')"
            :title="details"
            @click="tabIndex = 0"
          />
          <b-tab
            v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_RANKING')"
            :title="rank"
            @click="tabIndex = 1"
          />
          <!--          <b-tab v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_CHART')" @click="tabIndex = 2" :title="charts"></b-tab>-->
          <b-tab
            v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_MAP')"
            :title="map"
            @click="tabIndex = 2"
          />
        </b-tabs>
      </div>
      <div class="col-12 ecoHead">
        <h5 class="ecoTitle">
          {{ $t('ecoconduite.details.title') }}
        </h5>
        <div
          class="row"
          style="border-bottom: thin solid #ddd; padding-bottom: 15px"
        >
          <div class="col-2">
            <strong>{{ $t('Véhicule') }}</strong> <br />
            {{ item.vehicleName }}
          </div>
          <div v-if="item.driverName" class="col-2">
            <strong>{{ $t('ecoconduite.chauffeur') }}</strong> <br />
            {{ item.driverName }}
          </div>
          <div class="col-8">
            <strong>{{ $t('ecoconduite.details.period') }}</strong> <br />
            {{ dateStart }} - {{ dateEnd }}
          </div>
        </div>
      </div>
    </div>
    <div class="row d-flex">
      <!-- Tab Content -->
      <b-tabs id="detailsTabs" v-model="tabIndex" nav-wrapper-class="d-none">
        <!-- Détails -->
        <b-tab v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_DETAILS')">
          <RankingDetailsDetails :infos="item" />
        </b-tab>
        <!-- Note -->
        <b-tab v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_RANKING')">
          <RankingDetailsRank :infos="item" />
        </b-tab>
        <!-- Graphique -->
        <!--        <b-tab v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_CHART')">-->
        <!--          <RankingDetailsGraph :infos="item" />-->
        <!--        </b-tab>-->
        <!-- Carto -->
        <b-tab v-if="hasRight('GEOV3_ECOCONDUITE_TABLE_DETAILS_MAP')" lazy>
          <RankingDetailsMap :infos="item" />
        </b-tab>
      </b-tabs>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'
import RankingDetailsDetails from '@desktop/EcoConduite/RankingDetails/RankingDetailsDetails.vue'
import RankingDetailsRank from '@desktop/EcoConduite/RankingDetails/RankingDetailsRank.vue'
// import RankingDetailsGraph from "@desktop/EcoConduite/RankingDetails/RankingDetailsGraph.vue";
import RankingDetailsMap from '@desktop/EcoConduite/RankingDetails/RankingDetailsMap.vue'

export function formatDate(date) {
  return Vue.$date.formatDatetime(date, {
    seconds: true,
  })
}

export default {
  name: 'RankingDetailsModal',
  components: {
    RankingDetailsDetails,
    RankingDetailsRank,
    // RankingDetailsGraph,
    RankingDetailsMap,
  },
  inject: ['hasRight'],
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      details: this.$t('ecoconduite.details.tab.details'),
      rank: this.$t('ecoconduite.details.tab.rank'),
      // charts: this.$t("ecoconduite.details.tab.charts"),
      map: this.$t('ecoconduite.details.tab.map'),
      tabIndex: 0,
    }
  },
  computed: {
    dateStart() {
      return formatDate(this.item.activityStartDate)
    },
    dateEnd() {
      return formatDate(this.item.activityEndDate)
    },
  },
}
</script>
<style lang="scss">
/**
  WARNING: Attention to style section without scoped attribute. Styling global classes such as .modal-header will affect the entire app

  route name has been added to App.vue to scope the style manually. I.g .ecoconduite_module .my-global-class {}
 */

.ecoTitle {
  color: mediumseagreen;
  font-weight: 550;
}
.ecoconduite_module .modal-header {
  border-bottom: none !important;
  padding: 0.5rem 1rem 0.5rem 1rem !important;
  margin-bottom: -10px;
  background-color: #70bd95 !important;
  color: white;
}
.ecoconduite_module .modal-title {
  margin-left: auto;
  margin-right: auto;
}
.ecoconduite_module .modal-header .close {
  text-shadow: none;
  opacity: 1;
  color: white;
  font-weight: lighter;
  font-size: xx-large;
  margin-left: 0;
}

.ecoHead {
  display: block;
  flex-direction: column;
  justify-content: center;
}
.activeTab {
  color: #70bd95 !important;
}
a.nav-link {
  color: #2a2c2d;
}
#detailsTabs {
  width: 100%;
}
.nav-tabs {
  border-bottom: 0;
}
.nav-tabs .nav-link {
  border: 0;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  border-bottom: 3px solid #70bd95;
}
</style>

<template>
  <div class="">
    <div class="card">
      <div class="col-md-12">
        <span class="filter"
          >> {{ $t('ecoconduite.search_selection.title') }}</span
        >
      </div>

      <nav
        id="pills-tab"
        class="nav nav-pills flex-column flex-sm-row"
        role="tablist"
      >
        <a
          id="pills-car-tab"
          class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link active"
          data-toggle="pill"
          href="#pills-car"
          role="tab"
          aria-controls="pills-car"
          aria-selected="true"
          @click="toggleSelectedTab('vehicle')"
          >{{ $t('common.Véhicule') }}</a
        >
        <a
          id="pills-driver-tab"
          class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link"
          data-toggle="pill"
          href="#pills-driver"
          role="tab"
          aria-controls="pills-driver"
          aria-selected="false"
          @click="toggleSelectedTab('driver')"
          >{{ $t('common.Chauffeur') }}</a
        >
        <a
          id="pills-circuit-tab"
          class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link"
          data-toggle="pill"
          href="#pills-circuit"
          role="tab"
          aria-controls="pills-circuit"
          aria-selected="false"
          @click="toggleSelectedTab('circuit')"
          >{{ $t('Circuit') }}</a
        >
        <a
          id="pills-list-tab"
          class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link d-none"
          data-toggle="pill"
          href="#pills-list"
          role="tab"
          aria-controls="pills-list"
          aria-selected="false"
          >{{ $t('common.list') }}</a
        >
      </nav>
      <div id="pills-tabContent" class="tab-content">
        <div
          id="pills-car"
          class="tab-pane fade show active"
          role="tabpanel"
          aria-labelledby="pills-car-tab"
          style="overflow: auto"
        >
          <div class="p-2">
            <section v-if="errored">
              <p v-html="$t('ecoconduite.search_fail')" />
            </section>

            <section v-else>
              <div v-if="loading" class="loading">
                <b-spinner label="Chargement..." />
              </div>

              <div v-else>
                <!--  selection dut tout -->
                <div class="mt-2">
                  <div class="display_item">
                    <input
                      type="checkbox"
                      class="checkAllChildAndSubChild-car all-car"
                      @click="checkAllChildAndSubChild('car')"
                    />
                  </div>
                  <div
                    class="display_item ml-2"
                    @click="toggleAllSubCateg('car')"
                  >
                    <div class="chevron_height chevron_block_size">
                      <i
                        class="chevron_style fa fa-chevron-up main_rotate rotate-car"
                        aria-hidden="true"
                      />
                    </div>
                    <div class="chevron_height">
                      <i
                        class="chevron_style fa fa-chevron-down main_rotate rotate-car"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                </div>

                <!-- fin selection du tout -->

                <!-- vehicles -->
                <div
                  v-for="(vehicleCategory, index) in vehicleCategories"
                  :key="index"
                  data-vehicle-category
                  class="list-group-item p-0"
                >
                  <!-- Vehicle category (top) -->
                  <input
                    v-if="
                      shouldDisplayItemsUnderCategory('car', vehicleCategory.id)
                    "
                    :id="`vc-index-${index}`"
                    type="checkbox"
                    :class="`parentCkeck-${vehicleCategory.id}`"
                    class="all-car"
                    @click="checkAllChild(vehicleCategory.id)"
                  />

                  <em
                    v-if="
                      shouldDisplayItemsUnderCategory('car', vehicleCategory.id)
                    "
                    class="far fa-folder pl-2 pr-2"
                    @click="toggleSubCateg(vehicleCategory.id)"
                  />
                  <label
                    v-if="
                      shouldDisplayItemsUnderCategory('car', vehicleCategory.id)
                    "
                    class="form-check-label"
                    >{{ vehicleCategory.nom }}({{
                      countVehiclesUnderCategory(vehicleCategory.id)
                    }})</label
                  >

                  <!-- Vehicle category (children categories) -->
                  <div
                    v-for="sousVehicule in getChildrenCategories(
                      CategoriesVehicles,
                      vehicleCategory.id
                    )"
                    :key="sousVehicule.id"
                    data-vehicle-category-children
                    class="list-group-item p-0 ml-4 subCateg-car"
                    :class="`subCateg-${vehicleCategory.id} subCateg`"
                  >
                    <input
                      v-if="
                        shouldDisplayItemsUnderCategory('car', sousVehicule.id)
                      "
                      :id="`${vehicleCategory.id}-${sousVehicule.id}`"
                      type="checkbox"
                      :class="`childCkeck-${vehicleCategory.id} childCkeck-${vehicleCategory.id}-${sousVehicule.id}`"
                      class="all-car"
                      @change="
                        onItemInputChange(vehicleCategory.id, sousVehicule.id)
                      "
                    />
                    <em
                      v-if="
                        shouldDisplayItemsUnderCategory('car', sousVehicule.id)
                      "
                      class="far fa-folder pl-2 pr-2"
                      @click="toggleSubCateg(sousVehicule.id)"
                    />
                    <label
                      v-if="
                        shouldDisplayItemsUnderCategory('car', sousVehicule.id)
                      "
                      class="form-check-label pl-2"
                      >{{ sousVehicule.nom }}({{
                        getVehiclesByCategory(sousVehicule.id).length
                      }})</label
                    >

                    <!-- category -> (children category) -> vehicles -->
                    <div
                      v-for="(
                        sousCategVehiculeWithChild, indexl
                      ) in getVehiclesByCategory(sousVehicule.id)"
                      :key="indexl"
                      class="list-group-item p-0 ml-4 subCateg-car"
                      :class="`subCateg-${sousVehicule.id} subCateg`"
                    >
                      <input
                        :id="`${sousVehicule.id}-${sousCategVehiculeWithChild.id}`"
                        type="checkbox"
                        name="vehiculeSelected"
                        :value="sousCategVehiculeWithChild.id"
                        :class="`commonChild-${vehicleCategory.id} childCheckSubCateg-${sousVehicule.id}`"
                        class="all-car"
                        @change="
                          toggleParentCategoryInput(
                            $event,
                            vehicleCategory.id,
                            sousVehicule.id
                          )
                        "
                      />
                      <label class="form-check-label pl-2">{{
                        sousCategVehiculeWithChild.nom
                      }}</label>
                    </div>
                  </div>

                  <!--  category -> vehicles -->
                  <div v-if="vehicleCategory.parent_id === ''">
                    <div
                      v-for="(
                        sousCategVehicule, indexl
                      ) in getVehiclesByCategory(vehicleCategory.id)"
                      :key="indexl"
                      class="list-group-item p-0 ml-4 subCateg-car"
                      :class="`subCateg-${vehicleCategory.id} subCateg`"
                    >
                      <input
                        :id="`${vehicleCategory.id}-${sousCategVehicule.id}`"
                        type="checkbox"
                        :value="sousCategVehicule.id"
                        name="vehiculeSelected"
                        :class="`childCkeck-${vehicleCategory.id} childCkeck-${vehicleCategory.id}-${sousCategVehicule.id}`"
                        class="all-car"
                        @change="
                          onItemInputChange(
                            vehicleCategory.id,
                            sousCategVehicule.id
                          )
                        "
                      />
                      <label class="form-check-label pl-2">{{
                        sousCategVehicule.nom
                      }}</label>
                    </div>
                  </div>
                  <!-- fin  recuperation sous categories vehicules -->
                </div>
              </div>
            </section>
          </div>
        </div>
        <div
          id="pills-driver"
          class="tab-pane fade"
          role="tabpanel"
          aria-labelledby="pills-driver-tab"
          style="overflow: auto"
        >
          <div class="p-2">
            <section v-if="errored">
              <p>
                Nous sommes désolés, nous ne sommes pas en mesure de récupérer
                ces informations pour le moment. Veuillez réessayer
                ultérieurement.
              </p>
            </section>

            <section v-else>
              <div v-if="loading" class="loading">
                <b-spinner label="Chargement..." />
              </div>

              <div v-else>
                <!--  selection dut tout -->
                <div class="mt-2">
                  <div class="display_item">
                    <input
                      type="checkbox"
                      class="checkAllChildAndSubChild-driver all-driver"
                      @click="checkAllChildAndSubChild('driver')"
                    />
                  </div>
                  <div
                    class="display_item ml-2"
                    @click="toggleAllSubCateg('driver')"
                  >
                    <div class="chevron_height chevron_block_size">
                      <i
                        class="chevron_style fa fa-chevron-up rotate-driver"
                        aria-hidden="true"
                      />
                    </div>
                    <div class="chevron_height">
                      <i
                        class="chevron_style fa fa-chevron-down rotate-driver"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                </div>

                <!-- fin selection du tout -->

                <div
                  v-for="(driver, index) in listDriver"
                  :key="driver.id"
                  data-driver-categories
                  class="list-group-item p-0"
                >
                  <input
                    v-if="shouldDisplayItemsUnderCategory('driver', driver.id)"
                    :id="`dc-index-${index}`"
                    type="checkbox"
                    :class="`parentCkeck-${driver.id}`"
                    class="all-driver"
                    @click="checkAllChild(driver.id)"
                  />
                  <em
                    v-if="shouldDisplayItemsUnderCategory('driver', driver.id)"
                    class="fas fa-user pl-2 pr-2"
                    @click="toggleSubCateg(driver.id)"
                  />
                  <label
                    v-if="shouldDisplayItemsUnderCategory('driver', driver.id)"
                    class="form-check-label"
                    >{{ driver.libelle }}({{
                      countDriversUnderCategory(driver.id)
                    }})</label
                  >

                  <div
                    v-for="(sousDriver, indexl) in getChildrenCategories(
                      CategoriesChauffeur,
                      driver.id
                    )"
                    v-show="
                      shouldDisplayItemsUnderCategory('driver', driver.id)
                    "
                    :key="sousDriver.id"
                    data-driver-categories-children-category
                    class="list-group-item p-0 ml-4 subCateg-driver"
                    :class="`subCateg-${driver.id} subCateg`"
                  >
                    <input
                      v-if="
                        shouldDisplayItemsUnderCategory('driver', driver.id)
                      "
                      :id="`ds-index-${
                        indexl + sousDriver.libelle + driver.id
                      }`"
                      type="checkbox"
                      :class="`childCkeck-${driver.id}`"
                      class="all-driver"
                      @change="onItemInputChange(driver.id)"
                    />
                    <em
                      v-if="
                        shouldDisplayItemsUnderCategory('driver', driver.id)
                      "
                      class="fas fa-user pl-2 pr-2"
                      @click="toggleSubCateg(sousDriver.id)"
                    />
                    <label
                      v-if="
                        shouldDisplayItemsUnderCategory('driver', driver.id)
                      "
                      class="form-check-label pl-2"
                      >{{ sousDriver.libelle }}({{
                        getDriversByCategory(sousDriver.id).length
                      }})</label
                    >
                    <!-- categories -> children categories -> items-->
                    <div
                      v-for="sousCategChauffeurWithChild in getDriversByCategory(
                        sousDriver.id
                      )"
                      :key="sousCategChauffeurWithChild.id"
                      data-driver-categories-children-category-vehicles
                      class="list-group-item p-0 ml-4 subCateg-driver"
                      :class="`subCateg-${sousDriver.id} subCateg`"
                    >
                      <input
                        :id="`${sousDriver.id}-${sousCategChauffeurWithChild.id}`"
                        type="checkbox"
                        name="driverSelected"
                        :value="sousCategChauffeurWithChild.id"
                        :class="`commonChild-${driver.id} childCheckSubCateg-${sousDriver.id}`"
                        class="all-driver"
                        @change="
                          toggleParentCategoryInput(
                            $event,
                            driver.id,
                            sousDriver.id
                          )
                        "
                      />
                      <label class="form-check-label pl-2">{{
                        sousCategChauffeurWithChild.nom
                      }}</label>
                    </div>
                  </div>

                  <!-- driver categories -> items -->
                  <div v-if="driver.parent_id === null">
                    <div
                      v-for="sousCategChauffeur in getDriversByCategory(
                        driver.id
                      )"
                      :key="sousCategChauffeur.id"
                      class="list-group-item p-0 ml-4 subCateg-driver"
                      :class="`subCateg-${driver.id} subCateg`"
                    >
                      <input
                        :id="`${driver.id}-${sousCategChauffeur.id}`"
                        type="checkbox"
                        name="driverSelected"
                        :value="sousCategChauffeur.id"
                        :class="`childCkeck-${driver.id} childCkeck-${driver.id}-${sousCategChauffeur.id}`"
                        class="all-driver"
                        @change="
                          onItemInputChange(driver.id, sousCategChauffeur.id)
                        "
                      />
                      <label class="form-check-label pl-2">{{
                        sousCategChauffeur.nom
                      }}</label>
                    </div>
                  </div>
                  <!-- fin test recuperation sous categories chauffeurs -->
                </div>
              </div>
            </section>
          </div>
        </div>
        <div
          id="pills-circuit"
          class="tab-pane fade"
          role="tabpanel"
          aria-labelledby="pills-circuit-tab"
          style="overflow: auto"
        >
          <div class="p-2">
            <section v-if="errored">
              <p>
                Nous sommes désolés, nous ne sommes pas en mesure de récupérer
                ces informations pour le moment. Veuillez réessayer
                ultérieurement.
              </p>
            </section>

            <section v-else>
              <div v-if="loading" class="loading">
                <b-spinner label="Chargement..." />
              </div>
              <div v-else>
                <!--  selection dut tout -->
                <div class="mt-2">
                  <div class="display_item">
                    <input
                      type="checkbox"
                      class="checkAllChildAndSubChild-circuit all-circuit"
                      @click="checkAllChildAndSubChild('circuit')"
                    />
                  </div>
                  <div
                    class="display_item ml-2"
                    @click="toggleAllSubCateg('circuit')"
                  >
                    <div class="chevron_height chevron_block_size">
                      <i
                        class="chevron_style fa fa-chevron-up rotate-circuit"
                        aria-hidden="true"
                      />
                    </div>
                    <div class="chevron_height">
                      <i
                        class="chevron_style fa fa-chevron-down rotate-circuit"
                        aria-hidden="true"
                      />
                    </div>
                  </div>
                </div>

                <!-- Circuits -->

                <div
                  v-for="(circuit, index) in circuitCategories"
                  v-show="
                    shouldDisplayItemsUnderCategory('circuit', circuit.id)
                  "
                  :key="circuit.id"
                  data-circuits-categories
                  class="list-group-item p-0"
                >
                  <input
                    :id="`dc-index-${index}`"
                    type="checkbox"
                    :class="`parentCkeck-${circuit.id}`"
                    class="all-circuit"
                    @click="checkAllChild(circuit.id)"
                  />
                  <em
                    class="fas fa-road pl-2 pr-2"
                    @click="toggleSubCateg(circuit.id)"
                  />
                  <label class="form-check-label"
                    >{{ circuit.libelle }}({{
                      countCircuitsUnderCategory(circuit.id)
                    }})</label
                  >

                  <!-- Circuit categories -> children categories -->
                  <div
                    v-for="(sousCircuit, indexl) in getChildrenCategories(
                      CategoriesCircuit,
                      circuit.id
                    )"
                    :key="indexl"
                    data-circuits-categories-children-category
                    class="list-group-item p-0 ml-4 subCateg-circuit"
                    :class="`subCateg-${circuit.id} subCateg`"
                  >
                    <input
                      v-if="
                        shouldDisplayItemsUnderCategory(
                          'circuit',
                          sousCircuit.id
                        )
                      "
                      :id="`ds-index-${
                        indexl + sousCircuit.libelle + circuit.id
                      }`"
                      type="checkbox"
                      :class="`childCkeck-${circuit.id}`"
                      class="all-circuit"
                      @click="onItemInputChange(circuit.id)"
                    />
                    <em
                      v-if="
                        shouldDisplayItemsUnderCategory(
                          'circuit',
                          sousCircuit.id
                        )
                      "
                      class="fas fa-road pl-2 pr-2"
                      @click="toggleSubCateg(sousCircuit.id)"
                    />
                    <label
                      v-if="
                        shouldDisplayItemsUnderCategory(
                          'circuit',
                          sousCircuit.id
                        )
                      "
                      class="form-check-label pl-2"
                    >
                      {{ sousCircuit.libelle }}({{
                        getCircuitsByCategory(sousCircuit.id).length
                      }})
                    </label>

                    <!-- Circuit categories -> children categories -> vehicles -->
                    <div
                      v-for="(
                        sousCategCircuitWithChild, circuitIndex
                      ) in getCircuitsByCategory(sousCircuit.id)"
                      :key="circuitIndex"
                      data-circuits-categories-children-category-vehicle
                      class="list-group-item p-0 ml-4 subCateg-circuit"
                      :class="`subCateg-${sousCircuit.id} subCateg`"
                    >
                      <input
                        :id="`${sousCircuit.id}-${sousCategCircuitWithChild.circuit_id}`"
                        type="checkbox"
                        name="circuitSelected"
                        :value="sousCategCircuitWithChild.circuit_id"
                        :class="`commonChild-${circuit.id} childCheckSubCateg-${sousCircuit.id}`"
                        class="all-circuit"
                        @change="
                          toggleParentCategoryInput(
                            $event,
                            circuit.id,
                            sousCircuit.id
                          )
                        "
                      />
                      <label class="form-check-label pl-2">{{
                        sousCategCircuitWithChild.nom_court
                      }}</label>
                    </div>
                  </div>

                  <!-- Circuit categories -> vehicles -->
                  <div v-if="circuit.parent_id === null">
                    <div
                      v-for="(
                        sousCategCircuit, indexl
                      ) in getCircuitsByCategory(circuit.id)"
                      :key="indexl"
                      data-circuits-categories-vehicles
                      class="list-group-item p-0 ml-4 subCateg-circuit"
                      :class="`subCateg-${circuit.id} subCateg`"
                    >
                      <input
                        :id="`${circuit.id}-${sousCategCircuit.circuit_id}`"
                        type="checkbox"
                        name="circuitSelected"
                        :value="sousCategCircuit.circuit_id"
                        :class="`childCkeck-${circuit.id} childCkeck-${circuit.id}-${sousCategCircuit.circuit_id}`"
                        class="all-circuit"
                        @click="
                          onItemInputChange(
                            circuit.id,
                            sousCategCircuit.circuit_id
                          )
                        "
                      />
                      <label class="form-check-label pl-2">{{
                        sousCategCircuit.nom_court
                      }}</label>
                    </div>
                  </div>
                  <!-- fin test recuperation sous categories circuits -->
                </div>
              </div>
            </section>
          </div>
        </div>

        <div
          id="pills-list"
          class="tab-pane fade"
          role="tabpanel"
          aria-labelledby="pills-list-tab"
        >
          <div class="p-2">
            <section v-if="errored">
              <p>
                Nous sommes désolés, nous ne sommes pas en mesure de récupérer
                ces informations pour le moment. Veuillez réessayer
                ultérieurement.
              </p>
            </section>

            <section v-else>
              <div v-if="loading" class="loading">
                <b-spinner label="Chargement..." />
              </div>

              <div
                v-for="(list, index) in 30"
                v-else
                :key="index"
                class="list-group-item p-0"
              >
                <input
                  :id="`cc-index-${index}`"
                  v-model="checkedlists"
                  type="checkbox"
                  :value="list"
                  :class="`parentCkeck-${list.id}`"
                  @click="checkAllChild(list.id)"
                />

                <label class="form-check-label pl-2"
                  >Liste ....{{ index }}</label
                >
              </div>
            </section>
          </div>
        </div>
      </div>

      <!-- date debut   -->
      <dateForm />
      <!-- date debut   -->

      <!-- raport debut   -->

      <div class="d-flex flex-column bd-highlight mb-3 mt-5">
        <div class="pl-2 bd-highlight d-none">
          <strong>Rapport</strong>
        </div>
        <div class="pl-2 mt-2 mb-2 bd-highlight d-none">
          <div class="p-0 custom-control custom-radio custom-control-inline">
            <label class="customradio">
              <span class="radiotextsty">Par jour</span>
              <input type="radio" checked="checked" name="radio" />
              <span class="checkmark" />
            </label>
          </div>
          <div class="p-0 custom-control custom-radio custom-control-inline">
            <label class="customradio">
              <span class="radiotextsty">Par période</span>
              <input type="radio" name="radio" />
              <span class="checkmark" />
            </label>
          </div>
        </div>

        <div class="pl-2 bd-highlight d-none">
          <input type="checkbox" value />
          <label class="form-check-label pl-2"
            >Masquer les résultats sans donnée</label
          >
        </div>
        <div class="pl-2 bd-highlight d-none">
          <input type="checkbox" value />
          <label class="form-check-label pl-2"
            >Grouper le rapport par dates</label
          >
        </div>
      </div>

      <!-- raport fin   -->

      <div class="container mt-3 mb-3">
        <div class="d-flex bd-highlight mb-3">
          <div v-show="validForm" class="w-100 bd-highlight pr-2">
            <div
              class="alert alert-warning alert-dismissible fade show p-0"
              role="alert"
            >
              <ul class="ul_alert_style">
                <li
                  v-show="validDate"
                  v-html="$t('ecoconduite.no_date_selected')"
                />
              </ul>
            </div>
          </div>

          <div class="ml-auto bd-highlight">
            <div>
              <button class="block_shadow btn_validate" @click="showResults">
                {{ $t('buttons.valid') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
/* import axios from "axios"; */
import $ from 'jquery'
import dateForm from './dateForm.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'SearchForm',
  components: {
    dateForm,
  },
  data() {
    return {
      selectedTabName: 'vehicle',
      validForm: false,
      showItems: false,
      validDate: false,
      checkError: '',
      checkedlists: [],
      errored: false,
      option: {
        x: false,
        y: true,
        width: 5,
        widthOnHover: 5,
        wheelDistance: 1,
        keep: true,
        skidwayStyle: {
          'background-color': '#d7cfcf',
          'border-radius': '8px',
        },
        sliderStyle: {
          'background-color': '#555',
          'border-radius': '8px',
        },
      },
    }
  },
  computed: {
    vehicleCategories() {
      let vehicule = this.CategoriesVehicles
      return vehicule.filter(function (v) {
        return v.parent_id === ''
      })
    },

    listDriver() {
      let driver = this.CategoriesChauffeur
      return driver.filter(function (d) {
        return d.parent_id === null
      })
    },
    circuitCategories() {
      let circuit = this.CategoriesCircuit
      return circuit.filter(function (c) {
        return c.parent_id === null
      })
    },
    disabledtn() {
      return $('.subCateg-car').prop('checked', true).length
    },

    ...mapGetters({
      loading: 'ecoConduite/getLoading',
      CategoriesVehicles: 'ecoConduite/getCategoriesVehicles',
      CategoriesChauffeur: 'ecoConduite/getCategoriesChauffeur',
      CategoriesCircuit: 'ecoConduite/getCategoriesCircuit',

      availableVehicles: 'ecoConduite/getSousCategoriesVehicles',
      availableDrivers: 'ecoConduite/getSousCategoriesChauffeur',
      availableCircuits: 'ecoConduite/getSousCategoriesCircuit',
      searchBarInputs: 'searchbar/getSearchBarInputText',
      selectedDate: 'ecoConduite/getSelectedDate',
    }),
  },
  watch: {
    loading() {
      if (!this.loading) {
        this.$nextTick(() => {
          $('.subCateg').toggle(false)
        })
      }
    },
    searchBarInputs(input) {
      this.$nextTick(() => {
        $('.subCateg').toggle(false)

        this.unfoldMatchedSubcategories(this.availableVehicles, input, {
          nameProp: 'nom',
          categoryProp: 'categorie_id',
          categoryNameProp: 'categoryName',
        })
        this.unfoldMatchedSubcategories(this.availableDrivers, input, {
          nameProp: 'nom',
          categoryProp: 'chauffeur_categorie_id',
          categoryNameProp: 'chauffeur_categorie_nom',
        })
        this.unfoldMatchedSubcategories(this.availableCircuits, input, {
          nameProp: 'nom_court',
          categoryProp: 'category_id',
          categoryNameProp: 'categorie_nom',
        })
      })
    },
  },
  methods: {
    toggleSelectedTab(name) {
      this.selectedTabName = name
    },
    unfoldMatchedSubcategories(entities, input = '', options = {}) {
      let includes = (v) =>
        input.length >= 2 &&
        (v || '').toString().toLowerCase().indexOf(input.toLowerCase()) !== -1
      entities
        .filter(function (v) {
          return (
            //Match by item name
            includes(v[options.nameProp || 'nom']) ||
            //Match by item category name
            includes(v[options.categoryNameProp || 'categorie_nom']) ||
            //Match by item category -> parent category name
            (!v.parentCategoryName ? false : includes(v.parentCategoryName))
          )
        })
        .forEach((v) => {
          let itemCat = $(
            '.subCateg-' + v[options.categoryProp || 'categorie_id']
          )
          itemCat.toggle(true)
          itemCat.parents('.subCateg').toggle(true) //Unfold parent (if any)
        })
    },
    async showResults() {
      await this.selectedTab()
      this.$loader.showUntilPromiseResolves(
        this.$store.dispatch('ecoConduite/fetchChartsData', {
          type: this.selectedTabName,
          ids: this.getSelectedIds(),
        })
      )
    },
    getSelectedIds() {
      let getIds = (name) =>
        $(`input[name='${name}']:checked`)
          .toArray()
          .map((el) => $(el).val())
      let domInputNameTable = {
        vehicle: 'vehiculeSelected',
        driver: 'driverSelected',
        circuit: 'circuitSelected',
      }
      return getIds(domInputNameTable[this.selectedTabName])
    },
    async selectedTab() {
      this.$loader.show()

      var divCar = document
        .getElementById('pills-car-tab')
        .getAttribute('aria-selected')

      var divDriver = document
        .getElementById('pills-driver-tab')
        .getAttribute('aria-selected')

      var divCircuit = document
        .getElementById('pills-circuit-tab')
        .getAttribute('aria-selected')

      console.debugVerbose(8, 'SearchForm.selectedTab', {
        divCar,
        divDriver,
        divCircuit,
      })

      const isValidSelection = (count) =>
        count <= parseInt(import.meta.env.VITE_FILTERS_MAX_SELECTION || 100)
      const showInvalidSelectionAlert = () =>
        this.$store.dispatch('alert/addAlert', {
          type: 'info',
          title: 'Validation',
          text: `alerts.FILTERS_MAX_SELECTION`,
          params: {
            number: import.meta.env.VITE_FILTERS_MAX_SELECTION || 100,
          },
        })

      var idsList = []
      this.initialize()

      if (divCar === 'true') {
        console.log('search:vehicle')
        this.checkError = $('#pills-car-tab').text()
        $("input[name='vehiculeSelected']:checked").each(function () {
          idsList.push($(this).val())
        })
        if (this.selectedDate == null || this.selectedDate.length == 0) {
          if (this.selectedDate == null || this.selectedDate.length == 0) {
            this.validDate = true
            this.validForm = true
          }
          console.log('search:vehicle:invalid-date')
        } else {
          if (!isValidSelection(idsList.length)) {
            showInvalidSelectionAlert()
            console.log('search:vehicle:invalid-selection')
            return
          }

          console.log('search:vehicle:dispatch-action')
          this.$store.commit('ecoConduite/setSelectedTab', 'car')
          this.$store.commit('ecoConduite/setCheckedCategoriesIdList', idsList)
          await this.$store.dispatch('ecoConduite/carEcoResultsData')
          this.initialize()
        }
      }

      if (divDriver === 'true') {
        this.checkError = $('#pills-driver-tab').text()
        $("input[name='driverSelected']:checked").each(function () {
          idsList.push($(this).val())
        })
        if (this.selectedDate == null || this.selectedDate.length == 0) {
          if (this.selectedDate == null || this.selectedDate.length == 0) {
            this.validDate = true
            this.validForm = true
          }
        } else {
          if (!isValidSelection(idsList.length)) {
            showInvalidSelectionAlert()
            return
          }

          this.$store.commit('ecoConduite/setSelectedTab', 'driver')
          this.$store.commit('ecoConduite/setCheckedCategoriesIdList', idsList)
          await this.$store.dispatch('ecoConduite/driverEcoResultsData')
          this.initialize()
        }
      }
      if (divCircuit === 'true') {
        this.checkError = $('#pills-circuit-tab').text()
        $("input[name='circuitSelected']:checked").each(function () {
          idsList.push($(this).val())
        })
        if (this.selectedDate == null || this.selectedDate.length == 0) {
          if (this.selectedDate == null || this.selectedDate.length == 0) {
            this.validDate = true
            this.validForm = true
          }
        } else {
          if (!isValidSelection(idsList.length)) {
            showInvalidSelectionAlert()
            return
          }

          this.$store.commit('ecoConduite/setSelectedTab', 'circuit')
          this.$store.commit('ecoConduite/setCheckedCategoriesIdList', idsList)
          await this.$store.dispatch('ecoConduite/circuitEcoResultsData')
          this.initialize()
        }
      }

      this.$loader.hide()
    },
    initialize() {
      this.checkError = ''
      this.validForm = false
      this.validDate = false
    },
    toggleVisible() {
      this.showItems = !this.showItems
    },
    shouldDisplayItemsUnderCategory(tag, id) {
      switch (tag) {
        case 'car':
          return this.countVehiclesUnderCategory(id) > 0
        case 'driver':
          return this.countDriversUnderCategory(id) > 0

        case 'circuit':
          return this.countCircuitsUnderCategory(id) > 0
      }
    },
    getChildrenCategories(categories, id) {
      return categories.filter(function (v) {
        return v.parent_id === id
      })
    },
    countItemsUnderCategory(categories, getItemsFunctionName, categoryId) {
      let childrenCategories = this.getChildrenCategories(
        categories,
        categoryId
      )
      let sum = 0
      childrenCategories.forEach((childrenCategory) => {
        sum += this[getItemsFunctionName](childrenCategory.id).length
      })
      return this[getItemsFunctionName](categoryId).length + sum
    },
    countVehiclesUnderCategory(categoryId) {
      return this.countItemsUnderCategory(
        this.CategoriesVehicles,
        'getVehiclesByCategory',
        categoryId
      )
    },
    countDriversUnderCategory(categoryId) {
      return this.countItemsUnderCategory(
        this.CategoriesChauffeur,
        'getDriversByCategory',
        categoryId
      )
    },
    countCircuitsUnderCategory(categoryId) {
      return this.countItemsUnderCategory(
        this.CategoriesCircuit,
        'getCircuitsByCategory',
        categoryId
      )
    },
    getItemsByCategory(itemType, categoryId) {
      let listNames = {
        vehicle: 'availableVehicles',
        driver: 'availableDrivers',
        circuit: 'availableCircuits',
      }
      let props = {
        vehicle: ['nom', 'categorie_id', 'categoryName'],
        driver: ['nom', 'chauffeur_categorie_id', 'chauffeur_categorie_nom'],
        circuit: ['nom_court', 'category_id', 'categorie_nom'],
      }
      let includes = (v) =>
        this.searchBarInputs.length >= 2 &&
        (v || '')
          .toString()
          .toLowerCase()
          .indexOf(this.searchBarInputs.toLowerCase()) !== -1
      return this[listNames[itemType]].filter((v) => {
        return (
          (v[props[itemType][1]] || '').toString() == categoryId.toString() &&
          (this.searchBarInputs.length < 2 ||
            includes(v[props[itemType][0]]) || //By name
            includes(v[props[itemType][2]]) || //By cat name
            (!v.parentCategoryName ? false : includes(v.parentCategoryName))) //By parent cat name
        )
      })
    },
    getVehiclesByCategory(categoryId) {
      return this.getItemsByCategory('vehicle', categoryId)
    },
    getDriversByCategory(categoryId) {
      return this.getItemsByCategory('driver', categoryId)
    },
    getCircuitsByCategory(categoryId) {
      return this.getItemsByCategory('circuit', categoryId)
    },
    checkAllChildAndSubChild: function (tabName) {
      $('.checkAllChildAndSubChild-' + tabName).prop('checked')
        ? $('.all-' + tabName).prop('checked', true)
        : $('.all-' + tabName).prop('checked', false)
    },
    toggleAllSubCateg: function (tabName) {
      $('.subCateg-' + tabName).toggle()
      $('.rotate-' + tabName).toggleClass('down')
    },
    toggleSubCateg: function (id) {
      $('.subCateg-' + id).toggle()
    },
    checkAllChild: function (id) {
      if (
        typeof $('.parentCkeck-' + id)
          .parent()
          .attr('data-vehicle-category') !== 'undefined'
      ) {
        if ($('.parentCkeck-' + id).prop('checked')) {
          $('.parentCkeck-' + id)
            .next()
            .removeClass('far')
          $('.parentCkeck-' + id)
            .next()
            .addClass('fas')
        } else {
          $('.parentCkeck-' + id)
            .next()
            .removeClass('fas')
          $('.parentCkeck-' + id)
            .next()
            .addClass('far')
        }
      }
      $('.parentCkeck-' + id).prop('checked')
        ? $('.childCkeck-' + id + ', .commonChild-' + id).prop('checked', true)
        : $('.childCkeck-' + id + ', .commonChild-' + id).prop('checked', false)
    },
    onItemInputChange: function (idp, idc) {
      this.$nextTick(() => {
        //TODO: Refactor / Remove
        let key = idc ? `${idp}-${idc}` : `${idp}`
        $('.childCkeck-' + key).prop('checked')
          ? $('.childCheckSubCateg-' + idc).prop('checked', true)
          : $('.childCheckSubCateg-' + idc).prop('checked', false)

        this.toggleRootParentCategoryInput(idp)
      })
    },
    toggleRootParentCategoryInput(idp) {
      if (
        typeof $('.parentCkeck-' + idp)
          .parent()
          .attr('data-vehicle-category') !== 'undefined'
      ) {
        if (
          $('.parentCkeck-' + idp)
            .parent()
            .find('input')
            .slice(1)
            .filter(':checked').length !== 0
        ) {
          $('.parentCkeck-' + idp)
            .next()
            .removeClass('far')
          $('.parentCkeck-' + idp)
            .next()
            .addClass('fas')
        } else {
          $('.parentCkeck-' + idp)
            .next()
            .removeClass('fas')
          $('.parentCkeck-' + idp)
            .next()
            .addClass('far')
        }
      }
      if (
        $('.parentCkeck-' + idp)
          .parent()
          .find('input')
          .slice(1)
          .filter(':checked').length ==
        $('.parentCkeck-' + idp)
          .parent()
          .find('input')
          .slice(1).length
      ) {
        $('.parentCkeck-' + idp).prop('checked', true)
      } else {
        $('.parentCkeck-' + idp).prop('checked', false)
      }
    },
    toggleParentCategoryInput: function (e, idp) {
      this.$nextTick(() => {
        let parent = $(e.target).parent().parent()
        if (
          parent.find('input').slice(1).filter(':checked').length ==
          parent.find('input').slice(1).length
        ) {
          parent.find('.childCkeck-' + idp).prop('checked', true)
        } else {
          parent.find('.childCkeck-' + idp).prop('checked', false)
        }
        this.toggleRootParentCategoryInput(idp)
      })
    },
  },
}
</script>
<style scoped>
.loading {
  text-align: center;
  padding-top: 75px;
}

.card {
  box-shadow: 0px 0px 7px;
}

i {
  cursor: pointer;
}

section {
  height: 250px;
}

.filter {
  font-size: 12px;
  font-weight: 600;
}

.fa-folder:before,
.fa-user:before,
.fa-road:before {
  color: gray;
}
.category.list-group {
  min-height: 150px;
  max-height: 250px;
  overflow-x: hidden;
  overflow-y: auto;
}

a {
  color: #495057;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #6c757d;
  border-bottom: 4px solid;
  border-radius: 0;
  font-weight: 800;
}
.list-group-item {
  padding: 0.2rem 0rem;
  border: 0;
}
.nav-pills .nav-link.active {
  background-color: transparent;
}
.block_shadow {
  margin: 0px;
  border: 1px solid #fff;
  box-shadow: 0px 0px 7px;
  border-radius: 5px;
}
.btn_validate {
  background-color: #646464;
  color: #ffffff;
  font-size: 13px;
  padding: 2px 6px;
  font-family: Arial;
}
.btn_favorite {
  background-color: #ffffff;
  color: #000000;
  border-radius: 2px;
  font-size: 13px;
  padding: 1px 6px;
  font-family: Arial;
  box-shadow: 0px 0px 2px !important;
}

/*debut check*/
input[type='checkbox']:focus {
  outline: 1px solid rgba(0, 0, 0, 0.2);
}

input[type='checkbox'] {
  background-color: #fff;
  border: 1px solid #495057;
  border-radius: 3px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 15px;
  height: 15px;
  cursor: pointer;
  position: relative;
}

input[type='checkbox']:checked {
  border-radius: 3px;
  background-color: #495057;
  background: #495057
    url(data:image/gif;base64,R0lGODlhCwAKAIABAP////3cnSH5BAEKAAEALAAAAAALAAoAAAIUjH+AC73WHIsw0UCjglraO20PNhYAOw==)
    1px 1px no-repeat;
}
/*fin check*/

/*debut radio*/

.customradio {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 0px;
  cursor: pointer;
  font-size: 18px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.customradio input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkmark {
  position: absolute;
  top: 0.3rem;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: white;
  border-radius: 50%;
  border: 1px solid #495057;
}

.customradio:hover input ~ .checkmark {
  background-color: transparent;
}

.customradio input:checked ~ .checkmark {
  background-color: white;
  border: 1px solid #495057;
}

.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

.customradio input:checked ~ .checkmark:after {
  display: block;
}

.customradio .checkmark:after {
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #495057;
}

/*fin radio*/
.chevron_style {
  font-weight: bold;
  font-size: 11px;
  color: #000;
}
.chevron_height {
  height: 15px;
}
.display_item {
  display: inline-block;
}
.chevron_block_size {
  margin-top: -7px;
  margin-bottom: -7px;
}

.rotate {
  /* transition: all .1s linear; */
  -moz-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.main_rotate {
  -moz-transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  transition: all 0.2s linear;
}
.main_rotate.down {
  transform: rotate(180deg);
}
.ul_alert_style {
  padding-inline-start: 30px;
  margin-bottom: 0;
}

.fa-folder {
  cursor: pointer;
}
</style>

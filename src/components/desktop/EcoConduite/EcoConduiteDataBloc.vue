<template>
  <div class="row pt-2">
    <div class="col-md-6 pb-4">
      <collapse index="1" :name="$t('ecoconduite.Consommation')">
        <consommation
          :average="stats.consumptionAverage"
          :reference="stats.consumptionAverage"
        />
      </collapse>
    </div>

    <div v-if="false" class="col-md-6 pb-4">
      <collapse index="2" :name="$t('ecoconduite.Benchmark')">
        <benchmark />
      </collapse>
    </div>

    <div class="col-md-6 pb-4">
      <collapse index="3" :name="$t('ecoconduite.RejectCO2')">
        <rejetCo2 :value="stats.co2" :distance-meters="stats.distanceTotal" />
      </collapse>
    </div>

    <div class="col-md-6 pb-4">
      <collapse index="4" :name="$t('ecoconduite.Note conduite')">
        <note percent="71.5" :note="rating" />
      </collapse>
    </div>

    <div v-if="false" class="col-md-6 pb-4">
      <collapse index="test" name="Top-Flop">
        <topFlop />
      </collapse>
    </div>
  </div>
</template>
<script>
import consommation from './components/consommation.vue'
import note from './components/note.vue'
import benchmark from './components/benchmark.vue'
import rejetCo2 from './components/rejetCo2.vue'
import topFlop from './components/topFlop.vue'
import collapse from './components/collapse.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'EcoConduiteDataBloc',
  components: {
    consommation,
    benchmark,
    note,
    rejetCo2,
    topFlop,
    collapse,
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      stats: 'ecoConduite/getChartsData',
      trueRating: 'ecoConduite/getEcoResultsAvrRating',
    }),
    rating() {
      return parseFloat(this.trueRating || '0')
        .toFixed(1)
        .toString()
        .split('.')
        .join(',')
    },
  },
  methods: {},
}
</script>
<style lang="scss" scoped></style>

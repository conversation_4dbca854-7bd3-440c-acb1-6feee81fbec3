<template>
  <div class="container">
    <nav
      id="pills-tab"
      class="nav nav-pills flex-column flex-sm-row"
      role="tablist"
    >
      <a
        id="pills-car-tab"
        class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link active"
        data-toggle="pill"
        href="#pills-car"
        role="tab"
        aria-controls="pills-car"
        aria-selected="true"
        >Vehicule</a
      >
      <a
        id="pills-driver-tab"
        class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link"
        data-toggle="pill"
        href="#pills-driver"
        role="tab"
        aria-controls="pills-driver"
        aria-selected="false"
        >Chauffeur</a
      >
      <a
        id="pills-circuit-tab"
        class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link"
        data-toggle="pill"
        href="#pills-circuit"
        role="tab"
        aria-controls="pills-circuit"
        aria-selected="false"
        >Circuit</a
      >
      <a
        id="pills-list-tab"
        class="w-25 p-0 pt-2 pb-1 flex-sm-fill text-sm-center nav-link"
        data-toggle="pill"
        href="#pills-list"
        role="tab"
        aria-controls="pills-list"
        aria-selected="false"
        >Liste</a
      >
    </nav>
    <div id="pills-tabContent" class="tab-content">
      <div
        id="pills-car"
        class="tab-pane fade show active"
        role="tabpanel"
        aria-labelledby="pills-car-tab"
      >
        <div class="p-2">
          <section v-if="errored">
            <p>
              Nous sommes désolés, nous ne sommes pas en mesure de récupérer ces
              informations pour le moment. Veuillez réessayer ultérieurement.
            </p>
          </section>

          <section v-else>
            <div v-if="loading" class="loading">
              <b-spinner label="Chargement..." />
            </div>

            <div
              v-for="(vehicule, index) in listVehicule"
              v-else
              :key="index"
              class="list-group-item"
              style="overflow: auto"
            >
              <input
                :id="`vc-index-${index}`"
                v-model="checkedVehicules"
                type="checkbox"
                :value="vehicule"
                :class="`parentCkeck-${vehicule.id}`"
                @click="checkAllChild(vehicule.id)"
              />

              <i
                class="fas fa-truck pl-2 pr-2"
                @click="toggleSubCateg(vehicule.id)"
              />
              <label class="form-check-label"
                >{{ vehicule.nom }}({{
                  listSousVehicule(vehicule.id).length
                }})</label
              >
              <div
                v-for="(sousVehicule, indexl) in listSousVehicule(vehicule.id)"
                :key="indexl"
                class="list-group-item ml-4"
                :class="`subCateg-${vehicule.id}`"
              >
                <input
                  :id="`vs-index-${indexl + sousVehicule.nom + vehicule.id}`"
                  v-model="checkedVehicules"
                  type="checkbox"
                  :value="sousVehicule"
                  :class="`childCkeck-${vehicule.id}`"
                  @click="checkChild(vehicule.id)"
                />

                <label class="form-check-label pl-2">{{
                  sousVehicule.nom
                }}</label>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
/* import axios from "axios"; */
import $ from 'jquery'

import { mapGetters } from 'vuex'

export default {
  name: 'Tabs',
  components: {},
  data() {
    return {
      showItems: false,
      checkedVehicules: [],
      checkedDrivers: [],
      checkedCircuits: [],
      checkedlists: [],
      errored: false,
      option: {
        x: false,
        y: true,
        width: 5,
        widthOnHover: 5,
        keep: true,
        skidwayStyle: {
          'background-color': '#d7cfcf',
          'border-radius': '8px',
        },
        sliderStyle: {
          'background-color': '#555',
          'border-radius': '8px',
        },
      },
    }
  },
  computed: {
    listVehicule() {
      let vehicule = this.ResultsInfos.Vehicule
      return vehicule.filter(function (v) {
        return v.parent_id === 0
      })
    },

    listDriver() {
      let driver = this.ResultsInfos.Driver
      return driver.filter(function (d) {
        return d.parent_id === 0
      })
    },
    listCircuit() {
      let circuit = this.ResultsInfos.Circuit
      return circuit.filter(function (c) {
        return c.parent_id === 0
      })
    },

    ...mapGetters({
      loading: 'ecoConduite/getLoading',
      ResultsInfos: 'ecoConduite/getResultsInfo',
    }),
  },
  mounted() {
    //this.$store.dispatch("ecoConduite/getResults");
  },
  methods: {
    toggleVisible() {
      this.showItems = !this.showItems
    },
    listSousVehicule(id) {
      let sousVehicule = this.ResultsInfos.Vehicule
      return sousVehicule.filter(function (v) {
        return v.parent_id === id
      })
    },
    listSousDriver(id) {
      let sousDriver = this.ResultsInfos.Driver
      return sousDriver.filter(function (d) {
        return d.parent_id === id
      })
    },
    listSousCircuit(id) {
      let sousCircuit = this.ResultsInfos.Circuit
      return sousCircuit.filter(function (c) {
        return c.parent_id === id
      })
    },
    toggleSubCateg: function (id) {
      $('.subCateg-' + id).toggle()
    },
    checkAllChild: function (id) {
      $('.parentCkeck-' + id).prop('checked')
        ? $('.childCkeck-' + id).prop('checked', true)
        : $('.childCkeck-' + id).prop('checked', false)
    },
    checkChild: function (id) {
      $('.childCkeck-' + id + ':checked').length ==
      $('.childCkeck-' + id).length
        ? $('.parentCkeck-' + id).prop('checked', true)
        : $('.parentCkeck-' + id).prop('checked', false)
    },
  },
  /*items.data.Vehicule.nom */
}
</script>
<style scoped>
.loading {
  text-align: center;
  padding-top: 75px;
}

.card {
  box-shadow: 0px 0px 7px;
}

i {
  cursor: pointer;
}

section {
  height: 250px;
}

.filter {
  font-size: 12px;
  font-weight: 600;
}

.fa-truck:before {
  color: gray;
}
.category.list-group {
  min-height: 150px;
  max-height: 250px;
  overflow-x: hidden;
  overflow-y: auto;
}

a {
  color: #495057;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #6c757d;
  border-bottom: 4px solid;
  border-radius: 0;
  font-weight: 800;
}
.list-group-item {
  padding: 0.2rem 0rem;
  border: 0;
}
.nav-pills .nav-link.active {
  background-color: transparent;
}
.block_shadow {
  margin: 0px;
  border: 1px solid #fff;
  box-shadow: 0px 0px 7px;
  border-radius: 5px;
}
.btn_validate {
  background-color: #646464;
  color: #ffffff;
  font-size: 13px;
  padding: 2px 6px;
  font-family: Arial;
}
.btn_favorite {
  background-color: #ffffff;
  color: #000000;
  border-radius: 2px;
  font-size: 13px;
  padding: 1px 6px;
  font-family: Arial;
  box-shadow: 0px 0px 2px !important;
}
.list-group-item.ml-4 {
  display: none;
}
/*debut check*/
input[type='checkbox']:focus {
  outline: 1px solid rgba(0, 0, 0, 0.2);
}

input[type='checkbox'] {
  background-color: #fff;
  border: 1px solid #495057;
  border-radius: 3px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 15px;
  height: 15px;
  cursor: pointer;
  position: relative;
}

input[type='checkbox']:checked {
  border-radius: 3px;
  background-color: #495057;
  background: #495057
    url(data:image/gif;base64,R0lGODlhCwAKAIABAP////3cnSH5BAEKAAEALAAAAAALAAoAAAIUjH+AC73WHIsw0UCjglraO20PNhYAOw==)
    1px 1px no-repeat;
}
/*fin check*/

/*debut radio*/

.customradio {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 0px;
  cursor: pointer;
  font-size: 18px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.customradio input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkmark {
  position: absolute;
  top: 0.3rem;
  left: 0;
  height: 18px;
  width: 18px;
  background-color: white;
  border-radius: 50%;
  border: 1px solid #495057;
}

.customradio:hover input ~ .checkmark {
  background-color: transparent;
}

.customradio input:checked ~ .checkmark {
  background-color: white;
  border: 1px solid #495057;
}

.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

.customradio input:checked ~ .checkmark:after {
  display: block;
}

.customradio .checkmark:after {
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #495057;
}

/*fin radio*/
</style>

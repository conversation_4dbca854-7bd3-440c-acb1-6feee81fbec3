<template>
  <div>
    <div class="row-fluid">
      <div class="col-md-12">
        <SearchBar class="block" />
      </div>
    </div>

    <div class="row-fluid">
      <div class="col-md-12">
        <SearchForm class="block" />
      </div>
    </div>
  </div>
</template>

<script>
import SearchBar from '@desktop/EcoConduite/SearchBar.vue'
import SearchForm from '@desktop/EcoConduite/SearchForm.vue'
//import $ from "jquery";
export default {
  name: 'EcoConduiteComponent',
  components: {
    SearchBar,
    SearchForm,
  },
  props: {},
  mounted: function () {
    //let containerBlocWidth = screen.width / 4 + "px";
    //$(".containerBlocWidth").css("width", containerBlocWidth);
  },
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.block {
  margin-top: 15px;
}
</style>

<template>
  <div class="">
    <div class="vehiculeSearch">
      <label
        class="sr-only"
        for="inlineFormInputGroupUsername"
        v-text="$t('common.Rechercher')"
      />
      <div class="container color_background">
        <div class="input_search_container">
          <em
            v-show="inputText"
            class="fa fa-times input_search_icon"
            @click="emptySearch"
          />

          <input
            id="inlineFormInputGroupUsername"
            v-model="inputText"
            type="text"
            class="input_search"
            :placeholder="$t('common.Rechercher')"
            @keyup="handleInput"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'SearchBar',
  data() {
    return {
      inputText: '',
    }
  },

  computed: {},
  mounted() {
    this.$store.dispatch('searchbar/setSearchBarInputs', '')
  },
  methods: {
    handleInput() {
      this.$store.dispatch('searchbar/setSearchBarInputs', this.inputText)
    },
    emptySearch: function () {
      this.inputText = ''
      this.$store.dispatch('searchbar/setSearchBarInputs', this.inputText)
    },
  },
}
</script>
<style scoped>
i {
  cursor: pointer;
}
.input_search_container {
  display: flex;
  align-items: center;
}
.input_search_icon {
  color: red;
  padding: 5px;
  cursor: pointer;
}

.input_search:focus {
  background-color: #fff;
  border-color: #ced4da;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0);
  color: #495057;
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0);
}
.input_search {
  width: 100%;
  border: none;
}

.color_background {
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 3px;
  border-radius: 4px;
  box-shadow: 0px 0px 5px;
  padding: 1px 5px;
  position: relative;
}
</style>

<template>
  <div class="date_picker_form">
    <div class="input-group mr-sm-2">
      <div class="input-group-prepend">
        <div class="input-group-text">
          <img width="24px" height="24px" :src="calendarIcon" />
        </div>
      </div>
      <input
        v-model="range"
        type="text"
        class="form-control"
        @click="showPopCalander"
      />
    </div>

    <date-picker
      v-show="showCalendarPopup"
      v-model="range"
      :props="config"
      :lang="lang"
      type="date"
      :multiple="showMultipleDate"
      :range="showRangeDate"
      prefix-class="eco"
      format="DD MMMM YYYY"
      :disabled-date="disabledAfterYesterday"
      width="490"
      confirm
      :confirm-text="$t('buttons.valid')"
      :open="showCalendarPopup"
      popup-class="ecoconduite_classic_search_calendar_popup"
      @confirm="confirmSelection"
    >
      <div slot="footer" class="pl-2 mb-3 bd-highlight">
        <div
          class="p-0 custom-control custom-control-sm custom-control-inline d-none"
        >
          <label class="margin_auto mr-sm-2" for="debut"
            >{{ $t('common.debut') }} :</label
          >

          <input
            id="debut"
            v-model="debut"
            class
            type="time"
            value="00:00:00"
            name="debut"
          />
        </div>
        <div
          class="p-0 custom-control custom-control-sm custom-control-inline d-none"
        >
          <label class="margin_auto mr-sm-2" for="fin"
            >{{ $t('common.fin') }} :</label
          >

          <input id="fin" v-model="fin" class type="time" value="0:0" />
        </div>

        <div
          class="p-0 custom-control custom-control-sm custom-radio custom-control-inline"
        >
          <label class="customradio">
            <span class="radiotextsty">{{
              $t('ecoconduite.datepicker.by_day')
            }}</span>
            <input
              v-model="periode"
              type="radio"
              name="chaque jour"
              value="chaque jour"
              @click="showPeriode"
            />
            <span class="checkmark" />
          </label>
        </div>
        <div
          class="p-0 custom-control custom-control-sm custom-radio custom-control-inline"
        >
          <label class="customradio">
            <span class="radiotextsty">{{
              $t('ecoconduite.datepicker.by_period')
            }}</span>
            <input
              id="period"
              v-model="periode"
              type="radio"
              value="période"
              @click="showPeriode"
            />
            <span class="checkmark" />
          </label>
        </div>
      </div>

      <button
        slot="footer"
        class="mx-btn mx-datepicker-btn-confirm"
        @click="cancel"
      >
        {{ $t('buttons.cancel_alternative') }}
      </button>
    </date-picker>
  </div>
</template>

<script>
import DatePicker from 'vue2-datepicker'
import moment from 'moment'
import { mapGetters } from 'vuex'
import calendarIcon from '@c/desktop/EcoConduite/assets/calendar.svg'
export default {
  name: 'DateForm',

  components: {
    DatePicker,
  },
  props: {
    config: { type: Object, default: () => ({}) },
  },
  data() {
    return {
      date: '',
      range: [],
      debut: '00:00',
      fin: '00:00',
      periode: 'période',
      checkedData: true,
      showCalendarPopup: false,
      showInput: false,
      //i18n: Translate datepicker object inside i18n files instead (fallback to french)
      lang: this.$translations.datepicker || {
        days: ['Di', 'Lu', 'Ma', 'Me', 'Je', 'Ve', 'Sa'],
        months: [
          'Janvier',
          'Fevrier',
          'Mars',
          'Avril',
          'Mai',
          'Juin',
          'Juillet',
          'Aout',
          'Septembre',
          'Octobre',
          'Novembre',
          'Decembre',
        ],
      },
      calendarIcon,
    }
  },
  computed: {
    ...mapGetters({
      showMultipleDate: 'ecoConduite/getShowMultipleDate',
      showRangeDate: 'ecoConduite/getShowRangeDate',
    }),
  },
  mounted() {},
  methods: {
    showPeriode: function () {
      this.$store.dispatch('ecoConduite/switchPeriode')
    },
    cancel: function () {
      this.range = []
      this.showCalendarPopup = !this.showCalendarPopup
      this.$store.commit('ecoConduite/setSelectedDate', this.range)
    },
    confirmSelection: function (value) {
      let daysDiff =
        value.length === 1
          ? 0
          : Math.abs(moment(value[1]).diff(moment(value[0]), 'days'))

      let daysDiffLimit = parseInt(
        import.meta.env.VITE_FILTERS_MAX_SELECTION_DATES ||
          import.meta.env.VITE_FILTERS_MAX_SELECTION ||
          90
      )

      if (daysDiff >= daysDiffLimit) {
        return this.$store.dispatch('alert/addAlert', {
          type: 'info',
          title: 'Validation',
          text: `alerts.FILTERS_MAX_SELECTION`,
          params: {
            number:
              import.meta.env.VITE_FILTERS_MAX_SELECTION_DATES ||
              import.meta.env.VITE_FILTERS_MAX_SELECTION ||
              90,
          },
        })
      }

      this.$store.dispatch('ecoConduite/setDateRange', value)
      if (value[0] !== null && value[1] !== null && value.length !== 0) {
        let self = this
        let listDate = []
        for (var i = 0; i < value.length; i++) {
          let rangeDate = String(value[i]).split(' ')
          let startEndDate = []
          for (var j = 1; j < 4; j++) {
            startEndDate.push(rangeDate[j])
          }
          startEndDate.join('/')

          listDate.push(this.$date.formatDate(String(startEndDate)))
        }
        self.range =
          listDate.join(' -') + ' - ' + self.$t(`ecoconduite.${this.periode}`)
        /*         +
        " " +
        this.debut +
        " - " +
        this.fin; */

        this.$store.commit('ecoConduite/setSelectedDate', self.range)
        this.$emit('changeQueryInputDateTime', self.range)
        this.showCalendarPopup = !this.showCalendarPopup
      }
    },
    showPopCalander: function () {
      this.showCalendarPopup = !this.showCalendarPopup
    },
    disabledAfterYesterday(date) {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      return date > new Date(today.getTime() - 2 * 24 * 3600 * 1000)
    },
  },
}
</script>
<style scoped>
.input-group.mr-sm-2 {
  padding-right: 10px;
  position: absolute;
}
.input-group-text {
  background-color: transparent;
  border: 1px solid #212529;
}
.form-control {
  border-left: none;
  border-bottom: 1px solid #212529;
  border-top: 1px solid #212529;
  border-right: 1px solid #212529;
  padding-left: 0;
  font-size: 15px;
  height: 38px;
}
.form-control:focus {
  border-color: #212529;
  box-shadow: none;
}
.eco-datepicker-range {
  box-shadow: none;
}

.pos-rel {
  position: relative;
}
.p-0 {
  padding: 0;
}
.cursor-pointer {
  cursor: pointer;
}
.-date-range > i {
  position: absolute;
  top: 13px;
  left: 15px;
  pointer-events: none;
}
.-date-range > input {
  border: none;
  background-color: transparent;
  padding: 10px 15px 10px 35px;
  min-width: 240px;
}
.date_picker_form {
  padding: 10px 5px;
}

body .mx-datepicker-content {
  margin-left: 133px !important;
}
body .mx-datepicker-sidebar {
  margin-left: 133px !important;
}

/*debut radio*/

.customradio {
  display: block;
  position: relative;
  padding-left: 30px;
  margin-bottom: 0px;
  cursor: pointer;
  font-size: 15px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.customradio input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkmark {
  position: absolute;
  top: 0.3rem;
  left: 0;
  height: 15px;
  width: 15px;
  background-color: white;
  border-radius: 50%;
  border: 1px solid #495057;
}

.customradio:hover input ~ .checkmark {
  background-color: transparent;
}

.customradio input:checked ~ .checkmark {
  background-color: white;
  border: 1px solid #495057;
}

.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

.customradio input:checked ~ .checkmark:after {
  display: block;
}

.customradio .checkmark:after {
  top: 2px;
  left: 2px;
  width: 9px;
  height: 9px;
  border-radius: 50%;
  background: #495057;
}

/*fin radio*/

/* debut time */
.margin_auto {
  margin: auto;
}
input:focus {
  outline: 0;
}
input[type='time'] {
  border: 1px solid grey;
  border-radius: 5px;
  color: #2a2c2d;
  font-size: 12px;
  font-family: helvetica;
  width: 62px;
}

input[type='time']::-webkit-datetime-edit-fields-wrapper {
  display: flex;
}

input[type='time']::-webkit-datetime-edit-text {
  padding: 5px 4px;
}

input[type='time']::-webkit-datetime-edit-hour-field {
  background-color: #fff;
  border-radius: 15%;
  padding: 5px 5px;
}

input[type='time']::-webkit-datetime-edit-minute-field {
  background-color: #fff;
  border-radius: 15%;
  padding: 5px 5px;
}

input[type='time']::-webkit-datetime-edit-hour-field:hover,
input[type='time']::-webkit-datetime-edit-hour-field:focus,
input[type='time']::-webkit-datetime-edit-minute-field:hover,
input[type='time']::-webkit-datetime-edit-minute-field:focus {
  background-color: #343a40;
  color: #fff;
}

input[type='time']::-webkit-datetime-edit-ampm-field {
  background-color: #7155d3;
  border-radius: 15%;
  color: #fff;
  padding: 19px 13px;
}

input[type='time']::-webkit-clear-button {
  display: none;
}

input[type='time']::-webkit-inner-spin-button {
  display: none;
}

input[type='time']::-webkit-calendar-picker-indicator {
  display: none;
}

/* fin time */
</style>

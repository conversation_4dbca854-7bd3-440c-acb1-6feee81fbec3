<template>
  <div>
    <div class="col-md-12 mt-5 pb-0">
      <router-view class="ecoConduite" />
    </div>
  </div>
</template>
<script>
import $ from 'jquery'
import 'datatables.net'
import '@/libs/datetime-moment'

export default {
  name: 'EcoConduite',
  components: {},

  data() {
    return {
      option: {
        x: false,
        y: true,
        width: 5,
        widthOnHover: 5,
        keep: true,
        skidwayStyle: {
          'background-color': '#d7cfcf',
          'border-radius': '8px',
        },
        sliderStyle: {
          'background-color': '#555',
          'border-radius': '8px',
        },
      },
    }
  },
  mounted: function () {
    this.$store.dispatch('ecoConduite/initialize')
    this.$store.dispatch('navbar/setNavbarComponents', ['EcoConduiteNav'])
  },
  destroyed: function () {
    this.$store.dispatch('navbar/resetStore')
  },
  methods: {
    rotates: function (id) {
      $('#heading-' + id)
        .children()
        .find('i.fas.fa-chevron-up.rotate')
        .toggleClass('down')
    },
  },
}
</script>
<style lang="scss" scoped>
.menu_icon_color_active {
  color: #eb4c29;
}

i {
  cursor: pointer;
  color: gray;
}

.mt-5 {
  margin-top: 6rem !important;
}
.scrolling {
  height: 90vh;
  width: 100%;
}
.fixed-top {
  top: 30px;
  right: 100px;
}

.padding_div {
  padding: 1.8rem 5rem 0px 0px;
}
.fa-chevron-up:before {
  font-size: 25px;
  color: gray;
}
</style>

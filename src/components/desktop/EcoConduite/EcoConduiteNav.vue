<template>
  <nav class>
    <div class="float-right">
      <div class="d-inline p-2">
        <router-link :to="{ name: 'ecoconduiteBloc' }">
          <i
            class="fas fa-th-large"
            :class="{
              menu_icon_color_active: $store.state.ecoConduite.show_menu_bloc,
            }"
            @click="switchToBloc"
          />
        </router-link>
      </div>
      <div class="d-inline p-2">
        <router-link :to="{ name: 'ecoconduiteTable' }">
          <i
            class="fas fa-th-list"
            :class="{
              menu_icon_color_active: $store.state.ecoConduite.show_menu_table,
            }"
            @click="switchToTable"
          />
        </router-link>
      </div>
    </div>
  </nav>
</template>
<script>
export default {
  methods: {
    switchToBloc: function () {
      this.$store.dispatch('ecoConduite/switchToBloc')
    },
    switchToTable: function () {
      this.$store.dispatch('ecoConduite/switchToTable')
    },
  },
}
</script>
<style lang="scss" scoped>
.fa-th-large:before,
.fa-th-list:before {
  font-size: 32px;
}
.menu_icon_color_active {
  color: #eb4c29;
}

i {
  cursor: pointer;
  color: gray;
}
</style>

<template>
  <div class="container-fluid">
    <!--      <div class="card mt-2" style="font-size: 16.75px;">-->
    <!-- Details Modal -->
    <b-modal
      id="ranking_details_modal"
      size="xl"
      hide-footer
      modal-tall
      :title="$t('ecoconduite.ranking_details')"
    >
      <RankingDetailsModal style="z-index: 2500 !important" :item="elem" />
    </b-modal>
    <div class="d-flex mb-3">
      <div class="p-2 mr-auto">
        <strong
          class="font_size"
          v-text="$t('ecoconduite.Comparer les Véhicules')"
        />
        <br />
        <strong><span v-text="$t('ecoconduite.Date du rapport')" />:</strong>
        {{ $store.state.ecoConduite.showSelectedDate }}
      </div>
      <div class="p-2 ml-auto">
        <i class="fas fa-ellipsis-v" @click="exportFilesPopup" />
        <div class="card export_files_popup" data-status="0">
          <div
            class="p-1 cursor"
            @click="exportPdf"
            v-text="$t('ecoconduite.Exporter au format PDF')"
          />
          <div
            class="p-1 cursor"
            @click="exportXls"
            v-text="$t('ecoconduite.Exporter au format XLS')"
          />
        </div>
      </div>
    </div>
    <div class="table-responsive">
      <table
        id="TableEcoConduite"
        ref="TableEcoConduite"
        class="table tableStyle"
      >
        <thead>
          <tr class="informations_tr_style">
            <th :colspan="getColspan()" />
            <th colspan="1" v-text="$t('ecoconduite.Rejet')" />
            <th colspan="6" v-text="$t('ecoconduite.Consommation')" />
            <th colspan="5" v-text="$t('ecoconduite.Régime Moteur')" />
            <th colspan="3" v-text="$t('ecoconduite.Freinage')" />
            <th colspan="1" v-text="$t('ecoconduite.Accélération')" />
            <th colspan="6" v-text="$t('ecoconduite.ranking_details')" />
          </tr>
          <tr>
            <th v-show="hasRight()" class="weight_tr" />
            <th class="weight_tr" />
            <th class="weight_tr" />
            <th class="weight_tr" />
            <th class="weight_tr" />
            <th class="weight_tr" />
            <th class="weight_tr">(km)</th>
            <th class="weight_tr">(km/h)</th>
            <th class="weight_tr">(/10)</th>
            <th class="weight_tr">(Kg)</th>
            <th class="weight_tr">(L)</th>
            <th class="weight_tr">(L/100km)</th>
            <th class="weight_tr">(L/100km)</th>
            <th class="weight_tr">(L/h)</th>
            <th class="weight_tr">(L)</th>
            <th class="weight_tr">(h:m:s)</th>
            <th class="weight_tr">(h:m:s)</th>
            <th class="weight_tr">(%)</th>
            <th class="weight_tr">(%)</th>
            <th class="weight_tr" />
            <th class="weight_tr">(h:m:s)</th>
            <th class="weight_tr">(h:m:s)</th>
            <th class="weight_tr">(%)</th>
            <th class="weight_tr">(ratio)</th>
            <th class="weight_tr">(%)</th>
            <th colspan="6" class="weight_tr" />
          </tr>
          <tr>
            <th
              v-show="hasRight()"
              data-toggle="tooltip"
              :title="$t('ecoconduite.ranking_details')"
              v-text="$t('ecoconduite.ranking_details')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('Véhicule')"
              v-text="$t('Véhicule')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('Catégorie')"
              v-text="$t('Catégorie')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.chauffeur')"
              v-text="$t('ecoconduite.chauffeur')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('Début')"
              v-text="$t('Début')"
            />
            <th data-toggle="tooltip" :title="$t('Fin')" v-text="$t('Fin')" />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.distance')"
              v-text="$t('ecoconduite.table_column.distance')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.avg_speed_tooltip')"
              v-text="$t('ecoconduite.table_column.avg_speed')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.ranking')"
              v-text="$t('ecoconduite.table_column.ranking')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.co2_tooltip')"
              v-text="$t('ecoconduite.table_column.co2')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.consumption_tooltip')"
              v-text="$t('ecoconduite.table_column.consumption')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.consumption_avg_global')"
              v-text="$t('ecoconduite.table_column.consumption_avg_global')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.consumption_avg_move')"
              v-text="$t('ecoconduite.table_column.consumption_avg_move')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.consumption_avg_work')"
              v-text="$t('ecoconduite.table_column.consumption_avg_work')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.waste_in_liters_tooltip')"
              v-text="$t('ecoconduite.table_column.consumption_avg_work')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.time_vehicle_slow_tooltip')"
              v-text="$t('ecoconduite.table_column.time_vehicle_slow')"
            />
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.time_vehicle_eco_tooltip')"
              v-text="$t('ecoconduite.table_column.time_vehicle_eco')"
            />
            <th
              data-toggle="tooltip"
              :title="
                $t('ecoconduite.table_column.time_vehicle_low_rpm_tooltip')
              "
              v-text="$t('ecoconduite.table_column.time_vehicle_low_rpm')"
            />
            <th
              data-toggle="tooltip"
              :title="
                $t('ecoconduite.table_column.time_vehicle_high_rpm_tooltip')
              "
              v-text="$t('ecoconduite.table_column.time_vehicle_high_rpm')"
            />
            <th data-toggle="tooltip" :title="$t('ecoconduite.surregime')">
              {{ $t('ecoconduite.surregime') }}
            </th>
            <th
              data-toggle="tooltip"
              :title="$t('ecoconduite.table_column.time_vehicle_stop_tooltip')"
              v-text="$t('ecoconduite.table_column.time_vehicle_stop')"
            />
            <th
              data-toggle="tooltip"
              :title="
                $t(
                  'ecoconduite.table_column.time_vehicle_move_while_breaking_tooltip'
                )
              "
              v-text="
                $t('ecoconduite.table_column.time_vehicle_move_while_breaking')
              "
            />
            <th
              data-toggle="tooltip"
              :title="
                $t(
                  'ecoconduite.table_column.avg_vehicle_breaking_percentage_tooltip'
                )
              "
              v-text="
                $t('ecoconduite.table_column.avg_vehicle_breaking_percentage')
              "
            />
            <th
              data-toggle="tooltip"
              :title="
                $t(
                  'ecoconduite.table_column.vehicle_breaking_stop_count_tooltip'
                )
              "
              v-text="
                $t('ecoconduite.table_column.vehicle_breaking_stop_count')
              "
            />
            <th
              data-toggle="tooltip"
              :title="
                $t(
                  'ecoconduite.table_column.avg_vehicle_acceleration_percentage_tooltip'
                )
              "
              v-text="
                $t(
                  'ecoconduite.table_column.avg_vehicle_acceleration_percentage'
                )
              "
            />
            <th>
              {{ $t('ecoconduite.rank.stops') }}
            </th>
            <th>
              {{ $t('ecoconduite.rank.flex_acceleration') }}
            </th>
            <th>
              {{ $t('ecoconduite.rank.engine_under') }}
            </th>
            <th>
              {{ $t('ecoconduite.rank.engine_over') }}
            </th>
            <th>
              {{ $t('ecoconduite.rank.anticipated') }}
            </th>
            <th>
              {{ $t('ecoconduite.rank.braking') }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in EcoResults" :key="index">
            <!-- Détail de la note -->
            <td
              v-show="hasRight()"
              class="text-center"
              @click="openDetailsModal(item)"
            >
              <a style="cursor: pointer"><em class="fa fa-search-plus" /></a>
            </td>
            <!-- Véhicule -->
            <td>{{ item.vehicleName }}</td>
            <!-- Catégorie -->
            <td>{{ item.vehicleCategoryName }}</td>
            <!-- Driver -->
            <td>{{ item.driverName }}</td>
            <!-- Début -->
            <td>{{ getDate(item.activityStartDate) }}</td>
            <!--Fin -->
            <td>{{ getDate(item.activityEndDate) }}</td>
            <!-- Dist. -->
            <td class="align-right">
              {{ sur1000(item.distanceTotal, 1) }}
            </td>
            <!-- Vit. -->
            <td class="align-right">
              {{ getNumber(item.speedAverage, 1) }}
            </td>
            <!-- note -->
            <td class="align-right">
              {{ getNumber(item.rating, 1) }}
            </td>
            <!-- co2 -->
            <td class="align-right">
              {{ sur1000(item.co2, 1) }}
            </td>
            <!-- Tot. -->
            <td class="align-right">
              {{ sur1000(item.consumptionTotal, 1) }}
            </td>
            <!-- Globale -->
            <td class="align-right">
              {{ getNumber(item.consumptionAverage, 1) }}
            </td>

            <!-- Mvt. -->
            <td class="align-right">
              {{ item.consumptionAverageMove }}
            </td>
            <!-- Trav. -->
            <td class="align-right">
              {{ item.consumptionAverageWork }}
            </td>
            <!-- Gâchis -->
            <td class="align-right">
              {{ sur1000(item.consumptionIdle, 1) }}
            </td>
            <!-- Ralenti -->
            <td class="align-center">
              {{ sec2time(item.durationIdle) }}
            </td>
            <!-- Eco -->
            <td class="align-center">
              {{ sec2time(item.durationEngineEco) }}
            </td>
            <!-- Sous -->
            <td class="align-right">
              {{ getNumber(multiplyBy(item.ratioEngineUnder, 100), 1) }}
            </td>
            <!-- Sur -->
            <td class="align-right">
              {{ getNumber(multiplyBy(item.ratioEngineOver, 100), 1) }}
            </td>
            <!-- Surrégime -->
            <td class="align-right">
              {{ item.phaseEngineOver }}
            </td>
            <!-- Arrêt -->
            <td class="align-center">
              {{ sec2time(item.durationIdleExcess) }}
            </td>
            <!-- Tps freinage -->
            <td class="align-center">
              {{ sec2time(item.durationBrake) }}
            </td>

            <!-- Freinage -->
            <td class="align-right">
              {{ item.ratioBrakePedal }}
            </td>
            <!-- Frein/Arrêt -->
            <td class="align-right">
              {{ getNumber(item.ratioBrakeStop, 1) }}
            </td>
            <!-- Acc. -->
            <td class="align-right">
              {{ item.ratioAcceleratorPedal }}
            </td>
            <!-- Note Arret -->
            <td class="align-right">
              {{ item.ratingIdleExcess }}
            </td>
            <!-- Note souplesse -->
            <td class="align-right">
              {{ item.ratingAccelerateExcess }}
            </td>
            <!-- Note sous régime -->
            <td class="align-right">
              {{ item.ratingEngineUnder }}
            </td>
            <!-- Note surrégime -->
            <td class="align-right">
              {{ item.ratingEngineOver }}
            </td>
            <!-- Note Anticipation -->
            <td class="align-right">
              {{ item.ratingFreewheel }}
            </td>
            <!-- Note freinage -->
            <td class="align-right">
              {{ item.ratingBrake }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import jspdf from 'jspdf'
import autoTable from 'jspdf-autotable'
import templateHelpers from '@/services/templateHelpers'
import Helper from '@/services/helpers'
import moment from 'moment'
import { mapGetters } from 'vuex'
import RankingDetailsModal from '@desktop/EcoConduite/RankingDetails/RankingDetailsModal.vue'
import { faker } from '@/utils/faker'
import * as R from 'ramda'
import { usePdfExport } from '@/composables/usePdfExport'

const { generatePdf } = usePdfExport()

/* 
 * Exports Javascript array to xls (It triggers download prompt)
 *
 * Example:
 * results = [
 *   ['col1','col2'],
 *   [0,1],
 *   [1,0]
 * ]
 *
 
function exportToXls(results = [], filename = 'export') {
  var CsvString = "";
  results.forEach(function (item) {
    item.forEach(function (colItem) {
      CsvString += colItem + ";";
    });
    CsvString += "\r\n";
  });
  
  CsvString = "data:text/csv;charset=utf-8,%EF%BB%BF" + encodeURIComponent(CsvString);
  var x = document.createElement("A");
  x.setAttribute("href", CsvString);
  x.setAttribute("download", (filename||"export").split('.')[0]+".xls");
  document.body.appendChild(x);
  x.click();
}
*/

export default {
  name: 'EcoConduiteDataTable',
  components: {
    RankingDetailsModal,
  },
  mixins: [templateHelpers],
  provide: function () {
    return {
      hasRight: this.hasRight,
    }
  },
  data() {
    //jspdf options
    const docOptions = {
      orientation: 'l',
      format: 'a4',
    }
    const docStyle = {
      fontSize: 12,
    }
    const tableStyles = {
      theme: 'striped',
      startY: 10,
      headStyles: {
        fontSize: 7,
        fillColor: '#0A71AE',
        font: 'helvetica',
      },
      bodyStyles: {
        fontSize: 7,
        font: 'helvetica',
      },
      columnStyles: {},
      margin: { top: 10, right: 10, bottom: 10, left: 10 },
    }

    return {
      elem: '',
      rows: [],
      jsonData: [],
      dtHandle: null,
      option: {
        x: true,
        y: false,
        width: 5,
        widthOnHover: 5,
        keep: true,
        skidwayStyle: {
          'background-color': '#d7cfcf',
          'border-radius': '8px',
        },
        sliderStyle: {
          'background-color': '#555',
          'border-radius': '8px',
        },
      },
      //Set export pdf options
      exportPdfOptions: {
        docOptions,
        docStyle,
        tableStyles,
      },
    }
  },
  computed: {
    tableData: function () {
      return this.EcoResults
    },
    ...mapGetters({
      EcoResults: 'ecoConduite/getEcoResults',
      rightsList: 'auth/rightsList',
    }),
    /* Rename as 'EcoResults' to use a fake dataset */
    EcoResultsFake() {
      return R.range(1, 1000).map(() => {
        return {
          vehicleName: faker.name.findName(),
          vehicleCategoryName: faker.name.findName(),
          driverName: faker.name.findName(),
          activityStartDate: '10/05/2020',
          activityEndDate: '10/05/2020',
          distanceTotal: 1.5,
          speedAverage: 50.5,
          rating: 7.8,
          co2: 1,
          consumptionTotal: 1,
          consumptionAverageMove: 1,
          consumptionAverageWork: 1,
          consumptionIdle: 1,
          durationIdle: 1,
          durationEngineEco: 1,
          ratioEngineUnder: 100,
          ratioEngineOver: 100,
          phaseEngineOver: 1,
          durationIdleExcess: 1,
          ratioBrakePedal: 1,
          ratioBrakeStop: 1,
          ratioAcceleratorPedal: 1,
          durationBrake: 1,
        }
      })
    },
    shouldShowDriverColumn() {
      return this.$store.state.ecoConduite.selectedTab === 'driver'
    },
  },
  watch: {
    EcoResults: {
      handler() {
        $('#TableEcoConduite').DataTable().destroy()
        this.$nextTick(() => {
          this.initialize()
        })
      },
      deep: true,
    },
  },
  mounted() {
    this.initialize()
    $(document).ready(function () {
      $('[data-toggle="tooltip"]').tooltip()
    })
  },

  updated() {
    /*      this.$nextTick(() => {
      this.initialize();
    }); */
  },
  methods: {
    initialize: function () {
      $.fn.dataTable.moment(this.$date.getDatetimePattern())

      this.table = $('#TableEcoConduite').DataTable({
        columnDefs: [
          {
            visible: this.shouldShowDriverColumn,
            targets: 2,
          },
        ],

        /* data: vm.rows,*/
        /* destroy: true, */
        /* retrieve: true, */
        searching: false,
        ordering: true,
        paging: true,
        info: true,
        responsive: true,
        scrollX: true,
        lengthMenu: [
          [5, 25, 50, -1],
          [5, 25, 50, 'All'],
        ],
        pagingType: 'simple',
        language: {
          lengthMenu:
            `&nbsp;${this.$t(
              'ecoconduite.Lignes par page'
            )}: <select style="border: none">` +
            '<option selected value="5">5</option>' +
            '<option value="25">25</option>' +
            '<option value="50">50</option>' +
            `<option value="-1">${this.$t('datatable.paginate.all')}</option>` +
            '</select>',
          info: '_END_ sur _TOTAL_ lignes',
          infoEmpty: '',
          loadingRecords: 'Chargement en cours...',
          zeroRecords: 'Aucun &eacute;l&eacute;ment &agrave; afficher',
          emptyTable: this.$t(
            'ecoconduite.Aucune donnée disponible dans le tableau'
          ),
          paginate: {
            previous:
              "<i class='fas fa-chevron-left icone_style' style='font-size: 28px;'></i>",
            next: "<i class='fas fa-chevron-right' style='font-size: 28px;'></i>",
          },
        },
      })

      var oTable = $('#TableEcoConduite').dataTable()
      var oldoptions = oTable.fnSettings()
      var newoptions = $.extend(oldoptions, {
        searching: false,
        fnRowCallback: function (nRow, aData) {
          if (aData[0] == '104100') {
            $('td', nRow).css('background-color', '#28a745')
          } else {
            //$("td", nRow).addClass("bg_green");
          }

          if (aData[0] == '104101') {
            $(nRow).find('td:eq(3)').css('color', 'red')
          }
          if (aData[0] == '104102') {
            $(nRow).find('td:eq(2)').css('color', 'blue')
          }
        },
      })
      oTable.fnDestroy()
      $('#TableEcoConduite').dataTable(newoptions)

      $('#TableEcoConduite_length, #TableEcoConduite_info').css({
        float: 'none',
        'padding-top': '0.755em',
      })

      $('.dataTables_empty').css({
        'text-align': 'left',
      })
    },
    exportFilesPopup: function () {
      $('.export_files_popup').toggle()
    },
    exportXls() {
      let results = this.getExportDataAsArray() //Get export data as array
      let columns = this.getExportColumns()
      let name = 'ecoconduite_' + moment().format('DD[_]MM[_]YY')
      Helper().exportXlsdata(columns, results, `${name}.xls`)
      /*
      
      results.unshift(this.getExportColumns()); //Add columns
      exportToXls(results,'ecoconduite_'+moment().format('DD[_]MM[_]YY')); //trigger download
      */
    },
    getExportColumns() {
      return [
        'Véhicule',
        'Catégorie',
        this.shouldShowDriverColumn
          ? this.$t('ecoconduite.chauffeur')
          : undefined,
        'Début',
        'Fin',
        'Dist.',
        'Vit.',
        'Note',
        'CO2',
        'Tot.',
        'Globale',
        'Mvt.',
        'Trav.',
        'Gâchis',
        'Ralenti',
        'Eco',
        'Sous',
        'Sur',
        this.$t('ecoconduite.surregime'),
        ' Arrêt ',
        'Tps freinage',
        'Freinage',
        'Frein/Arrêt',
        'Acc.',
        'Note Arrêt',
        'Note souplesse',
        'Note sous régime',
        'Note surrégime',
        'Note anticipation',
        'Note freinage',
      ].filter((v) => v !== undefined)
    },
    /*Each item is an array*/
    getExportDataAsArray() {
      let normalizeNumber = function (formatFn) {
        return function () {
          let number = formatFn.apply(this, arguments)
          return number.toString().split('.').join(',')
        }
      }

      let getDate = this.getDate
      let sur1000 = normalizeNumber(this.sur1000)
      let sec2time = this.sec2time
      let getNumber = normalizeNumber(this.getNumber)
      let multiplyBy = this.multiplyBy

      return this.EcoResults.map((v) => {
        let item = v
        return [
          item.vehicleName,
          item.vehicleCategoryName,
          this.shouldShowDriverColumn ? item.driverName : undefined,
          getDate(item.activityStartDate),
          getDate(item.activityEndDate),
          sur1000(item.distanceTotal, 1),
          getNumber(item.speedAverage, 1),
          getNumber(item.rating, 1),
          sur1000(item.co2, 1), //Co2
          sur1000(item.consumptionTotal, 1), //Tot
          getNumber(item.consumptionAverage, 1), //Glob
          getNumber(item.consumptionAverageMove, 1), //Mvt
          getNumber(item.consumptionAverageWork, 1), //Trav
          sur1000(item.consumptionIdle, 1), // Gâchis
          sec2time(item.durationIdle),
          sec2time(item.durationEngineEco),
          getNumber(multiplyBy(item.ratioEngineUnder, 100), 1), //sous
          getNumber(multiplyBy(item.ratioEngineOver, 100), 1), //sur
          getNumber(item.phaseEngineOver, 1),
          sec2time(item.durationIdleExcess), //arret
          sec2time(item.durationBrake), //tps freinage
          getNumber(item.ratioBrakePedal, 1), //freinage
          getNumber(item.ratioBrakeStop, 1), //frain arret
          getNumber(item.ratioAcceleratorPedal, 1), //acce
          getNumber(item.ratingIdleExcess, 1), //note arrêt
          getNumber(item.ratingAccelerateExcess, 1), //note souplesse
          getNumber(item.ratingEngineUnder, 1), //note sous reg
          getNumber(item.ratingEngineOver, 1), //note surreg
          getNumber(item.ratingFreewheel, 1), //note anticipation
          getNumber(item.ratingBrake, 1), //note freinage
        ].filter((v) => v !== undefined)
      })
    },
    exportPdf() {
      const data = this.getExportDataAsArray()
      const columns = this.getExportColumns()

      return generatePdf(
        columns,
        'EcoConduiteTable',
        this.exportPdfOptions,
        data,
        false,
        false
      )
    },
    openDetailsModal(item) {
      this.elem = this.serializeItem(item)
      return this.$bvModal.show('ranking_details_modal')
    },
    hasRight(code = '') {
      // if (this.$env.isNotProducton()) {
      //   console.warn("hasRight", code, "Bypassing rights");
      //   return true;
      // }
      let rights = [
        'GEOV3_ECOCONDUITE_TABLE_DETAILS_DETAILS',
        'GEOV3_ECOCONDUITE_TABLE_DETAILS_RANKING',
        // "GEOV3_ECOCONDUITE_TABLE_DETAILS_CHART",
        // "GEOV3_ECOCONDUITE_TABLE_DETAILS_MAP",
      ]

      if (code == '') {
        for (let r of rights) {
          if (this.rightsList.indexOf(r.toUpperCase()) !== -1) {
            return true
          }
        }
        return false
      } else {
        return this.rightsList.indexOf(code.toUpperCase()) !== -1
      }
    },
    getColspan() {
      if (this.hasRight()) {
        return 9
      }
      return 8
    },

    /**
     * Vérifie les données fournies et recalcul la note globale si nécessaire
     * @param item
     * @returns {*}
     * <AUTHOR> 20240719 https://easyredmine.simpliciti.fr/issues/43908
     */
    serializeItem(item) {
      const scoreKeys = [
        'ratingIdleExcess',
        'ratingAccelerateExcess',
        'ratingEngineUnder',
        'ratingEngineOver',
        'ratingFreewheel',
        'ratingBrake',
      ]
      let needToRecomputeTotalRating = false
      let sumRatio = 0
      let sumWeightedScore = 0

      // Sérialisation des entrées
      if (typeof item.ratingIdleExcess === 'undefined') {
        needToRecomputeTotalRating = true
        item.ratingIdleExcess = 10
      }

      // recalcul de la note global
      if (needToRecomputeTotalRating) {
        for (const name of scoreKeys) {
          const k = name + 'Ratio'
          // on ignore la pondération d'une note si elle n'est pas définie
          if (typeof item[name] != 'undefined') {
            sumRatio += item.altData[k]
            sumWeightedScore += item[name] * item.altData[k]
          }
        }
        item.rating = Math.round((sumWeightedScore / sumRatio) * 10) / 10 // *10)/10 pour obtenir un arrondi à 1 décimale
      }

      return item
    },
  },
}
</script>
<style scoped>
.paginat_style {
  padding-top: 0.755em !important;
  float: none !important;
}

i {
  cursor: pointer;
}
.mt-5 {
  margin-top: 3rem !important;
}
.card {
  box-shadow: 0px 0px 7px;
}

.cursor {
  cursor: pointer;
}
.bg_red {
  background-color: red;
}
.bg_green {
  background-color: green !important;
}
.weight_tr {
  font-weight: 100;
}
.informations_tr_style {
  border-top-style: hidden;
  border-bottom: 3px solid;
}
.export_files_popup {
  display: none;
  position: absolute;
  width: 212px;
  right: 1%;
  z-index: 1;
  top: 3%;
}
.font_size {
  font-size: 1.2em;
}
.tableStyle {
  border-bottom: 0 !important;
  white-space: pre;
  display: block;
}
.align-right {
  text-align: right;
}
.align-center {
  text-align: center;
}
.table-responsive::-webkit-scrollbar {
  height: 5px;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
  right: 0em;
  content: '';
}
table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:before {
  right: 0em;
  content: '';
}
</style>

<template>
  <div class="carousel">
    <div id="slider" class="slider">
      <div id="slide" class="slide">
        <pin
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="01"
          color="#f84d55"
          pop-over-id="1"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="02"
          color="#80252e"
          pop-over-id="2"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="03"
          color="#80252e"
          pop-over-id="3"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="04"
          color="#0B729F"
          pop-over-id="4"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="05"
          pop-over-id="5"
          color="#0B729F"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="06"
          pop-over-id="6"
          color="#0B729F"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="07"
          pop-over-id="7"
          color="#0B729F"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="08"
          pop-over-id="8"
          color="#0B729F"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="09"
          pop-over-id="9"
          color="#0B729F"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="10"
          pop-over-id="10"
          color="#0B729F"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="11"
          pop-over-id="11"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="12"
          pop-over-id="12"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="13"
          pop-over-id="13"
        />
        <pin
          class
          url-img="https://homepages.cae.wisc.edu/~ece533/images/boat.png"
          ranking="14"
          pop-over-id="14"
        />
      </div>
    </div>
    <div class="m-0 p-0 street">
      <img class="street" :src="routeIcon" />
    </div>
    <button class="ctrl-btn pro-prev" @click="prev">
      <i class="fas fa-chevron-left" />
    </button>
    <button class="ctrl-btn pro-next" @click="next">
      <i class="fas fa-chevron-right" />
    </button>
  </div>
</template>
<script>
import pin from './pin'
import $ from 'jquery'
import routeIcon from '@c/desktop/EcoConduite/assets/route.svg'

export default {
  name: 'Carousel',
  components: {
    pin,
  },
  props: {
    urlImg: {
      type: String,
      default: '',
    },
    ranking: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showPopup: true,
      routeIcon,
    }
  },
  computed: {},
  methods: {
    next: function () {
      {
        var $target = $('#slider')
        if ($target.is(':animated')) return
        $target.animate(
          {
            scrollLeft: $target.scrollLeft() + 300,
          },
          800
        )
      }
    },
    prev: function () {
      {
        var $target = $('#slider')
        if ($target.is(':animated')) return
        $target.animate(
          {
            scrollLeft: $target.scrollLeft() - 300,
          },
          800
        )
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.street {
  margin-left: -1%;
  margin-right: -1%;
}
.carousel {
  min-width: 100%;
}

.slider {
  width: 100%;
  height: 75px;
  position: relative;
  margin: auto;
  overflow-x: hidden;
  overflow-y: hidden;
}

.slider .slide {
  display: flex;
  position: absolute;
  left: 0;
  transition: 0.3s left ease-in-out;
}

.slider .item {
  margin-right: 10px;
}

.slider .item:last-child {
  margin-right: 0;
}

.ctrl {
  text-align: center;
  margin-top: 5px;
}

.ctrl-btn {
  padding: 0px 0px 0px 0px;
  min-width: 20px;
  min-height: 75px;
  background: transparent;
  border: none;
  cursor: pointer;
  position: absolute;
  top: 0;
}

.ctrl-btn.pro-prev {
  left: 0;
}

.ctrl-btn.pro-next {
  right: 0;
}
</style>

<template>
  <div id="podium-box" class="row" style="height: 185px">
    <div class="col-md-4 step-container m-0 p-0">
      <div>
        <img
          class="podium_img"
          src="https://homepages.cae.wisc.edu/~ece533/images/airplane.png"
          width="50px"
          height="50px"
        />
      </div>
      <div id="third-step" class="step centerBoth podium-number">3</div>
    </div>
    <div class="col-md-4 step-container m-0 p-0">
      <div>
        <img class="crown" height="40px" :src="crownIcon" />
        <img
          class="podium_img"
          src="https://homepages.cae.wisc.edu/~ece533/images/airplane.png"
          width="50px"
          height="50px"
        />
      </div>
      <div id="first-step" class="step centerBoth podium-number">1</div>
    </div>
    <div class="col-md-4 step-container m-0 p-0">
      <div>
        <img
          class="podium_img"
          src="https://homepages.cae.wisc.edu/~ece533/images/airplane.png"
          width="50px"
          height="50px"
        />
      </div>
      <div id="second-step" class="step centerBoth podium-number">2</div>
    </div>
  </div>
</template>
<script>
import crownIcon from '@c/desktop/EcoConduite/assets/crown.svg'
export default {
  name: 'Podium',
  components: {},
  props: [],
  data() {
    return {
      crownIcon,
    }
  },
  computed: {},
}
</script>
<style lang="scss" scoped>
.crown {
  display: block;
  margin: auto;
}
.podium_img {
  border-radius: 50%;
  margin-bottom: 5px;
  border: 2px solid #f84d55;
}
#podium-box {
  margin: 0 auto;
  display: flex;
  padding: 0px 30px 10px 30px;
}

.podium-number {
  font-family: DaggerSquare;
  font-weight: bold;
  font-size: 2.5em;
  color: white;
}

.step-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.step-container > div:first-child {
  margin-top: auto;
  text-align: center;
}

.step {
  text-align: center;
}

#first-step {
  height: 50%;
  background: #f84d55;
}

#second-step {
  height: 35%;
  background: #80252e;
}

#third-step {
  height: 30%;
  background: #80252e;
}
</style>

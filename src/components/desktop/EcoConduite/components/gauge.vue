<template>
  <div class="circle" :style="{ backgroundImage: `url(${backgroundUrl})` }">
    <div class="s-hand hand" :style="{ transform: Rotate }" />
    <div class="center" />
    <div class="d-flex mb-3 infos_flex">
      <div class="mr-auto infos_size_numbrs">
        {{ min }}
      </div>
      <div class="d-flex justify-content-between">
        <div class="text-center infos_size_litre">Total : {{ litre }} L</div>
      </div>
      <div class="ml-auto infos_size_numbrs">
        {{ max }}
      </div>
    </div>
  </div>
</template>
<script>
import backgroundUrl from '@c/desktop/EcoConduite/assets/gauge.png'
export default {
  name: 'Gauge',
  props: {
    percentage: {
      type: [Number, String],
      default: 0.0,
    },
    min: {
      type: String,
      default: '0',
    },
    max: {
      type: String,
      default: '180',
    },
    litre: {
      type: [Number, String],
      default: 0.0,
    },
  },
  data() {
    return {
      backgroundUrl,
    }
  },
  computed: {
    Rotate() {
      const d = 130
      const p = parseFloat(this.percentage)
      const v = (parseFloat(this.percentage) * 260) / 100
      if (p >= 1 && p <= 100) {
        return `translate(-50%, -100%) rotate(${-(d - v)}deg)`
      }
      return `translate(-50%, -100%) rotate(-130deg)`
    },
  },
}
</script>
<style lang="scss" scoped>
.circle {
  width: 250px;
  height: 250px;
  // background: radial-gradient(white 65%, transparent 66%),
  /*conic-gradient(
      from -130deg,
      #3cb371 35%,
      rgb(255, 166, 0) 37%,
      rgb(253, 3, 3) 72%,
      transparent -130deg
    );*/

  background-repeat: no-repeat;
  background-size: 250px 210px;
  border-radius: 50%;
  position: relative;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  z-index: 0;
}

.hand {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -100%);
  transform-origin: center bottom;
  border-radius: 20px 20px 0px 0px;
}

.s-hand {
  height: 47%;
  width: 1%;
  z-index: 1;
  border: 1.8rem solid transparent;
  border-bottom: 8.3rem solid #626262;
}
.center {
  background-color: #626262;
  width: 23%;
  height: 23%;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.infos_flex {
  padding-top: 178px;
  width: 300px;
  left: 169px;
  margin-left: -15px;
}

.infos_size_numbrs {
  font-size: 25px;
}
.infos_size_litre {
  padding-top: 5px;
  font-size: 20px;
}
</style>

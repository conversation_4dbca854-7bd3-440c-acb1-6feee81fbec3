<template>
  <div :id="`accordion-${index}`" class="accordion">
    <div class="card">
      <div :id="`heading-${index}`" class="card-header">
        <h2 class="mb-0">
          <button
            class="btn btn-link collapsed"
            type="button"
            data-toggle="collapse"
            :data-target="`#collapse-${index}`"
            aria-expanded="true"
            :aria-controls="`collapse-${index}`"
            @click="rotates(index)"
          >
            <i class="fas fa-chevron-up rotate" />
          </button>
          <strong>{{ name }}</strong>
          <div v-if="false" class="float-right pr-2">
            <i class="fas fa-ellipsis-v" />
          </div>
        </h2>
      </div>
      <div
        :id="`collapse-${index}`"
        class="collapse show"
        :aria-labelledby="`heading-${index}`"
        :data-parent="`#accordion-${index}`"
      >
        <div class="card-body">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import $ from 'jquery'

export default {
  name: 'Collapse',
  components: {},
  props: {
    index: {
      type: String,
      default: '0',
    },
    name: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  methods: {
    rotates: function (id) {
      $('#heading-' + id)
        .children()
        .find('i.fas.fa-chevron-up.rotate')
        .toggleClass('down')
    },
  },
}
</script>
<style lang="scss" scoped>
.card-header {
  padding: 0;
  margin-bottom: 0;
  background-color: transparent;
  border-bottom: 0;
}

.card {
  box-shadow: 0px 0px 7px;
}

.collapse.show {
  min-height: 425px;
}

.fa-chevron-up:before,
.fa-ellipsis-v:before {
  font-size: 25px;
  color: gray;
}

.rotate {
  -moz-transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  transition: all 0.5s linear;
}
.rotate.down {
  transform: rotate(180deg);
}
</style>

<template>
  <div class="media">
    <div class="media-body text-center pt-3">
      <h6 class="m-0">{{ inactifs }} chauffeurs</h6>
      <h6 class="m-0">inactifs</h6>
    </div>
    <img class="ml-3" width="70px" height="70px" :src="garageIcon" />
  </div>
</template>
<script>
import garageIcon from '@c/desktop/EcoConduite/assets/garage.svg'
export default {
  name: 'InactifsDrivers',
  components: {},
  props: {
    inactifs: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {
      garageIcon,
    }
  },
  computed: {},
}
</script>
<style lang="scss" scoped></style>

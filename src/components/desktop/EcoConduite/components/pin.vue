<template>
  <div class="popover_block_container">
    <button
      :id="`btnPopId_${popOverId}`"
      tabindex="0"
      type="button"
      class="popover_icon"
      :data-popover-content="`#popId_${popOverId}`"
      data-toggle="popover"
      data-placement="right"
    >
      <div class="pin" :style="{ background: color }">
        <div class="center">
          <img
            :src="urlImg"
            class="rounded-circle"
            width="100%"
            height="100%"
          />
          <span class="text_pin">{{ ranking }}</span>
        </div>
      </div>
    </button>

    <div :id="`popId_${popOverId}`" style="display: none">
      <div class="popover-body">
        <div>Pierre SEMAR</div>
        <div>Chauffeur PL Berto</div>
        <div>Note période actuel : 7.6/10</div>
        <div>Note période précédente : 7.4/10</div>
        <div>Évolution : -5%</div>
        <div class="size_note">7e/150</div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
export default {
  name: 'Pin',
  props: {
    urlImg: {
      type: String,
      default: '',
    },
    ranking: {
      type: String,
      default: '0',
    },
    color: {
      type: () => String,
      default: 'mediumseagreen',
    },
    popOverId: {
      type: () => String,
      default: '0',
    },
  },
  mounted() {
    $(document).ready(function () {
      $('[data-toggle=popover]').popover({
        html: true,
        trigger: 'focus',
        content: function () {
          var content = $(this).attr('data-popover-content')
          return $(content).children('.popover-body').html()
        },
      })
    })
  },

  methods: {},
}
</script>
<style lang="scss" scoped>
.pin {
  display: inline-block;
  border-radius: 60% 50% 90% 1%/90% 50% 60% 15%;
  background: #dc3545;
  transform: rotate(-45deg);
  width: 60px;
  height: 60px;
  position: relative;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: 3px 10px 0px 10px;
}

.pin-label {
  width: 90%;
  height: 90%;
  font-size: 20px;
  font-weight: bold;
  background: #fff;
  text-align: center;
  position: absolute;
  border-radius: 50%;
  transform: rotate(45deg);
}

.center {
  font-size: 20px;
  font-weight: bold;
  background-color: #626262;
  width: 80%;
  height: 80%;
  border-radius: 50%;
  position: absolute;
  top: 45%;
  left: 55%;
  transform: translate(-50%, -50%) rotate(45deg);
  z-index: 6;
  line-height: 0.7;
}
.text_pin {
  margin-left: 0;
  color: #ffffff;
  font-size: 15px;
  font-weight: 400;
}

/* debut popover */

.popover_block_container {
  .popover_icon {
    background: none;
    color: none;
    border: none;
    padding: 0;
    outline: none;
    cursor: pointer;
  }
}
.popover-header {
  display: none;
}
.popover {
  top: -8% !important;
  max-width: 306.6px;
  border-radius: 6px;
  border: none;
  background-color: rgba(64, 64, 64, 0.91);
}
.popover[x-placement='right'] {
  left: -3% !important;
}

.popover[x-placement='left'] {
  left: 3% !important;
}

.popover-body {
  border: none;
  padding: 10px;
  color: #ffffff;
  font-size: 12px;
  z-index: 2;
  line-height: 1.53;
  letter-spacing: 0.1px;
  .popover_close {
    position: absolute;
    top: 5px;
    right: 10px;
    opacity: 1;
  }
}
.popover .arrow {
  display: none;
  content: none;
}
.size_note {
  text-align: center;
  font-size: 20px;
}
/* fin popover */
</style>

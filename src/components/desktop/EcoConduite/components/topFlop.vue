<template>
  <div class="d-flex mb-3">
    <div class="mr-auto">
      <h1 class="text-center green_color">
        <strong>Top 3 :</strong>
      </h1>
      <p class="m-0 mb-1">Rejet de C02</p>
      <p class="m-0 mb-1">Consommation Totale</p>
      <p class="m-0 mb-1">Régime moteur : Éco.</p>
    </div>
    <div class="d-flex justify-content-between align-self-end">
      <div class="text-center margin_top_100">
        <h1 class="blue_color">
          <strong>35e</strong>
        </h1>
        <p class="m-0 mb-1">Sur</p>
        <p class="m-0 mb-1">75 chauffeurs</p>
      </div>
    </div>
    <div class="ml-auto">
      <h1 class="text-center red_color">
        <strong>Flop 3 :</strong>
      </h1>
      <p class="m-0 mb-1">Régime moteur : Sur.</p>
      <p class="m-0 mb-1">Consommation Trav.</p>
      <p class="m-0 mb-1">Freinage Excéssif</p>
    </div>
  </div>
</template>
<script>
export default {
  name: 'TopFlop',
  props: {
    percentage: {
      type: String,
      default: '0',
    },
    min: {
      type: String,
      default: '0',
    },
    max: {
      type: String,
      default: '180',
    },
    litre: {
      type: String,
      default: '0',
    },
  },
  computed: {},
}
</script>
<style lang="scss" scoped>
.green_color {
  color: #3cb371;
}
.margin_top_100 {
  margin-top: 100%;
}
.blue_color {
  color: #0b729f;
}
.red_color {
  color: #f84d55;
}
</style>

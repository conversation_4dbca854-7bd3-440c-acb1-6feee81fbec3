<template>
  <div>
    <div class="d-flex mb-3">
      <div class="p-2 mr-auto">
        <div class="icon_wrapper">
          <img width="150px" height="150px" :src="cloudIcon" />

          <div class="centered_cloud">
            <span v-html="kg" />
          </div>

          <div class="text-dark">Total</div>
        </div>
      </div>

      <div class="p-2 ml-auto">
        <div class="icon_wrapper">
          <img width="150px" height="150px" :src="treeIcon" />

          <div class="centered_tree" v-html="trees">2</div>
          <div class="text-dark" v-text="$t('ecoconduite.Absorbable par')" />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6 offset-md-3">
        <div class="text-center">
          <div class="green_color size_text">
            <span v-html="gkm" />
            <br />
            <sup class="exhibitor">g/Km</sup>
          </div>
          <div class="text-dark pt-2" v-text="$t('ecoconduite.Rejet Moyen')" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import cloudIcon from '@c/desktop/EcoConduite/assets/cloud.svg'
import treeIcon from '@c/desktop/EcoConduite/assets/tree.svg'
export default {
  name: 'RejetCo2',
  props: {
    value: {
      type: String,
      default: '',
    },
    distanceMeters: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      cloudIcon,
      treeIcon,
    }
  },
  computed: {
    trees() {
      /*
      A mature tree absorbs carbon dioxide at a rate of 48 pounds per year. In one year, an acre of forest can absorb twice the CO2 produced by the average car's annual mileage.
      48 pounds => 21.7724
      
      */
      return Math.floor(
        (parseFloat(this.value || 0) / 1000).toFixed(0) / 21.7724
      )
    },
    kg() {
      return (parseFloat(this.value || 0) / 1000).toFixed(2) + '&nbsp;kg'
    },
    gkm() {
      let val =
        parseFloat(this.value || 0) /
        (parseFloat(this.distanceMeters || 0) / 1000)
      return !isNaN(val) ? val.toFixed(2) : 0
    },
  },
}
</script>
<style lang="scss" scoped>
.icon_wrapper {
  position: relative;
  text-align: center;
  color: white;
}
.centered_cloud {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 1.6em;
  transform: translate(-50%, -40%);
}
.text-dark {
  margin-top: -20px;
  font-size: 1.5em;
}
.centered_tree {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 1.6em;
  transform: translate(-50%, -90%);
}
.green_color {
  color: #3cb371;
}
.exhibitor {
  top: -0.7em;
  font-size: 55%;
  color: black;
}
.size_text {
  font-size: 2.5em;
}
</style>

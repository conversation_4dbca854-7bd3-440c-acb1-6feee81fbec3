<template>
  <div class="container">
    <gauge :percentage="averageFixed" min="0" max="180" :litre="averageFixed" />

    <div class="d-flex mb-3">
      <div class="mr-auto ml-auto">
        <span v-text="$t('ecoconduite.Moyenne')" /> : {{ averageFixed }} L/100km
      </div>
      <!--      <div class="d-flex justify-content-between">-->
      <!--        <div class="text-center">-->
      <!--          <span v-text="$t('ecoconduite.Référence')" /> :-->
      <!--          {{ referenceFixed }} L/100km-->
      <!--        </div>-->
      <!--      </div>-->
      <!--      <div class="ml-auto">-->
      <!--        <span v-text="$t('ecoconduite.Différence')" /> Réf.: ±{{ difference }}%-->
      <!--      </div>-->
    </div>
  </div>
</template>
<script>
import gauge from './gauge.vue'
export default {
  name: 'Consommation',
  components: {
    gauge,
  },
  props: {
    average: {
      type: [String, Number],
      default: '0',
    },
    reference: {
      type: [String, Number],
      default: '',
    },
  },
  computed: {
    averageFixed() {
      return parseFloat(this.average || 0).toFixed(0)
    },
    referenceFixed() {
      return parseFloat(this.reference || 0).toFixed(0)
    },
    difference() {
      return Math.abs(
        parseFloat(this.average || 0) - parseFloat(this.reference || 0)
      )
    },
  },
}
</script>

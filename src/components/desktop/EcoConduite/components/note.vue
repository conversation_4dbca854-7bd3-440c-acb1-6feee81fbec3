<template>
  <div
    class="progress-pie-chart"
    :style="{ backgroundImage: `url(${backgroundUrl})` }"
  >
    <div class="ppc-progress">
      <div class="ppc-progress-fill" />
    </div>
    <div class="ppc-percents">
      <div class="pcc-percents-wrapper">
        <span />
      </div>
    </div>
  </div>
</template>
<script>
import $ from 'jquery'
import backgroundUrl from '@c/desktop/EcoConduite/assets/note.png'
export default {
  name: 'Note',
  props: {
    percent: {
      type: String,
      default: '0',
    },
    note: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {
      backgroundUrl,
    }
  },
  computed: {},
  watch: {
    note() {
      this.update()
    },
  },
  mounted() {
    this.update()
  },
  methods: {
    update() {
      var $ppc = $('.progress-pie-chart'),
        note = this.note,
        percent = this.percent,
        deg = (360 * percent) / 100
      if (percent > 50) {
        $ppc.addClass('gt-50')
      }
      $('.ppc-progress-fill').css('transform', 'rotate(' + deg + 'deg)')
      $('.ppc-percents span').html(
        "<span style='font-size: 6rem ;font-weight: 500; color: #495057;' >" +
          note +
          '</span>' +
          "<hr style='border-top: 1px solid #212529;    margin: 10px 5px 0px 5px;'  >" +
          "<span style='color: #0B729F;font-weight: 700;font-size: 55px;' >" +
          10 +
          '</span>'
      )
    },
  },
}
</script>
<style lang="scss" scoped>
@mixin circle($size) {
  content: '';
  position: absolute;
  border-radius: 50%;
  left: calc(50% - #{$size/2});
  top: calc(50% - #{$size/2});
  width: $size;
  height: $size;
}

$size: 250px;
.progress-pie-chart {
  width: $size;
  height: $size;
  border-radius: 50%;
  background-color: #e5e5e5;
  position: relative;
  &.gt-50 {
    // background: conic-gradient(red, yellow, green);
    background-size: 250px 250px;
    background-repeat: no-repeat;
  }
}
.ppc-progress {
  @include circle($size);
  clip: rect(0, $size, $size, #{$size/2});
  .ppc-progress-fill {
    @include circle($size);
    clip: rect(0, #{$size/2}, $size, 0);
    background: #81ce97;
    transform: rotate(60deg);
  }
  .gt-50 & {
    clip: rect(0, #{$size/2}, $size, 0);
    .ppc-progress-fill {
      clip: rect(0, $size, $size, #{$size/2});
      background: #e5e5e5;
    }
  }
}
.ppc-percents {
  @include circle(#{$size/1.05});
  background: #fff;
  text-align: center;
  display: table;
  span {
    display: block;
    font-size: 2.6em;
    font-weight: bold;
    color: #81ce97;
  }
}
.pcc-percents-wrapper {
  display: table-cell;
  vertical-align: middle;
}

body {
  font-family: Arial;
  background: #f7f7f7;
}
.progress-pie-chart {
  margin: 30px auto 0;
}
</style>

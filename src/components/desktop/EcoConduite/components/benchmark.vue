<template>
  <div>
    <div class="row">
      <div class="col">
        <podium />
      </div>
      <div class="col">
        <div class="row">
          <div class="col account">
            <div class="float-right">
              <actifsDrivers actifs="57" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col">
            <div class="float-right">
              <inactifsDrivers inactifs="65" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <carousel />
    </div>
  </div>
</template>
<script>
import 'bootstrap'
import carousel from './carousel.vue'
import podium from './podium.vue'
import inactifsDrivers from './inactifsDrivers.vue'
import actifsDrivers from './actifsDrivers.vue'
export default {
  name: 'Benchmark',
  components: {
    podium,
    carousel,
    inactifsDrivers,
    actifsDrivers,
  },
  props: [],
  computed: {},
}
</script>
<style lang="scss" scoped>
.account {
  color: #f84d55;
}
</style>

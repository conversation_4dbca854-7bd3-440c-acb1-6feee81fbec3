<template>
  <div class="media">
    <div class="media-body text-center pt-2">
      <h6 class="m-0">{{ actifs }} chauffeurs</h6>
      <h6 class="m-0">actifs</h6>
    </div>
    <img class="pr-3 ml-4" width="60px" height="60px" :src="accountIcon" />
  </div>
</template>
<script>
import accountIcon from '@c/desktop/EcoConduite/assets/account.svg'
export default {
  name: 'ActifsDrivers',
  components: {},
  props: {
    actifs: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {
      accountIcon, // Assign the imported image to a data property
    }
  },
  computed: {},
}
</script>
<style lang="scss" scoped></style>

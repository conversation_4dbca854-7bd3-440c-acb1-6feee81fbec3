<template>
  <div class="list_item_btn" @click="$emit('click')">
    <VueIcon
      icon="mdi:bin"
      color="var(--color-red)"
      :icon-style="{ fontSize }"
      clickable
    ></VueIcon>
  </div>
</template>
<script setup>
const props = defineProps({
  fontSize: {
    type: String,
    default: '16px',
  },
})
</script>
<style lang="scss" scoped>
.list_item_btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>

<template>
  <BetterSelect
    v-model="modelValue"
    :loading="false"
    :options="options"
    label-prop="label"
    track-by="label"
  >
    <template #option="slotProps">
      <div class="option">
        <img :src="slotProps.option.src" />
        {{ slotProps.option.name }}
      </div>
    </template>
    <template #singleLabel="slotProps">
      <img :src="slotProps.option.src" />
      &nbsp;&nbsp;
      {{ slotProps.option.name }}
    </template>
  </BetterSelect>
</template>
<script setup>
import BetterSelect from '@c/shared/BetterSelect.vue'
import store from '@/store'
import icons from './icons'
import { useVModelProp } from '@/composables/useVModelProp'
import { useVModelEmit } from '@/composables/useVModelEmit'

const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

const props = defineProps({
  [useVModelProp()]: {
    type: String,
    default: () => null,
  },
})

const modelValue = computed({
  get() {
    // Retrieve the prop using the dynamic key
    return props[useVModelProp()]
  },
  set(newValue) {
    vModelEmit(newValue?.label || '')
  },
})

function getConstraintNameFromImage(image) {
  const storeConstraints = store.state.black_spot.constraints

  if (storeConstraints.length > 0) {
    const constraint = storeConstraints.find(
      (constraint) => constraint.image === image
    )

    if (constraint && constraint.label) {
      // use case for ".replace": to prevent wrap without css adjustments
      return constraint.label.replace(' ', '&nbsp;')
    }
  }

  return image
}

const options = computed(() => {
  return Object.keys(icons).map((key) => ({
    label: `${key}.png`,
    name: getConstraintNameFromImage(`${key}.png`),
    src: icons[key],
  }))
})
</script>
<style lang="scss" scoped>
.option img {
  height: 16px;
  width: 16px;
}
.option {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  line-height: 16px;
  column-gap: 15px;
  p {
    margin: 0px;
    padding: 0px;
  }
}
</style>

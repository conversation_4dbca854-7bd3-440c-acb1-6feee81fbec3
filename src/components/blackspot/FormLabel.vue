<template>
  <span class="form-label" :class="{ required }">
    <slot>
      {{ label }}
    </slot>
  </span>
</template>

<script setup>
const props = defineProps({
  required: {
    type: Boolean,
    default: false,
  },
  label: {
    type: String,
    default: '',
  },
})
</script>

<style lang="scss" scoped>
.form-label {
  font: normal normal normal 11px/14px Open Sans;

  &.required::after {
    content: ' *';
    color: red;
  }
}
</style>

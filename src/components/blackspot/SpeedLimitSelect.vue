<template>
  <BetterSelect
    v-model="modelValue"
    :loading="false"
    :options="options"
    label-prop="label"
  >
    <template #option="slotProps">
      <span v-text="slotProps.option.label"></span>
    </template>
    <template #singleLabel="slotProps">
      <span v-text="slotProps.option.label"></span>
    </template>
  </BetterSelect>
</template>
<script setup>
import BetterSelect from '@c/shared/BetterSelect.vue'
import store from '@/store'
import { useVModelProp } from '@/composables/useVModelProp'
import { useVModelEmit } from '@/composables/useVModelEmit'

const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

const props = defineProps({
  [useVModelProp()]: {
    type: Number,
    default: () => null,
  },
})

const options = computed(() => {
  return store.state.black_spot.speedLimits.map((item) => ({
    id: item.id,
    label: item.speed,
  }))
})

const modelValue = computed({
  get() {
    return props[useVModelProp()]
  },
  set(newValue) {
    vModelEmit(newValue?.id || null)
  },
})
</script>

<template>
  <div class="container">
    <div class="row">
      <div class="col p-0">
        <LoaderSpinner v-show="$store.getters['black_spot/isLoading']" />
        <BlackspotCollapsibleSection
          v-show="!$store.getters['black_spot/isLoading']"
          :title="$t('blackspot.list.filter.title')"
        >
          <form
            autocomplete="off"
            class="container"
            @submit="(e) => e.preventDefault()"
          >
            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label
                    for="blackspot-filter-form-name"
                    class="col-form-label-sm mb-0"
                  >
                    {{ $t('blackspot.list.filter.form.name') }}
                  </label>
                  <input
                    id="blackspot-filter-form-name"
                    v-model="filter.name"
                    type="text"
                    class="form-control form-control-sm"
                  />
                </div>
                <div class="form-group">
                  <label class="col-form-label-sm mb-0">
                    {{ $t('blackspot.list.filter.form.constraint') }}
                  </label>
                  <ConstraintSelect v-model="filter.constraintId" />
                </div>
              </div>
            </div>
          </form>
        </BlackspotCollapsibleSection>
        <div
          v-if="
            !$store.getters['black_spot/isLoading'] &&
            $store.getters['black_spot/blackSpotsLoaded'] &&
            !filteredBlackspots.length
          "
          style="text-align: center"
        >
          <span v-text="$t('blackspot.list_no_data')"></span>
        </div>
        <List
          v-show="!$store.getters['black_spot/isLoading']"
          :items="filteredBlackspots"
        >
          <template #item-icon="{ item }">
            <img
              :src="getIconSrc(item.constraintId, constraints)"
              :title="getIconTitle(item)"
              class="ml-2"
            />
          </template>
          <template #item-center="{ item }">
            <ListItemLabel>{{ item.name }}</ListItemLabel>
            <ListItemText>{{ item.constraintName }}</ListItemText>
          </template>
          <template #item-right="{ item }">
            <ListItemViewBtn
              v-b-tooltip.hover.viewport="
                $i18n.t('blackspot.list.tooltip.view')
              "
              @click="viewItem(item, $mitt)"
            />
            <ListItemEditBtn
              v-show="hasFeatureRight('blackspot_edit')"
              v-b-tooltip.hover.viewport="
                $i18n.t('blackspot.list.tooltip.edit')
              "
              @click="
                $router.push({
                  name: 'blackspotUpdate',
                  params: { id: item.id },
                })
              "
            />
            <ListItemRemoveBtn
              v-show="hasFeatureRight('blackspot_remove')"
              v-b-tooltip.hover.viewport="
                $i18n.t('blackspot.list.tooltip.remove')
              "
              @click="$emit('onRemove', item.id)"
            />
          </template>
        </List>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script setup>
import List from './List.vue'
import store from '@/store'
import ListItemEditBtn from './ListItemEditBtn.vue'
import ListItemLabel from './ListItemLabel.vue'
import ListItemText from './ListItemText.vue'
import ListItemViewBtn from './ListItemViewBtn.vue'
import { useBlackSpotMapZoom } from '@/composables/blackSpotMap.js'
import { getIconSrc } from '@/services/blackspot-service.js'
import BlackspotCollapsibleSection from '@/components/blackspot/BlackspotCollapsibleSection.vue'
import { useRightsPlugin } from '@/mixins/rights-mixin'
import ConstraintSelect from '@/components/blackspot/ConstraintSelect.vue'

const { viewItem } = useBlackSpotMapZoom()
const { hasFeatureRight } = useRightsPlugin()

const filter = ref({
  name: '',
  constraintId: '',
})

const constraints = computed(() => store.state.black_spot.constraints)

const blackspots = computed(() => {
  return store.getters['black_spot/blackSpots']
})

const filteredBlackspots = computed(() => {
  const { name, constraintId } = filter.value

  let mode = 'all'

  if (name === '' && constraintId !== '') {
    mode = 'constraintOnly'
  } else if (name !== '' && constraintId === '') {
    mode = 'nameOnly'
  } else if (name !== '' && constraintId !== '') {
    mode = 'and'
  }

  return blackspots.value.map((blackspot) => {
    if (mode === 'all') {
      blackspot.isVisible = true

      return blackspot
    }

    const nameMatch = blackspot.name.toLowerCase().includes(name.toLowerCase())
    const constraintMatch = blackspot.constraintType.id === constraintId

    if (mode === 'and') {
      blackspot.isVisible = nameMatch && constraintMatch
    } else if (mode === 'constraintOnly') {
      blackspot.isVisible = constraintMatch
    } else if (mode === 'nameOnly') {
      blackspot.isVisible = nameMatch
    }

    return blackspot
  })
})

function getIconTitle(item) {
  return (
    constraints.value.find((c) => c.id == item.constraintId)?.label ||
    getIconSrc(item.constraintId, constraints.value)
  )
}
</script>

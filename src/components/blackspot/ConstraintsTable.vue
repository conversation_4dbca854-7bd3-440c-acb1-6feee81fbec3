<template>
  <div style="height: calc(100% - 34px)">
    <TableModesToolbar
      :mode="mode"
      @switchToListMode="
        () => $router.push({ name: 'blackspotConstraintsList' })
      "
      @switchToTableMode="
        () => $router.push({ name: 'blackspotConstraintsTable' })
      "
      @switchToTablePlusMapMode="
        () => $router.push({ name: 'blackspotConstraintsTableMap' })
      "
    />
    <SimpleTable
      name="blackspot-constraints"
      :columns="{
        image: imageDef,
        label: true,
        defaultSpeed: true,
        color: colorDef,
      }"
      :filters="{ image: false, label: true, defaultSpeed: true }"
      :dataset="items"
      :column-defs="columnDefs"
      :default-sort-index="1"
      :auto-height-offset="10"
    >
      <template #label_title>
        <span>{{ $t('blackspot.constraints.label_column') }}</span>
      </template>
      <template #defaultSpeed_title>
        <span>{{ $t('blackspot.constraints.speed') }}</span>
      </template>
      <template #color_title>
        <span>{{ $t('blackspot.constraints.color') }}</span>
      </template>
    </SimpleTable>
  </div>
</template>
<script setup>
import SimpleTable from '@c/shared/DataTable/SimpleTable.vue'
import store from '@/store'
import i18n from '@/i18n'
import TableModesToolbar from '@c/shared/TableModesToolbar/TableModesToolbar.vue'
import icons from './icons'

const props = defineProps({
  mode: {
    type: String,
    default: 'table+map',
    enum: ['table', 'table+map'],
  },
})

const items = computed(() => {
  return store.state.black_spot.constraints
})

const imageDef = ref({
  title: '', //i18n.t('blackspot.consdtraints.image'),
  template: `<img
    :src="icons[row.image.toLowerCase().split('.')[0]]"
    :title="row.label"
  />`,
  filterBy(row) {
    return row.color
  },
  computed: {
    icons() {
      return icons
    },
  },
})

const colorDef = ref({
  title: i18n.t('blackspot.constraints.color'),
  template: `<div
  :data-value-encoded="encodedValue"
  :title="'#'+row.color"
  :style="'height:16px;width:16px;background-color:#'+row.color"></div>`,
  filterBy(row) {
    return row.color
  },
  computed: {
    encodedValue() {
      return window.btoa(this.row.color.split('#').join(''))
    },
  },
})

const columnDefs = ref([
  {
    targets: 0,
    width: '20px',
  },
  /*{
    targets: [2],
    ...createActionButtonDatatableColumn({
      title: i18n.t('blackspot.constraints.color'),
      name: 'ColorCmp',
      template: `<div :style="'height:16px;width:16px;background-color:#'+row.color"></div>`,
      filterBy(row) {
        return row.color
      },
    }),
  },*/
])
</script>

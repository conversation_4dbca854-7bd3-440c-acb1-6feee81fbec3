<template>
  <div v-show="can.viewPlanningForm" class="validity-periods-form">
    <div
      v-for="(item, itemIndex) in modelItems"
      :key="item.id || item.tempId"
      class="mt-1 validity-period-form-item"
    >
      <FormLabel v-show="item.id">
        {{ $t(titleI18n) }} #{{ itemIndex + 1 }}
      </FormLabel>
      <FormLabel v-show="!item.id">{{ $t(newLabelI18n) }}</FormLabel>
      <div class="date-wrapper">
        <BlackspotValidityPeriodDatetimeRangePicker
          v-model="item.model"
          :can-delete="can.deleteItemInPlanningForm"
          :can-update="can.updatePlanningForm"
        />
      </div>
    </div>
    <ButtonWrapper
      v-show="can.addItemInPlanningForm"
      class="mt-2"
      type="secondary"
      @click="addNewItem"
    >
      {{ $t(addI18n) }}
    </ButtonWrapper>
  </div>
</template>
<script setup>
import ButtonWrapper from '@c/shared/ButtonWrapper.vue'
import { throttle } from '@/utils/async'
import * as R from 'ramda'
import { uuidv4 } from '@/utils/crypto.js'
import { nextTick } from 'vue'
import { useRightsPlugin } from '@/mixins/rights-mixin'
import { useVModelEmit } from '@/composables/useVModelEmit'
import { useVModelProp } from '@/composables/useVModelProp'

const emit = defineEmits(['input'])
const { vModelEmit } = useVModelEmit(emit)

const props = defineProps({
  [useVModelProp()]: {
    type: Array,
    default: () => [],
  },
})

const { hasFeatureRight } = useRightsPlugin()

const titleI18n = 'blackspot.planning_title'
const addI18n = 'blackspot.planning_add'
const newLabelI18n = 'blackspot.planning_new'
const modelItems = ref([])

const can = computed(() => {
  return {
    viewPlanningForm: hasFeatureRight('blackspot_planning_view'),
    addItemInPlanningForm: hasFeatureRight('blackspot_planning_add'),
    updatePlanningForm: hasFeatureRight('blackspot_planning_edit'),
    deleteItemInPlanningForm: hasFeatureRight('blackspot_planning_remove'),
  }
})

function addNewItem() {
  //Add an empty picker only if there is no other empty pickers already.
  if (
    !modelItems.value.some(
      (item) => item.model.length === 0 || item.model.some((i) => i === null)
    )
  ) {
    modelItems.value.push({
      id: null,
      tempId: uuidv4(),
      model: [],
    })
  }
}

//Parent -> Child
watch(
  () => props[useVModelProp()],
  throttle(() => {
    if (
      JSON.stringify(modelItems.value) !==
      JSON.stringify(props[useVModelProp()])
    ) {
      console.log('Validity -> Parent to Child')
      modelItems.value = [...props[useVModelProp()].map((v) => ({ ...v }))]
    }
  }, 500)
)

//Child -> Parent
watch(
  () => modelItems,
  throttle(() => {
    nextTick(() => {
      console.log('Validity -> Child to Parent')
      vModelEmit(R.clone(modelItems.value))
    })
  }, 500),
  {
    deep: true,
  }
)
</script>
<style lang="scss" scoped>
.validity-periods-form {
  width: 100%;
}
</style>

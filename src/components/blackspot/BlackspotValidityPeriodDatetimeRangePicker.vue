<template>
  <date-picker
    id="validity-period-datetime-range-picker"
    v-model="selectedValue"
    :disabled="!canUpdate || (!canDelete && !canUpdate)"
    :editable="canUpdate"
    :clearable="canDelete"
    :lang="lang"
    type="datetime"
    range
    :multiple="false"
    :format="$date.getDatetimePattern({ seconds: false })"
    :open="visible"
    :show-week-number="true"
    :show-second="false"
    :placeholder="''"
    @change="handleChange"
    @focus="open('')"
    @input="handleInput"
  >
    <template #footer>
      <div class="container">
        <div class="row">
          <div class="col flex-center-right">
            <button class="mx-btn mx-datepicker-btn-confirm" @click="cancel">
              {{ $t('buttons.cancel_alternative') }}
            </button>
            <span>&nbsp;</span>
            <button class="mx-btn mx-datepicker-btn-confirm" @click="close">
              {{ $t('buttons.valid') }}
            </button>
          </div>
        </div>
      </div>
    </template>
  </date-picker>
</template>
<script>
import DatePicker from 'vue2-datepicker'
import 'vue2-datepicker/locale/fr'
import 'vue2-datepicker/index.css'
import { mapGetters } from 'vuex'
import i18nDatepickerMixin from '@/mixins/i18n-datepicker.js'
import { useVModelProp } from '@/composables/useVModelProp'
import { useVModelEmit } from '@/composables/useVModelEmit'
import { filterValidDates } from '@/utils/dates'

export default {
  components: {
    DatePicker,
  },
  mixins: [i18nDatepickerMixin],
  inject: {
    isSearchModuleDatePickerDisabledHandler: {
      default: () => () => {},
    },
  },
  props: {
    [useVModelProp()]: {
      type: Array,
      default: () => [],
    },
    canUpdate: {
      type: Boolean,
      default: true,
    },
    canDelete: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      selectedValue: [],
    }
  },
  computed: {
    ...mapGetters({
      selectedDateRanges: 'search_module/getSelectedDateRanges',
    }),
    vModelProp() {
      return useVModelProp()
    },
  },
  created() {
    this.selectedValue = Array.isArray(this[this.vModelProp])
      ? [...this[this.vModelProp]]
      : []
  },
  mounted() {
    if (this.selectedDateRanges.length > 0) {
      this.selectedValue = this.selectedDateRanges[0][0]
    }
    //Watch vModelProp
    this.$watch(
      () => this[this.vModelProp],
      (newValue) => {
        //Filter invalid values
        const validSelectedValue = filterValidDates(this.selectedValue)

        //If no valid values, set selectedValue to []
        if (!validSelectedValue.length) {
          this.selectedValue = []
          return
        }

        if (JSON.stringify(newValue) !== JSON.stringify(this.selectedValue)) {
          this.selectedValue = [...newValue]
        }
      },
      {
        deep: true,
        immediate: true,
      }
    )
  },
  methods: {
    close() {
      this.visible = false
    },
    cancel() {
      this.selectedValue = []
      this.visible = false
    },
    open() {
      this.visible = true
    },
    handleChange(value, type) {
      if (type === 'second') {
        this.visible = false
      }
    },
    handleInput(value) {
      const validDates = filterValidDates(value)

      const { vModelEmit } = useVModelEmit(this.$emit, this)
      //Return in case of no valid dates (hack to avoid to emit 1970-01-01)
      if (validDates.length === 0) {
        vModelEmit([])
        return
      }

      vModelEmit(validDates)
    },
  },
}
</script>
<style lang="scss">
#validity-period-datetime-range-picker {
  &.mx-datepicker {
    width: 100%;
  }

  .mx-input {
    height: 38px;
  }
}
</style>

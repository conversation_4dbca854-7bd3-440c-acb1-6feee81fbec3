<template>
  <div style="height: calc(100% - 34px)">
    <TableModesToolbar
      :mode="tableMode"
      style="margin-right: 20px"
      @switchToListMode="() => $router.push({ name: 'blackspotList' })"
      @switchToTableMode="() => $router.push({ name: 'blackspotTable' })"
      @switchToTablePlusMapMode="
        () => $router.push({ name: 'blackspotTableMap' })
      "
    />

    <SimpleTable
      :columns="{
        zoomColumn: zoomColumnDef,
        imageDef: imageDef,
        name: true,
        categoryName: true,
        constraintName: true,
        speedLimitSpeed: true,
      }"
      :filters="{
        name: true,
        categoryName: true,
        constraintName: true,
        speedLimitSpeed: true,
      }"
      :dataset="items"
      :column-defs="columnDefs"
      :default-sort-index="2"
      :auto-height-offset="0"
    >
      <template #name_title>
        <span>{{ $t('blackspot.name') }}</span>
      </template>
      <template #categoryName_title>
        <span>{{ $t('blackspot.category') }}</span>
      </template>
      <template #constraintName_title>
        <span>{{ $t('blackspot.constraint') }}</span>
      </template>
      <template #speedLimitSpeed_title>
        <span>{{ $t('blackspot.limitSpeed') }}</span>
      </template>
    </SimpleTable>
  </div>
</template>
<script setup>
import SimpleTable from '@c/shared/DataTable/SimpleTable.vue'
import store from '@/store'
import TableModesToolbar from '@c/shared/TableModesToolbar/TableModesToolbar.vue'
import { Icon } from '@iconify/vue2'
import { useBlackSpotMapZoom } from '@/composables/blackSpotMap.js'
import icons from './icons'

const { viewItem: viewItemHandler } = useBlackSpotMapZoom()

const props = defineProps({
  tableMode: {
    type: String,
    default: 'tableMap', //tableMap or table
  },
})

const items = computed(() => {
  return store.getters['black_spot/blackSpots']
})

const imageDef = ref({
  template: `<img
    :src="icons[row.constraintImage.toLowerCase().split('.')[0]]"
    :title="row.constraintName"
  />`,
  computed: {
    icons() {
      return icons
    },
  },
})

const zoomColumnDef = ref({
  title: '',
  components: {
    Icon,
  },
  template: `<div @click="handleZoom(row, $mitt)" class="view-map-action-icon" style="cursor:pointer;display:flex;justify-content:center;">
                    <Icon icon="radix-icons:magnifying-glass" color="var(--color-main)" :style="{fontSize:'16px'}"></Icon>
                    </div>`,
  methods: {
    handleZoom(row, mitt) {
      viewItemHandler(row, mitt)
    },
  },
})

const columnDefs = ref([
  {
    targets: 0,
    width: '20px',
  },
])
</script>

<template>
  <TreeItem
    ref="treeRef"
    v-model="valueAsArray"
    :children="computedTreeItemDataset"
    children-wrapper-style="max-height:300px;overflow:auto"
    :single-select="true"
  ></TreeItem>
</template>
<script setup>
import store from '@/store'
import { useVModelProp } from '@/composables/useVModelProp'
import { useVModelEmit } from '@/composables/useVModelEmit'

const props = defineProps({
  [useVModelProp()]: {
    type: Number,
    default: null,
  },
})
const emit = defineEmits(['input', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

const vModelProp = computed(() => useVModelProp())

const valueAsArray = computed({
  get() {
    // Return the value as an array if it exists, or an empty array
    return props[vModelProp.value] ? [props[vModelProp.value]] : []
  },
  set(newValue) {
    onInput(newValue)
  },
})

const treeItemContext = ref({
  flatItems: [],
  checked: [],
})
provide('tree-item-context', treeItemContext)

onMounted(() => {
  watch(
    () => props[vModelProp.value],
    () => {
      treeItemContext.value.checked = [...valueAsArray.value]
    }
  )
})

function onInput(payload = []) {
  const isDiffLen = payload.length !== valueAsArray.value.length
  const isDiffVal =
    payload.length > 0 &&
    payload.length === valueAsArray.value.length &&
    payload[0] !== valueAsArray.value[0]
  if (isDiffLen || isDiffVal) {
    vModelEmit(payload[0]) //Treeview is an [] but we want only the first item.
  }
}

const computedTreeItemDataset = computed(() => {
  return store.state.black_spot.categories.map((item) => {
    return {
      id: item.id,
      label: item.label,
      children: [],
      icon: 'mdi:road-variant',
      isCategory: true,
      parentId: null,
    }
  })
})
</script>

<template>
  <Tabs
    v-model="tabsModel"
    style="display: grid; grid-template-rows: 50px 1fr; height: 100%"
    content-wrapper-style="height:100%"
  >
  </Tabs>
</template>
<script setup>
import Tabs from '@c/shared/Tabs.vue'
import { useRightsPlugin } from '@/mixins/rights-mixin'

const emit = defineEmits(['clickBlackspot', 'clickBlackspotTypes'])
const props = defineProps({
  defaultTabName: {
    type: String,
    default: 'BLACKSPOTS_LIST',
  },
})
const { hasFeatureRight } = useRightsPlugin()

const tabsModel = ref({
  selectedTab: {
    tabName: props.defaultTabName,
  },
  items: [
    {
      tabName: 'BLACKSPOTS_LIST',
      label: 'blackspot.tabs.blackspot',
    },
    {
      tabName: 'BLACKSPOTS_TYPES_LIST',
      label: 'blackspot.tabs.blackspot_constraints',
    },
  ],
})

watch(
  () => tabsModel.value.selectedTab.tabName,
  (tabName) => {
    if (tabName === 'BLACKSPOTS_LIST') {
      emit('clickBlackspot')
    } else {
      emit('clickBlackspotTypes')
    }
  }
)

onMounted(() => {
  if (!hasFeatureRight('blackspot_constraints_view')) {
    tabsModel.value.items = tabsModel.value.items.filter(
      (item) => item.tabName !== 'BLACKSPOTS_TYPES_LIST'
    )

    tabsModel.value.selectedTab.tabName = 'BLACKSPOTS_LIST'
  }
})
</script>

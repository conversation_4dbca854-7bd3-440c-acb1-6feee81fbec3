<template>
  <div class="collapsible-section">
    <div class="collapsible-section-title" @click="handleSectionClick">
      {{ title }}
      <div
        class="em fas collapsable-toggle"
        :class="[
          {
            'fa-chevron-up': collapsed,
            'fa-chevron-down': !collapsed,
          },
        ]"
      />
    </div>
    <div v-show="collapsed" class="collapsible-section-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['input'])

const props = defineProps({
  title: {
    type: String,
    default: 'Sample title',
  },
  value: {
    type: Boolean,
    default: false,
  },
})

const collapsed = ref(props.value)

function setCollapsed(value) {
  if (collapsed.value !== value) {
    collapsed.value = value
    emit('input', value)
  }
}

function handleSectionClick() {
  setCollapsed(!collapsed.value)
}

watch(
  () => props.value,
  (value) => setCollapsed(value)
)
</script>

<style lang="scss" scoped>
.collapsible-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 0.5rem;

  &-title {
    width: 100%;
    background-color: var(--color-main);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 33px;
    padding: 0 1.5rem 0 1rem;
    font: normal normal bold 14px/18px Open Sans;
    color: white;
    cursor: pointer;
  }

  &-content {
    padding: 0.7rem 0.5rem 0.5rem;
  }
}
</style>

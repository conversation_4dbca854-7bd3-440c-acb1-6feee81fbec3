<template>
  <!-- Generic list item -->
  <li class="row ul_list__item m-0">
    <div class="col p-0">
      <!--LEFT-->
      <div>
        <slot name="left-slot">
          <div
            class="icon_wrapper"
            :style="
              'background-color:' + hexOpacity(iconBgColor, iconBgColorOpacity)
            "
          >
            <slot name="icon">
              <img :src="iconSrc" :title="iconTitle || iconSrc" />
            </slot>
          </div>
        </slot>
      </div>
      <!--CENTER-->
      <div>
        <slot name="center">
          <div>
            <span>Label</span>
          </div>
          <div>
            <span>Attribute: Value</span>
          </div>
        </slot>
      </div>
    </div>
    <!-- RIGHT -->
    <div class="col p-0 pr-2 justify-content-end no-gutters">
      <slot name="right"> (Buttons) </slot>
    </div>
  </li>
</template>
<script setup>
import hexOpacity from 'hex-color-opacity'

const props = defineProps({
  iconSrc: {
    type: String,
    default: 'https://placehold.co/20x20',
  },
  iconTitle: {
    type: String,
    default: '',
  },
  iconBgColor: {
    type: String,
    default: '#ffffff',
  },
  iconBgColorOpacity: {
    type: Number,
    default: 0.2,
  },
})
</script>
<style lang="scss" scoped>
.ul_list__item {
  box-shadow: 0px 1px 2px #0000005c;
  background-color: transparent;
  transition: all 0.3s ease;

  & > .col {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    column-gap: 10px;
  }

  .icon_wrapper {
    border-radius: 50px;
    padding: 5px 5px 7px;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &.highlighted {
    box-shadow: 0 0 10px 5px rgba(0, 0, 0, 0.5);
    background-color: rgba(255, 255, 0, 0.05);
  }
}
</style>

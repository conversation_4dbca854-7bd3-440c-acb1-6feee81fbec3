<template>
  <div class="container">
    <div class="row">
      <BlackspotCollapsibleSection :title="$t('blackspot.list.filter.title')">
        <form
          autocomplete="off"
          class="container"
          @submit="(e) => e.preventDefault()"
        >
          <div class="row">
            <div class="col-12">
              <div class="form-group">
                <label
                  for="blackspot-filter-form-name"
                  class="col-form-label-sm"
                >
                  {{ $t('blackspot.constraints-list.filter.form.name') }}
                </label>
                <input
                  id="blackspot-filter-form-name"
                  v-model="filter.label"
                  type="text"
                  class="form-control form-control-sm"
                />
              </div>
            </div>
          </div>
        </form>
      </BlackspotCollapsibleSection>
      <div class="col px-0">
        <ul v-if="filteredConstraints.length > 0">
          <li
            v-for="(item, i) in filteredConstraints"
            :key="i + '_item'"
            class="list-item m-0 pl-3 pr-1"
            :class="{ 'd-none': !(item.isVisible ?? true) }"
          >
            <div class="d-flex flex-row align-items-start">
              <div
                class="img-wrapper"
                :style="'background-color:' + hexOpacity('#' + item.color, 0.2)"
              >
                <img
                  :data-name="item.image"
                  :src="icons[item.image.toLowerCase().split('.')[0]]"
                  :title="item.label"
                />
              </div>
              <div class="d-flex flex-column">
                <span class="label" v-text="item.label"></span>
                <div>
                  <span class="speed">
                    {{ $t('blackspot.constraints.speed') }}:&nbsp;
                  </span>
                  <span class="speed" v-text="item.defaultSpeed"></span>
                  <span class="speed">&nbsp;Km/h</span>
                </div>
              </div>
            </div>
            <div class="col p-0 pr-2 col-flex no-gutters justify-content-end">
              <div
                v-show="hasFeatureRight('blackspot_constraints_edit')"
                v-b-tooltip.hover.viewport="
                  $i18n.t('blackspot.list.tooltip.edit')
                "
                class="accessible-clickable-icon"
                @click="
                  $router.push({
                    name: 'blackspotConstraintsUpdate',
                    params: { id: item.id },
                  })
                "
              >
                <VueIcon
                  icon="material-symbols:edit"
                  color="var(--color-main)"
                  :icon-style="{ fontSize: '16px' }"
                  clickable
                ></VueIcon>
              </div>
              <div
                v-show="hasFeatureRight('blackspot_constraints_remove')"
                v-b-tooltip.hover.viewport="
                  $i18n.t('blackspot.list.tooltip.remove')
                "
                class="accessible-clickable-icon"
                @click="removeItem(item)"
              >
                <VueIcon
                  icon="mdi:bin"
                  color="var(--color-red)"
                  :icon-style="{ fontSize: '16px' }"
                  clickable
                ></VueIcon>
              </div>
            </div>
          </li>
        </ul>
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup>
import store from '@/store'
import VueIcon from '@c/shared/VueIcon.vue'
import icons from './icons'
import hexOpacity from 'hex-color-opacity'
import api from '@/api'
import useModal from '@/composables/modal.js'
import i18n from '@/i18n'
import useToastComposable from '@/composables/toast'
import { useRightsPlugin } from '@/mixins/rights-mixin'
import BlackspotCollapsibleSection from '@/components/blackspot/BlackspotCollapsibleSection.vue'

const { showModalConfirm } = useModal()
const { hasFeatureRight } = useRightsPlugin()
const { showToast } = useToastComposable({
  store,
  i18n,
})

const filter = ref({
  label: '',
})

const constraints = computed(() => {
  return store.getters['black_spot/constraints']
})

const filteredConstraints = computed(() => {
  const label = filter.value.label
  const mode = label === '' ? 'all' : 'nameOnly'

  return constraints.value.map((constraint) => {
    if (mode === 'all') {
      constraint.isVisible = true
    } else {
      constraint.isVisible = constraint.label
        .toLowerCase()
        .includes(label.toLowerCase())
    }

    return constraint
  })
})

async function removeItem({ id }) {
  if (
    await showModalConfirm(i18n.t('blackspot.constraints.remove_confirmation'))
  ) {
    try {
      await api.blackSpotConstraint.delete(id)
      await store.dispatch('black_spot/loadConstraints')
      console.log('removeItem success')
      showToast({
        type: 'info',
        title: 'blackspot.generic_info_toast_title',
        text: 'crud_operation_messages.remove_success',
        timeout: 3000,
      })
    } catch (error) {
      if (error?.response?.status === 422) {
        showToast({
          type: 'warning',
          title: 'blackspot.constraints.remove.associated.title',
          text: 'blackspot.constraints.remove.associated.text',
          timeout: 4000,
        })
      }

      console.error(error.stack)
    }
  }
}
</script>
<style lang="scss" scoped>
ul {
  list-style: none;
  padding: 0px;
  // max height: calc(100vh - tabs height - small toolbar height - button row height - margin between list and button row - button bottom delta - filter title height)
  max-height: calc(100vh - 50px - 68px - 40px - 1rem - 1rem - 33px);
  height: 76vh;
  overflow-y: auto;

  .list-item {
    border-bottom: 1px solid #0000005c;
    padding: 0.75rem 0;
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: flex-start;
    justify-content: space-between;

    &:first-child {
      padding-top: 0.25rem;
    }
  }

  li,
  span,
  div {
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard */
  }

  .accessible-clickable-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .col-flex {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &:not(.no-gutters) {
      column-gap: 10px;
    }

    &.no-gutters {
      column-gap: 1px;
    }
  }

  .label {
    font: normal normal bold 12px/16px Open Sans;
    letter-spacing: 0px;
    color: var(--color-tundora);
  }
  .speed {
    font: normal normal normal 12px/16px Open Sans;
    letter-spacing: 0px;
    color: var(--color-tundora);
  }

  .img-wrapper {
    border-radius: 50px;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 5px 5px 7px;
    margin-right: 12px;
  }
}
</style>

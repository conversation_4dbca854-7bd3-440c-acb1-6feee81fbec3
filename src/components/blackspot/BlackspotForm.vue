<template>
  <div class="row m-0 p-0">
    <div class="col m-0 p-0">
      <div class="row m-0 p-0">
        <div class="col m-0 p-0">
          <FormLabel required :label="$t('blackspot.name')" />
          <input v-model="formRef.name" class="form-control" type="text" />
        </div>
      </div>
      <div class="row m-0 p-0">
        <div class="col m-0 p-0">
          <FormLabel :label="$t('blackspot.description')" />
          <textarea
            v-model="formRef.description"
            class="form-control"
            type="text"
          />
        </div>
      </div>
      <div class="row m-0 p-0 mt-2">
        <div class="col m-0 p-0">
          <FormLabel required :label="$t('blackspot.constraint')" />
          <ConstraintSelect v-model="formRef.constraintId"></ConstraintSelect>
        </div>
      </div>
      <div class="row m-0 p-0 mt-2">
        <div class="col m-0 p-0">
          <FormLabel required :label="$t('blackspot.category')" />
          <CategoriesTreeview v-model="formRef.categoryId"></CategoriesTreeview>
        </div>
      </div>
      <div class="row m-0 p-0 mt-2">
        <div class="col m-0 p-0">
          <FormLabel required :label="$t('blackspot.limitSpeed')" />
          <SpeedLimitSelect v-model="formRef.speedLimitId"></SpeedLimitSelect>
        </div>
      </div>
      <div class="row m-0 p-0 mt-2">
        <div class="col m-0 p-0 flex-center-left">
          <input v-model="formRef.enabled" class="mr-2" type="checkbox" />
          <FormLabel :label="$t('blackspot.enable_label')" />
        </div>
      </div>

      <slot name="bottom"></slot>

      <!-- BUTTONS -->
      <div class="row m-0 p-0 mt-2">
        <div class="col m-0 p-0 flex-center-right" style="column-gap: 10px">
          <ButtonWrapper type="secondary" @click="$emit('cancel')">
            {{ $t('blackspot.cancel_button') }}
          </ButtonWrapper>
          <ButtonWrapper
            :key="store.state.black_spot.blackspotLayerChangeTimestamp"
            type="primary"
            :disabled="!canValidate"
            @click="onValidate()"
          >
            {{ $t('blackspot.validate_button') }}
          </ButtonWrapper>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import store from '@/store'
import * as R from 'ramda'
import useBlackSpotForm from '@/composables/blackSpotForm.js'
import { useVModelProp } from '@/composables/useVModelProp'
import { useVModelEmit } from '@/composables/useVModelEmit'

const { isItemValid } = useBlackSpotForm()
const canValidate = ref(false) //computed(() => isItemValid(formRef))

const emit = defineEmits(['input', 'cancel', 'validate', 'update:modelValue'])

const { vModelEmit } = useVModelEmit(emit)

const createFormValues = () => ({
  name: '',
  constraintId: null,
  categoryId: null,
  description: '',
  speedLimitId: null,
  enabled: true,
})
const props = defineProps({
  [useVModelProp()]: {
    type: Object,
    default: () => ({}),
  },
})

const vModelProp = computed(() => useVModelProp())

const formRef = reactive(createFormValues())

watch(
  () => store.state.black_spot.blackspotLayerChangeTimestamp,
  () => {
    setTimeout(() => (canValidate.value = isItemValid(formRef)), 300)
  }
)
watch(
  () => formRef,
  () => {
    setTimeout(() => (canValidate.value = isItemValid(formRef)), 300)
  },
  {
    deep: true,
  }
)

watch(
  formRef,
  () => {
    vModelEmit(formRef)
  },
  {
    deep: true,
  }
)

onMounted(() => {
  watch(
    () => props[vModelProp.value],
    () => {
      Object.keys(props[vModelProp.value]).forEach((key) => {
        formRef[key] = props[vModelProp.value][key]
      })

      //console.log('form watch', props.value, 'formRef', formRef)
    },
    { deep: true }
  )
})

/**
 * @todo Validate required/invalid fields
 */
function onValidate() {
  if (!canValidate.value) {
    console.log('Form validation fail')
    return false
  }

  let newItem = { ...formRef }

  const segmentParts = store.state.black_spot.blackSpotSegmentPolylines

  let type = newItem.type ?? 'Z'

  if (segmentParts.length > 0) {
    type = 'S'
  } else {
    type = 'Z'
  }

  newItem.type = type

  //segments will be computed below
  newItem = R.pick(
    [
      'id',
      'name',
      'description',
      'type',
      'categoryId',
      'constraintId',
      'enabled',
      'geoJson',
      'lat',
      'lng',
      'radius',
      'shapeName',
      'xMin',
      'xMax',
      'yMin',
      'yMax',
      'speedLimitId',
    ],
    newItem
  )

  //If segment, collect segment parts
  if (newItem.type === 'S') {
    newItem.segments = segmentParts.map((item, orderNumber) => {
      let s = R.pick(['geoJson', 'latitude', 'longitude'], item)
      s.orderNumber = orderNumber
      return s
    })
    newItem.shapeName = 'linestring'
  } else {
    //circle
    //polygon
  }

  /*console.log({
    newItem,
  })*/

  emit('validate', newItem)
}
</script>

<template>
  <!-- Generic list -->
  <ul ref="scrollWrapper">
    <ListItem
      v-for="item in _items"
      :key="item[trackBy]"
      :icon-bg-color="item.iconBgColor"
      :icon-src="item.iconSrc"
      :class="{
        'd-none': !(item.isVisible ?? true),
        highlighted: item.id === highlightedId,
      }"
    >
      <template #icon>
        <slot name="item-icon" v-bind="{ item }"> </slot>
      </template>
      <template #center>
        <slot name="item-center" v-bind="{ item }"> </slot>
      </template>
      <template #right>
        <slot name="item-right" v-bind="{ item }"> </slot>
      </template>
    </ListItem>
  </ul>
</template>

<script setup>
import ListItem from './ListItem.vue'
import { EVENT_BLACKSPOT_HIGHLIGHT } from '@/services/blackspot-service'
import { mitt } from '@/plugins/mitt.js'

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
  trackBy: {
    type: String,
    default: 'id',
  },
})

const highlightedId = ref(-1)
const _items = ref([])
const scrollWrapper = ref(null)

async function highlightSelectedBlackspot(id) {
  const itemIndex = props.items.findIndex((item) => item.id === id)

  if (itemIndex !== -1) {
    _items.value.unshift(_items.value.splice(itemIndex, 1)[0])
    await scrollToTopSmoothly()

    highlightedId.value = id

    setTimeout(() => {
      highlightedId.value = -1
    }, 1000)
  }
}

function scrollToTopSmoothly() {
  return new Promise((resolve) => {
    const scrollWrapperElement = scrollWrapper.value
    if (scrollWrapperElement) {
      if (scrollWrapperElement.scrollTop === 0) {
        resolve() // Resolve immediately if scroll is already at the top
        return
      }

      // Add an event listener to resolve the promise when scrolling is complete
      scrollWrapperElement.addEventListener('scroll', function onScroll() {
        if (scrollWrapperElement.scrollTop === 0) {
          scrollWrapperElement.removeEventListener('scroll', onScroll)
          resolve()
        }
      })

      scrollWrapperElement.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
    } else {
      resolve() // Resolve immediately if the scroll wrapper element is not available
    }
  })
}

onMounted(() => {
  mitt.on(EVENT_BLACKSPOT_HIGHLIGHT, highlightSelectedBlackspot)
})

watch(
  () => props.items,
  (newValue, oldValue) => {
    _items.value = newValue
  }
)

onUnmounted(() => {
  mitt.off(EVENT_BLACKSPOT_HIGHLIGHT, highlightSelectedBlackspot)
})
</script>

<style lang="scss" scoped>
ul {
  list-style: none;
  padding: 0;
  margin: 0 0 1rem;
  // max height: calc(100vh - tabs height - small toolbar height - button row height - margin between list and button row - button bottom delta - filter (collapsed) height)
  max-height: calc(100vh - 50px - 68px - 40px - 1rem - 1rem - 33px);
  height: 76vh;
  overflow-y: auto;
}
</style>

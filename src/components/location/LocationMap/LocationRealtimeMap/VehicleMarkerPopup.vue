<template>
  <MapPopup :title="$t('common.map.popup.realtime.title')">
    <div>
      <div
        class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
      >
        <div>
          {{ $t('popup.realtime.vehicle.last_position_section_title') }}
        </div>
        <i
          :class="
            isLastPositionSectionCollapsed
              ? 'fas fa-chevron-up event-popup-chevron'
              : 'fas fa-chevron-down event-popup-chevron collapsed'
          "
          :aria-expanded="isLastPositionSectionCollapsed ? 'true' : 'false'"
          aria-controls="collapse-last-position"
          @click="
            isLastPositionSectionCollapsed = !isLastPositionSectionCollapsed
          "
        />
      </div>

      <b-collapse
        id="collapse-last-position"
        v-model="isLastPositionSectionCollapsed"
      >
        <div class="col-12 pl-4">
          <div class="my-1">
            <div class="row">
              <strong> {{ $t('common.date') }} </strong>
            </div>
            <div class="row">
              {{ normalizedItem.date | formatDate }}
            </div>
          </div>

          <div class="my-1">
            <div class="row">
              <strong> {{ $t('common.place') }} </strong>
            </div>
            <div class="row">
              {{ vehicleAddress }}
            </div>
          </div>

          <div class="my-1" data-field="vehicleSpeed">
            <div class="row">
              <strong>
                {{ $t('common.Vitesse') }}
              </strong>
            </div>
            <div class="row">
              {{ vehicleSpeed }}
            </div>
          </div>
        </div>
      </b-collapse>
    </div>

    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>{{ $t('popup.realtime.vehicle.vehicle_section_title') }}</div>
      <i
        :class="
          isVehicleSectionCollapsed
            ? 'fas fa-chevron-up event-popup-chevron'
            : 'fas fa-chevron-down event-popup-chevron collapsed'
        "
        :aria-expanded="isVehicleSectionCollapsed ? 'true' : 'false'"
        aria-controls="collapse-vehicle"
        @click="isVehicleSectionCollapsed = !isVehicleSectionCollapsed"
      />
    </div>

    <b-collapse id="collapse-vehicle" v-model="isVehicleSectionCollapsed">
      <div class="row">
        <div class="col-12 pl-4 mb-2">
          <div class="row">
            <div class="col-6 my-1">
              <div class="row m-0">
                <strong> {{ $t('common.matriculation') }} </strong>
              </div>
              <div class="row m-0">
                {{ vehicleMatriculation }}
              </div>
            </div>

            <div class="col-6 my-1">
              <div class="row m-0">
                <strong> {{ $t('common.Chauffeur') }} </strong>
              </div>
              <div class="row m-0">
                {{ vehicleDriver }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </b-collapse>

    <!--
    <div v-if="false">
      <div
        class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
      >
        <div>{{ $t('popup.realtime.sensor.title') }}</div>
        <i
          :class="
            isSensorSectionCollapsed
              ? 'fas fa-chevron-up event-popup-chevron'
              : 'fas fa-chevron-down event-popup-chevron collapsed'
          "
          :aria-expanded="isSensorSectionCollapsed ? 'true' : 'false'"
          aria-controls="collapse-sensor"
          @click="isSensorSectionCollapsed = !isSensorSectionCollapsed"
        />
      </div>

      <b-collapse id="collapse-sensor" v-model="isSensorSectionCollapsed">
        <div class="col-12 pl-4">
          <div class="my-1">
            <div
              v-for="(tor, index) in normalizedItem.sensorTor"
              :key="index"
              class="row"
            >
              <strong>
                {{ tor.name }}
              </strong>
              <div v-if="tor.enabled" class="text-success">Actif</div>
              <div v-if="!tor.enabled" class="text-danger">Inactif</div>
            </div>
          </div>
        </div>
      </b-collapse>
    </div>
    -->

    <!--
    <div v-if="false">
      <div
        class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
      >
        <div>{{ $t('popup.realtime.bus_can.title') }}</div>
        <i
          :class="
            isBusCanSectionCollapsed
              ? 'fas fa-chevron-up event-popup-chevron'
              : 'fas fa-chevron-down event-popup-chevron collapsed'
          "
          :aria-expanded="isBusCanSectionCollapsed ? 'true' : 'false'"
          aria-controls="collapse-bus-can"
          @click="isBusCanSectionCollapsed = !isBusCanSectionCollapsed"
        />
      </div>

      <b-collapse id="collapse-bus-can" v-model="isBusCanSectionCollapsed">
        <div class="col-12 pl-4">
          <div class="my-1">
            <div class="row">
              <strong>{{
                $t('popup.realtime.bus_can.consumption_title')
              }}</strong>
              <div class="ml-1">
                {{ sensorCanConsumptionLiters }}
              </div>
            </div>
          </div>

          <div class="py-1">
            <div class="row">
              <strong>{{ $t('popup.realtime.bus_can.vrm_title') }}</strong>
              <div class="ml-1">
                {{ sensorCanRPM }}
              </div>
            </div>
          </div>
        </div>
      </b-collapse>
    </div>
    -->
  </MapPopup>
</template>
<script>
import Vue from 'vue'
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'
import switchablePopupsMixin from '@/mixins/switchable-popup-mixin.ts'
import { getEmptyStringIfStringIncludes } from '@/utils/string'

/**
 * Retrieves position details (including sensor infos) from vehicle_id and date.
 * @deprecated
 */
function SensorPopupSectionMixin() {
  return {
    components: {
      SensorPopupSection,
    },
    mixins: [GPSPositionDetailsMixin],
    watch: {
      normalizedItem: {
        handler() {
          this.$trackWatch(1)
          if (this.normalizedItem.vehicleId && this.normalizedItem.date) {
            this.fetchPositionDetails(
              this.normalizedItem.vehicleId,
              this.normalizedItem.date
            )
          }
        },
        deep: true,
        immediate: true,
      },
    },
  }
}

export default {
  name: 'VehicleMarkerPopup',
  components: {
    MapPopup,
  },
  filters: {
    formatDate: function (value) {
      return Vue.$date.formatDatetimeWithSeconds(String(value))
    },
  },
  mixins: [switchablePopupsMixin],
  props: {
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    let spmLabelPrefix = getEmptyStringIfStringIncludes(
      this.$t('switchable_popups.label.location'),
      'switchable_popups'
    )
    return {
      spmLabelPrefix,
      spmLayerGroupName: 'vehicleMarkers',
      spmComputeNearbyLayersUsingLeafletLayerGroupLayersAccessor: true,
      isLastPositionSectionCollapsed: true,
      //isSensorSectionCollapsed: false,
      //isBusCanSectionCollapsed: false,
      isVehicleSectionCollapsed: false,
    }
  },
  computed: {
    normalizedItem() {
      return this.geometry.properties.normalizedItem || {}
    },
    vehicleCategory() {
      return this.normalizedItem.vehicleCategoryName || 'n/c'
    },
    vehicleMatriculation() {
      return this.normalizedItem.vehicleMatriculation || 'n/c'
    },
    vehicleSpeed() {
      if (!this.normalizedItem.speed) {
        return 'n/c'
      }
      return this.normalizedItem.speed + ' Km/h' || 'n/c'
    },
    vehicleAddress() {
      return this.normalizedItem.addressFormatted || 'n/c'
    },
    vehicleDriver() {
      return this.normalizedItem.driverName || 'n/c'
    },
    /*
    sensorCanConsumptionLiters() {
      return this.normalizedItem.sensorCanConsumptionLiters || 'n/c'
    },
    sensorCanRPM() {
      return this.normalizedItem.sensorCanRPM || 'n/c'
    },*/
  },
}
</script>
<style lang="scss" scoped>
.datetime {
  font: normal normal 600 14px/18px Open Sans;
  color: var(--color-tundora);
}
.label {
  font: normal normal normal 11px/18px Open Sans;
  color: var(--color-metal-rock);
}
.value {
  font: normal normal normal 12px/18px Open Sans;
  color: var(--color-tundora);
}
.custom-card {
  border: none !important;
}

.blue {
  color: var(--color-denim);
}

.small-text {
  font-size: 0.75rem;
}

.event-popup-chevron {
  position: absolute;
  right: 10px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
</style>

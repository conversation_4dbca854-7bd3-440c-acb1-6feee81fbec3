<template lang="pug">
SimplicitiMap(:fitBounds="fitBounds")
</template>
<script>
import SimplicitiMap from '@c/shared/SimplicitiMap/SimplicitiMap.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'LocationRealtimeMap',
  components: {
    SimplicitiMap,
  },
  provide: {
    isLocationMainSearch: true,
    isLocationRealtimeMap: true,
  },
  data() {
    return {
      fitBounds: [],
    }
  },
  computed: {
    ...mapGetters({
      vehicleMarkers:
        'location_module/getRealtimeMarkersFromActiveSearchFormTabResults',
    }),
  },
  created() {
    this.__fitBoundsHandler = () => {
      this.$nextTick(() => {
        this.fitBounds = this.vehicleMarkers.reduce((acum, v) => {
          acum = acum.concat([
            [v.item.normalizedItem.lat, v.item.normalizedItem.lng],
          ])
          return acum
        }, [])
      })
    }
    this.$mitt.on('SIMPLICITI_MAP__FIT_TO_BOUNDS', this.__fitBoundsHandler)
  },
  destroyed() {
    this.$mitt.off('SIMPLICITI_MAP__FIT_TO_BOUNDS', this.__fitBoundsHandler)
  },
}
</script>

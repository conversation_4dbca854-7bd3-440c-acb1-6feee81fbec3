<template lang="pug">
.location_history_map(style="height: inherit")
  SimplicitiMap(:fitBounds="fitBounds")
</template>
<script>
import { mapGetters } from 'vuex'
import mapMixin from '@/mixins/map.js'
import SimplicitiMap from '@c/shared/SimplicitiMap/SimplicitiMap.vue'

/**
 * Used by Location module
 * Some items might have multiple associated polylines (relatedPolylines attribute)
 * @param {*} items Normalized array of items
 */
function getLeafletPolylinesArrayFromMultipleItems(items) {
  return items
    .reduce((acum, item) => {
      if (item.relatedPolylines) {
        acum = [...acum, ...(item.relatedPolylines || [])]
      } else {
        acum.push(item.tripHistoryPolyline)
      }
      return acum
    }, [])
    .filter((polyline) => !!polyline)
}

/**
 * This component is used when a search by history (date selection) is performed.
 * @namespace components
 * @category components
 * @subcategory location/history
 * @module LocationHistoryMap
 **/
export default {
  components: {
    SimplicitiMap,
  },
  mixins: [mapMixin],
  provide: {
    isLocationMainSearch: true,
    isLocationHistoryMap: true,
  },
  data() {
    return {
      fitBounds: [],
    }
  },
  computed: {
    ...mapGetters({
      getResultsFromType: 'location_module/getResultsFromType',
    }),
    circuitExecutionsPolylines() {
      let tabName = this.$store.getters['search_module/activeSearchFormTabName']

      if (tabName !== 'circuit') {
        return []
      } else {
        let items = this.getResultsFromType(tabName)
        return getLeafletPolylinesArrayFromMultipleItems(items)
      }
    },
    tripHistoryPolylines() {
      let tabName = this.$store.getters['search_module/activeSearchFormTabName']
      if (tabName === 'circuit') {
        return []
      } else {
        let items = this.getResultsFromType(tabName)
        return getLeafletPolylinesArrayFromMultipleItems(items)
      }
    },
  },
  watch: {
    /**
     * Load Circ Execs linestrings dataset (entire results) into SimplicitiMap
     */
    circuitExecutionsPolylines: {
      async handler(data) {
        this.$trackWatch(1)
        if (data.length > 0) {
          await this.$store.dispatch('simpliciti_map/setDataset', {
            type: 'circuitExecutionsPolylines',
            data,
          })
        }
      },
      immediate: true,
    },
    /**
     * @todo: Store polylines in a group different than circuit executions
     */
    tripHistoryPolylines: {
      async handler(data) {
        if (data.length > 0) {
          await this.$store.dispatch('simpliciti_map/setDataset', {
            type: 'tripHistoryPolylines',
            data,
          })
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.bindFitToPolylinesBounds()
  },
  destroyed() {
    this.unbindFitToPolylinesBounds()
  },
  methods: {
    unbindFitToPolylinesBounds() {
      this.$mitt.off('SIMPLICITI_MAP__FIT_TO_BOUNDS', this.__fitBoundsHandler)
    },
    /**
     * @todo Improve/Remove
     */
    bindFitToPolylinesBounds() {
      this.__fitBoundsHandler = () => {
        let polylines =
          (this.circuitExecutionsPolylines.length > 0
            ? this.circuitExecutionsPolylines
            : this.tripHistoryPolylines) || []
        let bounds = polylines.reduce((acum, v) => {
          acum = acum.concat(v.polyline)
          return acum
        }, [])
        this.fitBounds = []
        this.$nextTick(() => {
          this.fitBounds = bounds
        })
      }

      //TODO: refactor: might collide with "generic-zoom-mixin"
      this.$mitt.on('SIMPLICITI_MAP__FIT_TO_BOUNDS', this.__fitBoundsHandler)
    },
  },
}
</script>

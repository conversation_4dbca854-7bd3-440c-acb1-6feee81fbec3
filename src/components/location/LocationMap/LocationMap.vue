<template lang="pug">
    .location_map
        LocationRealtimeMap(v-if="!isHistoryMode")
        LocationHistoryMap(v-if="isHistoryMode")
</template>
<script>
import LocationRealtimeMap from './LocationRealtimeMap/LocationRealtimeMap.vue'
import LocationHistoryMap from './LocationHistoryMap/LocationHistoryMap.vue'
import { mapGetters } from 'vuex'
export default {
  componentType: 'container',
  components: {
    LocationHistoryMap,
    LocationRealtimeMap,
  },
  computed: {
    ...mapGetters({
      isHistoryMode: 'location_module/currentTabContainsHistoryResults',
    }),
  },
}
</script>
<style lang="scss" scoped>
.location_map {
  height: 100%;
}
</style>

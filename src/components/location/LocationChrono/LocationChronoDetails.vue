<template>
  <div>
    <div v-if="isEmpty">
      <p>{{ $t('common.vehicle_date_no_results') }}</p>
    </div>

    <div v-if="!isEmpty">
      <div class="col-12">
        <h5 class="chrono-title">
          <s-chrono-icon class="mr-3" :color="iconColor" :size="18" />
          {{ $t('location.chrono.list_details_title') }}

          <div class="chrono-details-chevron-collapse">
            <i
              v-b-toggle.collapse-details
              :class="
                !isDetailsCollapsed
                  ? 'fas fa-chevron-down'
                  : 'fas fa-chevron-up'
              "
              @click="isDetailsCollapsed = !isDetailsCollapsed"
            />
          </div>
        </h5>
      </div>

      <b-collapse id="collapse-details" class="col">
        <div class="col-12 p-0">
          <div class="history-tl-container">
            <ul class="tl">
              <li
                v-for="(detail, index) in details"
                :key="index"
                class="tl-item"
                @click="click(detail)"
              >
                <img class="tl-item-icon" :src="detail.iconPath" />

                <div class="row">
                  <div class="col-11 offset-1">
                    <div class="row m-0">
                      <div class="col-6 p-0">
                        <div class="row m-0">
                          <div class="small-text font-weight-light">
                            {{ formatDate(detail.startDate) }}
                          </div>
                        </div>
                      </div>

                      <div class="col-6 p-0">
                        <div class="row m-0">
                          <strong>
                            {{ $t('location.chrono.list_details_duration') }} :
                          </strong>
                          <div class="small-text ml-1">
                            {{ detail.period }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="row m-0">
                      <div class="col-6 p-0">
                        <div class="row m-0">
                          <strong>
                            {{ 'N°' + (index + 1) }}
                          </strong>
                          <div class="small-text ml-1">
                            {{ detail.type }}
                          </div>
                        </div>
                      </div>

                      <div class="col-6 p-0">
                        <div class="row m-0">
                          <strong>
                            {{ $t('location.chrono.list_details_distance') }} :
                          </strong>
                          <div class="small-text ml-1">
                            {{ detail.distance + ' km' }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="row m-0">
                      <div class="col-6 p-0">
                        <div class="row m-0">
                          <strong>
                            {{ $t('location.chrono.list_details_service') }} :
                          </strong>
                          <div class="small-text ml-1">
                            {{ detail.service }}
                          </div>
                        </div>
                      </div>

                      <div class="col-6 p-0">
                        <div class="row m-0">
                          <div class="small-text ml-1">
                            {{ detail.driverName }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="row m-0">
                      <div class="col-12 p-0">
                        <div class="row m-0">
                          <strong>
                            {{ $t('location.chrono.list_details_address') }} :
                          </strong>
                          <div class="small-text ml-1">
                            {{ detail.address }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </b-collapse>
    </div>
  </div>
</template>

<script>
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import SChronoIcon from './assets/ChronoIcon.vue'
import TableButton from '@c/shared/TableButton.vue'

export default {
  name: 'LocationChronoDetails',
  components: {
    SChronoIcon,
  },
  mixins: [locationLayoutModesMixin],
  props: {
    isEmpty: {
      type: Boolean,
      required: true,
    },
    details: {
      type: Array,
      default: () => [],
    },
    iconColor: {
      type: String,
      default: '#164470',
    },
  },
  data() {
    return {
      isDetailsCollapsed: false,
    }
  },
  destroyed() {
    this.isDetailsCollapsed = false
  },
  methods: {
    formatDate(date) {
      if (!date) {
        return ''
      }
      return this.$date.adjustDateWithTimezone(date).format('HH:mm:ss')
    },
    click(detail) {
      this.$emit('on-item-click', detail)
    },
  },
}
</script>

<style lang="scss" scoped>
.blue {
  color: #164470;
}

.small-text {
  font-size: 0.75rem;
}

.history-tl-container {
  font-family: 'Roboto', sans-serif;
  width: 100%;
  margin: auto;
  display: block;
  position: relative;
}

.history-tl-container ul.tl {
  margin: 20px 0;
  padding: 0;
  display: inline-block;
}

.history-tl-container ul.tl li {
  list-style: none;
  margin: auto;
  min-height: 50px;
  border-left: 2px solid grey;
  padding: 0 0 5px 0px;
  position: relative;
}

.history-tl-container ul.tl li:last-child {
  border-left: 0;
}

.history-tl-container ul.tl li:hover::before {
  border-color: grey;
  transition: all 1000ms ease-in-out;
}

.tl-item:hover {
  cursor: pointer;
  background-color: lightgray;
  padding: 10px;
}
.tl-item .fa-search {
  opacity: 0;
}
.tl-item:hover .fa-search {
  opacity: 1;
  transition: opacity 0.1s linear;
}

.tl-item-icon {
  width: 30px;
  height: 30px;
  position: absolute;
  left: -16px;
  top: -5px;
}

.chrono-title {
  width: 100%;
  display: flex;
}

.tl {
  width: 100%;
}

ul.tl li .item-detail {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}

.chrono-details-chevron-collapse {
  position: absolute;
  right: 15px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.fa_search_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

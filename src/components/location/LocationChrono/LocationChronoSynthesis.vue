<template>
  <div>
    <div v-if="isEmpty">
      <p>{{ $t('common.vehicle_date_no_results') }}</p>
    </div>

    <div v-if="!isEmpty">
      <div class="col-12">
        <h5 class="chrono-title">
          <s-chrono-icon class="mr-3" :color="iconColor" :size="18" />
          {{ $t('location.chrono.synthesis_title') }}

          <div class="chrono-synthesis-chevron-collapse">
            <i
              v-b-toggle.collapse-synthesis
              :class="
                isSynthesisCollapsed
                  ? 'fas fa-chevron-down'
                  : 'fas fa-chevron-up'
              "
              @click="() => (isSynthesisCollapsed = !isSynthesisCollapsed)"
            />
          </div>
        </h5>
      </div>

      <b-collapse id="collapse-synthesis" class="col" :visible="true">
        <div class="row my-1">
          <div class="small-text col-6">
            <strong>{{ $t('location.chrono.synthesis_start_date') }}</strong>
            <p>{{ formatDate(synthesis.startDay) }}</p>
          </div>

          <div class="small-text col-6">
            <strong>{{ $t('location.chrono.synthesis_end_date') }}</strong>
            <p>{{ formatDate(synthesis.endDay) }}</p>
          </div>
        </div>

        <div class="row">
          <div class="small-text col-6">
            <span
              ><strong>{{
                $t('location.chrono.synthesis_amplitude')
              }}</strong></span
            >
            <p>{{ synthesis.amplitude }}</p>
          </div>

          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_stops_count') }}
            </strong>
            <p>{{ synthesis.nbStop }}</p>
          </div>
        </div>

        <div class="row my-1">
          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_distance') }}
            </strong>
            <p>{{ synthesis.distance + ' km' }}</p>
          </div>

          <div class="small-text col-6" data-field="vehicleSpeed">
            <strong>
              {{ $t('location.chrono.synthesis_avg_speed') }}
            </strong>
            <p>{{ synthesis.speed + ' km/h' }}</p>
          </div>
        </div>

        <div class="row my-1">
          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_service_time') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_2.svg" />
                <img src="../../shared/ChronoIcon/assets/chrono_4.svg" />
                <img src="../../shared/ChronoIcon/assets/chrono_3.svg" />
                {{ synthesis.periodService }}
              </p>
            </div>
          </div>

          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_driving_time') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_4.svg" />
                {{
                  synthesis.periodDrive + ' (' + synthesis.percentDrive + '%)'
                }}
              </p>
            </div>
          </div>
        </div>

        <div class="row my-1">
          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_available_time') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_2.svg" />
                {{
                  synthesis.periodAvailable +
                  ' (' +
                  synthesis.percentAvailable +
                  '%)'
                }}
              </p>
            </div>
          </div>

          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_work_time') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_3.svg" />
                {{ synthesis.periodWork + ' (' + synthesis.percentWork + '%)' }}
              </p>
            </div>
          </div>
        </div>

        <div class="row my-1">
          <div class="small-text col-12">
            <strong>
              {{ $t('location.chrono.synthesis_availability') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_2.svg" />
                <img src="../../shared/ChronoIcon/assets/chrono_3.svg" />
                {{
                  synthesis.periodWorkAvailable +
                  ' (' +
                  synthesis.percentWorkAvailable +
                  '%)'
                }}
              </p>
            </div>
          </div>
        </div>

        <div class="row my-1">
          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_max_rest_time') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_1.svg" />
                {{ synthesis.maxPeriodRest }}
              </p>
            </div>
          </div>
          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_max_total_rest_time') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_1.svg" />
                {{ synthesis.periodRest + ' (' + synthesis.percentRest + '%)' }}
              </p>
            </div>
          </div>
        </div>

        <div class="row my-1">
          <div class="small-text col-6">
            <strong>
              {{ $t('location.chrono.synthesis_start_max_duration') }}
            </strong>
            <div>
              <p>
                <img src="../../shared/ChronoIcon/assets/chrono_1.svg" />
                {{ synthesis.maxPeriodStart }}
              </p>
            </div>
          </div>
        </div>
      </b-collapse>
    </div>
  </div>
</template>

<script>
import SChronoIcon from './assets/ChronoIcon.vue'
import moment from 'moment'

export default {
  name: 'LocationChronoSynthesis',
  components: {
    SChronoIcon,
  },
  props: {
    isEmpty: {
      type: Boolean,
      required: true,
    },
    synthesis: {
      type: Object,
      default: () => ({}),
    },
    iconColor: {
      type: String,
      default: '#164470',
    },
  },
  data() {
    return {
      isSynthesisCollapsed: false,
    }
  },
  destroyed() {
    this.isSynthesisCollapsed = false
  },
  methods: {
    formatDate(date) {
      return this.$date.formatDatetime(this.$date.adjustDateWithTimezone(date))
    },
  },
}
</script>

<style scoped>
.small-text {
  font-size: 0.75rem;
}

.chrono-title {
  width: 100%;
  display: flex;
}

.chrono-synthesis-chevron-collapse {
  position: absolute;
  right: 15px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
</style>

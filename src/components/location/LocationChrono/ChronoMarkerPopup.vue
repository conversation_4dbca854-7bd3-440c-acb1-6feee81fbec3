<template>
  <MapPopup title="Relevé chronotachygraphe">
    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>Imputation</div>
      <i
        :class="
          isImputationSectionCollapsed
            ? 'fas fa-chevron-up event-popup-chevron'
            : 'fas fa-chevron-down event-popup-chevron collapsed'
        "
        :aria-expanded="isImputationSectionCollapsed ? 'true' : 'false'"
        aria-controls="collapse-imputation"
        @click="isImputationSectionCollapsed = !isImputationSectionCollapsed"
      />
    </div>

    <b-collapse
      id="collapse-imputation"
      v-model="isImputationSectionCollapsed"
      class="popup"
    >
      <div class="col-12 pl-4">
        <div class="my-1">
          <div class="row">
            <div class="col-3 px-0">
              <div>
                <strong> Début </strong>
              </div>
              <div>
                {{ formatDate(geometry.properties.startDate) }}
              </div>
            </div>

            <div class="col-3 px-0">
              <div>
                <strong> Fin </strong>
              </div>
              <div>
                {{ formatDate(geometry.properties.endDate) }}
              </div>
            </div>

            <div class="col-3 px-0">
              <div>
                <strong> Durée </strong>
              </div>
              <div>
                {{ geometry.properties.period }}
              </div>
            </div>

            <div class="col-3 px-0">
              <div>
                <strong> Distance </strong>
              </div>
              <div>
                {{ geometry.properties.distance + ' km' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </b-collapse>

    <div>
      <div
        class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
      >
        <div>Etat</div>
        <i
          :class="
            isStateSectionCollapsed
              ? 'fas fa-chevron-up event-popup-chevron'
              : 'fas fa-chevron-down event-popup-chevron collapsed'
          "
          :aria-expanded="isStateSectionCollapsed ? 'true' : 'false'"
          aria-controls="collapse-state"
          @click="isStateSectionCollapsed = !isStateSectionCollapsed"
        />
      </div>

      <b-collapse id="collapse-state" v-model="isStateSectionCollapsed">
        <div class="col-12 pl-4">
          <div class="my-1">
            <div class="row">
              <div class="col-6 px-0 mb-1">
                <div>
                  <strong> Conduite </strong>
                </div>
                <div class="row pl-3 align-items-center">
                  <div>
                    <img
                      :src="'./lib/realtimeMap/assets/picto_chrono/chrono_4.svg'"
                    />
                  </div>
                  {{ geometry.properties.periodDrive }}
                </div>
              </div>

              <div class="col-6 px-0 mb-1">
                <div>
                  <strong> Travail </strong>
                </div>
                <div class="row pl-3 align-items-center">
                  <div>
                    <img
                      :src="'./lib/realtimeMap/assets/picto_chrono/chrono_3.svg'"
                    />
                  </div>
                  {{ geometry.properties.periodWork }}
                </div>
              </div>

              <div class="col-6 px-0">
                <div>
                  <strong> Disponibilité </strong>
                </div>
                <div class="row pl-3 align-items-center">
                  <div>
                    <img
                      :src="'./lib/realtimeMap/assets/picto_chrono/chrono_2.svg'"
                    />
                  </div>
                  {{ geometry.properties.periodAvailable }}
                </div>
              </div>

              <div class="col-6 px-0 pb-1">
                <div>
                  <strong> Repos </strong>
                </div>
                <div class="row pl-3 align-items-center">
                  <div>
                    <img
                      :src="'./lib/realtimeMap/assets/picto_chrono/chrono_1.svg'"
                    />
                  </div>
                  {{ geometry.properties.periodRest }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </b-collapse>
    </div>
  </MapPopup>
</template>

<script>
import moment from 'moment'
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'

export default {
  name: 'ChronoMarkerPopup',
  components: {
    MapPopup,
  },
  props: {
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isImputationSectionCollapsed: true,
      isStateSectionCollapsed: false,
    }
  },
  created() {
    console.log(this.geometry.properties)
  },
  methods: {
    formatDate(date) {
      return this.$date.adjustDateWithTimezone(date).format('HH:mm:ss')
    },
  },
}
</script>

<style scoped>
.blue {
  color: var(--color-denim);
}

.small-text {
  font-size: 0.75rem;
}

.event-popup-chevron {
  position: absolute;
  right: 10px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
</style>

<template>
  <div ref="root" class="vehicle_chrono_table">
    <DataTable
      v-if="table"
      ref="datatable"
      class="table_theme_1"
      :ssr-paging="false"
      :ssr-show-multi-column-filter="false"
      :searching="true"
      :paging="true"
      name="locationDetailsChrono"
      row-id="index"
      scroll-y="250px"
      :columns="columns"
      :language="$translations.datatable"
      :extra-options="extraOptions || {}"
      :select="select"
      :default-sorting-column="1"
      :auto-height="true"
      :auto-height-offset="mode === 'table' ? 0 : 0"
      @select="onSelect"
      @deselect="onDeselect"
    />
  </div>
</template>
<script>
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import { mapGetters } from 'vuex'
import moment from 'moment'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'

/**
 * @todo Refactor/Extract
 */
function sortableColumn(sortBy, display, title) {
  return {
    data: {
      _: sortBy,
      filter: display, //Filter using what user see
      display,
      sort: sortBy,
    },
    title,
  }
}

export default {
  name: 'LocationChronoTable',
  components: {
    DataTable,
  },
  mixins: [datatableMixin, locationLayoutModesMixin],
  props: {
    mode: {
      type: String,
      default: 'table+map',
      enum: ['table', 'table+map'],
    },
  },
  data() {
    var self = this
    return {
      table: false,
      select: {
        items: 'row',
        style: 'single',
        info: false,
      },
      columns: this.buildColumnsArray(),
      // Set columnDefs array if necessary
      // columnDefs: [],
      extraOptions: this.buildExtraOptions(),
    }
  },
  computed: {
    ...mapGetters({
      results: 'location_module/getChronoImputations',
      selectedItem: 'location_module/selectedItem',
    }),
  },
  watch: {
    results() {
      this.$trackWatch(1)
      this.updateTable()
    },
  },
  mounted() {
    $.fn.dataTable.moment(this.$date.getDatetimePattern())
    this.updateTable()

    this.$store.dispatch('app/changeLayout', {
      origin: 'LocationChronoTable.vue::mounted',
      currentLayoutName: 'CHRONO_TABLE',
    })

    setTimeout(() => (this.table = true), 500)
  },
  destroyed() {
    if (this.$store.state.search_module.view !== 'selection') {
      this.$store.dispatch('app/changeLayout', {
        origin: 'LocationChronoTable.vue::destroyed',
        menu_collapsed: this.$store.getters['app/layout'].isMenuCollapsed,
        sub_menu: true,
      })
    }
  },
  methods: {
    onDeselect() {
      const resultArray = this.results.map((element) => [
        { lat: element.lat, lon: element.lng },
      ])
      this.$mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [resultArray])
    },
    onSelect({ item }) {
      this.$mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [[item.lat, item.lon]])
    },
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'locationDetailsChrono',
        items: this.results.map((res, index) => {
          const driver =
            res.badgeOn && res.driverName
              ? res.driverName
              : this.$t('common.not_available')

          return {
            index: index + 1,
            type: res.type,
            startDate: this.$date.formatDatetimeWithSeconds(
              this.$date.adjustDateWithTimezone(res.startDate)
            ),
            address: res.address,
            zone: res.zone,
            driver,
            period: res.period,
            service: res.service,
            distance: res.distance,
            speed: res.speed,
            lat: res.lat,
            lon: res.lng,
          }
        }),
      })
    },
    /**
     * Returns table columns
     */
    buildColumnsArray() {
      return [
        sortableColumn(
          `index`,
          `index`,
          this.$t('location_module.chrono.index')
        ),
        sortableColumn(`type`, `type`, this.$t('location_module.chrono.type')),
        sortableColumn(
          `startDate`,
          `startDate`,
          this.$t('location_module.chrono.startDate')
        ),
        sortableColumn(
          `address`,
          `address`,
          this.$t('location_module.chrono.address')
        ),
        sortableColumn(`zone`, `zone`, this.$t('location_module.chrono.zone')),
        sortableColumn(
          'driver',
          'driver',
          this.$t('location_module.chrono.driver')
        ),
        sortableColumn(
          `period`,
          `period`,
          this.$t('location_module.chrono.period')
        ),
        sortableColumn(
          `service`,
          `service`,
          this.$t('location_module.chrono.service')
        ),
        sortableColumn(
          `distance`,
          `distance`,
          this.$t('location_module.chrono.distance')
        ),
        sortableColumn(
          `speed`,
          `speed`,
          this.$t('location_module.chrono.speed')
        ),
      ]
    },
    /**
     * Returns columns extra options (e.g. text filters)
     */
    buildExtraOptions() {
      return {
        ...this.configureColumnsFilters((filters) => {
          let columnFilters = []
          this.columns.forEach((def, index) => {
            columnFilters.push(filters.createTextFilter({ column: index }))
          })

          return columnFilters
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.title,
.title span,
.title strong {
  font-size: 12px;
}
.vehicle_chrono_table {
  height: 100%;
}
.table_toolbar {
  display: flex;
  align-items: center;
}
.fas.fa-camera {
  color: #8c8c8c;
  cursor: not-allowed;
}
.fas.fa-camera.active {
  cursor: pointer;
}
</style>

<template>
  <div>
    <b-spinner
      v-if="isLoading"
      class="loader"
      variant="info"
      style="width: 3rem; height: 3rem; margin: 0 auto; display: block"
    />

    <div v-if="!isLoading">
      <div v-if="chronoHasData" class="location-chrono-card">
        <location-chrono-synthesis
          v-if="chronoData.hasOwnProperty('synthesis')"
          :synthesis="chronoData.synthesis"
          :is-empty="isSynthesisEmpty"
        />

        <location-chrono-details
          v-if="chronoData.hasOwnProperty('details')"
          class="mt-4"
          :details="chronoData.details"
          :is-empty="isImputationsEmpty"
          @on-item-click="onItemClick"
        />
      </div>

      <div v-if="!chronoHasData" class="row px-3">
        <div class="alert alert-info p-2">
          {{ $t('location_module.no_results.chrono') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import { mapGetters } from 'vuex'
import LocationChronoSynthesis from './LocationChronoSynthesis.vue'
import LocationChronoDetails from './LocationChronoDetails.vue'
import moment from 'moment'

export default {
  name: 'LocationChronoTab',
  components: {
    LocationChronoDetails,
    LocationChronoSynthesis,
  },
  mixins: [locationLayoutModesMixin],
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    view: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      color: '#164470',
      isLoading: false,
      markersArray: [],
    }
  },
  computed: {
    chronoData: {
      get() {
        return this.$store.state.location_module.chronoData
      },
      set(value) {
        this.$store.commit('location_module/SET_CHRONODATA', value)
      },
    },
    ...mapGetters({
      isImputationsEmpty: 'location_module/isChronoImputationsEmpty',
      isSynthesisEmpty: 'location_module/isChronoSynthesisEmpty',
    }),
    chronoHasData() {
      return (
        this.chronoData.synthesis &&
        Object.values(this.chronoData.synthesis).length > 0 &&
        this.chronoData.details &&
        this.chronoData.details.length > 0
      )
    },
    selectedItem() {
      return this.$store.state.location_module.selectedItem
    },
  },
  watch: {
    selectedItem: {
      handler() {
        this.$nextTick(() => {
          this.update()
        })
      },
    },
  },
  created() {
    this.update()
  },
  methods: {
    async update() {
      console.debugVerboseScope(
        ['location_module', 'chrono_feature'],
        5,
        'LocationChronoTab.vue::update'
      )

      this.isLoading = true

      // Get sensor data
      await this.$store.dispatch('location_module/updateMapPositionMarkers', {
        vehicleId: this.item.vehicleId,
        date: this.item.date,
      })

      const item = await this.$store.dispatch(
        'location_module/getVehicleChrono',
        this.item
      )

      if (item && item.hasOwnProperty('details')) {
        item.details.forEach((element) => {
          this.markersArray.push(element)
        })

        await this.$store
          .dispatch('simpliciti_map/setChronoMarkers', this.markersArray)
          .then(async () => {
            if (this.item.searchType === 'realtime') {
              await this.$store.dispatch(
                'location_module/getTripHistoryFromVehicleDate',
                { id: this.item.vehicleId, date: this.item.date }
              )
            }
          })
          .finally(() => {
            this.isLoading = false
          })
      }

      //Switch to table + map or map only mode
      this.switchToModeDependingIfData(
        this.chronoHasData,
        'LocationChronoTable',
        'LocationChronoMap'
      )
    },
    onItemClick(value) {
      this.flyToBounds(value.lat, value.lng)
    },
    flyToBounds(lat, lng, zoom = 18) {
      this.$mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [[lat, lng]])
    },
  },
}
</script>

<style scoped lang="scss">
.location-chrono-card {
  background: white;
  border-radius: 10px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.075);
}
</style>

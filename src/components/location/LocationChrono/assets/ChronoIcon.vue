<template>
  <div @click="click">
    <svg :style="style" viewBox="0 0 24 24">
      <path
        :fill="color"
        d="M4 2V8H2V2H4M2 22V16H4V22H2M5 12C5 13.11 4.11 14 3 14C1.9 14 1 13.11 1 12C1 10.9 1.9 10 3 10C4.11 10 5 10.9 5 12M16 4C20.42 4 24 7.58 24 12C24 16.42 20.42 20 16 20C12.4 20 9.36 17.62 8.35 14.35L6 12L8.35 9.65C9.36 6.38 12.4 4 16 4M15 13L19.53 15.79L20.33 14.5L16.5 12.2V7H15V13Z"
      />
    </svg>
  </div>
</template>

<script>
export default {
  name: 'ChronoIcon',
  props: {
    color: {
      type: String,
      default: '#164470',
    },
    size: {
      type: Number,
      required: true,
    },
  },
  computed: {
    style() {
      return 'width:' + this.size + ';height:' + this.size + 'px;'
    },
  },
  methods: {
    click() {
      this.$emit('click')
    },
  },
}
</script>

<style scoped></style>

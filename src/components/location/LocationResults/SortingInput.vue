<template lang="pug">
.sorting_input.align-items.center
  p.m-0.pr-0 {{$t('location_module.results_sorting.title')}}
  b-form-select(v-model="selected", :options="options")
</template>
<script>
export default {
  props: {
    value: {
      type: String,
      default: 'CONTACT_ON',
    },
  },
  data() {
    return {
      selected: 'CONTACT_ON',
      options: [
        {
          value: 'CONTACT_ON',
          text: this.$t('location_module.results_sorting.contact_on'),
        },
        {
          value: 'DATETIME_DESC',
          text: this.$t('location_module.results_sorting.datetime_asc'),
        },
        {
          value: 'DATETIME_ASC',
          text: this.$t('location_module.results_sorting.datetime_desc'),
        },
        {
          value: 'DEFAULT',
          text: this.$t('location_module.results_sorting.default'),
        },
      ],
    }
  },
  watch: {
    selected() {
      this.$trackWatch(1)
      this.$emit('input', this.selected)
    },
    value() {
      if (this.value != this.selected) {
        this.selected = this.value
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.sorting_input {
  display: flex;
  justify-content: center;
  align-items: center;
}
p {
  white-space: pre;
  color: grey;
  font-size: 12px;
  display: flex;
  align-items: center;
  padding-right: 7px;
  align-self: center;
}

select {
  align-self: center;
  color: var(--color-black);
  font-weight: bold;
  font-size: 14px;
  border: 0;
  outline: none;
  box-shadow: none !important;
  background: #fff
    url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="var(--color-black)" d="M1 3h22L12 22"%2F%3E%3C%2Fsvg%3E')
    no-repeat right 10px center/8px 10px;

  &:active {
    background: #fff
      url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="24" height="24" viewBox="0 0 24 24"%3E%3Cpath fill="var(--color-black)" d="M1 21h22L12 2"%2F%3E%3C%2Fsvg%3E')
      no-repeat right 10px center/8px 10px;
  }
}
</style>

<template>
  <div>
    <div class="row my-2">
      <div class="col-12 title">
        <simple-svg
          width="15px"
          height="15px"
          :src="identificationLeveeDetailPopupSection1Icon"
        />
        {{ $t('identification.table_item_popup.driver_behavior.title') }}

        <i
          :class="
            !value
              ? 'collapse-icon fas fa-chevron-down'
              : 'collapse-icon fas fa-chevron-up'
          "
          @click="() => $emit('input', !value)"
        />
      </div>
    </div>

    <b-collapse v-model="value" class="mt-2">
      <div class="row my-2 px-3">
        <div class="label">{{ $t('common.nom') }} :</div>
        <div class="ml-1 value">
          {{ behavior.name ? behavior.name : $t('common.not_available') }}
        </div>
      </div>

      <div class="row my-2 px-3">
        <div class="col-6 px-0">
          <div class="label">
            {{
              $t(
                'identification.table_item_popup.driver_behavior.access_blocage_lc'
              )
            }}
            :
          </div>
          <div v-if="hasBlockedAccesses">
            <div
              v-for="(item, index) in behavior.blockedAccess.infos"
              :key="index"
              class="ml-1 value"
            >
              {{ item }}
            </div>
          </div>
          <div v-else>
            <div class="ml-1 value blockedAccess-none">
              {{ $t('common.not_available') }}
            </div>
          </div>
        </div>

        <div class="col-6 px-0">
          <div class="label">
            {{
              $t(
                'identification.table_item_popup.driver_behavior.choix_chauffeur_blocage_lc'
              )
            }}
            :
          </div>
          <div v-if="hasDriversChoices">
            <div
              v-for="(item, index) in behavior.driverChoice.infos"
              :key="index"
              class="ml-1 value"
              v-html="item"
            />
          </div>
          <div v-else>
            <div class="ml-1 value driverChoice-none">
              {{ $t('common.not_available') }}
            </div>
          </div>
        </div>
      </div>
    </b-collapse>
  </div>
</template>

<script>
import identificationLeveeDetailPopupSection1Icon from '@c/location/LocationIdentBacs/modal/assets/identification-levee-detail-popup-section-1-icon.svg'
export default {
  name: 'LocationIdentModalBehaviorSection',
  props: {
    value: {
      type: Boolean,
      default: true,
    },
    behavior: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      identificationLeveeDetailPopupSection1Icon,
    }
  },
  computed: {
    hasBlockedAccesses() {
      return (
        this.behavior.blockedAccess.infos &&
        this.behavior.blockedAccess.infos.length > 0
      )
    },
    hasDriversChoices() {
      return (
        this.behavior.driverChoice.infos &&
        this.behavior.driverChoice.infos.length > 0
      )
    },
  },
}
</script>

<style scoped>
.title {
  font: normal normal bold 14px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-denim);
}
.label {
  font: normal normal bold 12px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.value {
  font: normal normal normal 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-metal-rock);
}
</style>

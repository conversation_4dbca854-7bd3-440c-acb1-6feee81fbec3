<template lang="pug">
.blacklist-history_table-wrapper
    CollapsibleSection.mt-2(:title="$t('identification.table_item_popup.last_info_section_title')" :value="true")
        .row.p-0.m-0
            .col-12(v-show="!lastInfosData.code&&!lastInfosData.fileName")
                .value {{$t('common.no_results_alternative')}}
            .col-4.p-0.m-0(v-show="lastInfosData.code&&lastInfosData.fileName")
                label.title {{$t('common.code')}}
                .value {{lastInfosData.code}}
            .col-4.p-0.m-0(v-show="lastInfosData.code&&lastInfosData.fileName")
                label.title {{$t('identification.table_item_popup.fileName')}}
                .value {{lastInfosData.fileName}}
            .col-4.p-0.m-0(v-show="lastInfosData.code&&lastInfosData.fileName")
                label.title {{$t('common.version')}}
                .value {{lastInfosData.versionedAtFormatted}}
    CollapsibleSection.mt-2(:title="$t('identification.table_item_popup.blacklist_history_details_section_title')" :value="true")
        .blacklist-history_table(ref="root")
            DataTable(
                v-if="table"
                class="table_theme_1"
                ref="datatable",
                :ssrPaging="false",
                :ssrShowMultiColumnFilter="false",
                :searching="true",
                :paging="true",
                :name="tableName",
                rowId="id",
                scrollY="250px",
                :columns="columns",
                :columnDefs="columnDefs",
                :language="$translations.datatable",
                :extraOptions="extraOptions || {}",
                :defaultSortingColumn="0"
                :autoHeight="true"
                @init="onInit"
            )
</template>
<script>
//import datatableApi from '@/api/datatableApi'
import CollapsibleSection from '@c/shared/SimplicitiMap/CollapsibleSection.vue'
import DataTable from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import { createSortableColumnDefinition } from '@/utils/datatable.js'
import moment from 'moment'
import APIUrls from '@/config/simpliciti-apis.js'

export default {
  name: 'IdentificationBlacklistHistoryTable',
  components: {
    DataTable,
    CollapsibleSection,
  },
  mixins: [datatableMixin],
  props: {
    blacklistId: {
      type: Number,
      default: 0,
      //default: 9,
    },
    blacklistVersionDatetime: {
      type: String,
      default: '',
      //default: '2022-08-19T07:30:03+02:00',
    },
    collectionDatetime: {
      type: String,
      default: '',
    },
  },
  data() {
    var self = this
    return {
      lastInfosData: {
        code: '',
        fileName: '',
        versionedAtFormatted: '',
      },
      tableName:
        'identification-blacklist-history' +
        '_' +
        this.blacklistId +
        '_' +
        Date.parse(this.blacklistVersionDatetime),
      isModalOpen: false,
      showInformationsPopup: false,
      table: false,
      columns: [
        createSortableColumnDefinition(
          `founderPuceNumber`,
          `founderPuceNumber`, //display
          this.$t('identification.table_item_popup.founder_puce_number')
        ),
        createSortableColumnDefinition(
          `memoryPuceNumber`,
          `memoryPuceNumber`, //display
          this.$t('identification.table_item_popup.memory_puce_number')
        ),
        createSortableColumnDefinition(
          `reason`,
          `reason`, //display
          this.$t('identification.table_item_popup.reason')
        ),
        createSortableColumnDefinition(
          `effectiveAt`,
          `effectiveAtFormatted`, //display
          this.$t('identification.table_item_popup.effectiveAt')
        ),
      ],
      columnDefs: [],
      extraOptions: {
        ...this.configureColumnsFilters((filters) => {
          return [
            filters.createTextFilter({ column: 0 }),
            filters.createTextFilter({ column: 1 }),
            filters.createTextFilter({ column: 2 }),
            filters.createTextFilter({ column: 3 }),
          ]
        }),
        ...this.configureFooter({
          exportToolbarProps: {
            enableExportXLS: false,
          },
        }),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      },
    }
  },
  mounted() {
    $.fn.dataTable.moment(this.$date.getDatetimePattern())
    setTimeout(() => {
      this.table = true
      setTimeout(() => {
        this.$refs.datatable.datatable.draw()
        this.refreshTable()
      }, 500)
    }, 200)
  },
  methods: {
    async refreshTable() {
      // get blacklist details directly from blacklist
      let res = await this.$api.v3.get(
        `${APIUrls.APIV3_GET_BIN_COLLECTION_BLACK_LISTS}/` + this.blacklistId,
        {},
        {
          headers: {
            Accept: 'application/json',
          },
        }
      )
      let blacklist = res?.data
      let detail = blacklist?.blackListDetails
      if (blacklist?.versionedAt !== this.blacklistVersionDatetime) {
        // get blacklist details histo if it is not the last version
        let res = await this.$api.v3.get(
          `${APIUrls.APIV3_GET_BIN_COLLECTION_BLACK_LIST_HISTOS}?page=1&itemsPerPage=30`,
          {
            'blackList.id': this.blacklistId,
            versionedAt: this.blacklistVersionDatetime,
          },
          {
            headers: {
              Accept: 'application/hal+json',
            },
          }
        )

        blacklist = res?.data?._embedded?.item[0]
        if (res?.data?._embedded?.item) {
          for (let i = 0; i < res?.data?._embedded?.item.length; i++) {
            if (
              res?.data?._embedded?.item[i].blackListDetailsHisto &&
              res?.data?._embedded?.item[i].blackListDetailsHisto.length > 0
            ) {
              blacklist = res?.data?._embedded?.item[i]
              break
            }
          }
        }
        detail = blacklist?.blackListDetailsHisto
      }
      this.lastInfosData = {
        ...this.lastInfosData,
        code: blacklist?.code,
        fileName: blacklist?.fileName,
        versionedAt:
          (blacklist?.versionedAt &&
            moment(blacklist?.versionedAt)._d.getTime()) ||
          '',
      }
      this.lastInfosData.versionedAtFormatted = this.$date.formatDatetime(
        this.lastInfosData.versionedAt
      )

      let items = (detail || []).map((item) => {
        let d = {
          founderPuceNumber: item.foundryNumber || '',
          memoryPuceNumber: item.memoryNumber || '',
          reason: item.reason || '',
          effectiveAt:
            item.effectiveAt && moment(item.effectiveAt).isValid()
              ? moment(item.effectiveAt)._d.getTime()
              : null,
        }
        d.effectiveAtFormatted = this.$date.formatDatetime(d.effectiveAt)
        return d
      })
      this.$store.dispatch('datatable/setTableItems', {
        name: this.tableName,
        items: items,
      })

      /*
      toggleDataWatch(this.tableName, true)
      let fetchOptions = {
        name: this.tableName,
        page: 1,
      }
      this.$loader && this.$loader.show()
      await this.$store.dispatch('datatable/fetchTableItems', fetchOptions)
      this.$loader && this.$loader.hide()*/
    },
    onInit() {
      //this.refreshTable()
    },
  },
}
</script>
<style lang="scss" scoped>
.blacklist-history_table {
  height: 100%;
  height: -moz-available; /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available; /* Mozilla-based browsers will ignore this. */
  height: fill-available;
  min-height: 200px;
}
.title {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.value {
  font: normal normal normal 11px/18px Open Sans;
  color: var(--color-tundora);
  word-break: break-word;
}
</style>

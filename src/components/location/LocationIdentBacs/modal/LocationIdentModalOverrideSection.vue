<template>
  <div>
    <div class="row">
      <div class="col-12 title">
        {{ $t('identification.table_item_popup.override.title') }}
        <i
          v-b-toggle
          :class="
            !value
              ? 'collapse-icon fas fa-chevron-down'
              : 'collapse-icon fas fa-chevron-up'
          "
          @click="() => $emit('input', !value)"
        />
      </div>
    </div>

    <b-collapse v-model="value" class="mt-2">
      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.override.initial.vehicle') }} :
        </div>
        <div class="ml-1 value">
          {{ initialVehicleName }}
        </div>
      </div>
      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.override.initial.round') }} :
        </div>
        <div class="ml-1 value">
          {{ initialRoundShortName }}
        </div>
      </div>
      <div v-if="initialVehicleName !== currentVehicleName" class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.override.current.vehicle') }} :
        </div>
        <div class="ml-1 value">
          {{ currentVehicleName }}
        </div>
      </div>
      <div
        v-if="initialRoundShortName !== currentRoundShortName"
        class="row px-3"
      >
        <div class="label">
          {{ $t('identification.table_item_popup.override.current.round') }} :
        </div>
        <div class="ml-1 value">
          {{ currentRoundShortName }}
        </div>
      </div>
      <div class="row px-3">
        <div class="edited-title">
          {{ $t('identification.table_item_popup.override.edited_title') }} :
        </div>
        <div class="ml-1 value">
          {{ formatEdited(override) }}
        </div>
      </div>
    </b-collapse>
  </div>
</template>

<script>
export default {
  name: 'LocationIdentModalOverrideSection',
  components: {},
  props: {
    value: {
      type: Boolean,
      default: true,
    },
    override: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isOverrideSectionUnfold: false,
    }
  },
  computed: {
    initialVehicleName() {
      // Access `override` and handle undefined cases
      return (
        this.override?.initial?.vehicle?.name || this.$t('common.not_available')
      )
    },
    initialRoundShortName() {
      return (
        this.override?.initial?.round?.shortName ||
        this.$t('common.not_available')
      )
    },
    currentVehicleName() {
      return (
        this.override?.current?.vehicle?.name || this.$t('common.not_available')
      )
    },
    currentRoundShortName() {
      return (
        this.override?.current?.round?.shortName ||
        this.$t('common.not_available')
      )
    },
  },
  methods: {
    formatEdited(override) {
      let dateEditedFormated = this.$date.formatDate(
        this.$date.adjustDateWithTimezone(override.editedAt)
      )
      return dateEditedFormated
        ? this.$t('identification.table_item_popup.override.edited_at') +
            ' ' +
            dateEditedFormated +
            ' ' +
            this.$t('identification.table_item_popup.override.edited_by') +
            ' ' +
            override.editedBy
        : this.$t('common.not_available')
    },
  },
}
</script>

<style scoped>
.title {
  font: normal normal bold 14px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-denim);
}

.label {
  font: normal normal bold 12px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}

.value {
  font: normal normal normal 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-metal-rock);
}

.edited-title {
  font: normal normal normal 10px/14px Open Sans;
  padding-top: 3px;
}
</style>

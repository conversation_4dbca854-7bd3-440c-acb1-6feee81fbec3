<template>
  <div>
    <div class="row">
      <div class="col-12 title">
        <simple-svg
          width="15px"
          height="15px"
          :src="identificationLeveeDetailPopupSection3Icon"
        />
        {{ $t('identification.table_item_popup.bans.title') }}

        <i
          v-b-toggle
          :class="
            !value
              ? 'collapse-icon fas fa-chevron-down'
              : 'collapse-icon fas fa-chevron-up'
          "
          @click="() => $emit('input', !value)"
        />
      </div>
    </div>

    <b-collapse v-model="value" class="mt-2">
      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.bans.identifier') }} :
        </div>
        <div class="ml-1 value">
          {{
            prohibition.id ||
            $t('identification.table_item_popup.bans.no_results')
          }}
          <a
            v-if="!!prohibition.id"
            href="#"
            @click="
              () =>
                (isBlacklistHistoryDetailsVisible =
                  !isBlacklistHistoryDetailsVisible)
            "
          >
            <span v-show="!isBlacklistHistoryDetailsVisible">{{
              $t(
                'identification.table_item_popup.view_blacklist_history_details_table'
              )
            }}</span>
            <span v-show="isBlacklistHistoryDetailsVisible">{{
              $t(
                'identification.table_item_popup.hide_blacklist_history_details_table'
              )
            }}</span>
          </a>
        </div>
      </div>

      <div v-if="isBlacklistHistoryDetailsVisible" class="row px-3">
        <div class="col-12 p-0">
          <BlacklistHistoryTableLegacy
            :blacklist-id="prohibition.id"
            :blacklist-version-datetime="prohibition.versionDate"
            :collection-datetime="collectionDatetime"
          />
        </div>
      </div>

      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.bans.puce') }} :
        </div>
        <div class="ml-1 value prohibition-isBlacklisted">
          {{ prohibition.isBlacklisted ? $t('common.yes') : $t('common.no') }}
        </div>
      </div>

      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.bans.file') }} :
        </div>
        <div class="ml-1 value">
          {{
            prohibition.filename
              ? prohibition.filename
              : $t('common.not_available')
          }}
        </div>
      </div>

      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.bans.file_version') }} :
        </div>
        <div class="ml-1 value">
          {{ formattedDate(prohibition.versionDate) }}
        </div>
      </div>

      <div v-if="prohibition.hasOwnProperty('issue')" class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.bans.reason') }} :
        </div>
        <div class="ml-1 value">
          {{ prohibition.issue }}
        </div>
      </div>
    </b-collapse>
  </div>
</template>

<script>
import identificationLeveeDetailPopupSection3Icon from '@c/location/LocationIdentBacs/modal/assets/identification-levee-detail-popup-section-3-icon.svg'
export default {
  name: 'LocationIdentModalProhibitionSection',
  components: {
    BlacklistHistoryTableLegacy: () =>
      import(
        /* webpackChunkName: "main"    */ '@c/location/LocationIdentBacs/modal/BlacklistHistoryTableLegacy.vue'
      ),
  },
  props: {
    value: {
      type: Boolean,
      default: true,
    },
    prohibition: {
      type: Object,
      default: () => ({}),
    },
    collectionDatetime: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isBlacklistHistoryDetailsVisible: false,
      identificationLeveeDetailPopupSection3Icon,
    }
  },
  methods: {
    formattedDate(date) {
      if (!date) {
        return this.$t('common.not_available')
      }
      return this.$date.formatDatetimeWithSeconds(
        this.$date.adjustDateWithTimezone(date)
      )
    },
  },
}
</script>

<style scoped>
.title {
  font: normal normal bold 14px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-denim);
}
.label {
  font: normal normal bold 12px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.value {
  font: normal normal normal 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-metal-rock);
}
</style>

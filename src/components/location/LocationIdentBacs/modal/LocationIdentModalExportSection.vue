<template>
  <div>
    <div class="row">
      <div class="col-12 title">
        <simple-svg
          width="15px"
          height="15px"
          :src="identificationLeveeDetailPopupSection2Icon"
        />
        {{ $t('identification.table_item_popup.export.title') }}

        <i
          v-b-toggle
          :class="
            !value
              ? 'collapse-icon fas fa-chevron-down'
              : 'collapse-icon fas fa-chevron-up'
          "
          @click="() => $emit('input', !value)"
        />
      </div>
    </div>

    <b-collapse v-model="value" class="mt-2">
      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.export.last_export_date') }} :
        </div>
        <div class="ml-1 value">
          {{
            exportData.lastExportDate
              ? exportData.lastExportDate
              : $t('common.not_available')
          }}
        </div>
      </div>

      <div class="row px-3">
        <div class="label">
          {{ $t('identification.table_item_popup.export.last_export_name') }} :
        </div>
        <div class="ml-1 value">
          {{
            exportData.fileExport
              ? exportData.fileExport
              : $t('common.not_available')
          }}
        </div>
      </div>

      <div class="row mt-1 px-3">
        <button
          class="btn btn-sm btn-light"
          @click="() => $emit('exportLeveeDetailsItem')"
        >
          {{
            $t('identification.table_item_popup.export.export_details_button')
          }}
        </button>
      </div>
    </b-collapse>
  </div>
</template>

<script>
import identificationLeveeDetailPopupSection2Icon from '@c/location/LocationIdentBacs/modal/assets/identification-levee-detail-popup-section-2-icon.svg'
export default {
  name: 'LocationIdentModalExportSection',
  props: {
    value: {
      type: Boolean,
      default: true,
    },
    exportData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      identificationLeveeDetailPopupSection2Icon,
    }
  },
}
</script>

<style scoped>
.title {
  font: normal normal bold 14px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-denim);
}
.label {
  font: normal normal bold 12px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.value {
  font: normal normal normal 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-metal-rock);
}
</style>

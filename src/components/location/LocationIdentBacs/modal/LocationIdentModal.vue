<template>
  <div
    v-if="isOpen"
    class="modal"
    tabindex="-1"
    role="dialog"
    style="overflow: auto; padding-bottom: 50px"
    :style="isOpen ? 'display: block;top: 20px;z-index: 1000000;' : ''"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <span>{{ $t('identification.table_item_popup.title') }}</span>
          <button
            type="button"
            class="modal__close_button"
            data-dismiss="modal"
            aria-label="Close"
            @click="closeModal"
          >
            <span aria-hidden="true" class="text-white">&times;</span>
          </button>
        </div>

        <div v-if="invalidData" class="modal-body">
          <p class="title">
            {{ $t('identification.table_item_popup.no_results') }}
          </p>
        </div>

        <div v-if="!invalidData" class="modal-body">
          <p class="title text-center">
            {{ 'Levée #' + data.id + ' ' + startDate }}
          </p>

          <location-ident-modal-behavior-section
            v-model="isBehaviorSectionUnfold"
            :behavior="data.behavior"
          />

          <location-ident-modal-prohibition-section
            v-if="data.prohibition && data.prohibition.versionDate"
            v-model="isProhibitionsSectionUnfold"
            class="mt-3"
            :prohibition="data.prohibition"
            :collection-datetime="data.startDate"
          />

          <location-ident-modal-override-section
            v-if="checkOverride(data.override)"
            v-model="isOverrideSectionUnfold"
            class="mt-3"
            :override="data.override"
          />

          <location-ident-modal-export-section
            v-model="isExportSectionUnfold"
            class="mt-3"
            :export-data="data.export"
            @exportLeveeDetailsItem="exportLeveeDetailsItem"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import LocationIdentModalBehaviorSection from './LocationIdentModalBehaviorSection.vue'
import LocationIdentModalProhibitionSection from './LocationIdentModalProhibitionSection.vue'
import LocationIdentModalExportSection from './LocationIdentModalExportSection.vue'
import LocationIdentModalOverrideSection from '@/components/location/LocationIdentBacs/modal/LocationIdentModalOverrideSection.vue'
import store from '@/store'
import { getNestedValue } from '@/utils/object'
import { exportToCSV } from '@/services/export-service.js'
export const LocationIdentModalIsolatedTestConfiguration = [
  'LocationIdentModal',
  () =>
    /* webpackChunkName: "main"    */
    import('@c/location/LocationIdentBacs/modal/LocationIdentModal.vue'),
  {
    props: {
      isOpen: true,
    },
    beforeEnter: (to, from, next) => {
      store.dispatch(
        'location_module/getIdentificationBacModaldetailsItem',
        139977814
      ) //CDC Talmondais / 04/04/21 / BOM1
      return !!next && next()
    },
  },
]

const exportColumns = {
  formattedDate: 'Date',
  formattedTime: 'Heure',
  vehicleName: 'Vehicule',
  circuitName: 'Circuit',
  puceNumber: 'Puce',
  weight: 'Poids',
  formattedAddress: 'Adresse',
  isIdentified: 'Identifie',
  isStopped: 'Stoppe',
  isHighPoint: 'Point haut',
  isBlacklisted: 'Blackliste',
  'behavior.blockedAccess': "Acces Blocage LC Liste d'interdiction",
  'behavior.driverChoice': "Choix chauffeur Liste d'interdiction",
  'prohibition.id': "ID Liste d'interdiction",
  'prohibition.versionDate': "Version liste d'interdiction",
  'prohibition.isBlacklisted': "Puce presente liste d'interdiction",
  'prohibition.filename': "Nom du fichier liste d'interdiction",
  'export.lastExportDate': 'Date du dernier export de la levee',
  'export.fileExport': "Nom du fichier du fichier d'export",
}

/**
 * @TODO: Refactor styles/skeleton into a shared component GenericModal (To apply the same styles to every bootstrap modal we have)
 * @namespace components
 * @category components
 * @subcategory location/identification
 * @module LocationIdentModal
 **/
export default {
  name: 'LocationIdentModal',
  components: {
    LocationIdentModalExportSection,
    LocationIdentModalProhibitionSection,
    LocationIdentModalBehaviorSection,
    LocationIdentModalOverrideSection,
  },
  props: {
    isOpen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isBehaviorSectionUnfold: true,
      isProhibitionsSectionUnfold: true,
      isExportSectionUnfold: true,
      isOverrideSectionUnfold: true,
    }
  },
  computed: {
    ...mapGetters({
      data: 'location_module/identificationBacModalData',
    }),
    startDate() {
      return this.$t('identification.table_item_popup.start_date', {
        date: this.$date.formatDate(
          this.$date.adjustDateWithTimezone(this.data.startDate)
        ),
        time: this.$date.formatTimeWithSeconds(
          this.$date.adjustDateWithTimezone(this.data.startDate)
        ),
      })
    },
    invalidData() {
      return Object.keys(this.data).length === 0
    },
    computedExportdetailsItem() {
      let self = this
      return Object.keys(exportColumns).reduce((result, columnName) => {
        result[columnName] = getNestedValue(
          this.data,
          columnName, //Nested path
          (this.data?.item || {})[columnName] || '', //Default value
          {
            transform(value) {
              if (typeof value === 'object') {
                return Object.keys(value)
                  .map((k) => value[k])
                  .join(',')
              }

              let parsedValue = value
              if (
                (columnName.indexOf('is') === 0 ||
                  columnName.includes('.is')) &&
                parsedValue !== 'n/c'
              ) {
                parsedValue = value ? true : false
                /*console.log('computedExportdetailsItem::boolean-transform', {
                  columnName,
                  value,
                  data: JSON.parse(JSON.stringify(self.data)),
                  firstV: getNestedValue(self.data, columnName),
                  defaultV: (self.data?.item || {})[columnName] || '',
                  parsedValue,
                })*/
              }

              if (typeof parsedValue === 'boolean') {
                return parsedValue
                  ? self.$t('export_feature.boolean_value_yes')
                  : self.$t('export_feature.boolean_value_no')
              }

              return parsedValue
            },
          }
        )
        return result
      }, {})
    },
  },
  methods: {
    /**
     * @returns {Boolean} Export successful
     */
    exportLeveeDetailsItem() {
      let detailsItem = this.computedExportdetailsItem
      return (
        typeof exportToCSV(
          [detailsItem],
          `${detailsItem.vehicleName}_${detailsItem.circuitName}_${detailsItem.formattedDate}_${detailsItem.formattedTime}`
            .split(' ')
            .join('-')
            .split('+')
            .join('')
            .split('--')
            .join('-'),
          {
            columns: exportColumns,
            delimiterCharacter: ';',
          }
        ) === 'string'
      )
    },
    closeModal() {
      this.$store.commit('location_module/SET_IDENTIFICATION_BAC_MODAL', false)
      this.resetCollapse()
    },
    resetCollapse() {
      this.isBehaviorSectionUnfold = true
      this.isProhibitionsSectionUnfold = true
      this.isExportSectionUnfold = true
      this.isOverrideSectionUnfold = true
    },
    checkOverride(override) {
      return override && override.isOverride
    },
  },
}
</script>

<style scoped>
.modal-header {
  background-color: var(--color-denim);
  padding: 5px;
  display: flex;
  font: normal normal 600 14px/18px Open Sans;
  color: white;
  font-weight: bold;
  text-align: center;
  justify-content: center;
}
.modal__close_button {
  background-color: transparent;
  position: absolute;
  color: white;
  right: 5px;
  top: 0px;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  text-shadow: 0 1px 0 #fff;
  border: 0;
}
.modal__close_button:focus {
  outline: 0;
}
.title {
  font: normal normal bold 14px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}

/*
@media (min-width: 1024px) {
  .modal-dialog {
    max-width: 992px;
  }
}*/

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 550px !important;
    margin: 1.75rem auto;
  }
}
</style>

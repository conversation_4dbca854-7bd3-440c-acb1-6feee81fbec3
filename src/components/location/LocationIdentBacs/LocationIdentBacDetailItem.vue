<template>
  <li class="tl-item ml-2 container" :class="selected" @click="click(item)">
    <div class="tl-item-icon">
      <MapMarkerCircleIcon
        v-b-tooltip.hover.right
        :fill-color="getIconColor(item)"
        :size="24"
        :title="
          !item.isStopped
            ? $t('location_module.identification_bacs.bac_collected')
            : $t('location_module.identification_bacs.bac_uncollected')
        "
      />
    </div>
    <div class="row pl-3">
      <div class="col-12">
        <div class="row">
          <div class="small-text font-weight-light mr-1">
            {{ item.formattedTime }}
          </div>
          <div class="ml-3">
            <SectionField
              is-row
              :title="$t('location_module.identification_bacs.list.puce')"
              title-class="text-bold"
              :value="item.puceNumber"
              value-class="pl-1"
            />
          </div>
        </div>

        <div class="row">
          <div class="col">
            <SectionField
              is-row
              :title="$t('common.Circuit')"
              title-class="text-bold"
              :value="item.circuitName"
              value-class="pl-1"
            />
          </div>
        </div>

        <div class="row">
          <div class="col-6">
            <SectionField
              is-row
              :title="
                $t('location_module.identification_bacs.table.chair_type')
              "
              title-class="text-bold"
              :value="item.chairType"
              value-class="pl-1"
            />
          </div>

          <div class="col-6">
            <SectionField
              is-row
              :title="$t('location_module.identification_bacs.table.weight')"
              title-class="text-bold"
              :value="item.weight"
              value-class="pl-1"
            />
          </div>
        </div>

        <div class="row">
          <div class="col-6">
            <SectionField
              is-row
              :title="
                $t('location_module.identification_bacs.table.is_identified')
              "
              title-class="text-bold"
              :has-value="false"
              has-icon
              :icon-class="
                !item.isIdentified ? 'fas fa-times ml-1' : 'fas fa-check ml-1'
              "
              :icon-style="!item.isIdentified ? 'color: red' : 'color: green'"
            />
          </div>
          <div class="col-6">
            <SectionField
              is-row
              :title="
                $t('location_module.identification_bacs.table.is_stopped')
              "
              title-class="text-bold"
              :has-value="false"
              has-icon
              :icon-class="
                !item.isStopped ? 'fas fa-times ml-1' : 'fas fa-check ml-1'
              "
              :icon-style="!item.isStopped ? 'color: red' : 'color: green'"
            />
          </div>
        </div>

        <div class="row">
          <div class="col-6">
            <SectionField
              is-row
              :title="
                $t('location_module.identification_bacs.table.is_highpoint')
              "
              title-class="text-bold"
              :has-value="typeof item.isHighPoint !== 'boolean'"
              :value="item.isHighPoint"
              :value-class="!item.isHighPoint ? 'pl-1' : ''"
              :has-icon="typeof item.isHighPoint === 'boolean'"
              :icon-class="
                item.isHighPoint ? 'fas fa-check ml-1' : 'fas fa-times ml-1'
              "
              :icon-style="item.isHighPoint ? 'color: green' : 'color: red'"
            />
          </div>
          <div class="col-6">
            <SectionField
              is-row
              :title="
                $t('location_module.identification_bacs.table.is_blacklisted')
              "
              title-class="text-bold"
              :has-value="false"
              has-icon
              :icon-class="
                !item.isBlacklisted ? 'fas fa-times ml-1' : 'fas fa-check ml-1'
              "
              :icon-style="!item.isBlacklisted ? 'color: red' : 'color: green'"
            />
          </div>
        </div>
      </div>
    </div>
  </li>
</template>

<script>
import SectionField from '@c/shared/SectionField.vue'
import MapMarkerCircleIcon from 'vue-material-design-icons/MapMarkerCircle.vue'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
import { mapGetters } from 'vuex'
export default {
  name: 'LocationIdentBacDetailItem',
  components: {
    SectionField,
    MapMarkerCircleIcon,
  },
  mixins: [removeNativeTooltipFromMaterialIconsMixin],
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    iconColor: {
      type: String,
      default: 'var(--color-dark-blue)',
    },
  },
  data() {
    return {
      selected: '',
    }
  },
  computed: {
    ...mapGetters({
      selectedItem: 'location_module/identificationBacSelected',
    }),
  },
  watch: {
    selectedItem() {
      this.selected = this.selectedItem === this.item.id ? 'selected' : ''
    },
  },
  methods: {
    isHighPointEmpty(value) {
      return value.length === 0
    },
    getIconColor(item) {
      return !item.isStopped ? this.iconColor : 'red'
    },
    click(item) {
      this.selected = 'selected'
      this.$emit('click', item)
    },
  },
}
</script>

<style lang="scss" scoped>
.tl-item {
  .fa-search {
    opacity: 0.5;
  }
}
.tl-item:hover {
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 10px;

  .fa-search {
    opacity: 1;
  }
}

.tl-item-icon {
  width: 20px;
  position: absolute;
  left: -11px;
  line-height: 15px;
  display: flex;
  justify-content: center;
  background-color: white;
  border-radius: 15px;
}

.selected {
  background-color: var(--color-active-item) !important;
}
</style>

<template>
  <div class="row">
    <div class="col-12">
      <SectionTitleWrapperLegacy
        :title="$t('identification.list_section.title')"
      >
        <template #icon>
          <FormatListBulletedIcon
            class="mr-1"
            :fill-color="iconColor"
            :size="24"
          />
        </template>
      </SectionTitleWrapperLegacy>
    </div>

    <div
      v-if="items.length === 0"
      class="row px-3 mx-0"
      style="margin-bottom: -1rem"
    >
      <div class="alert alert-info p-2">
        {{ $t('location_module.no_results.ident') }}
      </div>
    </div>

    <div v-if="items.length !== 0" class="col">
      <div class="col-12">
        <div class="history-tl-container">
          <ul class="tl">
            <div class="row">
              <location-ident-bac-detail-item
                v-for="(item, index) in items"
                :key="index"
                :item="item"
                :icon-color="iconColor"
                @click="click(item)"
              />
            </div>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import TableButton from '@c/shared/TableButton.vue'
import FormatListBulletedIcon from 'vue-material-design-icons/FormatListBulleted.vue'
import LocationIdentBacDetailItem from './LocationIdentBacDetailItem.vue'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
/**
 * List feature (details)
 * @namespace components
 * @category components
 * @subcategory location/identification
 * @module LocationIdentBacsDetails
 **/
export default {
  name: 'LocationIdentBacsDetails',
  components: {
    LocationIdentBacDetailItem,
    FormatListBulletedIcon,
  },
  mixins: [locationLayoutModesMixin, removeNativeTooltipFromMaterialIconsMixin],
  props: {
    items: {
      type: Array,
      default: () => [],
    },
    iconColor: {
      type: String,
      default: 'var(--color-dark-blue)',
    },
  },
  data() {
    return {
      isShowDetailIcon: false,
    }
  },
  methods: {
    click(detail) {
      this.$emit('on-item-click', detail)
    },
  },
}
</script>

<style lang="scss" scoped>
.blue {
  color: #164470;
}

.small-text {
  font-size: 0.75rem;
}

.history-tl-container {
  font-family: 'Roboto', sans-serif;
  width: 100%;
  margin: auto;
  display: block;
  position: relative;
}

.history-tl-container ul.tl {
  margin: 20px 0;
  padding: 0;
  display: inline-block;
}

.history-tl-container ul.tl li {
  list-style: none;
  margin: auto;
  min-height: 50px;
  border-left: 2px solid grey;
  padding: 0 0 20px 30px;
  position: relative;
}

.history-tl-container ul.tl li:last-child {
  border-left: 2px solid white;
}

.history-tl-container ul.tl li:hover::before {
  border-color: grey;
  transition: all 1000ms ease-in-out;
}

.chrono-title {
  width: 100%;
  display: flex;
}

.tl {
  width: 100%;
}

ul.tl li .item-detail {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
</style>

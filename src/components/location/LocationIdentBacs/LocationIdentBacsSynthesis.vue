<template>
  <div class="row">
    <div class="col-12">
      <SectionTitleWrapperLegacy
        class="align-items-center"
        :title="$t('identification.synthesis_section.title')"
      >
        <template #icon>
          <RoadVariantIcon class="mr-1" :fill-color="iconColor" :size="24" />
        </template>
      </SectionTitleWrapperLegacy>
    </div>

    <div
      v-if="synthesis.length === 0"
      class="row px-3 mx-0"
      style="margin-bottom: -1rem"
    >
      <div class="alert alert-info p-2">
        {{ $t('location_module.no_results.ident') }}
      </div>
    </div>

    <div v-if="synthesis.length > 0" class="col-12 pl-4">
      <div class="history-tl-container">
        <ul class="tl">
          <li v-for="(item, index) in synthesis" :key="index" class="tl-item">
            <div class="tl-item-icon">
              <img
                :src="
                  item.circuitName
                    ? './lib/realtimeMap/assets/picto_identification_bac/circuit_identified.svg'
                    : './lib/realtimeMap/assets/picto_identification_bac/circuit_no_identified.svg'
                "
                width="24px"
              />
            </div>

            <div class="item-wrapper">
              <div class="row">
                <div class="col-12">
                  <SectionField
                    is-row
                    :title="$t('common.Circuit') + ' :'"
                    title-class="text-bold"
                    :value="
                      item.circuitName
                        ? item.circuitName
                        : $t('identification.synthesis_section.no_circuit')
                    "
                    value-class="pl-1"
                  />
                </div>
              </div>
              <div class="row">
                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_levees_count'
                      ) + ' :'
                    "
                    :value="item.leveesTotal"
                    value-class="pl-1"
                  />
                </div>
                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_total_weight'
                      ) + ' :'
                    "
                    :value="item.leveesWeightTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_identified_levees_count'
                      ) + ' :'
                    "
                    :value="item.identifiedTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_not_identified_levees_count'
                      ) + ' :'
                    "
                    :value="item.unidentifiedTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_authorized_levees_count'
                      ) + ' :'
                    "
                    :value="item.authorizedTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_not_authorized_levees_count'
                      ) + ' :'
                    "
                    :value="item.unauthorizedTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_collected_levees_count'
                      ) + ' :'
                    "
                    :value="item.collectedTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_not_collected_levees_count'
                      ) + ' :'
                    "
                    :value="item.uncollectedTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_high_point_levees_count'
                      ) + ' :'
                    "
                    :value="item.highPointTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_non_high_point_levees_count'
                      ) + ' :'
                    "
                    :value="item.nonHighPointTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_stoped_levees_count'
                      ) + ' :'
                    "
                    :value="item.stoppedTotal"
                    value-class="pl-1"
                  />
                </div>

                <div class="col-6">
                  <SectionField
                    is-row
                    :title="
                      $t(
                        'identification.synthesis_section.label_blacklisted_count'
                      ) + ' :'
                    "
                    :value="item.blacklistedTotal"
                    value-class="pl-1"
                  />
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import RoadVariantIcon from 'vue-material-design-icons/RoadVariant.vue'
import SectionField from '@c/shared/SectionField.vue'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
/**
 * Synthesis list
 * @namespace components
 * @category components
 * @subcategory location/identification
 * @module LocationIdentBacsSynthesis
 **/
export default {
  name: 'LocationIdentBacsSynthesis',
  components: {
    RoadVariantIcon,
    SectionField,
  },
  mixins: [removeNativeTooltipFromMaterialIconsMixin],
  props: {
    synthesis: {
      type: Array,
      default: () => [],
    },
    iconColor: {
      type: String,
      default: 'var(--color-dark-blue)',
    },
  },
}
</script>

<style lang="scss" scoped>
.blue {
  color: #164470;
}

.small-text {
  font-size: 0.75rem;
}

.history-tl-container {
  font-family: 'Roboto', sans-serif;
  width: 100%;
  margin: auto;
  display: block;
  position: relative;
}

.history-tl-container ul.tl {
  margin: 20px 0;
  padding: 0;
  display: inline-block;
}

.history-tl-container ul.tl li {
  list-style: none;
  margin: auto;
  min-height: 50px;
  border-left: 2px solid grey;
  padding: 0 0 20px 30px;
  position: relative;
}

.history-tl-container ul.tl li:last-child {
  border-left: 2px solid white;
}

.history-tl-container ul.tl li:hover::before {
  border-color: grey;
  transition: all 1000ms ease-in-out;
}

.tl-item:hover {
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 10px;
}

.tl-item-icon {
  width: 20px;
  position: absolute;
  left: -11px;
  top: -5px;
  display: flex;
  justify-content: center;
  background-color: white;
}

.chrono-title {
  width: 100%;
  display: flex;
}

.tl {
  width: 100%;
}

ul.tl li .item-detail {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
</style>

<template>
  <MapPopup :title="$t('location.identification.popup.details_title')">
    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>{{ $t('common.map.popup.received_infos') }}</div>
      <i
        :class="
          !isLastInfosSectionCollapsed
            ? 'fas fa-chevron-down event-popup-chevron'
            : 'fas fa-chevron-up event-popup-chevron collapsed'
        "
        :aria-expanded="isLastInfosSectionCollapsed ? 'true' : 'false'"
        aria-controls="collapse-3"
        @click="isLastInfosSectionCollapsed = !isLastInfosSectionCollapsed"
      />
    </div>

    <b-collapse id="collapse-3" v-model="isLastInfosSectionCollapsed">
      <div class="col-12 pl-4">
        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('location.identification.popup.date') }}
            </strong>
          </div>
          <div class="row">
            {{ formatDate }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('location.identification.popup.position') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.formattedAddress
                ? geometry.properties.formattedAddress
                : 'n/c'
            }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('location.identification.popup.vehicle') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.vehicleName
                ? geometry.properties.vehicleName
                : ''
            }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('location.identification.popup.circuit') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.circuitName
                ? geometry.properties.circuitName
                : ''
            }}
          </div>
        </div>
      </div>
    </b-collapse>

    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>{{ $t('location.identification.popup.details_title') }}</div>
      <i
        :class="
          !isIdentificationBacsSectionCollapsed
            ? 'fas fa-chevron-down event-popup-chevron'
            : 'fas fa-chevron-up event-popup-chevron collapsed'
        "
        :aria-expanded="isIdentificationBacsSectionCollapsed ? 'true' : 'false'"
        aria-controls="collapse-3"
        @click="
          isIdentificationBacsSectionCollapsed =
            !isIdentificationBacsSectionCollapsed
        "
      />
    </div>

    <b-collapse
      id="collapse-3"
      v-model="isIdentificationBacsSectionCollapsed"
      class="pb-2"
    >
      <div class="col-12 pl-4">
        <div class="my-1">
          <div class="row align-items-center">
            <div class="col-6 px-0 text-left">
              <strong>
                {{ $t('location.identification.popup.details_founder_puce') }} :
              </strong>
            </div>
            <div class="col-6 px-0 text-left">
              {{
                geometry.properties.puceNumber
                  ? geometry.properties.puceNumber
                  : 'n/c'
              }}
            </div>
          </div>
        </div>

        <div class="my-1">
          <div class="row align-items-center">
            <div class="col-6 px-0 text-left">
              <strong>
                {{ $t('location.identification.popup.details_memory_puce') }} :
              </strong>
            </div>
            <div class="col-6 px-0 text-left">
              {{
                geometry.properties.memoryPuceNumber
                  ? geometry.properties.memoryPuceNumber
                  : 'n/c'
              }}
            </div>
          </div>
        </div>

        <div class="my-1">
          <div class="row align-items-center">
            <div class="col-6 px-0 text-left">
              <strong>
                {{ $t('location.identification.popup.details_weight') }} :
              </strong>
            </div>
            <div class="col-6 px-0 text-left">
              {{
                geometry.properties.weight ? geometry.properties.weight : 'n/c'
              }}
            </div>
          </div>
        </div>

        <div class="my-1">
          <div class="row align-items-center">
            <div class="col-6 px-0 text-left">
              <strong>
                {{ $t('location.identification.popup.details_is_identified') }}
                :
              </strong>
            </div>
            <div class="col-6 px-0 text-left">
              <i
                :class="
                  geometry.properties.isIdentified
                    ? 'ml-1 fas fa-check text-success'
                    : 'ml-1 fas fa-times text-danger'
                "
              />
            </div>
          </div>
        </div>

        <div class="my-1">
          <div class="row align-items-center">
            <div class="col-6 px-0 text-left">
              <strong>
                {{ $t('location.identification.popup.details_is_stopped') }} :
              </strong>
            </div>
            <div class="col-6 px-0 text-left">
              <i
                :class="
                  geometry.properties.isStopped
                    ? 'ml-1 fas fa-check text-success'
                    : 'ml-1 fas fa-times text-danger'
                "
              />
            </div>
          </div>
        </div>

        <div class="my-1">
          <div class="row align-items-center">
            <div class="col-6 px-0 text-left">
              <strong>
                {{ $t('location.identification.popup.details_is_high_point') }}
                :
              </strong>
            </div>
            <div class="col-6 px-0 text-left">
              <i
                v-if="typeof geometry.properties.isHighPoint === 'boolean'"
                class="ml-1 fas"
                :class="{
                  'text-success': geometry.properties.isHighPoint,
                  'fa-check': geometry.properties.isHighPoint,
                  'fa-times': !geometry.properties.isHighPoint,
                  'text-danger': !geometry.properties.isHighPoint,
                }"
              />
              <div v-if="typeof geometry.properties.isHighPoint !== 'boolean'">
                n/c
              </div>
            </div>
          </div>
        </div>

        <div class="my-1">
          <div class="row align-items-center">
            <div class="col-6 px-0 text-left">
              <strong>
                {{ $t('location.identification.popup.details_is_blacklisted') }}
                :
              </strong>
            </div>
            <div class="col-6 px-0 text-left">
              <i
                :class="
                  geometry.properties.isBlacklisted
                    ? 'ml-1 fas fa-check text-success'
                    : 'ml-1 fas fa-times text-danger'
                "
              />
            </div>
          </div>
        </div>
      </div>
    </b-collapse>
  </MapPopup>
</template>

<script>
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'
import switchablePopupsMixin from '@/mixins/switchable-popup-mixin.ts'
import geometryClickMixin from '@/components/shared/SimplicitiMap/geometry-click-mixin'
import { getEmptyStringIfStringIncludes } from '@/utils/string'
import { mapGetters } from 'vuex'

/**
 * Popup feature
 * @namespace components
 * @category components
 * @subcategory location/identification
 * @module LocationIdentBacsPopup
 **/
export default {
  name: 'LocationIdentBacsPopup',
  components: {
    MapPopup,
  },
  mixins: [switchablePopupsMixin, geometryClickMixin],
  props: {
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    let spmLabelPrefix = getEmptyStringIfStringIncludes(
      this.$t('switchable_popups.label.identification'),
      'switchable_popups'
    )
    return {
      isLastInfosSectionCollapsed: true,
      isIdentificationBacsSectionCollapsed: false,
      spmLayerGroupName: 'bacs_markers',
      spmLabelPath: 'properties.puceNumber',
      spmLabelPrefix,
    }
  },
  computed: {
    ...mapGetters({
      selectedItem: 'location_module/identificationBacSelected', // jump to selected row
    }),
    formatDate() {
      if (
        this.geometry.properties.formattedDate &&
        this.geometry.properties.formattedTime
      ) {
        return (
          this.geometry.properties.formattedDate +
          ' - ' +
          this.geometry.properties.formattedTime
        )
      }

      return ''
    },
  },
  mounted() {
    this.handleClickOnBinCollectionMarker(this.geometry.externalId)
  },
  destroyed() {
    this.handleClickOnBinCollectionMarker(null)
  },
}
</script>

<style scoped>
.blue {
  color: var(--color-denim);
}

.small-text {
  font-size: 0.75rem;
}

.event-popup-chevron {
  position: absolute;
  right: 10px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
</style>

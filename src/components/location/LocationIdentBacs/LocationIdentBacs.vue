<template>
  <div>
    <b-spinner
      v-if="isLoading"
      class="loader"
      variant="info"
      style="width: 3rem; height: 3rem; margin: 0 auto; display: block"
    />

    <div v-if="!isLoading" class="location-ident-bacs-card">
      <div class="row pl-3">
        <div class="col-6">
          <SectionField
            v-if="!isLoading"
            is-row
            :title="$t('common.Véhicule')"
            title-class="text-bold"
            :value="item.vehicleName"
            value-class="pl-1"
          />
        </div>
        <div class="col-6">
          <SectionField
            v-if="!isLoading"
            is-row
            :title="$t('common.date')"
            data-i18n-code="common.date"
            title-class="text-bold"
            :value="formattedDate(item.date)"
            value-class="pl-1"
          />
        </div>
      </div>

      <div v-if="!isLoading">
        <LocationIdentBacsSynthesis
          class="mt-3"
          :synthesis="identificationBacsSynthesis"
        />

        <LocationIdentBacsDetails
          class="mt-4"
          :items="identificationBacsDetails"
          @on-item-click="onItemClick"
        />
      </div>
    </div>
  </div>
</template>
<script>
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import mapMixin from '@/mixins/map.js'
import { mapGetters } from 'vuex'
import LocationIdentBacsSynthesis from './LocationIdentBacsSynthesis.vue'
import LocationIdentBacsDetails from './LocationIdentBacsDetails.vue'
import SectionField from '@c/shared/SectionField.vue'
import { featureFunctionIds, hasFeature } from '@/config/features'
import loggingService from '@/services/logging-service'

/**
 * List feature
 * @namespace components
 * @category components
 * @subcategory location/identification
 * @module LocationIdentBacs
 **/
export default {
  name: 'LocationIdentBacs',
  components: {
    LocationIdentBacsSynthesis,
    SectionField,
    LocationIdentBacsDetails,
  },
  mixins: [locationLayoutModesMixin, mapMixin],
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
    view: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      color: '#164470',
      isLoading: false,
      markersArray: [],
    }
  },
  computed: {
    ...mapGetters({
      identificationBacsSynthesis:
        'location_module/identificationBacsSynthesis',
      identificationBacsDetails: 'location_module/identificationBacsDetails',
    }),
    identificationBacsHaveData() {
      return (
        Array.isArray(this.identificationBacsDetails) &&
        this.identificationBacsDetails.length
      )
    },
  },
  watch: {
    item: {
      async handler() {
        this.$trackWatch(1)
        this.update()
      },
      deep: true,
    },
  },
  created() {
    this.update()
  },
  mounted() {
    if (hasFeature('enableAuditIdentification')) {
      loggingService.activityAuditManager.startActivityAudit(
        featureFunctionIds.identification
      )
    }
  },
  methods: {
    async update() {
      this.isLoading = true

      //Reset identification bacs synthesis & data
      await this.$store.dispatch(
        'location_module/resetIdentificationBacsSynthesis'
      )
      await this.$store.dispatch('location_module/resetIdentificationBacsData')

      /* 
      Deprecated: Disabled given it triggers a bug (watcher infinite loop)
      // Fetch chart trip history for newly selected vehicle to display its polylines
       await this.$store.dispatch(
         'location_module/getChartTripHistoryFromVehicleId',
         {
           id: this.item.id,
           date: this.item.date,
         }
       ) */

      //Then fetch newly selected item data
      await this.$store.dispatch(
        'location_module/fetchIdentificationBacsData',
        {
          vehicleId: this.item.id,
          date: this.item.date,
        }
      )

      this.isLoading = false

      //Switch to table + map or map only mode
      this.switchToModeDependingIfData(
        this.identificationBacsHaveData,
        'LocationIdentBacsTable',
        'LocationIdentBacsMap'
      )
    },
    async onItemClick(value) {
      await this.fitBoundsAndOpenPopup(
        { lat: value.lat, lng: value.lng },
        'bacs_markers',
        value.id
      )
      await this.$store.dispatch(
        'location_module/setIdentificationBacSelected',
        value.id
      )
    },
    formattedDate(date) {
      return this.$date.formatDate(date)
    },
  },
}
</script>

<style lang="scss" scoped>
.location-ident-bacs-card {
  background: white;
  border-radius: 10px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.075);
}
</style>

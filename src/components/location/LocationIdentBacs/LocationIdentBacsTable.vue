<template>
  <div ref="root" class="vehicle_alerts_table">
    <DataTable
      v-if="table"
      ref="datatable"
      class="table_theme_1"
      :ssr-paging="false"
      :ssr-show-multi-column-filter="false"
      :searching="true"
      :paging="true"
      name="locationIdentification"
      row-id="id"
      scroll-y="250px"
      :columns="columns"
      :column-defs="columnDefs"
      :language="$translations.datatable"
      :extra-options="extraOptions || {}"
      :select="select"
      :default-sorting-column="2"
      :default-sorting-column-direction="'desc'"
      :auto-height="true"
      :auto-height-offset="mode === 'table' ? 0 : 0"
      @select="onSelect"
      @deselect="onDeselect"
    />
  </div>
</template>

<script>
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import { mapGetters } from 'vuex'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import { createSortableColumnDefinition } from '@/utils/datatable.js'
import mapMixin from '@/mixins/map.js'
import {
  getChairSideOptions,
  getHighpointOptions,
} from '@/composables/useIdentificationModule'

/**
 * Table feature
 * @namespace components
 * @category components
 * @subcategory location/identification
 * @module LocationIdentBacsTable
 **/
export default {
  name: 'LocationIdentBacsTable',
  components: {
    DataTable,
  },
  mixins: [datatableMixin, locationLayoutModesMixin, mapMixin],
  props: {
    mode: {
      type: String,
      default: 'table+map',
      enum: ['table', 'table+map'],
    },
  },
  data() {
    var self = this
    return {
      isModalOpen: false,
      showInformationsPopup: false,
      table: false,
      select: {
        items: 'row',
        style: 'single',
        info: false,
      },
      columns: this.buildColumnsArray(),
      columnDefs: this.buildColumnDefs(),
      extraOptions: this.buildExtraOptions(),
    }
  },
  computed: {
    ...mapGetters({
      results: 'location_module/identificationBacsDetails',
      selectedItem: 'location_module/identificationBacSelected', // jump to selected row
    }),
  },
  watch: {
    results() {
      this.$trackWatch(1)
      this.updateTable()
    },
    // jump to selected row
    selectedItem() {
      this.selectRowUsingAttribute(parseInt(this.selectedItem), 'id', false)
    },
  },
  mounted() {
    console.debugVerboseScope(
      ['location_module', 'identification_feature', 'identification_table'],
      5,
      'LocationIdentBacsTable.vue::mounted'
    )
    $.fn.dataTable.moment(this.$date.getDatetimePattern())
    this.updateTable()

    this.$store.dispatch('app/changeLayout', {
      origin: 'TripHistoryTable.vue::mounted',
      currentLayoutName: 'IDENTIFICATION_TABLE',
      menu_full_collapse: true,
      menu_collapsed: true,
    })

    setTimeout(() => {
      this.table = true
      setTimeout(() => {
        if (this.$refs.datatable && this.$refs.datatable.datatable) {
          this.$refs.datatable.datatable.draw()
        }
      }, 500)
    }, 200)
  },
  destroyed() {
    if (this.$store.state.search_module.view !== 'selection') {
      this.$store.dispatch('app/changeLayout', {
        origin: 'LocationIdentBacsTable.vue::destroyed',
        currentLayoutName: 'IDENTIFICATION_LIST',
        menu_full_collapse: false,
        sub_menu: true,
      })
    }
    this.$store.commit('location_module/SET_IDENTIFICATION_BAC_MODAL', false)
  },
  methods: {
    onDeselect() {
      /*this.$mitt.emit(
        'SIMPLICITI_MAP_FIT_TO_BOUNDS',
        this.results.map((item) => [item.lat, item.lng])
      )*/
    },
    async onSelect({ item }) {
      await this.fitBoundsAndOpenPopup(
        { lat: item.lat, lng: item.lng },
        'bacs_markers',
        item.id
      )

      this.$store.commit(
        'location_module/setIdentificationBacSelected',
        item.id
      )
    },
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'locationIdentification',
        items: this.results,
      })
    },
    showBacModal(value, item) {
      this.$store
        .dispatch('location_module/getIdentificationBacModalDetails', item)
        .then(() => {
          this.$store.commit(
            'location_module/SET_IDENTIFICATION_BAC_MODAL',
            value
          )
        })
        .catch((error) => {
          console.log(error)
        })
    },
    /**
     * Returns table columns
     */
    buildColumnsArray() {
      return [
        {
          data: 'bacIcon',
          title: '',
        },
        {
          data: 'eyeIcon',
          title: '',
        },
        createSortableColumnDefinition(
          `timestamp`,
          `formattedDate`,
          this.$t('common.date')
        ),
        createSortableColumnDefinition(
          `timeUnix`,
          `formattedTime`,
          this.$t('common.hour')
        ),
        createSortableColumnDefinition(
          `vehicleName`,
          `vehicleName`,
          this.$t('common.Véhicule')
        ),
        createSortableColumnDefinition(
          `circuitName`,
          `circuitName`,
          this.$t('common.Circuit')
        ),
        createSortableColumnDefinition(
          `puceNumber`,
          `puceNumber`,
          this.$t('location_module.identification_bacs.table.puce')
        ),
        createSortableColumnDefinition(
          `memoryPuceNumber`,
          `memoryPuceNumber`,
          this.$t('location_module.identification_bacs.table.memory_puce')
        ),
        createSortableColumnDefinition(
          `weight`,
          `weight`,
          this.$t('location_module.identification_bacs.table.weight')
        ),
        createSortableColumnDefinition(
          `formattedAddress`,
          `formattedAddress`,
          this.$t('common.Adresse')
        ),
        createSortableColumnDefinition(
          `chairType`,
          `chairType`,
          this.$t('location_module.identification_bacs.table.chair_type')
        ),
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_identified'
          ),
          orderable: true,
          sort: 'isIdentified',
          render: createComponentRender(
            {
              name: 'BacIsIdentified',
              template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
              methods: {
                setIcon(item) {
                  return item.isIdentified ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isIdentified ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return row.isIdentified ? 'true' : 'false'
              },
            }
          ),
        },
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_stopped'
          ),
          orderable: true,
          sort: 'isStopped',
          render: createComponentRender(
            {
              name: 'BacIsStopped',
              template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
              methods: {
                setIcon(item) {
                  return item.isStopped ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isStopped ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return row.isStopped ? 'true' : 'false'
              },
            }
          ),
        },
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_highpoint'
          ),
          orderable: true,
          sort: 'isHighPoint',
          render: createComponentRender(
            {
              name: 'BacIsHighPoint',
              template: `<div>
            <em v-if="typeof this.row.isHighPoint==='boolean'" :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>
            <span v-if="typeof this.row.isHighPoint!=='boolean'">{{this.row.isHighPoint}}</span>
            </div>`,
              methods: {
                setIcon(item) {
                  return item.isHighPoint ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isHighPoint ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return typeof row.isHighPoint === 'boolean'
                  ? row.isHighPoint
                    ? 'true'
                    : 'false'
                  : row.isHighPoint
              },
            }
          ),
        },
        {
          title: this.$t(
            'location_module.identification_bacs.table.is_blacklisted'
          ),
          orderable: true,
          sort: 'isBlacklisted',
          render: createComponentRender(
            {
              name: 'BacIsBlacklisted',
              template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
              methods: {
                setIcon(item) {
                  return item.isBlacklisted ? 'fas fa-check' : 'fas fa-times'
                },
                getIconColor(item) {
                  return item.isBlacklisted ? 'color: green;' : 'color: red;'
                },
              },
            },
            {
              filterBy: (row) => {
                return row.isBlacklisted ? 'true' : 'false'
              },
            }
          ),
        },
      ]
    },
    /**
     * Returns table column defs
     */
    buildColumnDefs() {
      let self = this

      return [
        {
          targets: 0,
          orderable: false,
          render: createComponentRender({
            name: 'bacIcon',
            template: `<div style="display: flex;justify-content: flex-start;align-items: center;column-gap: 3px;">
                            <svg
                              :fill="!this.row.isStopped ? '#014470' : 'red'"
                              class="material-design-icon__svg"
                              v-b-tooltip.hover.right
                              :title="!this.row.isStopped ? $t('location_module.identification_bacs.bac_collected') : $t('location_module.identification_bacs.bac_uncollected')"
                              :width="20"
                              :height="20"
                              viewBox="0 0 24 24">
                                <path class="icon-svg" d="M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,12.5A1.5,1.5 0 0,1 10.5,11A1.5,1.5 0 0,1 12,9.5A1.5,1.5 0 0,1 13.5,11A1.5,1.5 0 0,1 12,12.5M12,7.2C9.9,7.2 8.2,8.9 8.2,11C8.2,14 12,17.5 12,17.5C12,17.5 15.8,14 15.8,11C15.8,8.9 14.1,7.2 12,7.2Z">
                                </path>
                            </svg>
                            <div v-show="this.row.hasInvalidCoords"
                              v-b-tooltip.hover.viewport
                              :title="$t('common.invalid_position')"
                            >
                              <em  style="color: var(--color-sandy-brown);" class="fa fa-exclamation-triangle"></em>
                            </div>
                      </div>`,
          }),
        },
        {
          targets: 1,
          orderable: false,
          render: createComponentRender({
            name: 'eyeIcon',
            template: `<em @click.self="showBacInformationModal" class="fas fa-eye" style="cursor:pointer;"></em>`,
            methods: {
              showBacInformationModal(e) {
                e.stopPropagation()
                self.showBacModal(true, this.row)
              },
            },
          }),
        },
        {
          targets: 10,
          orderable: true,
          sort: 'chairType',
          render: function (data) {
            return data ? data : '-'
          },
        },
        {
          targets: 11,
          orderable: true,
          sort: 'isIdentified',
          render: createComponentRender({
            name: 'BacIsIdentified',
            template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
            methods: {
              setIcon(item) {
                return item.isIdentified ? 'fas fa-check' : 'fas fa-times'
              },
              getIconColor(item) {
                return item.isIdentified ? 'color: green;' : 'color: red;'
              },
            },
          }),
        },
        {
          targets: 12,
          orderable: true,
          sort: 'isStopped',
          render: createComponentRender({
            name: 'BacIsStopped',
            template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
            methods: {
              setIcon(item) {
                return item.isStopped ? 'fas fa-check' : 'fas fa-times'
              },
              getIconColor(item) {
                return item.isStopped ? 'color: green;' : 'color: red;'
              },
            },
          }),
        },
        {
          targets: 13,
          title: this.$t(
            'location_module.identification_bacs.table.is_highpoint'
          ),
          orderable: true,
          sort: 'isHighPoint',
          render: createComponentRender({
            name: 'BacIsHighPoint',
            template: `<div>
          <em v-if="typeof this.row.isHighPoint==='boolean'" :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>
          <span v-if="typeof this.row.isHighPoint!=='boolean'">{{this.row.isHighPoint}}</span>
        </div>`,
            methods: {
              setIcon(item) {
                return item.isHighPoint ? 'fas fa-check' : 'fas fa-times'
              },
              getIconColor(item) {
                return item.isHighPoint ? 'color: green;' : 'color: red;'
              },
            },
          }),
        },
        {
          targets: 14,
          orderable: true,
          sort: 'isBlacklisted',
          render: createComponentRender({
            name: 'BacIsBlacklisted',
            template: `<em :class="setIcon(this.row)" :style="getIconColor(this.row)"></em>`,
            methods: {
              setIcon(item) {
                return item.isBlacklisted ? 'fas fa-check' : 'fas fa-times'
              },
              getIconColor(item) {
                return item.isBlacklisted ? 'color: green;' : 'color: red;'
              },
            },
          }),
        },
        {
          targets: 6,
          width: '500px',
        },
      ]
    },
    /**
     * Returns columns extra options (e.g. text filters)
     */
    buildExtraOptions() {
      return {
        ...this.configureColumnsFilters((filters) => {
          return [
            null, // bacIcon
            null, // eyeIcon
            filters.createTextFilter({ column: 2 }), // date
            filters.createTextFilter({ column: 3 }), // hour
            filters.createTextFilter({ column: 4 }), // vehicleName
            filters.createTextFilter({ column: 5 }), // circuitName
            filters.createTextFilter({ column: 6 }), // puceNumber
            filters.createTextFilter({ column: 7 }), // memoryPuceNumber
            filters.createTextFilter({ column: 8 }), // weight
            filters.createTextFilter({ column: 9 }), // formattedAddress
            filters.createSelectFilter({
              column: 10, // chairType
              selectOptions: getChairSideOptions(),
            }),
            filters.createSelectFilter({
              column: 11, // isIdentified
              isBoolean: true,
            }),
            filters.createSelectFilter({
              column: 12, // isStopped
              isBoolean: true,
            }),
            filters.createSelectFilter({
              column: 13, // isHighPoint
              selectOptions: getHighpointOptions(),
            }),
            filters.createSelectFilter({
              column: 14, // isBlacklisted
              isBoolean: true,
            }),
          ]
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}

.title,
.title span,
.title strong {
  font-size: 12px;
}

.vehicle_alerts_table {
  height: 100%;
  height: -moz-available;
  /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available;
  /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}

.table_toolbar {
  display: flex;
  align-items: center;
}
</style>

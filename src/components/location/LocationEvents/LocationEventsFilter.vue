<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  value: {
    type: Object,
    default: () => ({
      anomaly: false,
      eventExploitation: false,
      status: false,
    }),
  },
})

const emit = defineEmits(['input'])

const internalValue = ref(props.value)

watch(
  internalValue,
  (currentValue) => {
    emit('input', currentValue)
  },
  { deep: true }
)

let visible = ref(false)

const toggle = () => {
  visible.value = !visible.value
}

const onClickOutside = (event) => {
  visible.value = false
}
</script>

<template>
  <div class="event-filter">
    <div v-click-outside="onClickOutside" class="event-filter-wrapper">
      <div class="event-filter-title" @click="toggle">
        {{ title }}
      </div>
      <div class="event-filter-action" @click="toggle">
        <b-icon-chevron-down
          :rotate="visible ? '180' : '0'"
        ></b-icon-chevron-down>
      </div>
      <div
        class="event-filter-items"
        :class="{ 'event-filter-menu-visible': visible }"
      >
        <div class="event-filter-item">
          <input v-model="internalValue.anomaly" type="checkbox" />
          <span @click="internalValue.anomaly = !internalValue.anomaly">{{
            $t('events.search_filters.anomaly_title')
          }}</span>
        </div>
        <div class="event-filter-item">
          <input v-model="internalValue.eventExploitation" type="checkbox" />
          <span
            @click="
              internalValue.eventExploitation = !internalValue.eventExploitation
            "
            >{{ $t('events.search_filters.operation_message_title') }}</span
          >
        </div>
        <div class="event-filter-item">
          <input v-model="internalValue.status" type="checkbox" />
          <span @click="internalValue.status = !internalValue.status">{{
            $t('events.search_filters.status_title')
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.event-filter-wrapper {
  position: relative;
  display: flex;
  cursor: pointer;
  .event-filter-title {
    font-size: 13px;
    font-weight: bold;
    color: var(--color-dark-blue);
    flex-grow: 1;
  }
  .event-filter-action {
    font-weight: bold;
    color: var(--color-dark-blue);
    width: 24px;
    cursor: pointer;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
.event-filter-item {
  padding: 0px 2px 1px;
  color: var(--color-dark-blue);
  font-size: 13px;
  white-space: nowrap;
  cursor: pointer;
  display: flex;
  column-gap: 10px;
}
.event-filter-items {
  position: absolute;
  top: 20px;
  display: none;
  background-color: white;
  border: solid 1px var(--color-dark-blue);
  padding: 3px;
  z-index: 1;
  box-shadow: 1px 1px 13px rgb(0 0 0 / 43%);
}
.event-filter-menu-visible {
  display: block;
}
</style>

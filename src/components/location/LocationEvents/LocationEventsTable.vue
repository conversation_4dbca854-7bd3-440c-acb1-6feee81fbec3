<template>
  <div ref="root" class="vehicle_events_table">
    <PicturesGallery
      v-show="pictures.length > 0"
      :pictures="pictures"
      @close="closeGallery()"
    />
    <DataTable
      v-if="table"
      ref="datatable"
      class="table_theme_1"
      :ssr-paging="false"
      :ssr-show-multi-column-filter="false"
      :searching="true"
      :paging="true"
      name="locationEvents"
      row-id="id"
      scroll-y="250px"
      :columns="columns"
      :column-defs="columnDefs"
      :language="$translations.datatable"
      :extra-options="extraOptions || {}"
      :select="select"
      :default-sorting-column="1"
      :default-sorting-column-direction="'desc'"
      :auto-height="true"
      :auto-height-offset="mode === 'table' ? 0 : 0"
      @select="onSelect"
      @deselect="onDeselect"
    />
  </div>
</template>
<script>
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import { mapGetters } from 'vuex'
import moment from 'moment'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import mapMixin from '@/mixins/map.js'
import PicturesGallery from '@/components/shared/PicturesGallery/PicturesGallery.vue'
import { createSortableColumnDefinition } from '@/utils/datatable.js'

/**
 * @todo Refactor/Extract
 */
function sortableColumn(sortBy, display, title) {
  return {
    data: {
      _: sortBy,
      filter: display, //Filter using what user see
      display,
      sort: sortBy,
    },
    title,
  }
}

/**
 * Table feature
 * @namespace components
 * @category components
 * @subcategory location/events
 * @module LocationEventsTable
 **/
export default {
  name: 'LocationEventsTable',
  components: {
    DataTable,
    PicturesGallery,
  },
  mixins: [datatableMixin, locationLayoutModesMixin, mapMixin],
  provide() {
    return {
      datatablePdfExport: true,
      pdfExportFilenamePrefix: 'export_details_evet_',
    }
  },
  props: {
    mode: {
      type: String,
      default: 'table+map',
      enum: ['table', 'table+map'],
    },
    showRoundColumns: {
      type: Boolean,
      default: false,
    },
    showBinColumns: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    var self = this

    return {
      pictures: [],
      table: false,
      select: {
        items: 'row',
        style: 'single',
        info: false,
      },
      columns: this.buildColumnsArray(),
      columnDefs: this.buildColummDefs(),
      extraOptions: this.buildExtraOptions(),
    }
  },
  computed: {
    ...mapGetters({
      results: 'location_module/getEvents',
      selectedItem: 'location_module/selectedItem',
    }),
    isAggregateEventGroupBinEnabled() {
      let result = this.$store.getters['settings/getParameter'](
        'isAggregateEventGroupBinEnabled'
      )

      return result
    },
    isAggregateEventGroupRoundEnabled() {
      let result = this.$store.getters['settings/getParameter'](
        'isAggregateEventGroupRoundEnabled'
      )

      return result
    },
  },
  watch: {
    isAggregateEventGroupBinEnabled() {
      this.$trackWatch(1)
      this.forceTableReload()
    },
    isAggregateEventGroupRoundEnabled() {
      this.$trackWatch(1)
      this.forceTableReload()
    },
    results() {
      this.$trackWatch(1)
      this.updateTable()
    },
  },
  mounted() {
    $.fn.dataTable.moment(this.$date.getDatetimePattern())
    this.updateTable()

    this.$store.dispatch('app/changeLayout', {
      origin: 'LocationEventsTable.vue::mounted',
      currentLayoutName: 'LOCATION_EVENTS_TABLE',
      menu_full_collapse: true,
    })

    setTimeout(() => (this.table = true), 500)
  },
  destroyed() {
    if (this.$store.state.search_module.view !== 'selection') {
      this.$store.dispatch('app/changeLayout', {
        origin: 'LocationEventsTable.vue::destroyed',
        currentLayoutName: 'LOCATION_EVENTS_DETAILS',
        menu_collapsed: this.$store.getters['app/layout'].isMenuCollapsed,
        menu_full_collapse: false,
        sub_menu: true,
      })
    }
  },
  methods: {
    onDeselect() {},
    async onSelect({ e, item }) {
      e.stopPropagation()

      await this.fitBoundsAndOpenPopup(
        { lat: item.lat, lng: item.lng },
        'vehicle_events',
        item.id
      )
    },
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'locationEvents',
        items: this.results,
      })
    },
    forceTableReload() {
      if (this.table) {
        this.table = false
        this.columns = this.buildColumnsArray()
        this.extraOptions = this.buildExtraOptions()
        setInterval(() => {
          this.table = true
        }, 250)
      }
    },
    buildColumnsArray() {
      let columns = [
        {
          data: 'galleryIcon',
          title: '',
        },
        sortableColumn(
          `timestamp`,
          `formattedDate`,
          this.$t('location_module.events.date')
        ),
        sortableColumn(
          `timestamp`,
          `formattedTime`,
          this.$t('location_module.events.time')
        ),
        sortableColumn(
          `vehicleName`,
          `vehicleName`,
          this.$t('location_module.events.vehicle')
        ),
        sortableColumn(
          `label`,
          `label`,
          this.$t('location_module.events.name')
        ),
        sortableColumn(
          `address`,
          `address`,
          this.$t('location_module.events.address')
        ),
        sortableColumn(`city`, `city`, this.$t('location_module.events.city')),
        sortableColumn(
          `comment`,
          `comment`,
          this.$t('location_module.events.comment')
        ),
      ]

      if (
        this.$store.getters['settings/getParameter'](
          'isAggregateEventGroupRoundEnabled'
        )
      ) {
        columns.push(
          createSortableColumnDefinition(
            `circuitName`,
            `circuitName`,
            this.$t('events.table_column.circuit_name')
          )
        )
      }

      if (
        this.$store.getters['settings/getParameter'](
          'isAggregateEventGroupBinEnabled'
        )
      ) {
        columns.push(
          createSortableColumnDefinition(
            `puceNumber`,
            `puceNumber`,
            this.$t('events.table_column.puce_number')
          )
        )

        columns.push(
          createSortableColumnDefinition(
            `binCollectionSideLabel`,
            `binCollectionSideLabel`,
            this.$t('events.table_column.side')
          )
        )
      }

      columns.push(
        createSortableColumnDefinition(
          `isWorkDone`,
          `isWorkDone`,
          this.$t('events.events_table.work_done_label')
        )
      )

      return columns
    },
    /**
     * Returns table column defs
     */
    buildColummDefs() {
      let that = this
      return [
        {
          targets: 0,
          orderable: false,
          render: createComponentRender({
            name: 'PhotoActionButton',
            template: `<em @click="openPhotoGallery" class="fas fa-camera" :style="iconStyle"></em>`,
            computed: {
              iconStyle() {
                return this.row.photos !== undefined &&
                  this.row.photos.length > 0
                  ? `cursor: pointer;`
                  : `color: rgb(82 77 77 / 53%); cursor: not-allowed;`
              },
            },
            methods: {
              openPhotoGallery(e) {
                e.stopPropagation()
                if (this.row.photos.length > 0) {
                  that.toogleMenuDisplay(false)
                  that.pictures = this.row.photos
                }
              },
            },
          }),
        },
      ]
    },
    buildExtraOptions() {
      return {
        ...this.configureColumnsFilters((filters) => {
          return [
            null,
            filters.createTextFilter({ column: 1 }),
            filters.createTextFilter({ column: 2 }),
            filters.createTextFilter({ column: 3 }),
            filters.createTextFilter({ column: 4 }),
            filters.createTextFilter({ column: 5 }),
            filters.createTextFilter({ column: 6 }),
            filters.createTextFilter({ column: 7 }),
            filters.createTextFilter({ column: 8 }),
            filters.createTextFilter({ column: 9 }),
            filters.createTextFilter({ column: 10 }),
            filters.createSelectFilter({
              column: 11,
              selectOptions: [
                {
                  text: this.$t('common.yes'),
                  value: this.$t('common.yes'),
                },
                {
                  text: this.$t('common.no'),
                  value: this.$t('common.no'),
                },
              ],
            }),
          ]
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      }
    },
    /**
     * https://easyredmine.simpliciti.fr/issues/44724
     */
    closeGallery() {
      this.pictures = []
      this.toogleMenuDisplay(true)
    },
    /**
     * Pour rendre le bouton toogle_menu invisible quand on ouvre la galerie
     * https://easyredmine.simpliciti.fr/issues/44724
     * @param display
     * @returns {boolean}
     */
    toogleMenuDisplay(display = true) {
      if (display) {
        $('.toggle_menu').show()
      } else {
        $('.toggle_menu').hide()
      }
      return true
    },
  },
}
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}

.title,
.title span,
.title strong {
  font-size: 12px;
}

.vehicle_events_table {
  height: 100%;
  height: -moz-available;
  /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available;
  /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}

.table_toolbar {
  display: flex;
  align-items: center;
}

.fas.fa-camera {
  color: #8c8c8c;
  cursor: not-allowed;
}

.fas.fa-camera.active {
  cursor: pointer;
}
</style>

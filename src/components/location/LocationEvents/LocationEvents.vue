<template lang="pug">
.location_events
  .container
    .row(v-if="loading && events.length > 0")
      .col-12
        b-spinner(
          class="loader"
          variant="info"
          style="width: 3rem; height: 3rem; margin: 0 auto; display:block;"
        )
    .row(v-if="!loading && events.length > 0")
      .col-9.px-0
        LocationEventsFilter(:title="$t('location_module.event_filter.menu_title')" v-model="eventsFilter")
  .container(v-if="!loading")
    .row(v-for="event in events" :key="event.evenement_id" style="cursor: pointer;font-size: 1rem;" @click="handleItemClick(event)").mt-2.mb-2
      .col-12.px-0
        .card
          .card-body
            b-row.justify-content-around
              b-col(cols="2" align-self="center")
                CalendarAlertIcon(:color="event.color" :size="28")
              b-col(cols="10" align-self="center")
                b-row
                  span.text-sm-left {{ $date.formatDatetime($date.adjustDateWithTimezone(event.date)) }}
                b-row
                  span.font-weight-bold.mb-0(:style="style") {{ event.label }}
  .row.px-3(v-if="!loading && events.length === 0")
    .alert.alert-info.p-2 {{ $t('location_module.no_results.events') }}
</template>
<script>
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import mapMixin from '@/mixins/map.js'
import moment from 'moment'
import TableButton from '@c/shared/TableButton.vue'
import MapIcon from '@c/shared/TableModesToolbar/MapIcon.vue'
import { mapGetters } from 'vuex'
import CalendarAlertIcon from '@c/shared/SearchResults/Submenu/assets/CalendarAlertIcon.vue'
import LocationEventsFilter from './LocationEventsFilter.vue'
import { featureFunctionIds, hasFeature } from '@/config/features'
import loggingService from '@/services/logging-service'

/**
 * List feature
 * @namespace components
 * @category components
 * @subcategory location/events
 * @module LocationEvents
 **/
export default {
  name: 'VehicleEventsTab',
  components: {
    TableButton,
    MapIcon,
    CalendarAlertIcon,
    LocationEventsFilter,
  },
  mixins: [locationLayoutModesMixin, mapMixin],
  props: {
    vehicle: {
      type: Object,
      default: () => ({}),
    },
    view: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      eventsFilter: {
        anomaly: true,
        eventExploitation: false,
        status: false,
      },
    }
  },
  computed: {
    ...mapGetters({
      item: 'location_module/selectedItem',
      events: 'location_module/getEvents',
    }),
    color() {
      return '#ED2C2C'
    },
    style() {
      return `color: ${this.color};font-size: .9em;`
    },
    eventsHaveData() {
      return Array.isArray(this.events) && this.events.length
    },
  },
  watch: {
    eventsFilter: {
      async handler(value) {
        this.$trackWatch(1)
        this.update()
      },
      deep: true,
    },
    item: {
      async handler() {
        this.$trackWatch(1)
        this.update()
      },
      deep: true,
    },
  },
  created() {
    this.update()
  },
  mounted() {
    if (hasFeature('enableAuditEvents')) {
      loggingService.activityAuditManager.startActivityAudit(
        featureFunctionIds.events
      )
    }
  },
  methods: {
    async update() {
      this.loading = true
      await this.$store.dispatch('location_module/getVehicleEvents', {
        vehicleId: this.item.vehicleId,
        datetimeFrom: this.item.date,
        filters: this.eventsFilter,
      })
      this.loading = false

      //Switch to table + map or map only mode
      this.switchToModeDependingIfData(
        this.eventsHaveData,
        'LocationEventsTable',
        'LocationEventsMap'
      )
    },
    async handleItemClick({ id, lat, lng } = eventItem) {
      await this.fitBoundsAndOpenPopup(
        { lat: lat, lng: lng },
        'vehicle_events',
        id
      )
    },
  },
}
</script>
<style lang="scss" scoped>
span {
  color: #727272;
  font-size: 0.8em;
}
.card-title {
  border-bottom: none;
  font-weight: 600;
}
</style>

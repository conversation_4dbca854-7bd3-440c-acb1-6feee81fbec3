<template>
  <div>
    <b-spinner
      v-if="loading"
      class="loader"
      variant="info"
      style="width: 3rem; height: 3rem; margin: 0 auto; display: block"
    />
    <CircuitExecDetails
      v-if="hasSingleCircuitExecution"
      :item="item"
      @loadGPSPositions="loadGPSPositions"
      @loadTripHistory="loadTripHistory"
    />
    <div v-if="!hasSingleCircuitExecution">
      <CollapsibleSection
        v-for="executionItem in executionItems"
        :key="executionItem.id"
        v-model="executionItem.isUnfolded"
        :title="executionItem.title"
        class="mt-2"
        @input="(v) => onCollapsibleSectionToggle(executionItem, v)"
      >
        <CircuitExecDetails
          :key="executionItem.id"
          :circuit-details-from-a-p-i="executionItem.circuitDetailsFromAPI"
          :item="item"
          :circuit-execution-steps-from-a-p-i="
            executionItem.circuitExecutionStepsFromAPI
          "
          @loadGPSPositions="() => loadGPSPositions(executionItem)"
          @loadTripHistory="loadTripHistory"
        />
      </CollapsibleSection>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import CircuitExecDetails from '@c/location/LocationCircuitTab/CircuitExecDetails/CircuitExecDetails.vue'
import {
  getCircuitExecutionDetails,
  getCircuitExecutionInfos,
} from '@/services/circuit-service.js'
import CollapsibleSection from '@c/shared/SimplicitiMap/CollapsibleSection.vue'
import { linestringsToPolylines } from '@/mixins/map'
import { ref } from 'vue'
import { awaitSeconds } from '@/utils/promise'
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import useLocationCircuitContainers from '@/composables/useLocationCircuitContainers'
import loggingService from '@/services/logging-service'
import { featureFunctionIds, hasFeature } from '@/config/features'
import { adjustDateWithTimezone, getConsecutiveDays } from '@/utils/dates'
import moment from 'moment/moment'

const isLoadingGPSPositions = ref(false)
const isLoadingTripHistory = ref(false)

export default {
  name: 'LocationCircuitTab',
  components: {
    CircuitExecDetails,
    CollapsibleSection,
  },
  mixins: [locationLayoutModesMixin],
  provide() {
    return {
      isLoadingGPSPositions,
    }
  },
  inject: {
    showGPSPositionMarkersWhenAvailable: {
      default: () => ({}),
    },
  },
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  setup() {
    useLocationCircuitContainers()
    return {}
  },

  data() {
    return {
      loading: false,
      executionItems: [],
      isLoadingGPSPositions,
    }
  },
  computed: {
    /**
     * Use case: Location - Real-time/History
     */
    hasSingleCircuitExecution() {
      return this.item.circuitExecutions.length < 2
    },
    ...mapGetters({
      circuitDetailsFromStore: 'location_module/circuitDetails',
      circuitExecutionStepsFromStore: 'location_module/circuitExecutionSteps',
    }),
    circuitDetails() {
      return this.circuitDetailsFromAPI || this.circuitDetailsFromStore
    },
    circuitExecutionSteps() {
      return (
        this.circuitExecutionStepsFromAPI || this.circuitExecutionStepsFromStore
      )
    },
    circuitExecutionId() {
      return this.item.circuitExecutionId
    },
    /**
     * Returns the vehicle ID, falling back to item.id if vehicleId is not available
     * @returns {string} The vehicle ID
     */
    vehicleId() {
      return this.item.vehicleId || this.item.id
    },
    //Get start and end dates from item. Fields are different depending on if we're in circuit on vehicule search
    //So we need to handle both cases
    itemStartDate() {
      let date = ''

      if (this.item.circuitDatetimeFrom) {
        date = moment(
          adjustDateWithTimezone(this.item.circuitDatetimeFrom)
        ).format('YYYY-MM-DD HH:mm:ss')
      } else if (this.item.circuitExecutions?.length > 0) {
        date = moment(
          adjustDateWithTimezone(this.item.circuitExecutions[0].startDate)
        ).format('YYYY-MM-DD HH:mm:ss')
      }

      return date
    },
    itemEndDate() {
      let date = ''

      if (this.item.circuitDatetimeTo) {
        date = moment(
          adjustDateWithTimezone(this.item.circuitDatetimeTo)
        ).format('YYYY-MM-DD HH:mm:ss')
      } else if (this.item.circuitExecutions?.length > 0) {
        date = moment(
          adjustDateWithTimezone(this.item.circuitExecutions[0].endDate)
        ).format('YYYY-MM-DD HH:mm:ss')
      }

      return date
    },
    itemSectionsEndDate() {
      let date = ''

      if (this.item.circuitDatetimeTo) {
        date = moment(
          adjustDateWithTimezone(this.item.circuitDatetimeTo)
        ).format('YYYY-MM-DD HH:mm:ss')
      } else if (this.item.circuitExecutions?.length > 0) {
        date = moment(
          adjustDateWithTimezone(
            this.item.circuitExecutions[this.item.circuitExecutions.length - 1]
              .endDate
          )
        ).format('YYYY-MM-DD HH:mm:ss')
      }

      return date
    },
    unfoldedItem() {
      return this.executionItems.find((item) => item.isUnfolded === true)
    },
  },
  watch: {
    item: {
      handler() {
        this.$trackWatch(1)
        this.updateCircuitData()
      },
      deep: true,
    },
    circuitExecutionId: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          //Getting chart trip history allows to get historyStartDatetimeStamp and historyEndDatetimeStamp
          if (this.hasSingleCircuitExecution) {
            this.getChartTripHistoryFromVehicleId()
          } else {
            this.getChartTripHistoryFromVehicleId(this.unfoldedItem)
          }
        }
      },
    },
    unfoldedItem: {
      handler(newVal, oldVal) {
        if (newVal && newVal.id !== oldVal?.id) {
          // If a new item is unfolded, load its GPS positions and trip history
          this.loadGPSPositions(newVal)
          this.loadTripHistory(newVal)
        }
      },
    },
  },
  created() {
    this.updateCircuitData()
  },
  mounted() {
    // Auto switch to table+carto (default view)
    this.switchToTablePlusMapMode({
      bottom: 'CircuitExecTable',
      top: 'CircuitExecMap',
    })

    //Getting chart trip history allows to get historyStartDatetimeStamp and historyEndDatetimeStamp
    if (this.hasSingleCircuitExecution) {
      this.getChartTripHistoryFromVehicleId()
    } else {
      this.getChartTripHistoryFromVehicleId(this.unfoldedItem)
    }

    this.$store.dispatch('containersModuleStore/updateReferential')

    if (hasFeature('enableAuditLocationCircuitTab')) {
      loggingService.activityAuditManager.startActivityAudit(
        featureFunctionIds.locationCircuitTab
      )
    }
  },
  destroyed() {
    this.$store.state.location_module.hideTripHistoryPolylinesIfCircuitTab = true
  },
  methods: {
    /**
     * Shows a loader and returns a function to hide it
     * @returns {Function} Function to hide the loader
     */
    showLoader() {
      return (this.$loader && this.$loader.showAlternative()) || (() => ({}))
    },
    async updateCircuitData() {
      console.debug('LocationCircuitTab.vue::updateCircuitData')
      await this.updateStateWithMultipleCircuitExecutionUsingAPI()
      ;(async () => {
        try {
          // Automatically load positions
          if (this.hasSingleCircuitExecution) {
            await this.loadGPSPositions()
          } else {
            //Get gps positions of unfolded item only
            await this.loadGPSPositions(this.unfoldedItem)
          }
        } catch (err) {
          console.warn('Fail to load positions')
          throw err
        } finally {
          try {
            // Automatically trip history
            if (this.hasSingleCircuitExecution) {
              await this.loadTripHistory()
            } else {
              await this.loadTripHistory(this.unfoldedItem)
            }
          } catch (err) {
            console.warn('Fail to load trip history')
          }
        }
      })()
    },
    async loadTripHistory(executionItem = null) {
      console.debugVerboseScope(
        ['location_module', 'circuit_feature'],
        5,
        'LocationCircuitTab.vue::loadTripHistory'
      )

      const { startDate, endDate } =
        this.getCircuitExecutionStartAndEndDate(executionItem)

      this.$store.state.location_module.hideTripHistoryPolylinesIfCircuitTab = false
      const hideLoader = this.showLoader()

      isLoadingTripHistory.value = true
      await Promise.all([
        this.$store.dispatch('location_module/getTripHistoryFromVehicleDate', {
          id: this.item.id,
          date: this.item.date,
          startDate,
          endDate,
        }),
      ])
      hideLoader()
      isLoadingTripHistory.value = false
    },
    loadGPSPositions(executionItem = null) {
      const { startDate, endDate } =
        this.getCircuitExecutionStartAndEndDate(executionItem)

      this.showGPSPositionMarkersWhenAvailable.value = true
      isLoadingGPSPositions.value = true
      const hideLoader = this.showLoader()
      return this.$store
        .dispatch('location_module/updateMapPositionMarkers', {
          vehicleId: this.vehicleId,
          date: this.item.date,
          startDate,
          endDate,
        })
        .finally(() => {
          isLoadingGPSPositions.value = false
          hideLoader()
        })
    },
    /**
     * Used if selected item has multiple circuit executions only
     */
    async updateStateWithMultipleCircuitExecutionUsingAPI() {
      if (!this.hasSingleCircuitExecution) {
        this.loading = true
        //Reset execution items first
        this.executionItems = []
        //Then get circuit executions from API
        this.executionItems = await Promise.all(
          this.item.circuitExecutions.map(async (circuitExecution, index) => {
            //Get circuit start and end dates
            let circuitExecutionStartDate = circuitExecution.startDate
            let circuitExecutionEndDate = circuitExecution.endDate

            //Get a map of circuit dates
            const dateObjects = getConsecutiveDays(
              circuitExecutionStartDate,
              circuitExecutionEndDate
            )
            const formattedDates = dateObjects.map((date) =>
              moment(date).format('YYYY-MM-DD')
            )

            //Get circuit infos from start to end, even if end happens on a different day
            let response = await getCircuitExecutionInfos({
              circuitId: circuitExecution.circuitId || '',
              circuitExecutionId: circuitExecution.executionId,
              dates: formattedDates,
              getDetailsFn: getCircuitExecutionDetails,
            })

            response = {
              id: circuitExecution.executionId,
              title: `${circuitExecution.circuitName} (${this.formatDate(
                circuitExecution.startDate
              )} - ${this.formatEndDate(circuitExecution.endDate)})`,
              circuitDetailsFromAPI: response,
              circuitExecutionStepsFromAPI: { troncons: response.troncons },
              isUnfolded: index === 0,
            }
            return response
          })
        )

        this.onCollapsibleSectionToggle(this.executionItems[0], true) // Trigger map rendering

        this.loading = false
      }
    },
    formatDate(date) {
      return this.$date.formatDatetime(date)
    },
    formatEndDate(date) {
      const dateStr = this.formatDate(date)
      if (dateStr.includes('23:59')) {
        return '...'
      }
      return dateStr
    },
    onCollapsibleSectionToggle(executionItem, isUnfolded) {
      // Fold others
      this.executionItems.forEach((item) => {
        if (item.id != executionItem.id) {
          item.isUnfolded = false
        }
      })

      if (isUnfolded) {
        let polylines = linestringsToPolylines(
          executionItem.circuitDetailsFromAPI.troncons.map((t) => {
            t.number = t.id
            t.color = t.realise ? t.activite_couleur || '#70bd95' : '#ff4545'
            return t
          }),
          {
            type: 'circuit_execution',
          }
        )

        this.$store.dispatch('simpliciti_map/setDataset', {
          type: 'singleCircuitExecPolylines',
          data: polylines,
        })

        // Reset circuitExecutionDetails
        this.$store.commit('location_module/resetCircuitExecutionDetails')

        // Update circuitExecutionDetails with unfolded circuit
        this.$store.commit(
          'location_module/setCircuitExecutionDetails',
          executionItem.circuitDetailsFromAPI
        )

        try {
          // Try to scroll to top smoothly
          document
            .querySelector('.submenu_content')
            .scrollTo({ top: 0, behavior: 'smooth' })
        } catch (err) {
          console.warn(err)
        }
      } else {
        this.$store.dispatch('simpliciti_map/setDataset', {
          type: 'singleCircuitExecPolylines',
          data: [],
        })
        this.$store.commit('location_module/resetCircuitExecutionDetails')
      }
    },
    getChartTripHistoryFromVehicleId(executionItem = null) {
      const { startDate, endDate } =
        this.getCircuitExecutionStartAndEndDate(executionItem)

      this.$store.dispatch('location_module/getChartTripHistoryFromVehicleId', {
        id: this.vehicleId,
        date: this.item.date,
        startDate,
        endDate,
      })
    },
    getCircuitExecutionStartAndEndDate(executionItem = null) {
      const startDate = executionItem
        ? executionItem.circuitDetailsFromAPI?.dateFrom
        : this.itemStartDate

      const endDate = executionItem
        ? executionItem.circuitDetailsFromAPI?.dateTo
        : this.itemEndDate

      return { startDate, endDate }
    },
  },
}
</script>

<template lang="pug">
.trip_history_table(ref="root")
  .table_toolbar(
    v-show="!!circuitExecDate && !!hasCircuitExecToTime && !!hasCircuitExecToTime"
  )
    p.title 
      span {{circuitName}} 
      span(v-if="hasCircuitExecToTime") du 
      span(v-if="!hasCircuitExecToTime") le 
      strong {{circuitExecDate}} 
      span(v-if="hasCircuitExecToTime") de 
      span(v-if="!hasCircuitExecToTime") à 
      strong {{circuitExecFromTime}} 
      span(v-if="hasCircuitExecToTime") à 
      strong(v-if="hasCircuitExecToTime") {{circuitExecToTime}}
    CircuitExecutionStepsFilter(v-model="stepsFilter")
  DataTable(
    v-if="shouldRenderDatatable"
    class="table_theme_1"
    ref="datatable",
    :ssrPaging="false",
    :ssrShowMultiColumnFilter="false",
    :searching="true",
    :paging="true",
    name="locationDetailsCircuit",
    rowId="id",
    scrollY="250px",
    :columns="columns",
    :columnDefs="columnDefs",
    :language="$translations.datatable",
    :extraOptions="extraOptions || {}",
    :select="select",
    :defaultSortingColumn="0"
    @select="onSelect"
    @deselect="onDeselect"
    :autoHeight="true"
    :autoHeightOffset="0"
  )
</template>
<script>
import { mapGetters } from 'vuex'
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
//import { mapGetters } from 'vuex'
import moment from 'moment'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import { formatSeconds, updateTimeIfBeforeOrAfter } from '@/utils/dates.js'
import CircuitExecutionStepsFilter from '@c/shared/CircuitExecutionStepsFilter.vue'
import { createCircuitExecutionStepsFilter } from '@c/location/mixins/circuit-execution-mixin.js'
import store from '@/store'
import searchService from '@/services/search-service'

/**
 * @todo Refactor/Extract
 */
function sortableColumn(sortBy, display, title) {
  return {
    data: {
      _: sortBy,
      filter: display, //Filter using what user see
      display,
      sort: sortBy,
    },
    title,
  }
}

/**
 *
 * @namespace components
 * @category components
 * @subcategory location/circuit
 * @module CircuitExecTable
 **/
export default {
  name: 'CircuitExecTable',
  components: {
    DataTable,
    CircuitExecutionStepsFilter,
  },
  mixins: [datatableMixin],
  data() {
    var self = this
    return {
      shouldRenderDatatable: false,
      select: {
        items: 'row',
        style: 'single',
        info: false,
      },
      columns: this.buildColumnsArray(),
      columnDefs: this.buildColummDefs(),
      extraOptions: this.buildExtraOptions(),
      hasDisabledScrollAndPagnation: false,
    }
  },
  computed: {
    stepsFilter: {
      get() {
        return this.$store.state.location_module.stepsFilter
      },
      set(val) {
        this.$store.state.location_module.stepsFilter = val
      },
    },
    selectedStepNumber() {
      return this.$store.state.location_module.selectedStepNumber
    },
    ...mapGetters({
      circuitDetailsFromStore: 'location_module/circuitDetails',
      circuitExecutionStepsFromStore: 'location_module/circuitExecutionSteps',
    }),
    circuitDetails() {
      return this.circuitDetailsFromAPI || this.circuitDetailsFromStore
    },
    circuitExecutionSteps() {
      return (
        this.circuitExecutionStepsFromAPI || this.circuitExecutionStepsFromStore
      )
    },
    circuitName() {
      return this.circuitDetails.circuit_nom_court
    },
    circuitExecDate() {
      return this.$date.formatDate(this.circuitDetails.dh_exec_circuit_debut)
    },
    circuitExecFromTime() {
      return this.$date.formatTimeWithSeconds(
        this.circuitDetails.dh_exec_circuit_debut
      )
    },
    circuitExecToTime() {
      let circuitExecTo = moment(
        this.circuitDetails.dh_exec_circuit_fin
      ).format('YYYY-MM-DD')
      let circuitExecToTime = searchService.getRangeTimeSelected(
        circuitExecTo,
        'HH:mm'
      ).end

      return this.$date.formatTimeWithSeconds(
        updateTimeIfBeforeOrAfter(
          this.circuitDetails.dh_exec_circuit_fin,
          circuitExecToTime,
          'before'
        )
      )
    },
    hasCircuitExecToTime() {
      return moment(this.circuitDetails.dh_exec_circuit_fin).isValid()
    },
    /**
     * @it Should filter items
     * @it Should normalize items
     */
    tableItems() {
      return (this.circuitExecutionSteps.troncons || [])
        .filter(createCircuitExecutionStepsFilter(this.stepsFilter))
        .map((t) => {
          return {
            id: t.id,
            address: t.adresse || '',
            action: t.activite_nom,
            distance: t.distance_theorique * 1000,
            fromDate: this.$date.formatDatetime(t.dateheure_debut, {
              fallbackValue: '...',
            }),
            fromDateMilli: moment(t.dateheure_debut).isValid()
              ? moment(t.dateheure_debut)._d.getTime()
              : -1,
            durationSeconds: t.duree_sec,
            durationSecondsFormatted: formatSeconds(t.duree_sec) || '',
            done: t.realise,
            number: parseInt(t.number),
          }
        })
    },
    //Get table height
    tableHeight() {
      return store.getters['app/layout'].rightMenuBottomCurrentHeight
    },
  },
  watch: {
    selectedStepNumber() {
      this.$trackWatch(1)
      if (this.selectedStepNumberDisabled) {
        return
      }
      // console.log('List item selected', this.selectedStepNumber)
      this.disableSelectListeners = true
      this.selectRowUsingAttribute(
        parseInt(this.selectedStepNumber),
        'number',
        this.hasDisabledScrollAndPagnation
      )
      this.$nextTick(() => {
        setTimeout(() => {
          this.disableSelectListeners = false
        }, 500)
      })
    },
    circuitExecutionSteps() {
      this.$trackWatch(1)
      this.updateTable()
    },
    stepsFilter() {
      this.$trackWatch(1)
      this.updateTable()
    },
    //TODO Refactor if same logic is need in other location tabs
    tableHeight(newVal, oldVal) {
      this.$trackWatch(1)

      this.hasDisabledScrollAndPagnation = this.handleTableHeightWatcher(
        newVal,
        'number',
        parseInt(this.selectedStepNumber),
        this.hasDisabledScrollAndPagnation
      )
    },
  },
  mounted() {
    /**
     * Disables watch while syncting select state to list
     * @property
     */
    this.selectedStepNumberDisabled = false

    /**
     * Disable select listeners while syncting select state from list
     * @property
     */
    this.disableSelectListeners = false

    $.fn.dataTable && $.fn.dataTable.moment(this.$date.getDatetimePattern())
    this.updateTable()

    this.$store.dispatch('app/changeLayout', {
      origin: 'CircuitExecTable.vue::mounted',
      currentLayoutName: 'CIRCUIT_TABLE',
    })

    setTimeout(() => (this.shouldRenderDatatable = true), 500)
  },
  destroyed() {
    this.$store.dispatch(
      'simpliciti_map/highlightCircuitExecPolylineSection',
      null
    )
    if (this.$store.state.search_module.view !== 'selection') {
      this.$store.dispatch('app/changeLayout', {
        origin: 'CircuitExecTable.vue::destroyed',
        currentLayoutName: 'CIRCUIT_DETAILS',
        sub_menu: true,
      })
    }
  },
  methods: {
    onDeselect() {
      if (this.disableSelectListeners) {
        return
      }
      // console.log('table onDeselect')
      this.$store.dispatch(
        'simpliciti_map/highlightCircuitExecPolylineSection',
        null
      )
      this.updateSelectedStepNumber(null)
    },
    onSelect({ item }) {
      if (this.disableSelectListeners) {
        return
      }

      // console.log('table onSelect', item.number)
      this.$store.dispatch(
        'simpliciti_map/highlightCircuitExecPolylineSection',
        item.id
      )

      //Fly to polyline
      this.$mitt.emit('SIMPLICITI_MAP__FLY_TO_POLYLINE', {
        stepNumber: item.id, // trocons.id equals stepNumber
      })

      this.updateSelectedStepNumber(item.id)
    },
    /**
     * @param {string|null} [stepNumber]
     */
    updateSelectedStepNumber(stepNumber = null) {
      this.selectedStepNumberDisabled = true
      this.$store.state.location_module.selectedStepNumber = stepNumber
      setTimeout(() => {
        this.selectedStepNumberDisabled = false
      }, 500)
    },
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'locationDetailsCircuit',
        items: this.tableItems,
      })
    },
    /**
     * Returns table columns
     */
    buildColumnsArray() {
      return [
        {
          data: 'id',
          title: 'N°',
        },
        {
          data: 'address',
          title: this.$t('location.details.circuit_tab.table.from_address'),
        },
        {
          data: 'action',
          title: this.$t('location.details.circuit_tab.table.action'),
        },
        {
          data: 'distance',
          title: this.$t('location.details.circuit_tab.table.distance'),
        },
        sortableColumn(
          'fromDateMilli',
          'fromDate',
          this.$t('location.details.circuit_tab.table.from_datetime')
        ),
        sortableColumn(
          'durationSeconds',
          'durationSecondsFormatted',
          this.$t('common.duree')
        ),
        {
          data: 'done',
          title: this.$t('common.Réalisé'),
        },
      ]
    },
    /**
     * Returns table column defs
     */
    buildColummDefs() {
      return [
        {
          targets: 7,
          orderable: false,
          render: createComponentRender({
            name: 'doneIcon',
            template: `
            <div>
                <em v-if="row.done" class="fa fa-check" style="color:var(--color-silver-tree)"></em>
                <em v-if="!row.done" class="fa fa-times" style="color:var(--color-coral-red)"></em>
            </div>
            `,
          }),
        },
      ]
    },
    /**
     * Returns columns extra options (e.g. text filters)
     */
    buildExtraOptions() {
      return {
        ...this.configureColumnsFilters((filters) => {
          let columnFilters = []
          this.columns.forEach((def, index) => {
            columnFilters.push(filters.createTextFilter({ column: index }))
          })

          return columnFilters
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.title,
.title span,
.title strong {
  font-size: 12px;
}
.trip_history_table {
  height: 100%;
  height: -moz-available; /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available; /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}
.table_toolbar {
  display: flex;
  align-items: center;
}
</style>

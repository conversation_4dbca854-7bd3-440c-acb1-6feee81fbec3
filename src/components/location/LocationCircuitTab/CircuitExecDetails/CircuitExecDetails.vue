<template lang="pug">
.last_circuit_tab
    b-spinner(
        v-if="loading"
        class="loader"
        variant="info"
        style="width: 3rem; height: 3rem; margin: 0 auto; display:block;"
    )
    div(v-if="!loading && (!headInfos || !stepsInfos)")
      .row
        .col-12
          .alert.alert-info.p-2 {{$t('location_module.no_results.circuit')}}

    .last_circuit_tab__infos(v-if="!loading && !!headInfos&&!!stepsInfos")

      .head_infos(v-if="['circuit','driver'].includes(item.type)" style="justify-content: flex-start;")
        .info_item
            label.info_item_label Vehicle:
            .info_item_value {{item.vehicleName}}
        .info_item
            //@TODO: If no active circuit, print the circuit name here

      .head_infos(v-if="!!headInfos && headInfos.sameDay")
          .info_item(v-show="isSearchByVehicleOrDriver && headInfos.circuitName")
            label.info_item_label {{$t('common.Circuit')}}:
            .info_item_value {{headInfos.circuitName}}
          .info_item()
              label.info_item_label {{$t('common.date')}}:
              .info_item_value {{headInfos.dateFrom}}
          .info_item
              label.info_item_label {{$t('common.debut')}}:
              .info_item_value {{headInfos.timeFrom}}
          .info_item(v-show="!hasActiveCircuit")
              label.info_item_label {{$t('common.fin')}}:
              .info_item_value {{headInfos.timeTo}}
      .head_infos(v-if="!!headInfos && !headInfos.sameDay")
          .info_item
              label.info_item_label {{$t('common.debut')}}:
              .info_item_value {{headInfos.dateFrom}} {{headInfos.timeFrom}}
          .info_item(v-show="!hasActiveCircuit")
              label.info_item_label {{$t('common.fin')}}:
              .info_item_value {{headInfos.dateTo}} {{headInfos.timeTo}}
      .head_infos(v-if="!!headInfos")
          .info_item
              label.info_item_label {{$t('common.distance')}}:
              .info_item_value {{isNaN(parseInt(headInfos.distance)) ? headInfos.distance : parseInt(headInfos.distance).toFixed(2)}}Km
          .info_item
              label.info_item_label {{$t('common.Réalisation')}}:
              .info_item_value {{headInfos.donePercentage}}%
          .info_item
      .row
        .col

      .row
        .col.steps_filter_wrapper()
          CircuitExecutionStepsFilter(v-model="stepsFilter")
        .col
          .toolbar(v-if="!!stepsInfos")

            LocationExportPendulumButton(v-show="$store.getters['location_module/selectedItem'].circuitExecutionId"
            fontSize="18px"
            )

            div(v-b-tooltip.hover :title="$t('location.circuit.button_load_gps_positions')")

              //Button to load positions (Deprecated)
              //b-btn.table_mode_btn.mini-btn(variant="outline-primary"
                @click="()=>$emit('loadGPSPositions')"
                :disabled="isLoadingGPSPositions"
                )
                //em.fa.fa-map-pin(style="margin-right:0px!important;")
                simple-svg(
                    width="15px"
                    height="15px"
                    fill="var(--color-dark-blue)" fillClassName="cls-1"
                    :src="circuitExecGpsPositionsIcon"
                )

            //Load trip history button (deprecated)
            //div(v-b-tooltip.hover :title="$t('location.circuit.button_load_trip_history')")
              b-btn.table_mode_btn.mini-btn(variant="outline-primary"
                @click="(e)=>$emit('loadTripHistory') || e.target.blur()"
                :disabled="isLoadingTripHistory"
              )
                simple-svg(
                    width="15px"
                    height="15px"
                    fill="var(--color-dark-blue)" fillClassName="cls-1"
                    :src="viewTripHistoryIcon"
                )

      VerticalChart(v-if="!!stepsInfos" :nodes="stepsInfos"
        @nodeclick="onItemClick"
        :activeNodeId="activeNodeId"
      )
          template(v-slot:node_icon="slotProps")
              NodeIcon(:color="slotProps.node.color")
          template(v-slot:node_content="slotProps")
              span(v-if="slotProps.node.date") {{slotProps.node.date}}

              .row.m-0
                .col-6.p-0
                  .label {{$t('location.details.circuit_tab.label_step_number')}} {{slotProps.node.id}}
                .col-6.p-0
                  .label {{$t('location.details.circuit_tab.label_distance')}}:
                    span.value {{(slotProps.node.distance_theorique*1000).toFixed(2)}}m
              .row.m-0
                .col-12.p-0
                  .label {{$t('location.details.circuit_tab.label_action')}}:
                    span.value {{slotProps.node.activite_nom}}
              .row.m-0
                .col-6.p-0()
                  .label {{$t('location.details.circuit_tab.label_duration')}}:
                    span.value {{slotProps.node.duration}}
                .col-6.p-0
                  .label {{$t('location.details.circuit_tab.label_done_perc')}}:
                    em.fa.fa-check(v-show="slotProps.node.realise" style="color:var(--color-silver-tree)")
                    em.fa.fa-times(v-show="!slotProps.node.realise" style="color:var(--color-coral-red)")


</template>
<script>
import Vue from 'vue'
import moment from 'moment'
import VerticalChart from '@c/shared/SearchResults/Submenu/VerticalChart.vue'
import { mapGetters } from 'vuex'
import NodeIcon from '@c/shared/SearchResults/Submenu/CircuitExecutionStepIcon.vue'
import TableButton from '@c/shared/TableButton.vue'
import MapIcon from '@c/shared/TableModesToolbar/MapIcon.vue'
import { formatSeconds } from '@/utils/dates.js'
import CircuitExecutionStepsFilter from '@c/shared/CircuitExecutionStepsFilter.vue'
import { createCircuitExecutionStepsFilter } from '@c/location/mixins/circuit-execution-mixin.js'
import { APIV2RequestDatetimeFormat } from '@/config/simpliciti-apis.js'
import LocationExportPendulumButton from '@c/location/LocationExportPendulumButton.vue'
import { compareObjects } from '@/utils/object'
import circuitExecGpsPositionsIcon from '@c/location/LocationCircuitTab/CircuitExecDetails/assets/view-circ-exec-gps-positions.svg'
import viewTripHistoryIcon from '@c/location/LocationCircuitTab/CircuitExecDetails/assets/view-trip-history.svg'

function getFormattedTimeFromApiDate(date) {
  let m = moment(date, APIV2RequestDatetimeFormat)
  return (m.isValid() && Vue.$date.formatTimeWithSeconds(m)) || '...'
}

/**
 * Used by
 * Main search - history by circuit - circuit tab
 * Main search - realtime by [all] - circuit tab (last circuit)
 * @namespace components
 * @category components
 * @subcategory location/circuit
 * @module CircuitExecDetails
 **/
export default {
  name: 'CircuitExecDetails',
  components: {
    CircuitExecutionStepsFilter,
    VerticalChart,
    NodeIcon,
    TableButton,
    MapIcon,
    LocationExportPendulumButton,
  },
  inject: {
    isLoadingGPSPositions: {
      default: false,
    },
    isLoadingTripHistory: {
      default: false,
    },
  },
  props: {
    /**
     * selected item (From real-time/history search)
     */
    item: {
      type: Object,
      default: () => ({}),
    },
    /**
     * Used by: Location - History - Circuit tab - (Multiple circuit executions for the same vehicle)
     */
    circuitDetailsFromAPI: {
      type: Object,
      default: () => null,
    },
    /**
     * Used by: Location - History - Circuit tab - (Multiple circuit executions for the same vehicle)
     */
    circuitExecutionStepsFromAPI: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      loading: false,
      activeNodeId: null,
      circuitExecGpsPositionsIcon,
      viewTripHistoryIcon,
    }
  },
  computed: {
    ...mapGetters({
      circuitDetailsFromStore: 'location_module/circuitDetails',
      circuitExecutionStepsFromStore: 'location_module/circuitExecutionSteps',
    }),
    stepsFilter: {
      get() {
        return this.$store.state.location_module.stepsFilter
      },
      set(val) {
        this.$store.state.location_module.stepsFilter = val
      },
    },
    circuitDetails() {
      return this.circuitDetailsFromAPI || this.circuitDetailsFromStore
    },
    circuitExecutionSteps() {
      return (
        this.circuitExecutionStepsFromAPI || this.circuitExecutionStepsFromStore
      )
    },
    headInfos() {
      const item = this.circuitDetails

      const isCircuitCompletionRateIncludingIgnoredCircuitSteps =
        this.$rights.hasRight(
          'GEOV3_LOCALISATION_HISTO_VIEW_IGNORED_ROUTE_SECTIO'
        )
      const circuitCompletionRateField =
        isCircuitCompletionRateIncludingIgnoredCircuitSteps
          ? 'ignored_taux_realisation'
          : 'taux_realisation_circuit'

      return (
        (item &&
          !!item.circuit_id &&
          Object.keys(item).length > 0 && {
            circuitName: item.circuit_nom_court || '',
            dateFrom: this.$date.formatDate(item.dateFrom),
            timeFrom: getFormattedTimeFromApiDate(item.dateFrom),
            dateTo: this.$date.formatDate(item.dateTo),
            timeTo: getFormattedTimeFromApiDate(item.dateTo),
            sameDay: moment(item.dateFrom).isSame(moment(item.dateTo), 'day'),
            distance: item.lg_realisee_circuit || '...',
            donePercentage: item[circuitCompletionRateField] || '...',
          }) ||
        null
      )
    },
    isSearchByVehicleOrDriver() {
      return ['vehicle', 'driver'].includes(this.item.type)
    },
    unfilteredStepsInfos() {
      return (this.circuitExecutionSteps.troncons || []).map((t) =>
        Object.freeze({
          ...t,
          date: this.$date.formatTime(t.dateheure_debut, {
            fallbackValue: '...',
          }),
          duration: formatSeconds(t.duree_sec),
          color: t.realise ? t.activite_couleur || '#70BD95' : '#FF4545',
        })
      )
    },
    stepsInfos() {
      return (
        (this.unfilteredStepsInfos || []).filter(
          createCircuitExecutionStepsFilter(this.stepsFilter)
        ) || []
      )
    },
    hasActiveCircuit() {
      return this.item.searchType === 'realtime' && !!this.item.circuitId
    },
    selectedStepNumber() {
      return this.$store.state.location_module.selectedStepNumber
    },
  },
  watch: {
    selectedStepNumber() {
      this.$trackWatch(1)
      //Prevent memory leak
      if (this.selectedStepNumberUpdatedCooldown) {
        if (Date.now() - this.selectedStepNumberUpdatedCooldown < 1000) {
          return
        }
      }

      this.$nextTick(() => {
        this.updateSelectedStepNumber(this.selectedStepNumber)
      })
    },
    item: {
      handler(newVal, oldVal) {
        //this.redisplayTableIfDataProvided()

        //Prevent Vue reactivity issue, watcher is being triggered twice
        //Check for differences between newVal and oldVal
        const differences = compareObjects(newVal, oldVal)
        //Update with new object only if there are differences and circuitExecutionId has changed
        if (!this.circuitDetailsFromAPI) {
          if (
            Object.keys(differences).length > 0 &&
            (!!differences.vehicleId ||
              differences.circuitExecutionId ||
              newVal.circuitExecutions[0]?.executionId !==
                oldVal.circuitExecutions[0]?.executionId ||
              //Case of same circuit execution on several days
              (!moment(newVal.historyStartDatetime).isSame(
                oldVal.historyStartDatetime
              ) &&
                !moment(newVal.historyEndDatetime).isSame(
                  oldVal.historyEndDatetime
                )))
          ) {
            this.updateUsingVuex(newVal)
          }
        }
      },
      deep: true,
    },
    stepsFilter: {
      handler() {
        //this.redisplayTableIfDataProvided()
      },
    },
  },
  async mounted() {
    if (!this.circuitDetailsFromAPI) {
      void this.updateUsingVuex(null)
    }
  },
  destroyed() {
    this.$store.dispatch(
      'simpliciti_map/highlightCircuitExecPolylineSection',
      null
    )
  },
  methods: {
    redisplayTableIfDataProvided() {
      /*
      this.switchToListMode({
        mapComponent: 'CircuitExecMap',
      })*/
      /*
      if (this.bottomProps.circuitDetails.hasOwnProperty('circuit_id')) {
        this.switchToTablePlusMapMode({
          bottom: 'CircuitExecTable',
          top: 'CircuitExecMap',
          bottomProps: this.bottomProps,
        })
      }*/
    },
    async updateUsingVuex(item = null) {
      if (this.loading) {
        return false
      }
      this.loading = true
      await this.$store.dispatch(
        'location_module/updateCircuitExecutionDetails',
        {
          //Get vehicleId from item param or from this.item on component mounted
          vehicleId: item ? item.vehicleId : this.item.vehicleId,
        }
      )
      this.loading = false
    },
    /**
     * @param event
     * @param {Object} node {id:4} stepNumber
     * @param {Boolean} [updateLocalStateVariableOnly] Used when syncing selected from table
     */
    onItemClick({ event, node, updateLocalStateVariableOnly = false }) {
      if (updateLocalStateVariableOnly) {
        this.activeNodeId = node.id
        //console.log('onItemClick', this.activeNodeId)
        return
      }

      this.$nextTick(() => {
        //Update selected step number
        this.updateSelectedStepNumber(node.id)

        this.onItemLoupeClick({ event, node })
      })
    },
    /**
     * Will fly to polyline (trip-history step/troncon)
     * @param {*} param0
     */
    onItemLoupeClick({ event, node }) {
      event.stopPropagation()
      const self = this

      let nodeLayer = this.$map
        .getLeafletWrapperVM()
        .layerGroups.circuitPolylines.getLayers()
        .find((l) => l.externalId === node.id)
      let center = nodeLayer.getCenter()
      this.$map.getLeafletInstance().setView(center)

      /*console.log(2, 'onItemLoupeClick', {
        node,
      })*/

      if (this.activeNodeId !== node.id) {
        /*console.log(2, 'onItemLoupeClick.select', {
          node,
        })*/

        this.$nextTick(() => {
          this.onItemClick(node)
          this.$nextTick(() => {
            setTimeout(() => flyTo(), 500)
          })
        })
      } else {
        flyTo()
      }

      function flyTo() {
        self.$map.getLeafletInstance().fitBounds(nodeLayer.getLatLngs())
      }
    },
    updateSelectedStepNumber(stepNumber) {
      if (this.activeNodeId === stepNumber) {
        // console.log('onItemClick.deselect')
        this.activeNodeId = null
        this.$store.dispatch(
          'simpliciti_map/highlightCircuitExecPolylineSection',
          null
        )

        this.selectedStepNumberUpdatedCooldown = Date.now()
        this.$store.state.location_module.selectedStepNumber = null
      } else {
        // console.log('onItemClick.select')
        this.$store.dispatch(
          'simpliciti_map/highlightCircuitExecPolylineSection',
          stepNumber
        )
        this.activeNodeId = stepNumber
        this.selectedStepNumberUpdatedCooldown = Date.now()
        this.$store.state.location_module.selectedStepNumber = this.activeNodeId
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.last_circuit_tab__infos {
  background: white;
  border-radius: 10px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.075);
}
.info_item_value {
  text-align: left;
  font: normal normal normal 12px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-tundora);
}
.info_item_label {
  text-align: left;
  font: normal normal normal 11px/18px Open Sans;
  letter-spacing: 0px;
  color: var(--color-metal-rock);
  margin-right: 5px;
}
.info_item {
  display: flex;
  flex-grow: 1;
  min-width: 41px;
}
.head_infos {
  display: flex;
  justify-content: flex-start;
  column-gap: 5px;
  margin: 0px 5px;
  flex-wrap: wrap;
}
.toolbar {
  display: flex;
  justify-content: flex-end;
  column-gap: 5px;
  align-items: center;
}
.node,
.node_content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.node_content {
  flex-direction: column;
}
.node_union {
  width: 4px;
  background-color: var(--color-dark-blue);
  height: 1px;
  margin-left: 10px;
}
.steps_filter_wrapper {
  display: flex;
  align-items: center;
}
</style>

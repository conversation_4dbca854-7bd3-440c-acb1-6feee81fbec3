<template>
  <MapPopup :title="$t('common.map.popup.alert_title')">
    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>{{ $t('alerts.map_marker.last_infos_title') }}</div>
      <i
        :class="
          !isLastInfosSectionCollapsed
            ? 'fas fa-chevron-down event-popup-chevron'
            : 'fas fa-chevron-up event-popup-chevron collapsed'
        "
        :aria-expanded="!isLastInfosSectionCollapsed ? 'true' : 'false'"
        aria-controls="collapse-last-infos"
        @click="isLastInfosSectionCollapsed = !isLastInfosSectionCollapsed"
      />
    </div>

    <b-collapse id="collapse-last-infos" v-model="isLastInfosSectionCollapsed">
      <div class="col-12 pl-4">
        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('common.date') }}
            </strong>
          </div>
          <div class="row">
            {{ formatDate }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('common.position') }}
            </strong>
          </div>
          <div class="row">
            {{ geometry.properties.address ? geometry.properties.address : '' }}
          </div>
        </div>
      </div>
    </b-collapse>

    <div
      class="row py-2 pl-4 blue small-text align-items-center font-weight-bold"
    >
      <div>{{ $t('alerts.map_marker.title') }}</div>
      <i
        :class="
          !isAlertDetailsCollapsed
            ? 'fas fa-chevron-down event-popup-chevron'
            : 'fas fa-chevron-up event-popup-chevron collapsed'
        "
        :aria-expanded="!isAlertDetailsCollapsed ? 'true' : 'false'"
        aria-controls="collapse-alert-details"
        @click="isAlertDetailsCollapsed = !isAlertDetailsCollapsed"
      />
    </div>

    <b-collapse id="collapse-alert-details" v-model="isAlertDetailsCollapsed">
      <div class="col-12 pl-4 pb-2">
        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('location_module.alerts.type') }}
            </strong>
          </div>
          <div class="row">
            {{ geometry.properties.type ? geometry.properties.type : 'n/c' }}
          </div>
        </div>

        <div class="my-1">
          <div class="row">
            <strong>
              {{ $t('location_module.alerts.message') }}
            </strong>
          </div>
          <div class="row">
            {{
              geometry.properties.message ? geometry.properties.message : 'n/c'
            }}
          </div>
        </div>
      </div>
    </b-collapse>
  </MapPopup>
</template>

<script>
import MapPopup from '@c/shared/MapPopup/MapPopup.vue'
import switchablePopupsMixin from '@/mixins/switchable-popup-mixin.ts'
import { getEmptyStringIfStringIncludes } from '@/utils/string'

export default {
  name: 'LocationAlertPopup',
  components: {
    MapPopup,
  },
  mixins: [switchablePopupsMixin],
  props: {
    geometry: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    let spmLabelPrefix = getEmptyStringIfStringIncludes(
      this.$t('switchable_popups.label.alerts'),
      'switchable_popups'
    )
    return {
      spmLabelPrefix,
      spmLayerGroupName: 'vehicle_alerts_messages',
      isLastInfosSectionCollapsed: true,
      isAlertDetailsCollapsed: false,
    }
  },
  computed: {
    formatDate() {
      if (this.geometry.properties.date_creation) {
        return this.$date.formatDatetime(this.geometry.properties.date_creation)
      }
      return ''
    },
  },
}
</script>

<style scoped>
.blue {
  color: var(--color-denim);
}

.small-text {
  font-size: 0.75rem;
}

.event-popup-chevron {
  position: absolute;
  right: 10px;
  cursor: pointer;
}

strong {
  font: normal normal bold 11px/18px Open Sans;
  letter-spacing: 0;
  color: var(--color-tundora);
}
</style>

<template lang="pug">
.container
  .row(v-if="loading")
    b-spinner(
      class="loader"
      variant="info"
      style="width: 3rem; height: 3rem; margin: 0 auto; display:block;"
    )
  .alerts-list(v-if="!loading && alerts.length > 0")
    .row
      .col-12.text-right
        TableButton(
          @click="switchToTablePlusMapMode({ top: 'LocationAlertsMap', bottom: 'LocationAlertsTable' })"
          :title="$t('common.layout.modes.table_and_map_button_tooltip')"
          :html="$t('common.layout.modes.table_and_map_button_tooltip')"
          icon="table-map"
        )
    .row.mt-2.mb-2(
      v-for="alert in alerts"
      :key="alert.message_id"
      @click="ackAlert(alert.message_id)"
      style="cursor: pointer;"
    )
      .col-12
        .card
          .card-body
            .row.m-0.p-0
              .col-1.m-0.p-0.d-flex.align-items-center.justify-content-center
                i.fas.fa-exclamation-circle(:style="`font-size:1.5em;` + style")
              .col-10
                span {{ $date.formatDatetime(alert.date_creation) }}
                h6.card-title(:style="style") {{ alert.type }}
              .col-1.m-0.p-0
                .row.d-flex.flex-column.align-items-center.justify-content-center
                  i.fa.fa-search.p-2(:style="`cursor: pointer;`" @click="flyToBounds(alert.latitude, alert.longitude)")
                .row.d-flex.flex-column.align-items-center.justify-content-center
                  i.fas(
                    style="color: #70BD95;"
                    :class="{ 'fa-exclamation' : alert.statut === 0, 'fa-check' : alert.statut === 1, 'fa-check-double' : alert.statut === 2 }"
                  )
  .row.alert-container(v-if="!loading && alerts.length === 0")
    .alert.alert-info.p-2 {{ $t('location_module.alerts.no_alert') }}
</template>
<script>
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'
import { mapGetters } from 'vuex'
import TableButton from '@c/shared/TableButton.vue'
import MapIcon from '@c/shared/TableModesToolbar/MapIcon.vue'
import { featureFunctionIds, hasFeature } from '@/config/features'
import loggingService from '@/services/logging-service'

/**
 * List feature
 * @namespace components
 * @category components
 * @subcategory location/alerts
 * @module LocationAlerts
 **/
export default {
  name: 'LocationAlerts',
  components: {
    TableButton,
    MapIcon,
  },
  mixins: [locationLayoutModesMixin],
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
    }
  },
  computed: {
    ...mapGetters({
      alerts: 'location_module/getAlerts',
      unreadAlerts: 'location_module/getUnreadAlerts',
    }),
    color() {
      return '#ED2C2C'
    },
    style() {
      return `color: ${this.color};`
    },
  },
  watch: {
    unreadAlerts(oldVal, newVal) {
      this.$trackWatch(1)
      return newVal
    },
    item: {
      async handler() {
        this.$trackWatch(1)
        this.update()
      },
      deep: true,
    },
  },
  created() {
    this.update()
  },
  mounted() {
    this.switchToListMode({
      mapComponent: 'LocationAlertsMap',
    })

    if (hasFeature('enableAuditAlerts')) {
      loggingService.activityAuditManager.startActivityAudit(
        featureFunctionIds.alerts
      )
    }
  },
  methods: {
    async update() {
      this.loading = true
      await this.$store.dispatch('location_module/getVehicleAlerts', {
        vehicleId: this.item.vehicleId,
        datetimeFrom: this.item.date,
      })
      this.loading = false
    },
    ackAlert(alertId) {
      let alert = this.findAlertById(alertId)
      if (alert.statut !== 2) {
        this.$store.dispatch('location_module/acknowledgeAlert', alertId)
      }
    },
    flyToBounds(lat, lng, zoom = 18) {
      this.$mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [[lat, lng]])
    },
    findAlertById(alertId) {
      return this.$store.getters['location_module/findAlertById'](alertId)
    },
  },
}
</script>
<style lang="scss" scoped>
span {
  color: #727272;
  font-size: 0.8em;
}
.card-title {
  border-bottom: none;
  font-weight: 600;
}
.row.alert-container {
  width: calc(100% + 30px);

  .alert {
    width: 100%;
  }
}
.alerts-list {
  background: white;
  border-radius: 10px;
  padding: 16px;
  border: 1px solid rgba(0, 0, 0, 0.075);
}
</style>

<template>
  <div ref="root" class="vehicle_alerts_table">
    <DataTable
      v-if="table"
      ref="datatable"
      class="table_theme_1"
      :ssr-paging="false"
      :ssr-show-multi-column-filter="false"
      :searching="true"
      :paging="true"
      name="locationAlerts"
      row-id="id"
      scroll-y="250px"
      :columns="columns"
      :column-defs="columnDefs"
      :language="$translations.datatable"
      :extra-options="extraOptions || {}"
      :select="select"
      :default-sorting-column="1"
      :auto-height="true"
      :auto-height-offset="mode === 'table' ? 0 : 0"
      @select="onSelect"
      @deselect="onDeselect"
    />
  </div>
</template>
<script>
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import { mapGetters } from 'vuex'
import moment from 'moment'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import locationLayoutModesMixin from '@c/location/mixins/location-layout-modes.js'

/**
 * @todo Refactor/Extract
 */
function sortableColumn(sortBy, display, title) {
  return {
    data: {
      _: sortBy,
      filter: display, //Filter using what user see
      display,
      sort: sortBy,
    },
    title,
  }
}

/**
 * Table feature
 * @namespace components
 * @category components
 * @subcategory location/alerts
 * @module LocationAlertsTable
 **/
export default {
  name: 'LocationAlertsTable',
  components: {
    DataTable,
  },
  mixins: [datatableMixin, locationLayoutModesMixin],
  props: {
    mode: {
      type: String,
      default: 'table+map',
      enum: ['table', 'table+map'],
    },
  },
  data() {
    var self = this
    return {
      table: false,
      select: {
        items: 'row',
        style: 'single',
        info: false,
      },
      columns: [
        {
          data: 'zoomIcon',
          title: '',
        },
        sortableColumn(`date`, `date`, this.$t('location_module.alerts.date')),
        sortableColumn(
          `heure`,
          `heure`,
          this.$t('location_module.alerts.time')
        ),
        sortableColumn(`type`, `type`, this.$t('location_module.alerts.type')),
        sortableColumn(
          `message`,
          `message`,
          this.$t('location_module.alerts.message')
        ),
        sortableColumn(
          `adresse`,
          `adresse`,
          this.$t('location_module.alerts.address')
        ),
      ],
      columnDefs: [
        {
          targets: 0,
          orderable: false,
          render: createComponentRender({
            name: 'ZoomActionButton',
            template: `<em @click="handleZoom" class="fas fa-search" style="cursor:pointer;"></em>`,
            methods: {
              handleZoom(e) {
                e.stopPropagation()
                self.$mitt.emit('SIMPLICITI_MAP_FIT_TO_BOUNDS', [
                  [this.row.lat, this.row.lon],
                ])
              },
            },
          }),
        },
        {
          targets: 5,
          width: '500px',
        },
      ],
      extraOptions: {
        ...this.configureColumnsFilters((filters) => {
          return [
            null,
            filters.createTextFilter({ column: 1 }),
            filters.createTextFilter({ column: 2 }),
            filters.createTextFilter({ column: 3 }),
            filters.createTextFilter({ column: 4 }),
            filters.createTextFilter({ column: 5 }),
          ]
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      },
    }
  },
  computed: {
    ...mapGetters({
      results: 'location_module/getAlerts',
      selectedItem: 'location_module/selectedItem',
    }),
  },
  watch: {
    results() {
      this.$trackWatch(1)
      this.updateTable()
    },
  },
  mounted() {
    $.fn.dataTable.moment(this.$date.getDatetimePattern())
    this.updateTable()

    this.$store.dispatch('app/changeLayout', {
      origin: 'LocationAlertsTable.vue::mounted',
      currentLayoutName: 'LOCATION_ALERTS_TABLE',
      menu_full_collapse: true,
    })

    setTimeout(() => (this.table = true), 500)
  },
  destroyed() {
    if (this.$store.state.search_module.view !== 'selection') {
      this.$store.dispatch('app/changeLayout', {
        origin: 'LocationAlertsTable.vue::destroyed',
        currentLayoutName: 'LOCATION_DETAILS_TABLE',
        menu_collapsed: this.$store.getters['app/layout'].isMenuCollapsed,
        menu_full_collapse: false,
        sub_menu: true,
      })
    }
  },
  methods: {
    onDeselect() {},
    onSelect({ item }) {},
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'locationAlerts',
        items: this.results.map((res) => {
          return {
            id: res.message_id,
            type: res.type,
            message: res.message,
            date: this.$date.formatDate(res.date_creation),
            heure: this.$date.formatTimeWithSeconds(res.date_creation),
            adresse: res.adresse,
            lat: res.latitude,
            lon: res.longitude,
          }
        }),
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.title,
.title span,
.title strong {
  font-size: 12px;
}
.vehicle_alerts_table {
  height: 100%;
  height: -moz-available; /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available; /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}
.table_toolbar {
  display: flex;
  align-items: center;
}
</style>

<template lang="pug">
.login_as_feature(v-if="$store.state.auth.user_logged")
    img.mb-4(src="@c/shared/LoginModule/assets/brand-logo.svg")
    .laf-table-wrapper
        .laf-header
            .laf-title 
              //.em.fas.fa-arrow-left(v-show="view==='clients'" style="cursor:pointer;" @click="()=>$routerPlugin.routeToFirstGrantedModule()")
              .em.fas.fa-arrow-left(v-show="view==='users'" style="cursor:pointer;" @click="()=>view='clients'")
              span(v-show="view==='clients'") {{$t('login.login_as.client_table_title')}}
              span(v-show="view==='users'") {{$t('login.login_as.users_table_title')}}
            .laf-right
              .laf-login-as {{$store.getters['auth/loginAs'].login}} ({{$store.getters['auth/loginAs'].client}})
              .laf-logout(@click="logoutFromApplication") 
                  PowerIcon(:fillColor="appColors.color_denim" :size="30")
        LoginAsClientsTable(
            v-show="view==='clients'"
            :items="items" @rowActionloginAsChildClient="loginAsChildClient"
            @rowActionViewChildClientUsers="switchToChildClientUsers"
        )
        LoginAsClientUsersTable(
            v-show="view==='users'"
            :items="childClientUsers" @rowActionloginAsChildClientUser="loginAsChildClientUser"
        )
    .login_as_bottom
        a.login_as_bottom_support_link(rel="noopener noreferrer" href="https://espaceclientsimpliciti.microsoftcrmportals.com/" class="contact" target="_blank") {{ $t("auth.contacter_assistance") }}
</template>
<script setup>
import { ref, provide, onMounted } from 'vue'
import PowerIcon from 'vue-material-design-icons/Power.vue'
import LoginAsClientsTable from '@c/auth/LoginAsFeature/LoginAsClientsTable.vue'
import LoginAsClientUsersTable from '@c/auth/LoginAsFeature/LoginAsClientUsersTable.vue'
import { useProgresiveDatatableLoader } from '@c/shared/DataTable/DatatableLoader.vue'
import * as authService from '@/services/auth-service.js'
import { getClientLoginAsToken } from '@/services/auth-service'
import store from '@/store'
import loader from '@/plugins/loader.js'
import routerPlugin from '@/plugins/router-plugin.js'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
import appColors from '@/styles/colors'

const tableProgressiveLoader = useProgresiveDatatableLoader()
const loaderType = 'show' // 'show' or 'showAlternative'

// Reactive data
const isDatatableLoading = ref(true)
const items = ref([]) // Clients
const childClientUsers = ref([])
const view = ref('clients')
const selectedClientId = ref('')

// Provide data to child components
provide('isDatatableLoading', isDatatableLoading)

// Methods
const switchToChildClientUsers = async (clientId) => {
  selectedClientId.value = clientId
  view.value = 'users'
  childClientUsers.value = []
  isDatatableLoading.value = true
  tableProgressiveLoader.show()
  childClientUsers.value = await authService.getClientUsers(clientId)
  isDatatableLoading.value = false
  setTimeout(() => tableProgressiveLoader.hide(), 1000)
}

const updateChildClientsList = async () => {
  let jwt = store.getters['auth/jwt']
  if (!jwt) return

  items.value = [
    {
      id: store.getters['auth/clientId'],
      name: store.getters['auth/clientName'],
    },
  ]

  tableProgressiveLoader.show()
  authService
    .getClientChildsClientsProgressively(
      (clients) => {
        items.value = items.value.concat(clients)
        if (items.value.length > 0) {
          isDatatableLoading.value = false
          tableProgressiveLoader.show()
        }
      },
      { jwt }
    )
    .catch((err) => {
      console.error(err)
    })
    .finally(() => {
      isDatatableLoading.value = false
      tableProgressiveLoader.hide()
    })
}

const loginAsChildClientUser = (userId) => {
  loader.show(
    (async () => {
      const token = await authService.getClientLoginAsToken(
        store.getters['auth/parentJwt'],
        selectedClientId.value,
        userId
      )
      loginAsUsingToken(token, selectedClientId.value, true)
    })()
  )
}

const loginAsChildClient = (clientId) => {
  loader.show(
    (async () => {
      const token = await authService.getClientLoginAsToken(
        store.getters['auth/parentJwt'],
        clientId
      )
      loginAsUsingToken(token, clientId)
    })()
  )
}

const loginAsUsingToken = async (token, clientId, fullRefresh = false) => {
  routerPlugin.loginAsUsingToken(token, clientId, fullRefresh)
}

const logoutFromApplication = () => {
  routerPlugin.logoutFromApplication()
}

// Lifecycle hook
onMounted(() => {
  updateChildClientsList()
})
</script>
<style lang="scss" scoped>
.login_as_feature {
  width: -webkit-fill-available;
  min-height: calc(100vh);
  background: linear-gradient(
      to right,
      var(--color-denim-trans),
      var(--color-denim-trans)
    ),
    url(./assets/login-background.jpg) no-repeat top center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  height: auto;
  background-size: cover;
}
.laf-table-wrapper {
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #0000005c;
  border-radius: 10px;
  min-height: 300px;
  padding: 20px;
  max-width: 749px;
  flex-grow: 1;
  width: inherit;
}
.laf-header {
  display: flex;
  justify-content: space-between;
}
.laf-title {
  display: flex;
  column-gap: 5px;
  align-items: center;
  em {
    position: relative;
    top: 1px;
  }
  span {
    font: normal normal bold 18px/24px Open Sans;
    letter-spacing: 0px;
    color: #014470;
  }
}
.laf-logout {
  cursor: pointer;
}
.login_as_bottom {
  width: fill-available;
  width: -webkit-fill-available;
  padding: 5px;
  display: flex;
  align-items: flex-end;
  height: inherit;
  justify-content: space-between;
}
.login_as_bottom_support_link {
  font: normal normal bold 16px/18px Open Sans;
  color: white;
  margin: 15px;
}
.laf-right {
  display: flex;
}
.laf-login-as {
  position: relative;
  top: 3px;
}
img {
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  user-select: none; /* Standard */
}
</style>

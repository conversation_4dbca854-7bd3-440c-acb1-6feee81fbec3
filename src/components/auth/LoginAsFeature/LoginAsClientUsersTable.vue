<template lang="pug">
.login-as-table(ref="root")
  DataTable(
    v-if="table"
    class="table_theme_1"
    ref="datatable",
    :ssrPaging="false",
    :ssrShowMultiColumnFilter="false",
    :searching="true",
    :paging="true",
    name="auth_clients_users_table",
    rowId="id",
    scrollY="250px",
    :columns="columns",
    :columnDefs="columnDefs",
    :language="$translations.datatable",
    :extraOptions="extraOptions",
    :select="false",
    :defaultSortingColumn="0"
    :autoHeight="true"
    :autoHeightOffset="0"
  )
</template>
<script setup>
import { ref, watch, onMounted } from 'vue'
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import { createSortableColumnDefinition } from '@/utils/datatable.js'
import AccountCheckIcon from 'vue-material-design-icons/AccountCheck.vue'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
import stylesMixin from '@/mixins/styles-mixin.js'
import store from '@/store'
import i18n from '@/i18n'

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['rowActionloginAsChildClientUser'])

// Template refs
const root = ref(null)
const datatable = ref(null)

// Reactive data
const isModalOpen = ref(false)
const showInformationsPopup = ref(false)
const table = ref(false)

// Create columns with proper component references
const columns = ref([
  createSortableColumnDefinition('login', 'login', i18n.t('login.username')),
  createSortableColumnDefinition('name', 'name', i18n.t('common.nom')),
  createSortableColumnDefinition(
    'lastConnectionAt',
    'lastConnectionAt',
    i18n.t('login.login_as.column_last_connection_at')
  ),
  createSortableColumnDefinition(
    'lastActivityAt',
    'lastActivityAt',
    i18n.t('login.login_as.column_last_activity_at')
  ),
  {
    orderable: false,
    render: createComponentRender({
      name: 'LoginAsClientUserCell',
      mixins: [stylesMixin],
      template: `<div @click="handleClick" :title="$t('login.login_as.tooltip.as_client_user',{username:row.name})" style="cursor:pointer;"><AccountCheckIcon :title="$t('login.login_as.tooltip.view_users_list',{clientName:row.name})" :fillColor="appColors.color_silver_tree" :size="20"/></div>`,
      components: {
        AccountCheckIcon,
      },
      methods: {
        handleClick(e) {
          e.stopPropagation()
          emit('rowActionloginAsChildClientUser', this.row.id)
        },
      },
    }),
  },
])

const columnDefs = ref([])
const extraOptions = ref({
  ...datatableMixin.methods.configureColumnsFilters((filters) => {
    return [
      filters.createTextFilter({ column: 0 }),
      filters.createTextFilter({ column: 1 }),
      filters.createTextFilter({ column: 2 }),
      filters.createTextFilter({ column: 3 }),
      null,
    ]
  }),
  ...datatableMixin.methods.configureFooter({
    linesSelector: false,
  }),
  dom: 'rtip',
  pageLength: 25,
  info: true,
  ordering: false,
})

// Methods
const updateTable = () => {
  store.dispatch('datatable/setTableItems', {
    name: 'auth_clients_users_table',
    items: props.items,
  })
}

// Watchers
watch(
  () => props.items,
  () => {
    updateTable()
  }
)

// Lifecycle hooks
onMounted(() => {
  // Apply mixins
  removeNativeTooltipFromMaterialIconsMixin.mounted()
  $.fn.dataTable.moment(i18n.t('date.format.datetime'))
  updateTable()
  setTimeout(() => {
    table.value = true
    setTimeout(() => {
      datatable.value.datatable.draw()
    }, 500)
  }, 200)
})
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.title,
.title span,
.title strong {
  font-size: 12px;
}
.login-as-table {
  height: 100%;
  height: -moz-available; /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available; /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}
</style>

<template lang="pug">
.login-as-table(ref="root")
  DataTable(
    v-if="table"
    class="table_theme_1"
    ref="datatable",
    :ssrPaging="false",
    :ssrShowMultiColumnFilter="false",
    :searching="true",
    :paging="true",
    name="auth_clients_table",
    rowId="id",
    scrollY="250px",
    :columns="columns",
    :columnDefs="columnDefs",
    :language="$translations.datatable",
    :extraOptions="extraOptions",
    :select="false",
    :defaultSortingColumn="0"
    :autoHeight="true"
    :autoHeightOffset="0"
  )
</template>
<script setup>
import { ref, watch, onMounted } from 'vue'
import DataTable, {
  createComponentRender,
} from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import { createSortableColumnDefinition } from '@/utils/datatable.js'
import AccountCheckIcon from 'vue-material-design-icons/AccountCheck.vue'
import AccountMultiple from 'vue-material-design-icons/AccountMultiple.vue'
import { removeNativeTooltipFromMaterialIconsMixin } from '@/mixins/icons-mixin.js'
import store from '@/store'
import stylesMixin from '@/mixins/styles-mixin.js'
import i18n from '@/i18n'
import rightsPlugin from '@/plugins/rights-plugin.js'

const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits([
  'rowActionloginAsChildClient',
  'rowActionViewChildClientUsers',
])

// Template refs
const root = ref(null)
const datatable = ref(null)

// Reactive data
const isModalOpen = ref(false)
const showInformationsPopup = ref(false)
const table = ref(false)

// Create columns with proper component references
const columns = ref([
  createSortableColumnDefinition('name', 'name', 'Client'),
  {
    orderable: false,
    render: createComponentRender({
      name: 'LoginAsClientCell',
      mixins: [stylesMixin],
      template: `<div @click="handleClick" :title="$t('login.login_as.tooltip.as_client',{clientName:row.name})" style="cursor:pointer;"><AccountCheckIcon :title="$t('login.login_as.tooltip.as_client',{clientName:row.name})" :fillColor="appColors.color_silver_tree" :size="20"/></div>`,
      components: {
        AccountCheckIcon,
      },
      methods: {
        handleClick(e) {
          e.stopPropagation()
          emit('rowActionloginAsChildClient', this.row.id)
        },
      },
    }),
  },
  {
    orderable: false,
    render: createComponentRender({
      name: 'ViewUsersCell',
      mixins: [stylesMixin],
      template: `<div v-if="enabled" :title="$t('login.login_as.tooltip.view_users_list',{clientName:row.name})" @click="handleClick" style="cursor:pointer;"><AccountMultiple :title="$t('login.login_as.tooltip.view_users_list',{clientName:row.name})" :fillColor="appColors.color_denim" :size="20"/></div>`,
      components: {
        AccountMultiple,
      },
      computed: {
        enabled() {
          if (
            this.row.name.toLowerCase() !== 'sabatier' &&
            rightsPlugin.hasFeatureRight('common_login_as_user')
          ) {
            return true
          }
          return false
        },
      },
      methods: {
        handleClick(e) {
          e.stopPropagation()
          emit('rowActionViewChildClientUsers', this.row.id)
        },
      },
    }),
  },
])

const columnDefs = ref([])
const extraOptions = ref({
  ...datatableMixin.methods.configureColumnsFilters((filters) => {
    return [filters.createTextFilter({ column: 0 }), null, null]
  }),
  ...datatableMixin.methods.configureFooter({
    linesSelector: false,
  }),
  dom: 'rtip',
  pageLength: 25,
  info: true,
  ordering: false,
})

// Methods
const updateTable = () => {
  store.dispatch('datatable/setTableItems', {
    name: 'auth_clients_table',
    items: props.items,
  })
}

// Watchers
watch(
  () => props.items,
  () => {
    updateTable()
  }
)

// Lifecycle hooks
onMounted(() => {
  // Apply mixins
  removeNativeTooltipFromMaterialIconsMixin.mounted()
  $.fn.dataTable.moment(i18n.t('date.format.datetime'))
  updateTable()
  setTimeout(() => {
    table.value = true
    setTimeout(() => {
      datatable.value.datatable.draw()
    }, 500)
  }, 200)
})
</script>
<style lang="scss" scoped>
.title {
  padding: 5px 10px 0px 10px;
  margin-bottom: 0px;
  flex-shrink: 0;
}

.title,
.title span,
.title strong {
  font-size: 12px;
}

.login-as-table {
  height: 100%;
  height: -moz-available;
  /* WebKit-based browsers will ignore this. */
  height: -webkit-fill-available;
  /* Mozilla-based browsers will ignore this. */
  height: fill-available;
}

:deep {
  .export_toolbar {
    margin-left: 0 !important;
  }
}
</style>

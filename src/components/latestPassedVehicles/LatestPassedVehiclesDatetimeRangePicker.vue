<template>
  <date-picker
    v-model="selectedValue"
    class="latest-passed-vehicles-datetime-range-picker"
    :lang="lang"
    :type="selectedType === 'range' ? 'datetime' : 'date'"
    :range="selectedType === 'range'"
    :multiple="selectedType === 'each_day'"
    :format="$date.getDatetimePattern({ seconds: false })"
    :open="visible"
    :show-week-number="true"
    :show-second="false"
    :placeholder="$t('latestPassedVehicles.dates.placeholder') + '*'"
    @clear="$emit('onDateClear')"
    @change="handleChange"
    @focus="open('')"
  >
    <div slot="footer" class="container-fluid p-0">
      <div class="mx-0 mt-2 text-left">
        <b-form-group
          v-slot="{ ariaDescribedby }"
          :label="$t('searchModule.date_picker.selection_label')"
          label-class="date_picker_label"
        >
          <b-form-radio-group
            v-model="selectedType"
            class="date-picker-form-wrapper"
            :options="selectionOptions"
            :aria-describedby="ariaDescribedby"
            name="type-options"
          >
          </b-form-radio-group>
        </b-form-group>
      </div>
      <button class="mx-btn mx-datepicker-btn-confirm" @click="cancel">
        {{ $t('buttons.cancel_alternative') }}
      </button>
      <button class="mx-btn mx-datepicker-btn-confirm" @click="close">
        {{ $t('buttons.valid') }}
      </button>
    </div>
  </date-picker>
</template>
<script>
import DatePicker from 'vue2-datepicker'
import 'vue2-datepicker/locale/fr'
import 'vue2-datepicker/index.css'
import { mapGetters } from 'vuex'
import i18nDatepickerMixin from '@/mixins/i18n-datepicker.js'
import { getConsecutiveDays } from '@/utils/dates'

export default {
  components: {
    DatePicker,
  },
  mixins: [i18nDatepickerMixin],
  inject: {
    isSearchModuleDatePickerDisabledHandler: {
      default: () => () => {},
    },
  },
  data() {
    return {
      visible: false,
      selectedValue: [],
      selectedType: 'each_day',
      selectionOptions: [
        {
          text: this.$t('search_module.date_picker.mode_each_day'),
          value: 'each_day',
        },
        {
          text: this.$t('search_module.date_picker.mode_range'),
          value: 'range',
        },
      ],
    }
  },
  computed: {
    ...mapGetters({
      selectedDateRanges: 'search_module/getSelectedDateRanges',
    }),
  },
  watch: {
    selectedValue() {
      let value = this.selectedValue

      if (this.selectedType === 'range') {
        const dates = value.map((d) => new Date(d))
        value = getConsecutiveDays(dates[0], dates[1])
      }

      this.$emit('input', value)
    },
    selectedType() {
      this.visible = false
      this.$nextTick(() => {
        this.visible = true
      })
    },
  },
  mounted() {
    if (this.selectedDateRanges.length > 0) {
      this.selectedValue = this.selectedDateRanges[0][0]
    }
  },
  methods: {
    close() {
      this.visible = false
    },
    cancel() {
      this.selectedValue = []
      this.visible = false
    },
    open() {
      this.visible = true
    },
    handleChange(value, type) {
      if (type === 'second') {
        this.visible = false
      }
    },
  },
}
</script>
<style lang="scss">
.latest-passed-vehicles-datetime-range-picker.mx-datepicker {
  width: 100%;
  max-width: 280px;
}
.mx-input {
  color: #495057;
  box-shadow: none;

  &::-webkit-input-placeholder,
  &::placeholder,
  &:placeholder-shown {
    color: var(--color-slate-gray);
    opacity: 1;
  }
}
</style>

<template lang="pug">
.table-wrapper(style="max-width:600px")
    DataTable(
    v-if="table"
    class="table_theme_1"
    ref="datatable",
    :ssrPaging="false",
    :ssrShowMultiColumnFilter="false",
    :searching="true",
    :paging="true",
    name="latestPassedVehicles",
    rowId="id", 
    :columns="columns",
    :columnDefs="columnDefs",
    :language="datatableLanguage",
    :extraOptions="extraOptions||{}",
    :defaultSortingColumn="2"
    defaultSortingColumnDirection="desc"
    :autoHeight="true"
    :autoHeightOffset="50"
    )
    </template>
<script>
import DataTable from '@/components/shared/DataTable/DataTable.vue'
import $ from 'jquery'
import datatableMixin from '@/mixins/datatable.js'
import TableModesToolbar from '@c/shared/TableModesToolbar/TableModesToolbar.vue'
import { Icon } from '@iconify/vue2'
import {
  createSortableColumnDefinition,
  createActionButtonDatatableColumn,
} from '@/utils/datatable.js'
import ZoneTypeIcon from '@c/zones/ZoneTypeIcon.vue'

export default {
  name: 'LatestPassedVehiclesTable',
  components: {
    DataTable,
    TableModesToolbar,
  },
  mixins: [datatableMixin],
  props: {
    items: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    var self = this
    return {
      table: false,
      columns: [
        createActionButtonDatatableColumn({
          name: 'ShowTripHistoryActionButton',
          components: {
            Icon,
          },
          template: `<div @click="handleClick" class="view-map-action-icon" style="cursor:pointer;display:flex;justify-content:center;">
                    <Icon icon="radix-icons:magnifying-glass" color="var(--color-main)" :style="{fontSize:'16px'}"></Icon>
                    </div>`,
          handleClick(row) {
            self.$emit('onDetails', row)
          },
        }),
        createSortableColumnDefinition(
          `vehicleName`,
          `vehicleName`,
          this.$t('common.vehicule')
        ),
        createSortableColumnDefinition(
          `datetimeStamp`,
          `datetimeFormatted`,
          this.$t('common.date')
        ),
        createActionButtonDatatableColumn({
          name: 'OpenTripHistoryTabInLocationModuleButton',
          components: {
            Icon,
          },
          template: `<div @click="handleClick" class="view-map-action-icon" style="cursor:pointer;display:flex;justify-content:center;">
                      <em
                        v-b-tooltip.hover.bottom.viewport
                        :title="$t('latestPassedVehicles.table.go_to_vehicle_trip_history')"
                        class="fa fa-history"
                        style="color: var(--color-main)"
                      />
                    </div>`,
          handleClick(row) {
            //Add aram vehicles visible in table
            self.$emit('onOpenTripHistoryTabInLocationModule', row)
          },
        }),
      ],
      columnDefs: [
        {
          targets: [0],
          width: '40px',
        },
      ],
      extraOptions: {
        ...this.configureColumnsFilters((filters) => {
          return [
            null,
            filters.createTextFilter({ column: 1 }),
            filters.createTextFilter({ column: 2 }),
          ]
        }),
        ...this.configureFooter(),
        dom: 'rtip',
        pageLength: 25,
        info: true,
      },
    }
  },
  computed: {
    datatableLanguage() {
      return {
        ...this.$translations.datatable,
        emptyTable: this.$t('common.no_results_alternative'),
      }
    },
  },
  watch: {
    items: {
      handler() {
        this.updateTable()
      },
      deep: true,
    },
  },
  mounted() {
    $.fn.dataTable.moment(this.$date.getDatetimePattern())
    this.updateTable()
    setTimeout(() => (this.table = true), 500)
  },
  methods: {
    updateTable() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'latestPassedVehicles',
        items: this.items,
      })
    },
    destroyed() {
      this.$store.dispatch('datatable/setTableItems', {
        name: 'latestPassedVehicles',
        items: [],
      })
    },
  },
}
</script>
<style lang="scss">
.table-wrapper .view-map-action-icon:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
</style>

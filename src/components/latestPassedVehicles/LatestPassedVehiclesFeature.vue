<template>
  <div class="wrapper latest-passed-vehicles-feature">
    <!-- LEFT: DETAILS -->
    <div
      v-if="routeNameRef === 'results' && selectedItemRef"
      class="details-wrapper"
      style="min-height: 400px"
    >
      <BackTitleButton
        style="margin-top: 15px; margin-left: 5px"
        :color="colors.color_main"
        :title="
          $t('latestPassedVehicles.details_header', {
            vehicleName: selectedItemRef.vehicleName,
            date: formatDate(selectedItemRef.datetime),
          })
        "
        @click="handleBackFromDetails"
      ></BackTitleButton>
      <TripHistoryList :item="selectedItemRef" />
    </div>

    <!-- RIGHT: SEARCH/RESULTS -->
    <div style="flex-grow: 1; flex-basis: 0" class="lpvf_search_wrapper">
      <!-- RESULTS -->
      <div v-show="routeNameRef === 'results'" class="mb-4">
        <BackTitleButton
          style="margin-top: 15px; margin-left: 5px"
          :color="colors.color_main"
          :title="$t('latestPassedVehicles.back_to_search_selection')"
          @click="handleBackToSearchSelection"
        ></BackTitleButton>
      </div>

      <!-- SEARCH/RESULTS -->
      <div
        v-show="routeNameRef === 'search' || routeNameRef === 'results'"
        style="max-width: 425px"
      >
        <p class="title">{{ $t('latestPassedVehicles.labels.address') }}</p>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            align-items: center;
            column-gap: 5px;
          "
        >
          <SimplicitiAddressAutocomplete
            ref="simplicitiAddressAutocompleteRef"
            v-model="addressRef"
            disable-zone-addresses
            :auto-select-first="false"
            default-search-mode="form"
          />
          <div
            v-b-tooltip.hover.viewport="
              $t('latestPassedVehicles.address_marker_center_button')
            "
            style="cursor: pointer; border-radius: 5px; padding: 3px"
            @click="flyToAddressMarker"
          >
            <Icon
              icon="mdi:image-filter-center-focus"
              color="var(--color-main)"
              :style="{ fontSize: '30px' }"
            ></Icon>
          </div>
        </div>
      </div>

      <!-- RESULTS -->
      <div
        v-show="routeNameRef === 'results'"
        class="results-wrapper"
        style="min-height: 400px"
      >
        <LatestPassedVehiclesTable
          class="mt-3"
          :items="resultsArrRef"
          @onDetails="onDetails"
          @onOpenTripHistoryTabInLocationModule="
            onOpenTripHistoryTabInLocationModule
          "
        />
      </div>

      <!-- SEARCH -->
      <div v-show="routeNameRef === 'search'">
        <p class="title mt-4">
          {{ $t('latestPassedVehicles.labels.vehicles') }}
        </p>

        <input
          v-model="filterText"
          class="form-control mb-2"
          style="max-width: 280px"
          type="text"
          :placeholder="
            $t('latestPassedVehicles.vehicle_filter_placeholder') + '*'
          "
        />
        <TreeItem
          ref="treeRef"
          v-model="vehiclesRef"
          :children="computedTreeItemDataset"
          :children-wrapper-style="treeItemChildrenWrapperStyle"
        />

        <p class="title mt-4">{{ $t('latestPassedVehicles.labels.dates') }}</p>
        <LatestPassedVehiclesDatetimeRangePicker
          ref="datetimeRangeVmRef"
          v-model="datetimeRangeRef"
          class=""
        />
        <!-- Radius selector -->
        <p class="title mt-4">{{ $t('latestPassedVehicles.labels.radius') }}</p>
        <b-form-select
          v-model="radius"
          class="latest-passed-vehicles-radius-select"
          :options="radiusOptions"
        />
        <div class="mt-2 col d-flex justify-content-end">
          <CustomButton
            v-show="canReset"
            class="mr-2"
            :type="'secondary'"
            @click="handleResetForm"
          >
            {{ $t('common.Annuler') }}
          </CustomButton>
          <CustomButton :disabled="!canValidate" @click="handleValidate">
            {{ $t('common.Valider') }}
          </CustomButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { Icon } from '@iconify/vue2'
import moment from 'moment'
import colors from '@/styles/colors.js'
import BackTitleButton from '@c/shared/BackTitleButton.vue'
import TitleWrapper from '@c/shared/TitleWrapper.vue'
import TreeItem from '@/components/shared/TreeItem/TreeItem.vue'
import LatestPassedVehiclesDatetimeRangePicker from './LatestPassedVehiclesDatetimeRangePicker.vue'
import CustomButton from '@/components/shared/ButtonWrapper.vue'
import SimplicitiAddressAutocomplete from '@c/shared/SimplicitiAddressAutocomplete/SimplicitiAddressAutocomplete.vue'
import { formatDate, getDaysDifferenceBetweenMinMaxDates } from '@/utils/dates'
import {
  getEnvValue,
  envNames,
  getDevOnlyQueryStringValue,
} from '@/services/env-service'
import store from '@/store'
import LatestPassedVehiclesTable from './LatestPassedVehiclesTable.vue'
import { sortObjectsByTimestamp } from '@/utils/object'
import TripHistoryList from '@c/shared/TripHistoryList/TripHistoryList.vue'
import { useGeocodingComposable } from '@c/shared/geocoding/geocoding-mixin.js'
import loader from '@/plugins/loader.js'
import { provide } from 'vue'
import { usePushDateRangeToSelection } from '@/composables/usePushDateRangeToSelection'
import mitt from '@/plugins/mitt.js'
import router from '@/router'
import { computeStartAndEndTimeOfDate } from '@/services/search-service'
import { fetchLatestPassedVehicles } from '@/services/latest-passed-vehicles-service'

const geocodingComposable = useGeocodingComposable()

const { pushDateRangeToSelection } = usePushDateRangeToSelection()

/**
 * @const routeNameRef
 * The variable contains a refence to a string with the possible values of 'search' and 'results'.
 */
const routeNameRef = ref('search')
const selectedItemRef = ref(
  null /*{
  id: 48556,
  date: new Date(
    'Thu Jun 29 2023 07:25:20 GMT+0200 (Central European Summer Time)'
  ),
}*/
)

const lastSearchUUID = ref(null)
const currentSearchUUID = computed(() =>
  window.btoa(`s${addressRef.value?.lat}${addressRef.value?.lng}`)
)
const shouldUpdateResults = computed(() => {
  if (addressRef.value?.lat && addressRef.value?.lng) {
    return lastSearchUUID.value != currentSearchUUID.value
  }
})

const isDatatableCenterLoaderVisible = reactive({
  value: true,
})

const contextStore = reactive({
  addressValue: null,
})
const datetimeRangeVmRef = ref(null)
const treeRef = ref(null)
const simplicitiAddressAutocompleteRef = ref(null)
const addressRef = ref(null) //dataset: DGD / 1, Rue Marcel Paul, 45770, Saran / 29/06/23 / BOM (all)
const vehiclesRef = ref([])

const filterText = ref('')

//Force tree view to un-collapse categories when filtering.
const forceShowTreeItemChildren = ref(false)
watchEffect(() => {
  forceShowTreeItemChildren.value = filterText.value.length > 0
})
provide('forceShowTreeItemChildren', forceShowTreeItemChildren)

const datetimeRangeRef = ref([])
const resultsArrRef = ref([])
const treeItemChildrenWrapperStyle = ref(`max-height:200px;overflow:auto`)
const maxSearchLimitCount = computed(() => {
  //vehicles * dates
  return getEnvValue(
    envNames.VITE_LASTPASSEDVEHICLES_FEATURE_SEARCH_SELECTION_LIMIT,
    getEnvValue(envNames.VITE_FILTERS_MAX_SELECTION, 100),
    {
      transform(value) {
        if (getDevOnlyQueryStringValue('maxselection')) {
          return parseInt(getDevOnlyQueryStringValue('maxselection'))
        }

        value = parseInt(value)
        return isNaN(value) ? 100 : value
      },
    }
  )
})
const isSearchSelectionWithinLimits = computed(() => {
  const selectedItemsCount =
    (vehiclesRef.value.length || 1) * (datetimeRangeRef.value.length || 1)

  return selectedItemsCount <= maxSearchLimitCount.value
})
const canValidate = computed(() => {
  return (
    addressRef.value?.lat &&
    addressRef.value?.lng &&
    vehiclesRef.value.length > 0 &&
    datetimeRangeRef.value.length > 0
  )
})
const canReset = computed(() => {
  return (
    (addressRef.value?.lat && addressRef.value?.lng) ||
    vehiclesRef.value.length > 0 ||
    datetimeRangeRef.value.length > 0 ||
    filterText.value.length > 0
  )
})

const radius = computed({
  get() {
    return store.getters['latestPassedVehicles/getRadius']
  },
  set(value) {
    store.dispatch('latestPassedVehicles/setRadius', value)
  },
})

const radiusOptions = computed(() => {
  return store.getters['latestPassedVehicles/getRadiusOptions']
})

const setMapToolboxContentMaxWidth = inject(
  'setMapToolboxContentMaxWidth',
  null
)

const isDatatableLoaderVisible = reactive({ value: false })

watchEffect(() => {
  if (setMapToolboxContentMaxWidth) {
    const isDetailsView =
      routeNameRef.value === 'results' && !!selectedItemRef.value

    if (isDetailsView) {
      setMapToolboxContentMaxWidth('initial')
    } else {
      setMapToolboxContentMaxWidth('640px')
    }
  }
})

watch(
  addressRef,
  () => {
    if (addressRef.value?.lat && addressRef.value?.lng) {
      geocodingComposable.addLocateAddressMarker(
        addressRef.value.lat,
        addressRef.value.lng,
        addressRef.value.formatted
      )
      geocodingComposable.centerOnLocateAddressMarker(
        addressRef.value.lat,
        addressRef.value.lng,
        //Pass current zoom level
        store.state.simpliciti_map.zoomLevel
      )

      //Check if address has change and trigger a new search
      if (routeNameRef.value === 'results' && shouldUpdateResults.value) {
        console.log('Triggering a new search after address change')
        handleValidate()
      }
    } else {
      geocodingComposable.removeLocateAddressMarker()
    }

    contextStore.addressValue = addressRef.value
  },
  {
    immediate: true,
    deep: true,
  }
)

function flyToAddressMarker() {
  if (addressRef.value?.lat && addressRef.value?.lng) {
    geocodingComposable.centerOnLocateAddressMarker(
      addressRef.value.lat,
      addressRef.value.lng
    )
  }
}

async function onDetails(tableRow) {
  selectedItemRef.value = {
    id: tableRow.vehicleId,
    date: tableRow.datetime,
    ...tableRow,
  }
}

function handleBackFromDetails() {
  selectedItemRef.value = null
}

function handleBackToSearchSelection() {
  routeNameRef.value = 'search'
  selectedItemRef.value = null
}

function handleValidate() {
  //Limit selection count
  if (!isSearchSelectionWithinLimits.value) {
    return store.dispatch('alert/addAlert', {
      type: 'info',
      title: 'Validation',
      text: `alerts.FILTERS_MAX_SELECTION`,
      params: {
        number: maxSearchLimitCount.value,
      },
    })
  }

  //Check difference in days
  const differenceInDays = getDaysDifferenceBetweenMinMaxDates(
    datetimeRangeRef.value
  )

  if (differenceInDays > 90) {
    return store.dispatch('alert/addAlert', {
      type: 'info',
      title: 'Validation',
      text: `latestPassedVehicles.dateranges_max_period_exceed`,
      params: {
        count: differenceInDays,
      },
    })
  }
  //Control radius
  if (!radius.value) {
    return store.dispatch('alert/addAlert', {
      type: 'info',
      title: 'Validation',
      text: `latestPassedVehicles.radius_not_selected`,
    })
  }

  if (!typeof radius.value === 'number') {
    return store.dispatch('alert/addAlert', {
      type: 'info',
      title: 'Validation',
      text: `latestPassedVehicles.radius_not_a_number`,
    })
  }

  //Control radius value
  if (radius.value > store.state.latestPassedVehicles.maxRadius) {
    return store.dispatch('alert/addAlert', {
      type: 'info',
      title: 'Validation',
      text: `latestPassedVehicles.dateranges_max_radius_exceeded`,
      params: {
        maxRadius: radius.value,
      },
    })
  }

  //Perform API request (split technique) and retrieve normalized results.
  datetimeRangeVmRef.value.visible = false

  //Give some time to datatable to erease previous results
  setTimeout(async () => {
    resultsArrRef.value = []
    isDatatableLoaderVisible.value = false // Visible only if partial results are present
    isDatatableCenterLoaderVisible.value = true
    datetimeRangeVmRef.value.visible = false

    loader.show()

    let uiUpdatePromise = null

    try {
      await fetchLatestPassedVehicles({
        vehicles: vehiclesRef.value,
        datetimeRange: datetimeRangeRef.value,
        address: addressRef.value,
        radius: radius.value,
        withSubsetResult: async (results) => {
          resultsArrRef.value = sortObjectsByTimestamp(
            [...resultsArrRef.value, ...results],
            'desc',
            'datetimeStamp'
          )

          if (resultsArrRef.value.length > 0 && !uiUpdatePromise) {
            uiUpdatePromise = new Promise((resolve) => {
              setTimeout(() => {
                isDatatableCenterLoaderVisible.value = false
                isDatatableLoaderVisible.value = true
                resolve()
              }, 1000)
            })
          }
        },
      })

      if (uiUpdatePromise) {
        await uiUpdatePromise
      }
    } catch (e) {
      console.error('Error fetching vehicles:', e)
    } finally {
      isDatatableLoaderVisible.value = false
      isDatatableCenterLoaderVisible.value = false
      lastSearchUUID.value = currentSearchUUID.value
      loader.hide()
    }
  }, 1000)

  routeNameRef.value = 'results'
}

watch(
  () => store.getters['latestPassedVehicles/addressResult'],
  (value) => {
    addressRef.value = {
      ...value,
    }
  }
)

onMounted(() => {
  store.state.latestPassedVehicles.isLatestPassedVehiclesFeatureActive = true
})

onUnmounted(() => {
  store.dispatch('latestPassedVehicles/reset')

  geocodingComposable.removeLocateAddressMarker()
})

provide('addressFormWrapperStyle', 'left:0px;!important')
provide('isDatatableLoaderVisible', isDatatableLoaderVisible)
provide('latestPassedVehiclesContextStore', contextStore)
provide('isDatatableLoading', isDatatableCenterLoaderVisible)

const computedTreeItemDataset = createTeeItemComputedDataset()

function createTeeItemComputedDataset() {
  const rawItems = computed(() => store.getters['search_module/getVehicles'])
  const rawItemCategories = computed(
    () => store.getters['search_module/getVehicleCategories']
  )

  return computed(() => {
    const filterHandler = (propName) => (circuitItem) => {
      let categoryItem = categories.find((c) => c.id == circuitItem.categoryId)
      return (
        !filterText.value ||
        categoryItem.filterMatchUpwards(filterText.value) ||
        circuitItem[propName]
          .toLowerCase()
          .includes(filterText.value.toLowerCase())
      )
    }

    let categories = rawItemCategories.value.map((categoryItem) => {
      return {
        id: categoryItem.id,
        label: categoryItem.name,
        children: [],
        icon: 'mdi:truck',
        isCategory: true,
        parentId: categoryItem.parent_id || null,
        filterMatchUpwards(text = '') {
          const match = this.label
            .toString()
            .toLowerCase()
            .includes(text.toString().toLowerCase())
          if (match) {
            return true
          } else {
            let parentItem = this.parentId
              ? categories.find((item) => item.id === this.parentId)
              : null
            if (parentItem) {
              return parentItem.filterMatchUpwards(text)
            }
          }
          return false
        },
        countTotalOrphans(children) {
          let count = 0
          children = children || this.children

          count += children.filter((c) => c.isCategory !== true).length

          for (let item of children) {
            count += (item.children || []).filter(
              (child) => child.isCategory !== true
            ).length
            count += this.countTotalOrphans(
              (item.children || []).filter((c) => c.isCategory === true)
            )
          }
          return count
        },
        countTotalOrphansFromFlatArray(currentId, children) {
          let count = 0
          currentId = currentId || this.id
          children = children || this.children

          let cats = rawItemCategories.value

          count += this.children.filter((ccc) => ccc.isCategory !== true).length //direct orphans

          cats
            .filter((c) => c.parent_id == currentId)
            .forEach((c) => {
              //direct childs

              let childs = cats.filter((c) => c.parent_id == c.id)
              count += this.countTotalOrphansFromFlatArray(c.id, childs)

              /*count+= categories.find(cc=>cc.id == c.id).children.filter(ccc=>ccc.isCategory !== true).length
          if(c.parent_id == currentId){
            count+= this.countTotalOrphansFromFlatArray(c.id)
          }*/
            })
          return count
        },
      }
    })

    //Allocate items in each category
    rawItems.value.filter(filterHandler('name')).forEach((circuitItem) => {
      let categoryItem = categories.find((c) => c.id == circuitItem.categoryId)

      let computedCircuitItem = {
        id: circuitItem.id,
        label: circuitItem.name,
        children: [],
      }

      if (categoryItem) {
        categoryItem.children.push(computedCircuitItem)
      } else {
        categories.push(computedCircuitItem)
      }
    })

    //Nest categories
    let childCategories = []
    rawItemCategories.value.forEach((categoryItem) => {
      if (categoryItem.parent_id) {
        let parentCategoryItem = categories.find(
          (c) => c.id == categoryItem.parent_id
        )
        let childIndex = categories.findIndex((cc) => cc.id == categoryItem.id)

        //If the child is a category with no childs and is not a parent, skip
        if (
          categories[childIndex].isCategory === true &&
          categories[childIndex].countTotalOrphansFromFlatArray() == 0
          //categories[childIndex].children.length === 0 &&
        ) {
          return
        }

        parentCategoryItem.children.push(categories[childIndex])
        childCategories.push(categories[childIndex].id)
      }
    })

    return categories.filter(
      (c) =>
        c.countTotalOrphans() > 0 && !childCategories.some((cId) => cId == c.id)
    )
    //.filter(filterHandler('label'))
  })
}

function handleResetForm() {
  //Reset state
  addressRef.value = null
  vehiclesRef.value = []
  datetimeRangeRef.value = []
  filterText.value = ''

  //Trigger children methods by ref
  datetimeRangeVmRef.value?.cancel()
  treeRef.value?.clearSelection()
  simplicitiAddressAutocompleteRef.value?.callAddressFormSelectOnFormReset()
}

async function onOpenTripHistoryTabInLocationModule(item) {
  const payload = getTripHistoryPayload(item)

  //Simulate a search in history mode
  store.state.location_module.isHistoryMode = true

  try {
    //Navigate to location module
    if (router.currentRoute.name !== 'location_module') {
      await router.push('/')
    }

    //Perform search with correct vehicle ids and dates
    await store.dispatch('location_module/performSearchSplitRequests', payload)

    store.state.location_module.loading = false

    //Emit clicked vehicleId and datetime
    mitt.emit('onOpenTripHistoryFromLatestPassedVehicles', {
      date: item.datetime,
      vehicleId: item.vehicleId,
    })
  } catch (error) {
    console.error('Error opening trip history:', error)
  }
}

function getTripHistoryPayload(item) {
  const payload = {}

  //Sets vehicle ids
  payload.ids = resultsArrRef.value.map((res) => res.vehicleId)

  let selectedDates = []

  //Compute start and end date from item datetime, setting start to 00:00 and end to 23:59
  //For now, API returns only one date even if a range, or several dates are selected
  const [m, mEnd] = computeStartAndEndTimeOfDate(
    item.datetime,
    0,
    1,
    '00:00',
    '23:59',
    false
  )

  selectedDates = pushDateRangeToSelection(m, mEnd, selectedDates)

  //Sets dates
  payload.selectedDateRanges = selectedDates

  //Sets selection type
  payload.selectionType = 'vehicle'

  return payload
}

window._lpv = {
  addressRef,
  vehiclesRef,
  datetimeRangeRef,
  routeNameRef,
  setMapToolboxContentMaxWidth,
  canValidate,
  selectedItemRef,
  test() {
    addressRef.value = {
      lat: 47.87697,
      lng: 1.90778,
      formatted: '495, Rue De La Basse Mouillère, 45160, Olivet',
    }
    vehiclesRef.value = [
      8152, 5239, 8106, 5245, 5236, 5262, 27173, 5263, 27167, 28947, 28948,
      5255, 5246, 5241, 5259, 48556, 48557, 8151, 27175, 5272, 58248,
    ]
    datetimeRangeRef.value = [
      '2023-06-25T22:00:00.000Z',
      '2023-06-26T22:00:00.000Z',
      '2023-06-27T22:00:00.000Z',
    ]
  },
}
</script>
<style lang="scss" scoped>
.wrapper {
  min-height: 400px;
  min-width: 580px;
  display: flex;
  column-gap: 10px;
}

.title {
  font: normal normal bold 14px/19px Open Sans;
  letter-spacing: 0px;
  color: var(--color-main);
}

input {
  margin: 5px 0px;
  line-height: 10px;
  font: normal normal normal 14px/16px Open Sans;
  height: 35px;
}

.latest-passed-vehicles-radius-select {
  max-width: 280px;
  color: var(--color-slate-gray);
  font-size: 14px;
  height: 35px;
}
</style>

<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="39.8" height="53" viewBox="0 0 39.8 53">
  <defs>
    <filter id="Pointeur" x="0" y="0" width="39.8" height="53" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1.5" result="blur"/>
      <feFlood flood-opacity="0.361"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Pointeur-carto-arrivée" transform="translate(4.5 3.5)">
    <g transform="matrix(1, 0, 0, 1, -4.5, -3.5)" filter="url(#Pointeur)">
      <path id="Pointeur-2" data-name="Pointeur" d="M20.4,2A15.4,15.4,0,0,0,5,17.4C5,28.95,20.4,46,20.4,46S35.8,28.95,35.8,17.4A15.4,15.4,0,0,0,20.4,2Z" transform="translate(-0.5 1.5)" fill="#ff4545"/>
    </g>
    <rect id="Rectangle_872" data-name="Rectangle 872" width="14" height="14" rx="1" transform="translate(8.384 9.336)" fill="#fff"/>
  </g>
</svg>

import '@/libs/debug'
import 'leaflet/dist/leaflet.css'
import '@csstools/normalize.css'
import 'nprogress/nprogress.css'

import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-vue/dist/bootstrap-vue.css'
import 'bootstrap/dist/js/bootstrap.bundle.min'

import '@/styles/main.scss'

import '@/plugins/vuejs-logger'
import '@/plugins/loader.js'
import '@/plugins/vue-local-storage'
import '@/plugins/global-mixins.js'
import '@/plugins/rights-plugin.js'
import '@/plugins/vue-services.js'
import '@/plugins/router-plugin.js'

import resizeableDirective from '@/directives/resizeable.js'

import i18n from '@/i18n'
import Vue from 'vue'
import router from '@/router'
import store from '@/store'
import '@/filters/index'
import '@/plugins/bootstrap-vue'
import '@/plugins/vue-simple-svg'
import '@/plugins/vue-scrollbar-directive'
import '@/plugins/vue-meta'
import '@/plugins/vue-esc'
import '@/plugins/plugin-i18n-dates'
import '@/plugins/device-detector'
import '@/plugins/vue-select'
import '@/plugins/error-logger'
import '@/plugins/measurement-units'
import '@/plugins/mitt'
import '@/components/desktop/EcoConduite/globals.js'
import '@/plugins/vue-map.js'
import '@/plugins/alert-popup.js'
import '@/plugins/transform-plugin.js'
import '@/plugins/vue-portal.js'
import '@/plugins/click-outside'
import '@/plugins/watch-call-tracker'
import '@/plugins/reset-module-state'

import '@/libs/vue2-datepicker.js'

import '@/plugins/datetime-moment-plugin'

import './components/shared/TLayout/TLayout.vue' //Will load plugin

import { getQueryStringValue } from '@/utils/querystring'
import envService from '@/services/env-service.js'
import useHistorySelection from './composables/useHistorySelection'
import 'iconify-icon'
import { Icon as GlobalIcon } from '@iconify/vue2'
import { getPluginProviders } from '@/plugins/providePlugin.js'

//@ts-ignore
if (typeof window?.debug?.disableShorcut !== undefined) {
  //@ts-ignore
  window.debug.disableShorcut = () =>
    !envService.isBeta() &&
    !envService.isStagging() &&
    !envService.isPreprod() &&
    !envService.isTestEnv()
}

/**
 * Detect whenever google API ready callback is called or not and add a flag we can track if called succefully (used to determine if we show google basemaps or not)
 */
function loadGoogleMapAPIAsync() {
  import('@googlemaps/js-api-loader').then((jsAPILoader) => {
    try {
      function waitForGoogleCallback(cb, start = Date.now()) {
        if (Date.now() - start > 1000 * 5) {
          cb(true)
        }
        if (window.__googleMapsCallback) {
          return cb()
        } else {
          setTimeout(() => waitForGoogleCallback(cb, start), 30)
        }
      }
      waitForGoogleCallback((hasTimeout = false) => {
        if (hasTimeout) {
          window.__gapiLoadSuccess = false
        } else {
          let originalCallback = window.__googleMapsCallback
          window.__googleMapsCallback = function () {
            window.__gapiLoadSuccess = true
            originalCallback.apply(
              window,
              Array.prototype.slice.call(arguments)
            )
          }
        }
      })
      new jsAPILoader.Loader({
        apiKey: import.meta.env.VITE_GOOGLE_KEY,
        version: 'weekly',
      }).load()
    } catch (err) {
      window.__gapiLoadSuccess = false
      console.debugVerbose(8, 'GOOGLE_API_FAIL_TO_LOAD', err.stack)
      Vue.$log.warn('GOOGLE_API_FAIL_TO_LOAD')
    }
  })
}

loadGoogleMapAPIAsync()

Vue.config.productionTip = false

// Vue.use(Vuelidate)

Vue.directive('resizeable', resizeableDirective)
Vue.component('GlobalIcon', GlobalIcon)

let App

if (getQueryStringValue('mockapp') === '1' && !envService.isProduction()) {
  App = () => /* webpackChunkName: "main" */ import('@/AppBasic.vue')
} else {
  App = () => /* webpackChunkName: "main" */ import('@/App.vue')
}

const vueInstance = new Vue({
  i18n,
  router,
  store,
  // Provide access to plugins from SFC components
  // Since we can't use 'this' in those components, use provide globally (#46797)
  provide() {
    return getPluginProviders()
  },
  render: (h) => h(App),
}).$mount('#app')

useHistorySelection({
  store,
})

window.Vue = Vue
window.vue = vueInstance

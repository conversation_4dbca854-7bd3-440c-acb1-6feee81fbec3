/**
 * @namespace Stores
 * @category Stores
 * @module simpliciti-map-store
 * */

import Vue from 'vue'
import positionMarkersService from '@/services/position-markers-service.js'

/**
 * Used by Location module.
 * Applies the speed filter from MapOptions into a single position.
 * @param {*} position
 * @param {*} speedFilter
 * @returns
 */
function isSpeedFilterCriteriaMeet(position, speedFilter) {
  if (speedFilter.condition === '<') {
    return position.vitesse < speedFilter.value
  }
  if (speedFilter.condition === '<=') {
    return position.vitesse <= speedFilter.value
  }
  if (speedFilter.condition === '==') {
    return position.vitesse === speedFilter.value
  }
  if (speedFilter.condition === '>=') {
    return position.vitesse >= speedFilter.value
  }
  if (speedFilter.condition === '>') {
    return position.vitesse > speedFilter.value
  }
  return false
}

function newState() {
  return {
    mapReverseGeocodingOnMouseClickEnabled: true,

    tripStepsmarkers: [], // Contact Off/On colors: 1 (green) ---- 1 (red)
    speedPolylines: [], //Trace vitesse
    alertsMarkers: [],
    eventsMarkers: [],
    circuitExecutionsPolylines: [],
    tripHistoryPolylines: [],
    singleCircuitExecPolylines: [],
    singleTripHistoryPolylines: [],
    chronoPolyline: [],
    chronoMarkers: [],

    circuitVersionsPolylines: [],

    //Used by Location - Realtime/History - Details - History tab
    highlightPolylineSections: {
      singleCircuitExecPolylines: null,
      singleTripHistoryPolylines: null,
    },
    identificationBacsMarkers: [],
    containersMarkers: [],

    /**
     * Used by containers module to update data when user pan the map
     */
    moveendTimestamp: null,
    /**
     * Used by containers module to update data when user pan the map
     */
    mapViewportBounds: null,
    zoomLevel: null,
    lastReverseGeocodingFormatedAdress: {
      formatted: null,
      lat: null,
      lng: null,
    },
  }
}
export default {
  namespaced: true,
  state: newState(),
  getters: {
    mapReverseGeocodingOnMouseClickEnabled: (state) =>
      state.mapReverseGeocodingOnMouseClickEnabled,
    identificationBacsMarkers: (state) => state.identificationBacsMarkers,
    hasData: (state) => (name) => state[name].length > 0,
    tripStepsmarkers: (state) => state.tripStepsmarkers,
    /**
     * Has Contact ON/OFF data
     */
    hasTripStepDataOfType: (state) => (typeName) =>
      state.tripStepsmarkers.findIndex((item) => item.type === typeName) >= 0,
    speedPolylines: (state) => state.speedPolylines,
    alertsMarkers: (state) => state.alertsMarkers,
    eventsMarkers: (state) => state.eventsMarkers,
    containersMarkers: (state) => state.containersMarkers,
    chronoMarkers: (state) => state.chronoMarkers,
    circuitExecutionsPolylines: (state) => state.circuitExecutionsPolylines,
    tripHistoryPolylines: (state) => state.tripHistoryPolylines,
    singleCircuitExecPolylines: (state) => state.singleCircuitExecPolylines,
    singleTripHistoryPolylines: (state) => state.singleTripHistoryPolylines,
  },
  mutations: {
    SET_DATASET(state, { type, data }) {
      state[type] = data || []
      //console.debugVerbose(8,"SET_DATASET", type, (data || []).length);
    },
    resetState(state) {
      let s = newState()
      for (var x in s) {
        state[x] = s[x]
      }
    },
    /**
     * Used by Location - History details
     * @param {*} state
     * @param {*} options.type singleCircuitExecPolylines/singleTripHistoryPolylines
     * @param {*} options.sectionNumber History step number
     * @returns
     */
    highlightPolylineSection(state, { type, sectionNumber }) {
      console.log('highlightPolylineSection', {
        type,
        sectionNumber,
      })

      state.highlightPolylineSections[type] = sectionNumber

      state[type].forEach((thp) => {
        Vue.set(thp, 'highlighted', false)
      })

      if (sectionNumber === null) {
        return
      }
      if (isNaN(parseInt(sectionNumber))) {
        console.warn(
          'highlightPolylineSection::Invalid section number',
          sectionNumber
        )
      }

      //Support highlighting multiple polylines (#31781)
      state[type].forEach((polyline, index) => {
        if (parseInt(polyline.stepNumber) == parseInt(sectionNumber)) {
          let item = state[type][index]
          Vue.set(item, 'highlighted', true)
          //state[type].splice(index, 1) //Trick for reactivity
          //state[type].push(item)
        }
      })
    },
  },
  actions: {
    /**
     * @deprecated
     * @param {*} param0
     */
    initialize({ dispatch }, store) {
      // Initialize the position markers service with the store instance
      // This ensures the service can access speed filter getters
      positionMarkersService.initializeStore(store || this)

      dispatch('zones/syncClientZones', null, {
        root: true,
      })
    },
    /**
     * Set a dataset directly
     * (tripStepmarkers, speedPolylines, etc)
     */
    setDataset({ commit }, { type, data }) {
      // Handle vehiclePositionMarkers through the new service
      if (type === 'vehiclePositionMarkers') {
        positionMarkersService.setPositions(data)
        return
      }

      if (data instanceof Array && data.length === 0) {
        //console.debugVerbose(8,'simpliciti_map::setDataset::empty-array-skip', type)
      }
      commit('SET_DATASET', {
        type,
        data,
      })
    },

    /**
     * Clear multiple datasets at once
     * @param {Object} context - Vuex context
     * @param {Array} datasets - Array of dataset names to clear
     */
    clearDatasets({ state }, datasets) {
      // Handle vehiclePositionMarkers through the new service
      if (datasets.includes('vehiclePositionMarkers')) {
        positionMarkersService.clear()
      }

      const clearedDatasets = datasets.reduce((acc, datasetName) => {
        if (datasetName === 'vehiclePositionMarkers') {
          // Skip - handled by service above
          return acc
        }
        if (state[datasetName]) {
          acc[datasetName] = []
        }
        return acc
      }, {})
      Object.assign(state, clearedDatasets)
    },
    /**
     * Set the chrono markers from chrono data
     *
     * @param commit
     * @param data Array, the chrono data
     */
    setChronoMarkers({ commit }, data) {
      if (!Array.isArray(data) && !data.length > 0) {
        return
      }

      commit('SET_DATASET', {
        type: 'chronoMarkers',
        data: data,
      })
    },
    /**
     * Set trip step markers from polylines data (search: linestringsToPolylines)
     */
    setTripStepMarkers({ commit }, data) {
      let normalizedData = []
      data.forEach((item) => {
        normalizedData.push({
          number: item.stepNumber,
          lat: item.polyline[0][0],
          lng: item.polyline[0][1],
          color: '#1aaa42',
          type: 'contact_on',
        })
        normalizedData.push({
          number: item.stepNumber,
          lat: item.polyline[item.polyline.length - 1][0],
          lng: item.polyline[item.polyline.length - 1][1],
          color: 'var(--color-coral-red)',
          type: 'contact_off',
        })
      })
      commit('SET_DATASET', {
        type: 'tripStepsmarkers',
        data: normalizedData,
      })
    },
    resetStore({ commit }) {
      commit('resetState')
      positionMarkersService.clear()
    },
    /**
     * Used by Location - Realtime/History - Details - History tab
     * @param {*} param0
     * @param {*} sectionNumber
     */
    highlightTripHistoryPolylineSection({ commit }, sectionNumber) {
      commit('highlightPolylineSection', {
        type: 'singleTripHistoryPolylines',
        sectionNumber,
      })
    },
    /**
     * Used by Location - Realtime/History - Details - History tab
     * @param {*} param0
     * @param {*} sectionNumber
     */
    highlightCircuitExecPolylineSection({ commit }, sectionNumber) {
      commit('highlightPolylineSection', {
        type: 'singleCircuitExecPolylines',
        sectionNumber,
      })
    },
  },
}

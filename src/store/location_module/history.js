import historyApi from '@/api/historyApi.js'
import { linestringsToPolylines } from '@/mixins/map'
import { isUnitTest } from '@/utils/unit.js'
import { normalizePositionV2 } from '@/services/entities/position-entity.js'
import {
  fillPolylinesGaps,
  normalizeTripHistoryItem,
} from '@/services/history-service'
import {
  filterValidPositionHandler,
  splitInvalidAndValidPositions,
} from '@/services/position-service'
import { trycatchAPIErrors } from '@/utils/api'

const scope = {
  currentTripHistoryLoading: false,
}

/**
 *
 * @param {*} err
 * @param {*} dispatch
 * @returns {*}
 */
export async function dispatchAlertForAPIV2Error(err, dispatch) {
  if (err.message.includes(`Vous n'avez pas accès aux véhicules fournis`)) {
    return dispatch(
      'alert/addAlert',
      {
        type: 'warning',
        title: '403',
        text: "Vous n'avez pas accès aux véhicules fournis",
      },
      {
        root: true,
      }
    )
  }
  console.error(err)
}

export default {
  state: {
    currentTripHistory: [],
    currentChartTripHistory: [],
  },
  getters: {
    currentTripHistory(state) {
      return state.currentTripHistory || []
    },
  },
  mutations: {
    currentTripHistory(state, tripHistory = []) {
      state.currentTripHistory = tripHistory || []
    },
    currentChartTripHistory: (state, currentChartTripHistory = []) =>
      (state.currentChartTripHistory = currentChartTripHistory),
  },
  actions: {
    /**
     * Updates simpliciti_map store position markers data from
     * v2/historique/positions_capteurs
     *
     * Does simpliciti_map/setDataset with vehiclePositionMarkers
     *
     * @TODO Skip if positions already present
     */
    async updateMapPositionMarkers({ dispatch, rootGetters }, options = {}) {
      console.debug('[location_module] Updating map position markers:', {
        vehicleId: options.vehicleId,
        date: options.date,
      })

      const { vehicleId, date } = options
      if (!vehicleId) {
        return
      }

      //TripHistoryTable.spec.js will fail due to unauthorized API call
      if (!isUnitTest()) {
        await dispatch(
          'map_options/syncSensorConfig',
          {
            vehicleId,
          },
          {
            root: true,
          }
        )
      }

      let shouldSkipPositionsFetch =
        isUnitTest() && options.fakePositions && options.fakeSensorConfig

      let response = shouldSkipPositionsFetch
        ? {}
        : await historyApi.getVehiclePositionsV3(vehicleId, date, options)

      let { valid: positions, invalid: invalidPositions } =
        splitInvalidAndValidPositions(
          (response?.positions || []).map(normalizePositionV2)
        )

      console.debug('[location_module] Got positions response:', {
        positionsCount: positions.length,
        hasConfig: Object.keys(response.config || {}).length > 0,
      })

      if (response) {
        //reComputeSensorsConfigActive: Otherwise, sensor config items remain grey out!
        let config = rootGetters['map_options/sensorsConfig']
        if (isUnitTest() && options.fakePositions && options.fakeSensorConfig) {
          positions = options.fakePositions
          config = options.fakeSensorConfig
        }
        ;(config?.sensors || []).forEach((sensor) => {
          sensor.active = positions.some(
            (position) =>
              position.code == sensor.code ||
              position.code_capteurs.includes(sensor.code)
          )
        })
        dispatch('map_options/setSensorsConfig', config, {
          root: true,
        })
      }

      console.debug(
        '[location_module] Dispatching positions to map:',
        positions.length
      )

      dispatch(
        'simpliciti_map/setDataset',
        {
          type: 'vehiclePositionMarkers',
          data: positions,
        },
        {
          root: true,
        }
      )
    },
    /**
     * Retrieves trip history details for a specific vehicle on a given date.
     *
     * Commits normalized trip history from results.
     * Commits polylines from results.
     *
     * @param {Object} context - The Vuex context object containing dispatch, commit, and rootGetters.
     * @param {Object} payload - An object containing the vehicle ID and date.
     * @param {string} payload.id - The ID of the vehicle.
     * @param {string} payload.date - The date for which trip history is requested.
     * @returns {Array} Non-normalized trip history (APIV2 troncons_details).
     */
    async getTripHistoryFromVehicleDate(context, payload) {
      console.debugVerboseScope(
        ['location_module', 'history'],
        5,
        'store::location_module/getTripHistoryFromVehicleDate',
        {
          context,
          payload,
        }
      )

      const { dispatch, commit, rootGetters } = context
      const { id, date, startDate = '', endDate = '' } = payload

      const setLoading = (value) => {
        scope.currentTripHistoryLoading = value
      }

      const setTripHistory = (data) => {
        if (!data || !data[0]) {
          commit('currentTripHistory', [])
          dispatch(
            'simpliciti_map/setDataset',
            { type: 'singleTripHistoryPolylines', data: [] },
            { root: true }
          )
          dispatch('simpliciti_map/setTripStepMarkers', [], { root: true })
          return
        }

        const tripHistory = data[0]
        const currentTripHistory = tripHistory.troncons.map(
          normalizeTripHistoryItem(id)
        )
        commit('currentTripHistory', currentTripHistory)
        setLoading(false)

        const tripHistoryPolylineColor = (
          rootGetters['location_module/selectedItem'] || {}
        ).tripHistoryPolyline?.color

        let polylines = linestringsToPolylines(currentTripHistory, {
          color: tripHistoryPolylineColor || undefined,
        })

        //feat: Fill gaps https://easyredmine.simpliciti.fr/issues/41178
        polylines = fillPolylinesGaps(polylines)

        dispatch(
          'simpliciti_map/setDataset',
          { type: 'singleTripHistoryPolylines', data: polylines },
          { root: true }
        )
        dispatch(
          'simpliciti_map/setDataset',
          {
            type: 'speedPolylines',
            data: currentTripHistory.reduce((accumulated, currTripHistory) => {
              const linestrings = (currTripHistory.speedLinestring || []).map(
                (linestringColor) => {
                  return {
                    ...currTripHistory,
                    linestring: linestringColor.linestring,
                    color: linestringColor.couleur,
                  }
                }
              )
              return accumulated.concat(linestringsToPolylines(linestrings))
            }, []),
          },
          { root: true }
        )

        dispatch('simpliciti_map/setTripStepMarkers', polylines, { root: true })
      }

      const logError = (err) => {
        console.warn('ERR', err)
        dispatchAlertForAPIV2Error(err, dispatch)
      }

      try {
        setLoading(true)
        const tripHistoryData =
          (await historyApi.getTripHistoryFromVehicleId(
            id,
            date,
            startDate,
            endDate
          )) || []
        setTripHistory(tripHistoryData)
        return tripHistoryData
      } catch (err) {
        logError(err)
        return []
      }
    },
    /**
     * Retrieves one vehicle trip history (normalized for a vertical chart)
     *
     * Updates the vehicle positions (To render trip positions markers)
     * Updates the vehicle trip history linestring (To render trip polylines)
     * Gets startDate and endDate params if provided, otherwise fallback to date
     *
     * @param dispatch
     * @param commit
     * @param state
     * @param {int} id Vehicle ID
     * @param {string} date YYYY-MM-DD
     * @param startDate
     * @param endDate
     */
    async getChartTripHistoryFromVehicleId(
      { dispatch, commit, state },
      { id, date, startDate = '', endDate = '' }
    ) {
      console.debugVerboseScope(
        ['location_module', 'history'],
        5,
        'store::location_module/getChartTripHistoryFromVehicleId'
      )

      /*
      //Re-use data from store
      if (
        !!state.currentChartTripHistory &&
        state.currentChartTripHistory.length > 0
      ) {
        return state.currentChartTripHistory;
      }*/

      const fetchChartTripHistory = async () => {
        const tripHistory = await dispatch('getTripHistoryFromVehicleDate', {
          id,
          date,
          startDate,
          endDate,
        })
        const chartTripHistory =
          await historyApi.getChartTripHistoryFromVehicleId(
            id,
            date,
            startDate,
            endDate,
            tripHistory
          )

        commit('currentChartTripHistory', chartTripHistory)
        return state.currentChartTripHistory
      }

      return await trycatchAPIErrors(fetchChartTripHistory, [], dispatch)
    },
  },
}

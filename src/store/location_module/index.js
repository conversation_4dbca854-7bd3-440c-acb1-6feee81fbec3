import mainSearch from './main_search'
import history from './history'
import circuitDetails from './circuit-details'
import messages from './messages'
import chrono from './chrono'
import alerts from './alerts'
import Vue from 'vue'
import events from './events'
import positionAnalysis from './position-analysis'
import identification from './identification'
import { createMarkerFromRealtimeItem } from '@/mixins/map.js'
import { actions as exportActions } from './exports.js'
import { getNestedValue } from '~/utils/object'
import rightsPlugin from '@/plugins/rights-plugin.js'
import useLocationAutoSelectFirst from '@/composables/useLocationAutoSelectFirst'
import useHistoryLocation from '@/composables/useHistoryLocation'

const { persistSelectedItem } = useHistoryLocation()

const { newState: asfNewState } = useLocationAutoSelectFirst()

function newState() {
  return {
    //Main search results
    vehicleResults: [],
    driverResults: [],
    circuitResults: [],

    isAutoRefreshEnabled: false,

    //Main search selected item
    selectedItem: {},
    //SubMenu history tab state
    ...history.state,
    //SubMenu circuit tab state
    ...circuitDetails.state,
    //SubMenu messages tab state
    ...messages.state,
    //SubMenu alerts tab state
    ...alerts.state,
    //SubMenu events tab state
    ...events.state,
    //SubMenu events tab position-analysis tab state
    ...positionAnalysis.state,
    //SubMenu chrono tab state
    ...chrono.state,
    //SubMenu identification tab state
    ...identification.state,
    //mainSearch state
    ...mainSearch.state,
    //Used by some components (far in the tree) to change the submenu current tab after a layout change (i.g: see LocationEventsTable)
    subMenuView: '',
    //loading state
    loading: false,
    //auto select first composable state
    ...asfNewState(),
  }
}

/***
 * @namespace Stores
 * @category Stores
 * @module location-module-store
 */
export default {
  namespaced: true,
  state: newState(),
  getters: {
    loading: (state) => state.loading,
    ...mainSearch.getters,
    isRealtimeSearch(state, getters, rootState, rootGetters) {
      return (
        rootGetters['search_module/getSelection'].selectedDateRanges.length ===
        0
      )
    },
    subMenuView: (state) => state.subMenuView,
    selectedItem(state) {
      let computed = { ...(state.selectedItem || {}) }

      let historyDatetimeStampFallbackValue = (fieldName) =>
        getNestedValue(state, 'currentChartTripHistory.0.' + fieldName, null, {
          transform(v) {
            if (!v) {
              return undefined
            } else {
              return v._d.getTime() //timestamp
            }
          },
        })

      computed.historyStartDatetimeStamp =
        computed.historyStartDatetimeStamp ||
        historyDatetimeStampFallbackValue('fromDatetime')
      computed.historyEndDatetimeStamp =
        computed.historyEndDatetimeStamp ||
        historyDatetimeStampFallbackValue('toDatetime')

      return computed
    },
    getResultsFromType(state) {
      return (type) => state[`${type}Results`]
    },
    getRealtimeMarkersFromActiveSearchFormTabResults(
      state,
      getters,
      rootState,
      rootGetters
    ) {
      const items = getters['getResultsFromActiveSearchFormTab'] || []
      return items.map(createMarkerFromRealtimeItem)
    },
    getResultsFromActiveSearchFormTab(state, getters, rootState, rootGetters) {
      let currentTabName = rootGetters['search_module/activeSearchFormTabName']
      return (
        (!!currentTabName && getters['getResultsFromType'](currentTabName)) ||
        []
      )
    },
    getRawResultsFromActiveSearchFormTab(
      state,
      getters,
      rootState,
      rootGetters
    ) {
      let currentTabName = rootGetters['search_module/activeSearchFormTabName']
      return getters['getResultsFromType'](currentTabName).map(
        (item) => item.raw
      )
    },
    currentTabContainsHistoryResults(state, getters, rootState, rootGetters) {
      let currentTabName = rootGetters['search_module/activeSearchFormTabName']
      return (
        (getters['getResultsFromType'](currentTabName).length > 0 &&
          getters['getResultsFromType'](currentTabName)[0].searchType ===
            'history') ||
        false
      )
    },
    hasCircuitResultsFromHistory(state, getters) {
      return (
        (getters['getResultsFromType']('circuit').length > 0 &&
          getters['getResultsFromType']('circuit')[0].searchType ===
            'history') ||
        false
      )
    },
    areResultsFromTypeXFromHistory: (state, getters) => (type) => {
      const results = getters['getResultsFromType'](type) || []
      return (
        (results.length > 0 && results[0].searchType === 'history') || false
      )
    },
    getZoomCenter(state) {
      return state.zoom_center
    },
    ...history.getters,
    ...circuitDetails.getters,
    ...messages.getters,
    ...alerts.getters,
    ...events.getters,
    ...positionAnalysis.getters,
    ...chrono.getters,
    ...identification.getters,
  },
  mutations: {
    setLoading(state, value) {
      state.loading = value
    },
    setResults(state, { type, value }) {
      state[`${type}Results`] = value
    },
    /**
     * Main search split API requests. API responses need to be merged into existing results.
     */
    mergeResults(state, { type, value }) {
      let filteredValues = value
      // un circuit peut être exécuté à cheval sur plusieurs jours.
      if (type === 'circuit') {
        filteredValues = filteredValues.filter(
          (item) =>
            !state[`${type}Results`].find(
              (i) => i.circuitExecutionId === item.circuitExecutionId
            )
        )
      }
      state[`${type}Results`] = [
        ...state[`${type}Results`],
        ...filteredValues,
        /*
       @todo: Refactor/Remove: Still needed? Will skip results if history... because the same vehicle can be present in multiple days
        ...value.filter(
          (item) => !state[`${type}Results`].find((i) => i.id == item.id)
        ),
        */
      ]
    },

    selectItem(state, item = {}) {
      state.selectedItem = item || {}
    },
    resetState(state) {
      let s = newState()
      for (var x in s) {
        state[x] = s[x]
      }
    },
    ...history.mutations,
    ...circuitDetails.mutations,
    ...alerts.mutations,
    ...events.mutations,
    ...positionAnalysis.mutations,
    ...chrono.mutations,
    ...identification.mutations,
    resetSelectedItemStateCommon(state) {
      let newPartialState = {
        currTripHistory: [], //Trip history (Itineraire)
        currentChartTripHistory: [],
        events: [], //Events tab
        positionAnalysisList: [], //Position analysis tab
        positionAnalysisPopupOpened: false,
        positionAnalysisResult: {},
        chronoData: {}, //Chrono tab
        identificationBacsSynthesis: [], //Identification tab
        identificationBacsDetails: [],
        identificationBacModal: {
          isOpen: false,
          data: {},
        },
        subMenuView: '',
      }
      for (var x in newPartialState) {
        state[x] = newPartialState[x]
      }
    },
    resetSelectedItem(state) {
      state.selectedItem = {}
    },
  },
  actions: {
    /**
     * Sometimes the circuit completion rate from circuit tab has a newer % and in that case we decided to patch the results from the main search.
     * @returns
     */
    patchRealtimeResultCircuitCompletionRate({ rootGetters, state, getters }) {
      const selectedItem = getters['selectedItem']

      //bail out if there is no circuit execution on the way (En cours)
      if (!selectedItem.circuitId) {
        return
      }

      const currentTabName =
        rootGetters['search_module/activeSearchFormTabName']
      const results = state[`${currentTabName}Results`]

      //Grab % from circuit tab
      const circuitDetails = getters['circuitDetails']

      const isCircuitCompletionRateIncludingIgnoredCircuitSteps =
        rightsPlugin.hasRight(
          'GEOV3_LOCALISATION_HISTO_VIEW_IGNORED_ROUTE_SECTIO'
        )
      const circuitCompletionRateField =
        isCircuitCompletionRateIncludingIgnoredCircuitSteps
          ? 'ignored_taux_realisation'
          : 'taux_realisation_circuit'

      const circuitCompletionRate = circuitDetails[circuitCompletionRateField]

      if (selectedItem.circuitCompletionRate == circuitCompletionRate) {
        return
      } else {
        //This means that the circuit details tab has a more recent %

        //Match selectedItem inside results by id field and patch element updating rate completion %
        let index = results.findIndex((x) => x.id == selectedItem.id)
        if (index > -1) {
          Vue.set(results, index, {
            ...results[index],
            circuitCompletionRate: circuitCompletionRate,
          })

          //FRA (https://easyredmine.simpliciti.fr/issues/47071) trigger the call api on the wrong vehicle when we stay on the circuit tab and select another vehicle
          // in the CircuitExecDetails.vue it detects a difference between the new data and the old data
          // Vue.set(selectedItem, '_updatedAt', Date.now()) //Dummy set to trigger a Vue refresh
        }
      }
    },
    clearResultsByType({ commit }, type) {
      commit('setResults', { type, value: [] })
    },
    resetStore({ commit, dispatch }) {
      commit('resetState')
      dispatch('resetSelectedItemRelatedStores')
    },
    resetSelectedItemRelatedStores({ dispatch }) {
      dispatch(
        'simpliciti_map/clearDatasets',
        [
          'identificationBacsMarkers',
          'tripStepsmarkers',
          'speedPolylines',
          'vehiclePositionMarkers',
          'singleTripHistoryPolylines',
          'alertsMarkers',
          'singleCircuitExecPolylines',
          'eventsMarkers',
          'chronoMarkers',
        ],
        {
          root: true,
        }
      )
      dispatch('map_options/resetStore', null, { root: true })
    },
    async selectItem({ commit, dispatch, rootGetters }, item) {
      await dispatch('resetSelectedItemState')

      Vue.nextTick(() => {
        commit('selectItem', item)
      })

      let openSubMenu = item.openSubMenu ? item.openSubMenu : false
      delete item.openSubMenu

      if (
        Object.keys(item).length > 0 &&
        openSubMenu &&
        !rootGetters['app/layout'].isSubMenuVisible
      ) {
        dispatch(
          'app/changeLayout',
          {
            origin: 'store/location_module::selectItem',
            right_menu: false,
            right_menu_bottom: false,
            sub_menu: () =>
              /* webpackChunkName: "main" */
              import('@c/shared/SearchResults/Submenu/SubMenu.vue'),
          },
          { root: true }
        )
      }
      if (Object.keys(item).length > 0) {
        persistSelectedItem(item)
      }
    },
    async selectItemById({ getters, dispatch }, { id, selectionType }) {
      return new Promise(async (resolve) => {
        if (id && selectionType) {
          await dispatch('resetSelectedItemState')
          Vue.nextTick(async () => {
            await dispatch(
              'selectItem',
              getters['getResultsFromType'](selectionType).find(
                (item) => item.id == id
              )
            )
            resolve()
          })
        }
      })
    },
    /**
     * Reset all the data related to an item selection (vehicle/driver/circuit). (i.g alerts, events, chrono, etc)
     */
    resetSelectedItemState({ state, commit, dispatch }) {
      if (!state.selectedItem || Object.keys(state.selectedItem).length === 0) {
        return
      }
      //Data related to selection:
      commit('resetCircuitExecutionDetails') //Circuit execution details

      commit('resetAlertsState') //Alerts tab
      commit('resetSelectedItemStateCommon')
      //Data loaded into other stores
      dispatch('resetSelectedItemRelatedStores')
    },
    onModuleExit({ dispatch }) {
      dispatch('search_module/resetStatePartial', null, {
        root: true,
      })
      vue.$store.state.search_module.searchString = ''
    },
    ...mainSearch.actions,
    ...history.actions,
    ...circuitDetails.actions,
    ...messages.actions,
    ...alerts.actions,
    ...events.actions,
    ...positionAnalysis.actions,
    ...chrono.actions,
    ...identification.actions,
    ...exportActions,
  },
}

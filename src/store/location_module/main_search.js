import moment from 'moment'
import { createCircuitColorsManager } from '@/services/location-service.js'
import APIUrls from '@/config/simpliciti-apis.js'
import api from '@/api'
import {
  parseLocationItem,
  getHistoryAPIQueryStringDatePart,
  getItemsFromHistoryVehicleDriverAPIV2Response,
  getItemsFromHistoryCircuitAPIV2Response,
  getItemsFromHistoryDriverAPIV2Response,
  getItemsFromHistoryVehicleDriverAPIV3Response,
} from './helpers'
import { createPromiseTracker, splitOperation } from '@/utils/promise'
import { chunkArray, chunkIds } from '@/utils/array'
import { getEnvValue } from '@/services/env-service'
import { fetchVehicleDatesWithDataManager } from '@/services/search-service.js'
import { createOperationCancelManager } from '@/utils/workflow'
import useSearchService from '@/composables/useSearchService'
import { getRelativeFormattedPastDate, isMomentToday } from '@/utils/dates'
import store from '@/store'
const LocationServiceTripHistoryColorManager = createCircuitColorsManager()
const searchCancelManager = createOperationCancelManager('location search')
const promiseTracker = createPromiseTracker()
const searchRequestURLResolver = createSearchRequestURLResolver()
const { getVehicleDatesDataManagerSegregationKey } = useSearchService()
/**
 * Generates API Url to hit based on search type (vehicle/driver/circuit) and mode (realtime/history)
 * @returns
 */
export function createSearchRequestURLResolver() {
  const baseUrlData = {
    realTime: APIUrls.APIV3_GEORED_SERVICE_REALTIME,
    history: {
      vehicle: APIUrls.APIV3_GEORED_SERVICE_HISTORY_VEHICLES,
      driver: APIUrls.APIV2_HISTORIQUE_BY_DRIVER_DATES,
      circuit: APIUrls.APIV2_HISTORIQUE_BY_CIRCUIT_DATES,
    },
  }
  return function getLocationMainSearchRequestURL(
    selectionType,
    isHistoryMode,
    allIds,
    extraParams,
    shouldUseApiV3 = false
  ) {
    //Throw error if invalid selection type
    const validSelectionTypes = ['vehicle', 'driver', 'circuit']
    if (!validSelectionTypes.includes(selectionType)) {
      console.error(`Invalid selection type : ${selectionType}`)
      return
    }

    //Edge cases : throw console error if incompatibility of api use
    if (!isHistoryMode && !shouldUseApiV3) {
      console.error('API V3 should be called for real-time search')
      return
    }

    if (isHistoryMode && selectionType === 'vehicle' && !shouldUseApiV3) {
      console.error('API V3 should be called for vehicle history search')
      return
    }

    if (
      isHistoryMode &&
      shouldUseApiV3 &&
      ['circuit', 'driver'].includes(selectionType)
    ) {
      console.error(
        'API V2 should be called for round and driver history search'
      )
      return
    }

    //Get baseUrl depending on isHistoryMode and selectionType
    const baseUrl = isHistoryMode
      ? baseUrlData.history[selectionType]
      : baseUrlData.realTime
    const requestParamName = {
      vehicle: shouldUseApiV3 ? 'vehicleId[]' : 'vehicule_id',
      driver: shouldUseApiV3 ? 'driverId[]' : 'chauffeur_id',
      circuit: shouldUseApiV3 ? 'roundId[]' : 'circuit_id',
    }[selectionType]

    // Format payload depending on api version
    const queryParams = shouldUseApiV3
      ? allIds.map((id) => `${requestParamName}=${id}`).join('&')
      : `${requestParamName}=${allIds.join(',')}`

    return `${baseUrl}?${queryParams}${extraParams}`
  }
}

const searchModuleSelectedKeys = {
  vehicle: 'selectedVehiclesIds',
  driver: 'selectedDriversIds',
  circuit: 'selectedCircuitsIds',
}

/***
 * @namespace Stores
 * @category Stores
 * @module location-module-store-main-search
 * @memberof location-module-store
 */
export default {
  namespaced: true,
  state: {
    shouldUseApiV3: false,
    isHistoryMode: false,
  },
  getters: {
    mainSearchPromiseTracker: () => promiseTracker,
  },
  actions: {
    /**
     * Mark all active search as canceled. Search operations will abort/bail-out.
     * @param {*} storeParams
     */
    cancelAnyOperationInProgress(storeParams) {
      console.debugVerbose(8, 'location cancelAnyOperationInProgress')
      searchCancelManager.cancelAnyOperationInProgress()
    },
    /**
     * Split by items feature: Requests are split if items (vehicle/driver/circuit) are > 5
     * Merge strategy: Merge results if not first request
     * Sequential: Yes
     * Parallel: No
     * @param {Array} payload.ids Optional vehicle/drivers/circuit ids.
     * @param {Function} payload.onSuccessSplitItems Optional Hook used by unit-test.
     * @returns
     */
    async performSearchSplitRequests(
      { dispatch, state, rootGetters, commit },
      payload = {}
    ) {
      /*console.debugVerbose(8,'location search split-start', {
        payload,
      })*/
      return new Promise(async (resolve, reject) => {
        commit('setLoading', true)

        try {
          const trackAll = console.trackTime(
            'location search split operation',
            [8, 'location']
          )

          const { selectionType } = payload
          const searchSelection = rootGetters['search_module/getSelection']

          let allIds =
            payload.ids ||
            searchSelection[searchModuleSelectedKeys[selectionType]]

          if (payload.selectedDateRanges) {
            searchSelection.selectedDateRanges = payload.selectedDateRanges
          }

          const datesLen = searchSelection.selectedDateRanges.length

          const hasTodayInRange = searchSelection.selectedDateRanges.some(
            ([startDate, endDate]) =>
              isMomentToday(startDate) || isMomentToday(endDate)
          )

          //Feat: If a vehicle has no data for the selected dates, skip it. (#40548)
          // Don't check vehicles dates with data if search is made in history mode and has today in range (#47486)
          if (
            selectionType === 'vehicle' &&
            state.isHistoryMode &&
            !hasTodayInRange
          ) {
            console.debugVerbose(
              8,
              'DEBUG: Filtering out vehicle ids with no data'
            )
            const dates = searchSelection.selectedDateRanges.map((arr) =>
              moment(arr[0]).format('YYYY-MM-DD')
            )
            const vehicleDatesDataAvail =
              await fetchVehicleDatesWithDataManager.checkVehicleDatesWithData(
                {
                  vehicleIds: allIds,
                  dates,
                },
                getVehicleDatesDataManagerSegregationKey(
                  rootGetters['auth/toClientId'] || rootGetters['auth/clientId']
                )
              ) //[{vehicleId:1,dates:[],noDates:[]}]

            allIds = allIds.filter((vehicleId) => {
              //If some date is missing in vehicleDatesDataAvail noDates, include.
              const match = vehicleDatesDataAvail.find(
                (i) => i.vehicleId === vehicleId
              )
              if (match) {
                const exclude =
                  dates.filter((d) => match.noDates.includes(d)).length ===
                  dates.length
                if (exclude) {
                  console.debugVerbose(
                    8,
                    'performSearchSplitRequests: Exclude vehicle id from search (no-data)',
                    vehicleId
                  )
                } else {
                  console.debugVerbose(
                    8,
                    'performSearchSplitRequests: Include vehicle id from search (data)',
                    vehicleId
                  )
                }
                return !exclude
              } else {
                return true
              }
            })
          }

          const groupLengths = {
            vehicle: getEnvValue(
              'VITE_LOCATION_SEARCH_VEHICLE_SIZE',
              getEnvValue('VITE_LOCATION_SEARCH_ITEMS_SIZE', 2, {
                transform: (v) => parseInt(v) || 2,
              })
            ),
            driver: getEnvValue(
              'VITE_LOCATION_SEARCH_DRIVER_SIZE',
              getEnvValue('VITE_LOCATION_SEARCH_ITEMS_SIZE', 2, {
                transform: (v) => parseInt(v) || 2,
              })
            ),
            circuit: getEnvValue(
              'VITE_LOCATION_SEARCH_CIRCUIT_SIZE',
              getEnvValue('VITE_LOCATION_SEARCH_ITEMS_SIZE', 2, {
                transform: (v) => parseInt(v) || 2,
              })
            ),
          }

          LocationServiceTripHistoryColorManager.resetState()
          const searchItem = searchCancelManager.createNew()
          const splitScope = {
            firstDone: false, //Flag to indicate whenever to merge any posterior operation after first one.
            firstPromise: null,
          }
          const maxParallelRequestsCount = getEnvValue(
            'location_search_max_parallel_requests',
            10,
            {
              transform: (v) => parseInt(v) || 10,
            }
          )

          //Tries to find a chunks combination to control number of parallel requests
          await splitOperation({
            sequential: true,
            generateSubsets() {
              let chunks = chunkIds(
                allIds,
                datesLen || 1,
                maxParallelRequestsCount
              )
              console.debugVerboseScope(
                8,
                'location',
                'location main search split 1 subsets',
                {
                  maxParallelRequestsCount,
                  chunksLen: chunks.length,
                  chunks,
                  allIds,
                }
              )
              return chunks
            },
            async handleSubset(ids, upperIndex, len) {
              let maxItemsSize = groupLengths[selectionType] //default to 5

              let track = console.trackTime(
                `location search split operation (parallel) (est ${
                  ids.length * datesLen
                } reqs) (${upperIndex + 1}/${len})`,
                [8, 'location']
              )
              //Tries to respect a max number of vehicles per request (customizable)
              await splitOperation({
                sequential: false,
                generateSubsets() {
                  let c = chunkArray(ids, maxItemsSize)
                  console.debugVerboseScope(
                    8,
                    'location',
                    'location search split 2 subsets',
                    c
                  )
                  return c
                },
                async handleSubset(chunkOfIds, index, len) {
                  if (!searchItem.shouldContinue()) {
                    searchItem.logAbort('split-handler-before')
                    return null
                  }
                  //console.debugVerbose(8,'location search split handleSubset', chunkOfIds)

                  const key = `Key:${upperIndex}-${index}`

                  let trackInner = console.trackTime(
                    `location search split operation (chunk operation) ${key} (${
                      index + 1
                    }/${len})`,
                    false
                  )

                  //Split one request per date/day (History)
                  await dispatch('performSearch', {
                    ...payload,
                    ids: chunkOfIds,
                    splitScope,
                    merge: splitScope.firstDone,
                    searchItem,
                    key,
                  })
                  trackInner.stop()
                },
              })
              track.stop()
            },
          })

          searchItem.remove()

          /*
        console.debugVerbose(8,'performSearchSplitRequests::after', {
          results:
            rootGetters['location_module/getResultsFromType'](selectionType),
        })*/

          resolve()

          trackAll.stop()
        } finally {
          /*setTimeout(() => {
            if (!searchCancelManager.thereArePendingOperations()) {
              commit('setLoading', false)
            }
          }, 1000)*/
        }
      })
    },

    /**
     * Performs search by vehicle/circuit/drivers using real-time/history API.
     * INTERNAL Called from performSearchSplitRequests
     * @internal
     * @param {string} selectionType vehicle/driver/circuit
     * @param {Array} ids Ids from items (vehicleIds, driverIds, circuitIds)
     * @param {Object} searchItem Cancel manager instance
     * @param {Object} splitScope Holds internal scope from split fn
     * @param {string} key Internal unique key
     * @returns
     */
    async performSearch(
      { dispatch, state, rootGetters, rootState },
      payload = {}
    ) {
      /*console.debugVerbose(8,'location main search performSearch', {
        payload,
      })*/

      if (!payload.selectionType) {
        throw new Error('selectionType required')
      }

      if (!payload.searchItem.shouldContinue()) {
        payload.searchItem.logAbort('performSearch-1')
        return
      }

      const { selectionType } = payload

      const searchSelection = {
        ...rootGetters['search_module/getSelection'],
      }

      const allIds =
        payload.ids || searchSelection[searchModuleSelectedKeys[selectionType]]
      const isFirstAndLastDay = rootState.search_module.isFirstAndLastDay
      const dateRanges = searchSelection.selectedDateRanges
      const skip = allIds.length === 0

      /*
      console.log(
        'location performSearch',
        {
          selectionType,
          isHistoryMode,
          allIds,
          payload,
        },
        {
          skip,
        }
      )*/

      if (skip) {
        return
      }

      if (state.isHistoryMode) {
        //Split one request per date/day (History mode)
        await splitOperation({
          sequential: false, //Parallel
          generateSubsets() {
            return dateRanges.map((singleDateRange) => {
              const start = moment(singleDateRange[0])
                .hour(moment(singleDateRange[0]).hour())
                .minute(moment(singleDateRange[0]).minute())._d
              const end = moment(singleDateRange[1])
                .hour(moment(singleDateRange[1]).hour())
                .minute(moment(singleDateRange[1]).minute())._d
              return [start, end]
            })
          },
          async handleSubset(dateArr, index, len) {
            let track = console.trackTime(
              `location search split operation (history-date-subset) 
              ${payload.key}
              (${index + 1}/${len})`,
              false
            )
            await performSearchInternal(
              allIds,
              [dateArr], //Convert to date ranges
              selectionType,
              state.isHistoryMode,
              isFirstAndLastDay
            )
            track.stop()
          },
        })
      } else {
        await performSearchInternal(
          allIds,
          dateRanges,
          selectionType,
          state.isHistoryMode,
          isFirstAndLastDay
        )
      }

      /**
       * API call, normalize response and add data to store.
       */
      async function performSearchInternal(
        ids,
        dates,
        selectionType,
        isHistoryMode,
        isFirstAndLastDay
      ) {
        state.shouldUseApiV3 = !isHistoryMode || selectionType === 'vehicle'

        const shouldUseApiV3 = state.shouldUseApiV3

        let extraParams = ''
        extraParams += `${getHistoryAPIQueryStringDatePart(
          dates,
          isFirstAndLastDay,
          shouldUseApiV3
        )}`

        if (isHistoryMode) {
          extraParams += '&linestring=1' // Bring circuit execution linestrings, works only with apiV2 in history mode
        } else {
          const realTimeNbMonths =
            store.getters['auth/getRealTimeNbMonthsFromClientParams']

          extraParams += `&newerThan=${getRelativeFormattedPastDate(
            'months',
            realTimeNbMonths
          )}` //Ticket #47822 : compliance to CNIL rules, only request data newer than given date in real-time mode (fallback to 3)
        }

        //Temporary until using APIV3 for history mode for rounds and drivers
        if (!shouldUseApiV3) {
          extraParams += '&linestring_troncons=1'
          if (selectionType === 'driver') {
            extraParams += '&linestring_multiple=true'
          }
        }

        let infos

        // Get infos from correct API version
        if (shouldUseApiV3) {
          infos = (
            await promiseTracker.trackPromise(
              api.v3.get(
                searchRequestURLResolver(
                  selectionType,
                  isHistoryMode,
                  ids,
                  extraParams,
                  shouldUseApiV3
                )
              )
            )
          ).data
        } else {
          infos = (
            await promiseTracker.trackPromise(
              api.v2.post(
                searchRequestURLResolver(
                  selectionType,
                  isHistoryMode,
                  ids,
                  extraParams,
                  shouldUseApiV3
                )
              )
            )
          ).data
        }

        if (!payload.searchItem.shouldContinue()) {
          payload.searchItem.logAbort('after-api-response')
          return null
        }

        if (payload.splitScope.firstDone) {
          await payload.splitScope.firstPromise
        }

        let promise = dispatch('setResultsFromAPIResponse', {
          selectionType,
          infos,
          merge: payload.splitScope.firstDone,
          isHistoryMode,
          shouldUseApiV3,
        })

        if (!payload.splitScope.firstDone) {
          payload.splitScope.firstDone = true
          payload.splitScope.firstPromise = promise
        }

        return await promise
      }
    },
    /**
     *
     * Transform/Normalize and set/merge real-time/history APIV2 responses into Location store
     *
     * @param {Object} VuexContext
     * @param {String} options.selectionType Required (vehicle/driver/circuit)
     * @param {Array} options.infos Required
     * @param {Boolean} options.merge Whenever to merge results into existing results or overwrite them
     * @param options
     */
    async setResultsFromAPIResponse(
      { commit, rootGetters, state, dispatch },
      options = {}
    ) {
      if (options.infos === null) {
        return
      }

      /*
      console.debugVerbose(8,'location search set/merge', {
        options,
      })*/

      //Normalization requires search_module to be initialized
      await dispatch(
        'search_module/initialize',
        {},
        {
          root: true,
        }
      )

      let { selectionType, infos, isHistoryMode, shouldUseApiV3 } = options

      const normalizedResults = getNormalizedResults(
        infos,
        selectionType,
        rootGetters,
        isHistoryMode,
        shouldUseApiV3
      )

      commit(options.merge ? 'mergeResults' : 'setResults', {
        type: selectionType,
        value: normalizedResults,
      })

      let hasResults =
        state.vehicleResults.length > 0 ||
        state.driverResults.length > 0 ||
        state.circuitResults.length > 0

      dispatch('search_module/setHasResults', hasResults, {
        root: true,
      })
    },
  },
}

/**
 * Normalizes API responses into a flat array
 *
 * -APIV2 Responses are not flat and there are different (history/realtime)
 */
export function getNormalizedResults(
  apiResponse,
  selectionType,
  rootGetters,
  isHistoryMode = false,
  shouldUseAPIV3 = false
) {
  let options = { selectionType }

  options.isHistoryMode = isHistoryMode
  options.shouldUseApiV3 = shouldUseAPIV3

  let flatResults = []
  if (options.isHistoryMode) {
    if (['vehicle'].includes(selectionType)) {
      flatResults = shouldUseAPIV3
        ? getItemsFromHistoryVehicleDriverAPIV3Response(apiResponse)
        : getItemsFromHistoryVehicleDriverAPIV2Response(apiResponse)
    }

    if (['driver'].includes(selectionType)) {
      flatResults = getItemsFromHistoryDriverAPIV2Response(apiResponse)
    }

    if (['circuit'].includes(selectionType)) {
      flatResults = getItemsFromHistoryCircuitAPIV2Response(apiResponse)
    }

    flatResults = flatResults.map((item) => {
      item.couleur =
        LocationServiceTripHistoryColorManager.getUnusedColorAsString(
          shouldUseAPIV3 ? item.vehicleId : item.vehicule_id
        )
      //console.info('Grabing color for vehicle', item.vehicule_id, item.couleur)
      return item
    })
  } else {
    flatResults = apiResponse //Realtime API already brings a flat array
  }

  return (flatResults || []).map((item) =>
    parseLocationItem(item, options, rootGetters)
  )
}
